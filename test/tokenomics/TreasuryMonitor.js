/**
 * Treasury Monitoring System
 * 
 * Monitors the hot wallet balance and treasury sustainability in real-time.
 * Tracks inflows (purchases) vs outflows (rewards) to assess economic balance.
 */

import { ethers } from 'ethers';
import fetch from 'node-fetch';

export class TreasuryMonitor {
    constructor(config, provider) {
        this.config = config;
        this.provider = provider;
        this.isMonitoring = false;
        
        // Balance tracking
        this.balanceHistory = [];
        this.currentBalance = 0;
        this.initialBalance = 0;
        
        // Flow tracking
        this.inflows = []; // Purchases, deposits
        this.outflows = []; // Rewards, creator payments
        
        // Monitoring interval
        this.monitoringInterval = null;
        this.balanceCheckInterval = 5000; // Check every 5 seconds
        
        // Sustainability metrics
        this.sustainabilityMetrics = {
            totalInflows: 0,
            totalOutflows: 0,
            netFlow: 0,
            burnRate: 0, // ETH per second
            projectedRuntime: 0, // Seconds until treasury depleted
            riskLevel: 'low'
        };

        console.log('💰 TreasuryMonitor initialized');
    }

    /**
     * Start monitoring treasury balance
     */
    async start() {
        if (this.isMonitoring) {
            console.warn('⚠️ TreasuryMonitor already running');
            return;
        }

        try {
            // Get initial balance
            this.initialBalance = await this.getHotWalletBalance();
            this.currentBalance = this.initialBalance;
            
            console.log(`💰 Initial treasury balance: ${this.initialBalance} ETH`);
            
            // Record initial balance
            this.recordBalance(this.initialBalance, 'initial');
            
            // Start monitoring
            this.isMonitoring = true;
            this.startTime = Date.now();
            
            // Set up periodic balance checks
            this.monitoringInterval = setInterval(async () => {
                await this.checkBalance();
            }, this.balanceCheckInterval);
            
            console.log('🚀 TreasuryMonitor started');
            
        } catch (error) {
            console.error('❌ Failed to start TreasuryMonitor:', error);
            throw error;
        }
    }

    /**
     * Stop monitoring treasury balance
     */
    async stop() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        this.endTime = Date.now();
        
        // Clear monitoring interval
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }
        
        // Final balance check
        await this.checkBalance();
        
        console.log('🛑 TreasuryMonitor stopped');
        console.log(`📊 Monitored for ${this.endTime - this.startTime}ms`);
        console.log(`💰 Final balance: ${this.currentBalance} ETH`);
        console.log(`📈 Balance change: ${(this.currentBalance - this.initialBalance).toFixed(6)} ETH`);
    }

    /**
     * Get hot wallet balance from blockchain
     */
    async getHotWalletBalance() {
        try {
            const balanceWei = await this.provider.getBalance(this.config.hotWalletAddress);
            const balanceETH = parseFloat(ethers.formatEther(balanceWei));
            return balanceETH;
        } catch (error) {
            console.error('❌ Failed to get hot wallet balance:', error);
            throw error;
        }
    }

    /**
     * Get hot wallet balance via API
     */
    async getHotWalletBalanceAPI() {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/wallet/balance`, {
                method: 'GET',
                headers: {
                    'Authorization': `Bearer ${this.config.apiAuthToken || 'test-token'}`
                }
            });

            if (!response.ok) {
                throw new Error(`API balance check failed: ${response.status}`);
            }

            const result = await response.json();
            return parseFloat(result.balance);
        } catch (error) {
            console.error('❌ Failed to get balance via API:', error);
            // Fallback to direct blockchain query
            return await this.getHotWalletBalance();
        }
    }

    /**
     * Check current balance and update tracking
     */
    async checkBalance() {
        if (!this.isMonitoring) return;

        try {
            const newBalance = await this.getHotWalletBalance();
            const balanceChange = newBalance - this.currentBalance;
            
            // Record balance change
            this.recordBalance(newBalance, 'periodic', balanceChange);
            
            // Update current balance
            this.currentBalance = newBalance;
            
            // Analyze balance change
            if (Math.abs(balanceChange) > 0.001) { // Significant change (> 0.001 ETH)
                this.analyzeBalanceChange(balanceChange);
            }
            
            // Update sustainability metrics
            this.updateSustainabilityMetrics();
            
        } catch (error) {
            console.error('❌ Balance check failed:', error);
        }
    }

    /**
     * Record balance in history
     */
    recordBalance(balance, type, change = 0) {
        const record = {
            timestamp: Date.now(),
            balance: balance,
            change: change,
            type: type, // 'initial', 'periodic', 'transaction'
            blockNumber: null // Could be populated if needed
        };

        this.balanceHistory.push(record);
        
        if (type === 'periodic' && Math.abs(change) > 0.001) {
            console.log(`💰 Balance change: ${change > 0 ? '+' : ''}${change.toFixed(6)} ETH (${balance.toFixed(6)} ETH total)`);
        }
    }

    /**
     * Analyze balance change to determine if it's inflow or outflow
     */
    analyzeBalanceChange(change) {
        const changeRecord = {
            timestamp: Date.now(),
            amount: Math.abs(change),
            type: change > 0 ? 'inflow' : 'outflow',
            reason: this.inferChangeReason(change)
        };

        if (change > 0) {
            this.inflows.push(changeRecord);
            this.sustainabilityMetrics.totalInflows += Math.abs(change);
            console.log(`📈 Treasury inflow detected: +${change.toFixed(6)} ETH`);
        } else {
            this.outflows.push(changeRecord);
            this.sustainabilityMetrics.totalOutflows += Math.abs(change);
            console.log(`📉 Treasury outflow detected: ${change.toFixed(6)} ETH`);
        }

        // Update net flow
        this.sustainabilityMetrics.netFlow = this.sustainabilityMetrics.totalInflows - this.sustainabilityMetrics.totalOutflows;
    }

    /**
     * Infer the reason for balance change based on amount and timing
     */
    inferChangeReason(change) {
        const amount = Math.abs(change);
        
        if (change > 0) {
            // Inflow - likely from purchases or deposits
            if (amount > 1.0) {
                return 'large_purchase_or_deposit';
            } else if (amount > 0.1) {
                return 'medium_purchase';
            } else {
                return 'small_purchase_or_fee';
            }
        } else {
            // Outflow - likely rewards or creator payments
            if (amount > 1.0) {
                return 'large_reward_payout';
            } else if (amount > 0.1) {
                return 'creator_reward_or_medium_payout';
            } else {
                return 'small_reward_or_gas_fee';
            }
        }
    }

    /**
     * Update sustainability metrics
     */
    updateSustainabilityMetrics() {
        const currentTime = Date.now();
        const elapsedSeconds = (currentTime - this.startTime) / 1000;
        
        // Calculate burn rate (ETH per second)
        if (elapsedSeconds > 0) {
            const totalBurned = this.initialBalance - this.currentBalance;
            this.sustainabilityMetrics.burnRate = totalBurned / elapsedSeconds;
        }
        
        // Calculate projected runtime
        if (this.sustainabilityMetrics.burnRate > 0) {
            this.sustainabilityMetrics.projectedRuntime = this.currentBalance / this.sustainabilityMetrics.burnRate;
        } else {
            this.sustainabilityMetrics.projectedRuntime = Infinity;
        }
        
        // Assess risk level
        this.sustainabilityMetrics.riskLevel = this.assessRiskLevel();
    }

    /**
     * Assess treasury risk level
     */
    assessRiskLevel() {
        const balanceRatio = this.currentBalance / this.initialBalance;
        const netFlowRatio = this.sustainabilityMetrics.netFlow / this.initialBalance;
        
        if (balanceRatio < 0.1 || netFlowRatio < -0.5) {
            return 'critical';
        } else if (balanceRatio < 0.3 || netFlowRatio < -0.2) {
            return 'high';
        } else if (balanceRatio < 0.7 || netFlowRatio < -0.1) {
            return 'medium';
        } else {
            return 'low';
        }
    }

    /**
     * Track specific inflow (purchase, deposit)
     */
    trackInflow(amount, reason, metadata = {}) {
        const inflow = {
            timestamp: Date.now(),
            amount: parseFloat(amount),
            type: 'inflow',
            reason,
            metadata
        };

        this.inflows.push(inflow);
        this.sustainabilityMetrics.totalInflows += parseFloat(amount);
        this.sustainabilityMetrics.netFlow = this.sustainabilityMetrics.totalInflows - this.sustainabilityMetrics.totalOutflows;

        console.log(`📈 Tracked inflow: +${amount} ETH (${reason})`);
    }

    /**
     * Track specific outflow (reward, creator payment)
     */
    trackOutflow(amount, reason, metadata = {}) {
        const outflow = {
            timestamp: Date.now(),
            amount: parseFloat(amount),
            type: 'outflow',
            reason,
            metadata
        };

        this.outflows.push(outflow);
        this.sustainabilityMetrics.totalOutflows += parseFloat(amount);
        this.sustainabilityMetrics.netFlow = this.sustainabilityMetrics.totalInflows - this.sustainabilityMetrics.totalOutflows;

        console.log(`📉 Tracked outflow: -${amount} ETH (${reason})`);
    }

    /**
     * Get current treasury status
     */
    getTreasuryStatus() {
        return {
            currentBalance: this.currentBalance,
            initialBalance: this.initialBalance,
            balanceChange: this.currentBalance - this.initialBalance,
            balanceChangePercent: ((this.currentBalance - this.initialBalance) / this.initialBalance * 100).toFixed(2),
            ...this.sustainabilityMetrics,
            monitoringDuration: this.isMonitoring ? Date.now() - this.startTime : this.endTime - this.startTime
        };
    }

    /**
     * Get balance history
     */
    getBalanceHistory() {
        return this.balanceHistory;
    }

    /**
     * Get flow analysis
     */
    getFlowAnalysis() {
        return {
            inflows: this.inflows,
            outflows: this.outflows,
            inflowSummary: this.summarizeFlows(this.inflows),
            outflowSummary: this.summarizeFlows(this.outflows),
            netFlow: this.sustainabilityMetrics.netFlow,
            flowBalance: this.sustainabilityMetrics.totalInflows - this.sustainabilityMetrics.totalOutflows
        };
    }

    /**
     * Summarize flows by reason
     */
    summarizeFlows(flows) {
        const summary = {};
        
        flows.forEach(flow => {
            if (!summary[flow.reason]) {
                summary[flow.reason] = {
                    count: 0,
                    totalAmount: 0,
                    averageAmount: 0
                };
            }
            
            summary[flow.reason].count++;
            summary[flow.reason].totalAmount += flow.amount;
            summary[flow.reason].averageAmount = summary[flow.reason].totalAmount / summary[flow.reason].count;
        });
        
        return summary;
    }

    /**
     * Get comprehensive analysis
     */
    async getAnalysis() {
        // Ensure we have the latest balance
        if (this.isMonitoring) {
            await this.checkBalance();
        }

        return {
            treasuryStatus: this.getTreasuryStatus(),
            balanceHistory: this.getBalanceHistory(),
            flowAnalysis: this.getFlowAnalysis(),
            sustainabilityAssessment: {
                isSustainable: this.sustainabilityMetrics.netFlow >= 0,
                riskLevel: this.sustainabilityMetrics.riskLevel,
                projectedRuntime: this.sustainabilityMetrics.projectedRuntime,
                burnRate: this.sustainabilityMetrics.burnRate,
                recommendations: this.generateRecommendations()
            },
            timestamp: Date.now()
        };
    }

    /**
     * Generate recommendations based on treasury analysis
     */
    generateRecommendations() {
        const recommendations = [];
        const status = this.getTreasuryStatus();

        if (status.riskLevel === 'critical') {
            recommendations.push('CRITICAL: Treasury balance critically low - immediate action required');
            recommendations.push('Consider reducing reward payouts or increasing purchase incentives');
        } else if (status.riskLevel === 'high') {
            recommendations.push('HIGH RISK: Treasury balance declining rapidly');
            recommendations.push('Monitor closely and consider adjusting tokenomics parameters');
        } else if (status.riskLevel === 'medium') {
            recommendations.push('MEDIUM RISK: Treasury showing negative trend');
            recommendations.push('Review reward distribution and purchase patterns');
        } else {
            recommendations.push('Treasury balance stable');
            recommendations.push('Current tokenomics parameters appear sustainable');
        }

        if (this.sustainabilityMetrics.netFlow < 0) {
            recommendations.push('Net outflow detected - more rewards being paid than purchases received');
        } else {
            recommendations.push('Positive net flow - treasury is growing');
        }

        return recommendations;
    }

    /**
     * Export treasury data
     */
    exportData() {
        return {
            treasuryStatus: this.getTreasuryStatus(),
            balanceHistory: this.balanceHistory,
            inflows: this.inflows,
            outflows: this.outflows,
            sustainabilityMetrics: this.sustainabilityMetrics,
            analysis: this.getAnalysis(),
            exportTimestamp: Date.now()
        };
    }
}

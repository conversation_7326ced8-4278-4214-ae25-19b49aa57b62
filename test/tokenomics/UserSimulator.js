/**
 * User Behavior Simulation Classes
 * 
 * Simulates realistic user behavior patterns for tokenomics stress testing.
 * Each user type has distinct behavior patterns that trigger real game functions.
 */

import fetch from 'node-fetch';

export class UserSimulator {
    constructor({ type, account, config, id }) {
        this.type = type;
        this.account = account;
        this.config = config;
        this.id = id;
        
        // Behavior configuration based on user type
        this.behaviorConfig = this.getBehaviorConfig(type);
        
        // State tracking
        this.sessionData = {
            transactions: [],
            rewards: [],
            purchases: [],
            environments: [],
            startTime: null,
            endTime: null
        };

        console.log(`👤 Created ${type} user simulator: ${id} (${account.address})`);
    }

    /**
     * Get behavior configuration for user type
     */
    getBehaviorConfig(type) {
        const configs = {
            grinder: {
                playPattern: 'max_completion',
                spendingBehavior: 'zero_spending',
                rewardEarning: 'maximum',
                sessionDuration: 180000, // 3 minutes
                levelsPerSession: 10,
                completionRate: 1.0, // 100% completion
                purchaseProbability: 0.0, // Never purchases
                maxRewardMultiplier: 1.25 * 1.125 * 1.075 // All bonuses
            },
            whale: {
                playPattern: 'minimal_grinding',
                spendingBehavior: 'heavy_purchasing',
                rewardEarning: 'minimal',
                sessionDuration: 60000, // 1 minute
                levelsPerSession: 2,
                completionRate: 0.3, // 30% completion
                purchaseProbability: 0.8, // 80% purchase rate
                maxRewardMultiplier: 1.0 // No bonuses
            },
            creator: {
                playPattern: 'content_creation',
                spendingBehavior: 'environment_purchases',
                rewardEarning: 'creator_rewards',
                sessionDuration: 120000, // 2 minutes
                levelsPerSession: 5,
                completionRate: 0.6, // 60% completion
                purchaseProbability: 0.3, // 30% purchase rate
                environmentCreationRate: 0.7, // 70% chance to create environment
                creatorRewardRate: 0.5 // 50% of environment sales
            },
            casual: {
                playPattern: 'moderate_play',
                spendingBehavior: 'occasional_powerups',
                rewardEarning: 'moderate',
                sessionDuration: 150000, // 2.5 minutes
                levelsPerSession: 7,
                completionRate: 0.6, // 60% completion
                purchaseProbability: 0.2, // 20% purchase rate
                maxRewardMultiplier: 1.125 // Some bonuses
            }
        };

        return configs[type] || configs.casual;
    }

    /**
     * Simulate a complete gaming session
     */
    async simulateGamingSession() {
        try {
            console.log(`🎮 Starting ${this.type} session for ${this.id}...`);
            this.sessionData.startTime = Date.now();

            // Authenticate with the game server
            await this.authenticateWithServer();

            // Execute behavior pattern based on user type
            switch (this.type) {
                case 'grinder':
                    await this.simulateGrindingSession();
                    break;
                case 'whale':
                    await this.simulateWhaleSession();
                    break;
                case 'creator':
                    await this.simulateCreatorSession();
                    break;
                case 'casual':
                    await this.simulateCasualSession();
                    break;
            }

            this.sessionData.endTime = Date.now();
            console.log(`✅ Completed ${this.type} session for ${this.id}`);

        } catch (error) {
            console.error(`❌ Error in ${this.type} session for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Authenticate with the game server (simulated)
     */
    async authenticateWithServer() {
        // In a real implementation, this would handle OrangeID authentication
        // For testing, we'll simulate successful authentication
        console.log(`🔐 Authenticated ${this.id} with server`);
    }

    /**
     * Simulate grinder behavior - max rewards, zero spending
     */
    async simulateGrindingSession() {
        const { levelsPerSession, completionRate, maxRewardMultiplier } = this.behaviorConfig;

        for (let level = 1; level <= levelsPerSession; level++) {
            // Simulate perfect level completion
            const baseReward = 1250; // Base reward per level
            const levelMultiplier = level;
            const difficultyMultiplier = 1.25; // Assuming hard difficulty
            
            const maxReward = Math.floor(
                baseReward * levelMultiplier * maxRewardMultiplier * difficultyMultiplier
            );

            // Award maximum possible rewards
            await this.awardTokens(maxReward, `Level ${level} completion - perfect score`);

            // Small delay between levels
            await this.delay(500);
        }

        console.log(`🏆 Grinder ${this.id} completed ${levelsPerSession} levels with max rewards`);
    }

    /**
     * Simulate whale behavior - heavy spending, minimal grinding
     */
    async simulateWhaleSession() {
        const { levelsPerSession, purchaseProbability } = this.behaviorConfig;

        // Heavy purchasing before playing
        await this.simulateHeavyPurchasing();

        // Minimal grinding with purchased power-ups
        for (let level = 1; level <= levelsPerSession; level++) {
            // Minimal rewards due to poor performance
            const minimalReward = Math.floor(1250 * level * 0.3); // 30% of base reward
            await this.awardTokens(minimalReward, `Level ${level} completion - minimal effort`);

            await this.delay(1000);
        }

        console.log(`💰 Whale ${this.id} completed session with heavy spending`);
    }

    /**
     * Simulate creator behavior - environment creation and moderate play
     */
    async simulateCreatorSession() {
        // Create custom environments
        await this.simulateEnvironmentCreation();

        // Moderate gameplay
        await this.simulateModerateGameplay();

        console.log(`🎨 Creator ${this.id} completed session with environment creation`);
    }

    /**
     * Simulate casual player behavior
     */
    async simulateCasualSession() {
        const { levelsPerSession, purchaseProbability } = this.behaviorConfig;

        // Occasional purchases
        if (Math.random() < purchaseProbability) {
            await this.simulateOccasionalPurchase();
        }

        // Moderate gameplay
        await this.simulateModerateGameplay();

        console.log(`🎯 Casual player ${this.id} completed session`);
    }

    /**
     * Simulate environment creation (creator behavior)
     */
    async simulateEnvironmentCreation() {
        try {
            const environmentDescription = this.generateRandomEnvironmentDescription();
            
            console.log(`🎨 ${this.id} creating environment: "${environmentDescription}"`);

            const response = await fetch(`${this.config.apiBaseUrl}/generate-environment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.apiAuthToken || 'test-token'}`
                },
                body: JSON.stringify({
                    environmentDescription,
                    creatorUserId: this.id,
                    creatorAddress: this.account.address
                })
            });

            if (!response.ok) {
                throw new Error(`Environment creation failed: ${response.status}`);
            }

            const environment = await response.json();
            this.sessionData.environments.push(environment);

            console.log(`✅ Environment created by ${this.id}: ${environment.name}`);
            return environment;

        } catch (error) {
            console.error(`❌ Environment creation failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate mystical environment purchases (triggers creator rewards)
     */
    async simulateMysticalEnvironmentPurchases() {
        try {
            const purchaseAmount = 25000; // 25,000 WISH tokens (ETH Test Mode: 2,500 ETH)
            
            console.log(`💰 ${this.id} purchasing Mystical Environment for ${purchaseAmount} tokens`);

            // Spend tokens for environment purchase
            await this.spendTokens(purchaseAmount, 'Mystical Environment Purchase');

            // This should trigger 50% creator reward distribution
            console.log(`🎁 Mystical Environment purchase should trigger 50% creator reward`);

        } catch (error) {
            console.error(`❌ Mystical environment purchase failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Simulate heavy purchasing (whale behavior)
     */
    async simulateHeavyPurchasing() {
        const purchases = [
            { item: 'Power-up Bundle', amount: 5000 },
            { item: 'Shield Upgrade', amount: 3000 },
            { item: 'Weapon Enhancement', amount: 4000 },
            { item: 'Speed Boost', amount: 2000 }
        ];

        for (const purchase of purchases) {
            await this.spendTokens(purchase.amount, purchase.item);
            await this.delay(200);
        }
    }

    /**
     * Simulate occasional purchase (casual behavior)
     */
    async simulateOccasionalPurchase() {
        const casualPurchases = [
            { item: 'Health Potion', amount: 500 },
            { item: 'Shield Boost', amount: 750 },
            { item: 'Damage Upgrade', amount: 1000 }
        ];

        const purchase = casualPurchases[Math.floor(Math.random() * casualPurchases.length)];
        await this.spendTokens(purchase.amount, purchase.item);
    }

    /**
     * Simulate moderate gameplay
     */
    async simulateModerateGameplay() {
        const { levelsPerSession, completionRate, maxRewardMultiplier } = this.behaviorConfig;

        for (let level = 1; level <= levelsPerSession; level++) {
            const baseReward = 1250;
            const reward = Math.floor(
                baseReward * level * completionRate * (maxRewardMultiplier || 1.0)
            );

            await this.awardTokens(reward, `Level ${level} completion`);
            await this.delay(800);
        }
    }

    /**
     * Award tokens to user (calls real API)
     */
    async awardTokens(amount, reason) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/tokens/award`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.apiAuthToken || 'test-token'}`
                },
                body: JSON.stringify({
                    userId: this.id,
                    amount: amount,
                    reason: reason,
                    metadata: {
                        userType: this.type,
                        walletAddress: this.account.address,
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Token award failed: ${response.status}`);
            }

            const result = await response.json();
            this.sessionData.rewards.push(result);

            console.log(`🎁 Awarded ${amount} tokens to ${this.id} for: ${reason}`);
            return result;

        } catch (error) {
            console.error(`❌ Token award failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Spend tokens from user (calls real API)
     */
    async spendTokens(amount, reason) {
        try {
            const response = await fetch(`${this.config.apiBaseUrl}/tokens/spend`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${this.config.apiAuthToken || 'test-token'}`
                },
                body: JSON.stringify({
                    userId: this.id,
                    amount: amount,
                    reason: reason,
                    metadata: {
                        userType: this.type,
                        walletAddress: this.account.address,
                        timestamp: Date.now()
                    }
                })
            });

            if (!response.ok) {
                throw new Error(`Token spend failed: ${response.status}`);
            }

            const result = await response.json();
            this.sessionData.purchases.push(result);

            console.log(`💸 ${this.id} spent ${amount} tokens for: ${reason}`);
            return result;

        } catch (error) {
            console.error(`❌ Token spend failed for ${this.id}:`, error);
            throw error;
        }
    }

    /**
     * Generate random environment description for creators
     */
    generateRandomEnvironmentDescription() {
        const themes = [
            'Mystical crystal caverns with floating islands',
            'Cyberpunk neon cityscape with rain',
            'Ancient temple ruins in a jungle',
            'Underwater coral reef with bioluminescence',
            'Volcanic landscape with lava flows',
            'Ice planet with aurora borealis',
            'Desert oasis with sandstorms',
            'Space station with nebula backdrop'
        ];

        return themes[Math.floor(Math.random() * themes.length)];
    }

    /**
     * Simulate coordinated behavior across multiple accounts
     */
    async simulateCoordinatedBehavior() {
        console.log(`🤝 ${this.id} starting coordinated behavior simulation`);
        
        // Simulate coordinated actions that might stress the system
        await this.simulateGamingSession();
        
        // Add small random delay to avoid perfect synchronization
        await this.delay(Math.random() * 2000);
    }

    /**
     * Simulate maximum stress behavior
     */
    async simulateMaxStressBehavior() {
        console.log(`⚡ ${this.id} starting maximum stress behavior`);
        
        // Rapid-fire actions to stress test the system
        const promises = [];
        
        // Multiple concurrent actions
        if (this.type === 'grinder') {
            promises.push(this.simulateGrindingSession());
        } else if (this.type === 'whale') {
            promises.push(this.simulateHeavyPurchasing());
        } else if (this.type === 'creator') {
            promises.push(this.simulateEnvironmentCreation());
        }
        
        await Promise.all(promises);
    }

    /**
     * Get session summary
     */
    getSessionSummary() {
        return {
            userId: this.id,
            userType: this.type,
            account: this.account.address,
            sessionDuration: this.sessionData.endTime - this.sessionData.startTime,
            totalRewards: this.sessionData.rewards.reduce((sum, r) => sum + r.amount, 0),
            totalSpent: this.sessionData.purchases.reduce((sum, p) => sum + p.amount, 0),
            transactionCount: this.sessionData.transactions.length,
            environmentsCreated: this.sessionData.environments.length
        };
    }

    /**
     * Utility method for delays
     */
    async delay(ms) {
        return new Promise(resolve => setTimeout(resolve, ms));
    }
}

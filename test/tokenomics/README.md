# WarpSector Tokenomics Stress Testing Framework

## Overview

This comprehensive stress testing framework validates the economic sustainability and security of WarpSector's tokenomics system through **real user behavior simulation** on your local Hardhat blockchain (Chain ID 31337).

## Key Features

✅ **Real User Behavior Simulation** - Not arbitrary transactions  
✅ **Treasury Sustainability Validation** - Ensures economic balance  
✅ **Creator Reward Testing** - Validates 50% distribution accuracy  
✅ **ETH Test Mode Integration** - Uses actual 90% discount pricing  
✅ **Multi-Attack Vector Testing** - Comprehensive security validation  
✅ **Comprehensive Reporting** - Detailed analysis and recommendations  

## Architecture

### Core Components

1. **TokenomicsStressTest** - Main orchestrator
2. **UserSimulator** - Realistic user behavior patterns
3. **TransactionTracker** - Real-time transaction monitoring
4. **TreasuryMonitor** - Hot wallet balance tracking
5. **ValidationSystem** - Comprehensive result validation

### User Types Simulated

| User Type | Behavior Pattern | Spending | Reward Earning |
|-----------|------------------|----------|----------------|
| **Grinder** | Max level completion | ZERO spending | MAX rewards (1250 * level * bonuses) |
| **Whale** | Minimal grinding | Heavy purchasing | Minimal rewards |
| **Creator** | Environment creation | Environment purchases | 50% creator rewards |
| **Casual** | Moderate play | Occasional power-ups | Moderate rewards |

## Attack Vectors Tested

### 1. Sequential Grinding Attack
- **Objective**: Test if grinder earnings can drain treasury
- **Method**: Multiple grinders earning maximum rewards simultaneously
- **Validation**: Grinder outflows ≤ payer inflows

### 2. Creator Reward Exploitation
- **Objective**: Test creator reward sustainability
- **Method**: Mystical Environment purchases triggering 50% rewards
- **Validation**: Accurate 50% distribution without treasury drain

### 3. Multi-Account Coordination
- **Objective**: Test coordinated behavior impact
- **Method**: Same user with multiple wallets
- **Validation**: Treasury maintains positive balance

### 4. Treasury Drain Test
- **Objective**: Maximum stress scenario
- **Method**: All users acting simultaneously
- **Validation**: Treasury survives maximum load

## Prerequisites

### 1. Local Development Environment
```bash
# Hardhat local node running
npx hardhat node
# Should be accessible at http://localhost:8545 with Chain ID 31337
```

### 2. Game Server Running
```bash
# Start the game server
cd server && npm start
# Should be accessible at http://localhost:3001
```

### 3. Hot Wallet Configuration
- **Address**: `******************************************`
- **Private Key**: Configured in `.env` file
- **Balance**: Minimum 1 ETH for testing

### 4. Test Accounts
- Hardhat provides 20 accounts with 10,000 ETH each
- Framework uses multiple accounts to simulate different users

## Usage

### Quick Start
```bash
# Run full stress test suite (5 minutes)
node test/tokenomics/runStressTest.js

# Run with custom duration (10 minutes)
node test/tokenomics/runStressTest.js --duration 600000

# Run specific scenario only
node test/tokenomics/runStressTest.js --scenario grinder

# Generate detailed report
node test/tokenomics/runStressTest.js --report-file stress-report.json --verbose
```

### Available Scenarios
- `grinder` - Sequential grinding attack test
- `creator` - Creator reward distribution test
- `coordination` - Multi-account coordination test
- `drain` - Treasury drain stress test

### Command Line Options
```bash
--duration <ms>       Test duration in milliseconds (default: 300000)
--users <number>      Maximum concurrent users (default: 10)
--report-file <path>  Save detailed report to file
--verbose             Enable verbose logging
--scenario <name>     Run specific scenario only
--help               Show help message
```

## Expected Results

### Success Criteria ✅
- Treasury balance remains positive throughout testing
- Creator rewards distributed at exactly 50% of environment purchases
- Grinder earnings don't exceed sustainable limits
- All transactions confirmed on blockchain
- Error rate below 10%
- System remains stable under maximum load

### Failure Indicators ❌
- Treasury balance goes negative
- Creator rewards deviate significantly from 50%
- High transaction error rates (>10%)
- System crashes or becomes unresponsive
- Grinder earnings exceed purchase inflows

## Sample Output

```
🚀 Starting WarpSector Tokenomics Stress Test
============================================================
📋 Test Configuration:
   Duration: 300 seconds
   Max Users: 10
   API URL: http://localhost:3001/api
   Hot Wallet: ******************************************

🔍 Validating prerequisites...
   ✅ Game Server: OK
   ✅ Hardhat Node: OK
   ✅ Hot Wallet Balance: OK
   ✅ Test Accounts: OK

🎮 Running Sequential Grinding Test (Attack Vector 1)...
💰 Initial treasury balance: 10000.0 ETH
🎯 Starting grinder session: grinder_1
📊 Treasury impact after grinder_1: 0.125000 ETH

🎨 Running Creator Reward Test (Attack Vector 2)...
🎨 Phase 1: Environment Creation
✅ Environment created: Mystical crystal caverns by creator_1
💰 Phase 2: Mystical Environment Purchases
🐋 whale_1 purchasing Mystical Environment for 25000 tokens
🎁 Expected creator reward: 12500 tokens

📊 STRESS TEST RESULTS
============================================================
📋 Test Summary:
   Total Duration: 298 seconds
   Users Simulated: 10
   Total Transactions: 47
   Test Completion: Full duration

💰 Treasury Analysis:
   Initial Balance: 10000.0 ETH
   Final Balance: 9999.2 ETH
   Balance Change: -0.8 ETH
   Risk Level: low

🔍 VALIDATION RESULTS
==================================================
Overall Result: ✅ PASS
Tests Passed: 23/25 (92.0%)
Critical Issues: 0
Warnings: 2

💡 RECOMMENDATIONS:
   1. Treasury balance maintained throughout testing
   2. Creator rewards distributed correctly
   3. No critical vulnerabilities detected
```

## Validation Criteria

The framework validates multiple aspects:

### Treasury Sustainability
- Balance remains positive
- Change doesn't exceed 50% of initial
- Net flow sustainable
- Risk level acceptable

### Creator Rewards
- 50% distribution accuracy (±5% tolerance)
- Rewards triggered by environment purchases
- Proper ETH transfer to creators

### Grinder Behavior
- Maximum reward earning
- Zero token spending
- High activity levels

### Transaction Integrity
- Low error rates (<10%)
- Reasonable response times (<5s)
- Balanced token flows

### Economic Balance
- System sustainability
- Reasonable burn rates
- Adequate projected runtime

## Troubleshooting

### Common Issues

1. **"Server not available"**
   - Ensure game server is running on localhost:3001
   - Check server logs for errors

2. **"Wrong chain ID"**
   - Verify Hardhat node is running with Chain ID 31337
   - Check MetaMask network configuration

3. **"Hot wallet balance too low"**
   - Ensure hot wallet has at least 1 ETH
   - Check `.env` configuration

4. **"Not enough test accounts"**
   - Verify Hardhat is providing 20 test accounts
   - Check Hardhat configuration

### Debug Mode
```bash
# Enable verbose logging
node test/tokenomics/runStressTest.js --verbose

# Check individual components
node -e "import('./TokenomicsStressTest.js').then(m => console.log('Import OK'))"
```

## Integration with CI/CD

The stress test can be integrated into your deployment pipeline:

```yaml
# Example GitHub Actions workflow
- name: Run Tokenomics Stress Test
  run: |
    npx hardhat node &
    npm run server &
    sleep 10
    node test/tokenomics/runStressTest.js --duration 60000 --report-file ci-report.json
```

## Contributing

When adding new test scenarios:

1. Create new user behavior patterns in `UserSimulator.js`
2. Add validation criteria in `ValidationSystem.js`
3. Update attack vectors in `TokenomicsStressTest.js`
4. Document expected behaviors and validation criteria

## Security Considerations

- Tests run on local development network only
- Private keys are for development accounts only
- No real funds are used in testing
- All transactions are reversible on local network

---

For more information, see the main project documentation and `LOCAL_TESTING_GUIDE.md`.

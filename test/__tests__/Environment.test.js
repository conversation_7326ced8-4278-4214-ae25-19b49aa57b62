import { Environment } from '../../src/ai/Environment.js';

// Mock HTMLImageElement for testing
class MockImage {
    constructor(width = 800, height = 600) {
        this.width = width;
        this.height = height;
        this.src = '';
    }
}

// Mock CanvasRenderingContext2D for testing
class MockCanvasContext {
    constructor() {
        this.saveCalls = [];
        this.restoreCalls = [];
        this.fillRectCalls = [];
        this.drawImageCalls = [];
        this.globalAlpha = 1;
        this.fillStyle = '#000000';

        // Create Jest spy functions
        this.save = jest.fn(() => this.saveCalls.push(true));
        this.restore = jest.fn(() => this.restoreCalls.push(true));
        this.fillRect = jest.fn((x, y, width, height) => {
            this.fillRectCalls.push({ x, y, width, height });
        });
        this.drawImage = jest.fn((image, x, y, width, height) => {
            this.drawImageCalls.push({ image, x, y, width, height });
        });
    }
}

describe('Environment', () => {
    let environment;
    let mockCtx;
    let mockBackgroundImage;
    let mockPreviousBackground;

    beforeEach(() => {
        environment = new Environment();
        mockCtx = new MockCanvasContext();
        mockBackgroundImage = new MockImage(800, 600);
        mockPreviousBackground = new MockImage(1024, 768);
        
        // Clear console mocks
        jest.clearAllMocks();
    });

    describe('Constructor', () => {
        test('should initialize with default values', () => {
            expect(environment.currentEnvironment).toBeNull();
            expect(environment.backgroundImage).toBeNull();
            expect(environment.gameplayModifiers).toBeNull();
            expect(environment.isTransitioning).toBe(false);
            expect(environment.transitionProgress).toBe(0);
            expect(environment.transitionDuration).toBe(2000);
            expect(environment.previousBackground).toBeNull();
            expect(environment.defaultEnvironment).toBeDefined();
            expect(environment.defaultEnvironment.type).toBe('space');
            expect(environment.defaultEnvironment.name).toBe('Deep Space');
        });

        test('should have default environment with proper structure', () => {
            const defaultEnv = environment.defaultEnvironment;
            expect(defaultEnv.type).toBe('space');
            expect(defaultEnv.name).toBe('Deep Space');
            expect(defaultEnv.description).toBeDefined();
            expect(defaultEnv.imagePrompt).toBeDefined();
            expect(defaultEnv.gameplayModifiers).toBeDefined();
            expect(defaultEnv.gameplayModifiers.enemySpeedMultiplier).toBe(1.0);
            expect(defaultEnv.gameplayModifiers.enemyHealthMultiplier).toBe(1.0);
            expect(defaultEnv.gameplayModifiers.enemySpawnRateMultiplier).toBe(1.0);
            expect(defaultEnv.gameplayModifiers.enemyProjectileSpeedMultiplier).toBe(1.0);
            expect(defaultEnv.gameplayModifiers.compatibleEnemyTypes).toBeDefined();
            expect(defaultEnv.gameplayModifiers.environmentHazards).toEqual([]);
            expect(defaultEnv.gameplayModifiers.enemyTypeModifiers).toBeDefined();
        });
    });

    describe('initialize', () => {
        test('should initialize with default space environment', async () => {
            await environment.initialize();
            
            expect(environment.currentEnvironment).toEqual(environment.defaultEnvironment);
            expect(environment.gameplayModifiers).toEqual(environment.defaultEnvironment.gameplayModifiers);
        });

        test('should create a copy of default environment', async () => {
            await environment.initialize();
            
            expect(environment.currentEnvironment).not.toBe(environment.defaultEnvironment);
            expect(environment.gameplayModifiers).not.toBe(environment.defaultEnvironment.gameplayModifiers);
        });
    });

    describe('setEnvironment', () => {
        beforeEach(async () => {
            await environment.initialize();
        });

        test('should set new environment with valid data', async () => {
            const environmentData = {
                type: 'volcanic',
                name: 'Volcanic Wasteland',
                description: 'A dangerous volcanic environment',
                imagePrompt: 'Volcanic landscape with lava flows',
                gameplayModifiers: {
                    enemySpeedMultiplier: 1.2,
                    enemyHealthMultiplier: 1.3,
                    enemySpawnRateMultiplier: 0.8,
                    enemyProjectileSpeedMultiplier: 1.1,
                    environmentEffects: ['Lava damage'],
                    compatibleEnemyTypes: ['fire', 'earth'],
                    environmentHazards: ['lava_pools'],
                    enemyTypeModifiers: {
                        fire: 1.5,
                        earth: 1.2,
                        water: 0.5
                    }
                }
            };

            await environment.setEnvironment(environmentData, mockBackgroundImage);

            expect(environment.currentEnvironment.type).toBe('volcanic');
            expect(environment.currentEnvironment.name).toBe('Volcanic Wasteland');
            expect(environment.currentEnvironment.description).toBe('A dangerous volcanic environment');
            expect(environment.backgroundImage).toBe(mockBackgroundImage);
            expect(environment.gameplayModifiers).toEqual(environmentData.gameplayModifiers);
            expect(environment.isTransitioning).toBe(true);
            expect(environment.previousBackground).toBeNull();
        });

        test('should use default values for missing environment data', async () => {
            const environmentData = {
                type: 'custom'
            };

            await environment.setEnvironment(environmentData, mockBackgroundImage);

            expect(environment.currentEnvironment.type).toBe('custom');
            expect(environment.currentEnvironment.name).toBe('Custom Environment');
            expect(environment.currentEnvironment.description).toBe('Custom generated environment');
            expect(environment.gameplayModifiers).toEqual(environment.defaultEnvironment.gameplayModifiers);
        });

        test('should store previous background when transitioning', async () => {
            // Set initial background
            environment.backgroundImage = mockPreviousBackground;
            
            const environmentData = {
                type: 'ice',
                name: 'Ice Caverns'
            };

            await environment.setEnvironment(environmentData, mockBackgroundImage);

            expect(environment.previousBackground).toBe(mockPreviousBackground);
        });

        test('should not allow environment change during transition', async () => {
            environment.isTransitioning = true;
            
            const environmentData = {
                type: 'forest',
                name: 'Enchanted Forest'
            };

            const consoleSpy = jest.spyOn(console, 'warn');
            await environment.setEnvironment(environmentData, mockBackgroundImage);

            expect(consoleSpy).toHaveBeenCalledWith('Environment transition already in progress');
            expect(environment.currentEnvironment.type).toBe('space'); // Should remain default
        });
    });

    describe('startTransition', () => {
        test('should start transition with correct values', () => {
            environment.startTransition();

            expect(environment.isTransitioning).toBe(true);
            expect(environment.transitionProgress).toBe(0);
        });
    });

    describe('updateTransition', () => {
        beforeEach(() => {
            environment.isTransitioning = true;
            environment.previousBackground = mockPreviousBackground;
        });

        test('should update transition progress correctly', () => {
            const deltaTime = 500; // 0.5 seconds
            
            environment.updateTransition(deltaTime);

            expect(environment.transitionProgress).toBe(0.25); // 500 / 2000
        });

        test('should complete transition when progress reaches 1.0', () => {
            environment.transitionProgress = 0.9;
            const deltaTime = 200; // Should push it over 1.0
            
            environment.updateTransition(deltaTime);

            expect(environment.transitionProgress).toBe(1.0);
            expect(environment.isTransitioning).toBe(false);
            expect(environment.previousBackground).toBeNull();
        });

        test('should not update when not transitioning', () => {
            environment.isTransitioning = false;
            const initialProgress = environment.transitionProgress;
            
            environment.updateTransition(1000);

            expect(environment.transitionProgress).toBe(initialProgress);
        });

        test('should handle deltaTime that exceeds remaining time', () => {
            environment.transitionProgress = 0.9;
            const deltaTime = 500; // More than needed to complete
            
            environment.updateTransition(deltaTime);

            expect(environment.transitionProgress).toBe(1.0);
            expect(environment.isTransitioning).toBe(false);
        });
    });

    describe('getGameplayModifiers', () => {
        test('should return current gameplay modifiers when set', async () => {
            const modifiers = { enemySpeedMultiplier: 1.5 };
            environment.gameplayModifiers = modifiers;
            
            const result = environment.getGameplayModifiers();
            
            expect(result).toBe(modifiers);
        });

        test('should return default modifiers when none set', () => {
            environment.gameplayModifiers = null;
            
            const result = environment.getGameplayModifiers();
            
            expect(result).toBe(environment.defaultEnvironment.gameplayModifiers);
        });
    });

    describe('getEnvironmentType', () => {
        test('should return current environment type when set', async () => {
            await environment.initialize();
            environment.currentEnvironment.type = 'volcanic';
            
            const result = environment.getEnvironmentType();
            
            expect(result).toBe('volcanic');
        });

        test('should return default type when no current environment', () => {
            environment.currentEnvironment = null;
            
            const result = environment.getEnvironmentType();
            
            expect(result).toBe(environment.defaultEnvironment.type);
        });
    });

    describe('getEnvironmentName', () => {
        test('should return current environment name when set', async () => {
            await environment.initialize();
            environment.currentEnvironment.name = 'Volcanic Wasteland';
            
            const result = environment.getEnvironmentName();
            
            expect(result).toBe('Volcanic Wasteland');
        });

        test('should return default name when no current environment', () => {
            environment.currentEnvironment = null;
            
            const result = environment.getEnvironmentName();
            
            expect(result).toBe(environment.defaultEnvironment.name);
        });
    });

    describe('getEnvironmentDescription', () => {
        test('should return current environment description when set', async () => {
            await environment.initialize();
            environment.currentEnvironment.description = 'A dangerous volcanic environment';
            
            const result = environment.getEnvironmentDescription();
            
            expect(result).toBe('A dangerous volcanic environment');
        });

        test('should return default description when no current environment', () => {
            environment.currentEnvironment = null;
            
            const result = environment.getEnvironmentDescription();
            
            expect(result).toBe(environment.defaultEnvironment.description);
        });
    });

    describe('isEnemyCompatible', () => {
        beforeEach(async () => {
            await environment.initialize();
            environment.gameplayModifiers.compatibleEnemyTypes = ['fire', 'earth'];
        });

        test('should return true for compatible enemy types', () => {
            expect(environment.isEnemyCompatible('fire')).toBe(true);
            expect(environment.isEnemyCompatible('earth')).toBe(true);
        });

        test('should return false for incompatible enemy types', () => {
            expect(environment.isEnemyCompatible('water')).toBe(false);
            expect(environment.isEnemyCompatible('air')).toBe(false);
        });
    });

    describe('getEnvironmentHazards', () => {
        beforeEach(async () => {
            await environment.initialize();
        });

        test('should return environment hazards when set', () => {
            environment.gameplayModifiers.environmentHazards = ['lava_pools', 'toxic_gas'];
            
            const result = environment.getEnvironmentHazards();
            
            expect(result).toEqual(['lava_pools', 'toxic_gas']);
        });

        test('should return empty array when no hazards', () => {
            environment.gameplayModifiers.environmentHazards = null;
            
            const result = environment.getEnvironmentHazards();
            
            expect(result).toEqual([]);
        });
    });

    describe('applyEnvironmentEffects', () => {
        beforeEach(async () => {
            await environment.initialize();
            environment.gameplayModifiers = {
                enemySpeedMultiplier: 1.2,
                enemyHealthMultiplier: 1.3,
                enemyProjectileSpeedMultiplier: 1.1,
                enemyTypeModifiers: {
                    fire: 1.5,
                    water: 0.8
                }
            };
        });

        test('should apply environment effects to enemy stats', () => {
            const enemyStats = {
                type: 'fire',
                speed: 100,
                health: 200,
                projectileSpeed: 50
            };

            const result = environment.applyEnvironmentEffects(enemyStats);

            expect(result.speed).toBe(180); // 100 * 1.2 * 1.5
            expect(result.health).toBe(390); // 200 * 1.3 * 1.5
            expect(result.projectileSpeed).toBeCloseTo(82.5); // 50 * 1.1 * 1.5
        });

        test('should use default type modifier when not specified', () => {
            const enemyStats = {
                type: 'air',
                speed: 100,
                health: 200,
                projectileSpeed: 50
            };

            const result = environment.applyEnvironmentEffects(enemyStats);

            expect(result.speed).toBe(120); // 100 * 1.2 * 1.0
            expect(result.health).toBe(260); // 200 * 1.3 * 1.0
            expect(result.projectileSpeed).toBeCloseTo(55); // 50 * 1.1 * 1.0
        });

        test('should handle missing enemyTypeModifiers object', () => {
            delete environment.gameplayModifiers.enemyTypeModifiers;
            
            const enemyStats = {
                type: 'fire',
                speed: 100,
                health: 200,
                projectileSpeed: 50
            };

            const result = environment.applyEnvironmentEffects(enemyStats);

            expect(result.speed).toBe(120); // 100 * 1.2 * 1.0
            expect(result.health).toBe(260); // 200 * 1.3 * 1.0
            expect(result.projectileSpeed).toBeCloseTo(55); // 50 * 1.1 * 1.0
        });
    });

    describe('render', () => {
        beforeEach(async () => {
            await environment.initialize();
        });

        test('should render transition effect when transitioning', () => {
            environment.isTransitioning = true;
            environment.previousBackground = mockPreviousBackground;
            environment.backgroundImage = mockBackgroundImage;
            
            environment.renderTransition = jest.fn();
            
            environment.render(mockCtx, 800, 600);
            
            expect(environment.renderTransition).toHaveBeenCalledWith(mockCtx, 800, 600);
        });

        test('should render background when not transitioning', () => {
            environment.isTransitioning = false;
            environment.backgroundImage = mockBackgroundImage;
            
            environment.renderBackground = jest.fn();
            
            environment.render(mockCtx, 800, 600);
            
            expect(environment.renderBackground).toHaveBeenCalledWith(mockCtx, 800, 600, mockBackgroundImage, 1.0);
        });

        test('should render default starfield when no background', () => {
            environment.isTransitioning = false;
            environment.backgroundImage = null;
            
            environment.renderDefaultStarfield = jest.fn();
            
            environment.render(mockCtx, 800, 600);
            
            expect(environment.renderDefaultStarfield).toHaveBeenCalledWith(mockCtx, 800, 600);
        });
    });

    describe('renderBackground', () => {
        test('should render background with correct scaling for wider image', () => {
            const wideImage = new MockImage(1200, 600); // 2:1 aspect ratio
            
            environment.renderBackground(mockCtx, 800, 600, wideImage, 1.0);
            
            expect(mockCtx.save).toHaveBeenCalled();
            expect(mockCtx.globalAlpha).toBe(1.0);
            expect(mockCtx.drawImage).toHaveBeenCalled();
            
            const drawCall = mockCtx.drawImageCalls[0];
            expect(drawCall.width).toBe(1200); // Full height, scaled width
            expect(drawCall.height).toBe(600);
            expect(drawCall.x).toBe(-200); // Centered horizontally
            expect(drawCall.y).toBe(0);
        });

        test('should render background with correct scaling for taller image', () => {
            const tallImage = new MockImage(600, 900); // 2:3 aspect ratio
            
            environment.renderBackground(mockCtx, 800, 600, tallImage, 1.0);
            
            const drawCall = mockCtx.drawImageCalls[0];
            expect(drawCall.width).toBe(800); // Full width
            expect(drawCall.height).toBe(1200); // Scaled height
            expect(drawCall.x).toBe(0);
            expect(drawCall.y).toBe(-300); // Centered vertically
        });

        test('should render background with specified alpha', () => {
            environment.renderBackground(mockCtx, 800, 600, mockBackgroundImage, 0.5);
            
            expect(mockCtx.globalAlpha).toBe(0.5);
        });

        test('should restore context after rendering', () => {
            environment.renderBackground(mockCtx, 800, 600, mockBackgroundImage, 1.0);
            
            expect(mockCtx.save).toHaveBeenCalled();
            expect(mockCtx.restore).toHaveBeenCalled();
        });
    });

    describe('renderTransition', () => {
        beforeEach(() => {
            environment.isTransitioning = true;
            environment.previousBackground = mockPreviousBackground;
            environment.backgroundImage = mockBackgroundImage;
        });

        test('should render both backgrounds with correct alpha values', () => {
            environment.transitionProgress = 0.5;
            
            environment.renderBackground = jest.fn();
            
            environment.renderTransition(mockCtx, 800, 600);
            
            expect(environment.renderBackground).toHaveBeenCalledWith(mockCtx, 800, 600, mockPreviousBackground, 0.5);
            expect(environment.renderBackground).toHaveBeenCalledWith(mockCtx, 800, 600, mockBackgroundImage, 0.5);
        });

        test('should add white overlay effect in first half of transition', () => {
            environment.transitionProgress = 0.3;

            environment.renderTransition(mockCtx, 800, 600);

            expect(mockCtx.fillStyle).toBe('#ffffff');
            // For t=0.3, eased = 4 * 0.3^3 = 0.108, alpha = (0.5 - 0.108) * 2 = 0.784
            expect(mockCtx.globalAlpha).toBeCloseTo(0.784, 2);
            expect(mockCtx.fillRect).toHaveBeenCalledWith(0, 0, 800, 600);
        });

        test('should not add overlay effect in second half of transition', () => {
            environment.transitionProgress = 0.7;
            
            environment.renderTransition(mockCtx, 800, 600);
            
            expect(mockCtx.fillRectCalls.length).toBe(0);
        });
    });

    describe('renderDefaultStarfield', () => {
        beforeEach(() => {
            // Reset mock context
            mockCtx = new MockCanvasContext();
        });

        test('should render dark background with stars', () => {
            environment.renderDefaultStarfield(mockCtx, 800, 600);

            // Check that fillStyle was set to dark background first
            expect(mockCtx.fillRectCalls[0]).toEqual({ x: 0, y: 0, width: 800, height: 600 });

            // Should draw 100 stars
            expect(mockCtx.fillRectCalls.length).toBe(101); // Background + 100 stars
        });

        test('should draw stars with random positions and sizes', () => {
            // Mock Math.random to return predictable values for first star
            const mockRandom = jest.spyOn(Math, 'random')
                .mockReturnValue(0.5); // Return 0.5 for all calls

            environment.renderDefaultStarfield(mockCtx, 800, 600);

            // Check that fillRect was called for the first star
            // x = 0.5 * 800 = 400, y = 0.5 * 600 = 300, size = 0.5 * 1.5 + 0.5 = 1.25
            expect(mockCtx.fillRect).toHaveBeenCalledWith(400, 300, 1.25, 1.25);

            mockRandom.mockRestore();
        });
    });

    describe('easeInOutCubic', () => {
        test('should return 0 for input 0', () => {
            expect(environment.easeInOutCubic(0)).toBe(0);
        });

        test('should return 1 for input 1', () => {
            expect(environment.easeInOutCubic(1)).toBe(1);
        });

        test('should return 0.5 for input 0.5', () => {
            expect(environment.easeInOutCubic(0.5)).toBe(0.5);
        });

        test('should return correct values for first half', () => {
            expect(environment.easeInOutCubic(0.25)).toBeCloseTo(0.0625, 4);
        });

        test('should return correct values for second half', () => {
            expect(environment.easeInOutCubic(0.75)).toBeCloseTo(0.9375, 4);
        });
    });

    describe('resetToDefault', () => {
        beforeEach(async () => {
            await environment.initialize();
            environment.currentEnvironment.type = 'volcanic';
            environment.backgroundImage = mockBackgroundImage;
            environment.isTransitioning = true;
            environment.transitionProgress = 0.5;
            environment.previousBackground = mockPreviousBackground;
        });

        test('should reset all properties to default values', () => {
            environment.resetToDefault();
            
            expect(environment.currentEnvironment).toEqual(environment.defaultEnvironment);
            expect(environment.gameplayModifiers).toEqual(environment.defaultEnvironment.gameplayModifiers);
            expect(environment.backgroundImage).toBeNull();
            expect(environment.isTransitioning).toBe(false);
            expect(environment.transitionProgress).toBe(0);
            expect(environment.previousBackground).toBeNull();
        });

        test('should create copies of default environment', () => {
            environment.resetToDefault();
            
            expect(environment.currentEnvironment).not.toBe(environment.defaultEnvironment);
            expect(environment.gameplayModifiers).not.toBe(environment.defaultEnvironment.gameplayModifiers);
        });
    });

    describe('getStatus', () => {
        beforeEach(async () => {
            await environment.initialize();
            environment.backgroundImage = mockBackgroundImage;
            environment.isTransitioning = true;
            environment.transitionProgress = 0.5;
        });

        test('should return complete environment status', () => {
            const status = environment.getStatus();
            
            expect(status).toHaveProperty('type');
            expect(status).toHaveProperty('name');
            expect(status).toHaveProperty('description');
            expect(status).toHaveProperty('isTransitioning');
            expect(status).toHaveProperty('transitionProgress');
            expect(status).toHaveProperty('hasCustomBackground');
            expect(status).toHaveProperty('gameplayModifiers');
            
            expect(status.type).toBe('space');
            expect(status.name).toBe('Deep Space');
            expect(status.isTransitioning).toBe(true);
            expect(status.transitionProgress).toBe(0.5);
            expect(status.hasCustomBackground).toBe(true);
        });

        test('should return correct status when no custom background', () => {
            environment.backgroundImage = null;
            
            const status = environment.getStatus();
            
            expect(status.hasCustomBackground).toBe(false);
        });
    });
});
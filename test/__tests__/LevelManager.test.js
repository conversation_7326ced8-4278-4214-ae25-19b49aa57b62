import { LevelManager } from '../../src/managers/LevelManager.js';
import { GAME_CONFIG, ENVIRONMENT_TYPES } from '../../src/config/gameConfig.js';

// Use actual GAME_CONFIG and ENVIRONMENT_TYPES

describe('LevelManager', () => {
    let levelManager;

    beforeEach(() => {
        levelManager = new LevelManager();
        // Clear console mocks
        jest.clearAllMocks();
    });

    describe('Constructor', () => {
        test('should initialize with default values', () => {
            expect(levelManager.currentLevel).toBe(1);
            expect(levelManager.levelInProgress).toBe(false);
            expect(levelManager.levelStartTime).toBe(0);
            expect(levelManager.levelCompletionTime).toBe(0);
            expect(levelManager.levelConfig).toBeNull();
            expect(levelManager.maxLevels).toBe(50);
            expect(levelManager.currentScore).toBe(0);
            expect(levelManager.levelScore).toBe(0);
            expect(levelManager.enemiesDefeated).toBe(0);
            expect(levelManager.levelEnemiesDefeated).toBe(0);
            expect(levelManager.totalScore).toBe(0);
            expect(levelManager.levelStartScore).toBe(0);
            expect(levelManager.perfectCompletion).toBe(true);
            expect(levelManager.speedBonus).toBe(0);
            expect(levelManager.accuracyBonus).toBe(0);
            expect(levelManager.requiredEnemiesDefeated).toBe(0);
            expect(levelManager.wavesCompleted).toBe(0);
            expect(levelManager.requiredWaves).toBe(0);
            expect(levelManager.onLevelStartCallback).toBeNull();
            expect(levelManager.onLevelCompleteCallback).toBeNull();
            expect(levelManager.onScoreUpdateCallback).toBeNull();
        });
    });

    describe('startLevel', () => {
        test('should start level with specified number', () => {
            const levelConfig = levelManager.startLevel(5);
            
            expect(levelManager.currentLevel).toBe(5);
            expect(levelManager.levelInProgress).toBe(true);
            expect(levelManager.levelStartTime).toBeGreaterThan(0);
            expect(levelManager.levelCompletionTime).toBe(0);
            expect(levelManager.levelScore).toBe(0);
            expect(levelManager.levelEnemiesDefeated).toBe(0);
            expect(levelManager.perfectCompletion).toBe(true);
            expect(levelManager.speedBonus).toBe(0);
            expect(levelManager.accuracyBonus).toBe(0);
            expect(levelConfig).not.toBeNull();
            expect(levelConfig.levelNumber).toBe(5);
        });

        test('should start next level if no level number specified', () => {
            levelManager.currentLevel = 3;
            const levelConfig = levelManager.startLevel();
            
            expect(levelManager.currentLevel).toBe(4);
            expect(levelConfig.levelNumber).toBe(4);
        });

        test('should set completion criteria based on level config', () => {
            const levelConfig = levelManager.startLevel(1);
            
            expect(levelManager.requiredEnemiesDefeated).toBe(levelConfig.totalEnemies);
            expect(levelManager.requiredWaves).toBe(levelConfig.totalWaves);
            expect(levelManager.wavesCompleted).toBe(0);
        });

        test('should trigger level start callback if set', () => {
            const mockCallback = jest.fn();
            levelManager.setOnLevelStart(mockCallback);
            
            levelManager.startLevel(1);
            
            expect(mockCallback).toHaveBeenCalledWith(1, expect.any(Object));
        });
    });

    describe('generateLevelConfig', () => {
        test('should generate correct config for level 1', () => {
            const config = levelManager.generateLevelConfig(1);
            
            expect(config.levelNumber).toBe(1);
            expect(config.difficulty).toBe(1);
            expect(config.totalEnemies).toBeGreaterThan(0);
            expect(config.totalWaves).toBe(3);
            expect(config.environment).toBe(ENVIRONMENT_TYPES.SPACE);
            expect(config.hasBoss).toBe(false);
            expect(config.hasSpecialMechanics).toBe(false);
            expect(config.timeLimit).toBeGreaterThan(0);
            expect(config.scoreTarget).toBeGreaterThan(0);
            expect(config.completionReward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD);
            expect(config.difficultyMultiplier).toBe(1.0);
            expect(config.enemyDistribution).toBeDefined();
            expect(config.conditions).toBeDefined();
            expect(config.backgroundMusic).toBeDefined();
            expect(config.visualEffects).toBeDefined();
        });

        test('should increase difficulty with level number', () => {
            const config1 = levelManager.generateLevelConfig(1);
            const config5 = levelManager.generateLevelConfig(5);
            const config10 = levelManager.generateLevelConfig(10);
            
            expect(config5.difficulty).toBeGreaterThan(config1.difficulty);
            expect(config10.difficulty).toBeGreaterThan(config5.difficulty);
            expect(config5.totalEnemies).toBeGreaterThan(config1.totalEnemies);
            expect(config10.totalEnemies).toBeGreaterThan(config5.totalEnemies);
            expect(config5.difficultyMultiplier).toBeGreaterThan(config1.difficultyMultiplier);
            expect(config10.difficultyMultiplier).toBeGreaterThan(config5.difficultyMultiplier);
        });

        test('should have boss every 10th level', () => {
            const config10 = levelManager.generateLevelConfig(10);
            const config20 = levelManager.generateLevelConfig(20);
            const config5 = levelManager.generateLevelConfig(5);
            
            expect(config10.hasBoss).toBe(true);
            expect(config20.hasBoss).toBe(true);
            expect(config5.hasBoss).toBe(false);
        });

        test('should have special mechanics every 5th level', () => {
            const config5 = levelManager.generateLevelConfig(5);
            const config10 = levelManager.generateLevelConfig(10);
            const config7 = levelManager.generateLevelConfig(7);
            
            expect(config5.hasSpecialMechanics).toBe(true);
            expect(config10.hasSpecialMechanics).toBe(true);
            expect(config7.hasSpecialMechanics).toBe(false);
        });

        test('should cap difficulty multiplier at 3.0', () => {
            const config100 = levelManager.generateLevelConfig(100);
            
            expect(config100.difficultyMultiplier).toBe(3.0);
        });
    });

    describe('selectLevelEnvironment', () => {
        test('should return space for early levels', () => {
            expect(levelManager.selectLevelEnvironment(1)).toBe(ENVIRONMENT_TYPES.SPACE);
            expect(levelManager.selectLevelEnvironment(5)).toBe(ENVIRONMENT_TYPES.SPACE);
        });

        test('should return special environments for boss levels', () => {
            const bossEnvironments = [
                ENVIRONMENT_TYPES.VOLCANIC,
                ENVIRONMENT_TYPES.CRYSTAL,
                ENVIRONMENT_TYPES.ICE
            ];
            
            const env10 = levelManager.selectLevelEnvironment(10);
            const env20 = levelManager.selectLevelEnvironment(20);
            const env30 = levelManager.selectLevelEnvironment(30);
            
            expect(bossEnvironments).toContain(env10);
            expect(bossEnvironments).toContain(env20);
            expect(bossEnvironments).toContain(env30);
        });

        test('should cycle through environments for later levels', () => {
            const environments = [
                ENVIRONMENT_TYPES.SPACE,
                ENVIRONMENT_TYPES.UNDERWATER,
                ENVIRONMENT_TYPES.VOLCANIC,
                ENVIRONMENT_TYPES.CRYSTAL,
                ENVIRONMENT_TYPES.FOREST,
                ENVIRONMENT_TYPES.DESERT,
                ENVIRONMENT_TYPES.ICE
            ];
            
            const env6 = levelManager.selectLevelEnvironment(6);
            const env15 = levelManager.selectLevelEnvironment(15);
            const env25 = levelManager.selectLevelEnvironment(25);
            
            expect(environments).toContain(env6);
            expect(environments).toContain(env15);
            expect(environments).toContain(env25);
        });
    });

    describe('generateEnemyDistribution', () => {
        test('should generate appropriate distribution for early levels', () => {
            const distribution = levelManager.generateEnemyDistribution(3, ENVIRONMENT_TYPES.SPACE);
            
            expect(distribution.basic).toBe(0.7);
            expect(distribution.advanced).toBe(0.3);
            expect(distribution.elite).toBe(0.0);
            expect(distribution.environmentalBonus).toBeDefined();
        });

        test('should generate appropriate distribution for mid levels', () => {
            const distribution = levelManager.generateEnemyDistribution(7, ENVIRONMENT_TYPES.SPACE);
            
            expect(distribution.basic).toBe(0.5);
            expect(distribution.advanced).toBe(0.4);
            expect(distribution.elite).toBe(0.1);
        });

        test('should generate appropriate distribution for later levels', () => {
            const distribution = levelManager.generateEnemyDistribution(15, ENVIRONMENT_TYPES.SPACE);
            
            expect(distribution.basic).toBe(0.3);
            expect(distribution.advanced).toBe(0.5);
            expect(distribution.elite).toBe(0.2);
        });

        test('should include environmental bonus based on environment', () => {
            const spaceDistribution = levelManager.generateEnemyDistribution(10, ENVIRONMENT_TYPES.SPACE);
            const volcanicDistribution = levelManager.generateEnemyDistribution(10, ENVIRONMENT_TYPES.VOLCANIC);
            
            expect(spaceDistribution.environmentalBonus.air).toBe(1.2);
            expect(volcanicDistribution.environmentalBonus.fire).toBe(1.6);
        });
    });

    describe('generateLevelConditions', () => {
        test('should generate basic conditions for early levels', () => {
            const conditions = levelManager.generateLevelConditions(1);
            
            expect(conditions.primary).toBe('defeat_all_enemies');
            expect(conditions.secondary).toEqual([]);
            expect(conditions.bonus).toContain('perfect_accuracy');
            expect(conditions.bonus).toContain('speed_completion');
            expect(conditions.bonus).toContain('no_damage_taken');
        });

        test('should add secondary objectives based on level', () => {
            const conditions3 = levelManager.generateLevelConditions(3);
            const conditions5 = levelManager.generateLevelConditions(5);
            const conditions7 = levelManager.generateLevelConditions(7);
            
            expect(conditions3.secondary).toContain('complete_under_time_limit');
            expect(conditions5.secondary).toContain('maintain_accuracy_above_70');
            expect(conditions7.secondary).toContain('take_minimal_damage');
        });
    });

    describe('selectBackgroundMusic', () => {
        test('should return boss theme for boss levels', () => {
            expect(levelManager.selectBackgroundMusic(10, 'space')).toBe('boss_theme');
            expect(levelManager.selectBackgroundMusic(20, 'volcanic')).toBe('boss_theme');
        });

        test('should return environment-specific music for regular levels', () => {
            expect(levelManager.selectBackgroundMusic(5, 'space')).toBe('space_ambient');
            expect(levelManager.selectBackgroundMusic(7, 'underwater')).toBe('underwater_theme');
            expect(levelManager.selectBackgroundMusic(9, 'volcanic')).toBe('volcanic_intensity');
        });
    });

    describe('selectVisualEffects', () => {
        test('should return environment-specific effects', () => {
            const spaceEffects = levelManager.selectVisualEffects(5, 'space');
            const underwaterEffects = levelManager.selectVisualEffects(5, 'underwater');
            const volcanicEffects = levelManager.selectVisualEffects(5, 'volcanic');
            
            expect(spaceEffects).toContain('star_field');
            expect(underwaterEffects).toContain('water_bubbles');
            expect(volcanicEffects).toContain('lava_particles');
        });

        test('should add intensity effects for higher levels', () => {
            const effects5 = levelManager.selectVisualEffects(5, 'space');
            const effects15 = levelManager.selectVisualEffects(15, 'space');
            
            expect(effects5).not.toContain('intensity_overlay');
            expect(effects15).toContain('intensity_overlay');
        });
    });

    describe('update', () => {
        test('should not update if level not in progress', () => {
            levelManager.levelInProgress = false;
            const initialCompletionTime = levelManager.levelCompletionTime;
            
            levelManager.update(100, {});
            
            expect(levelManager.levelCompletionTime).toBe(initialCompletionTime);
        });

        test('should update completion time when level in progress', () => {
            levelManager.levelInProgress = true;
            levelManager.levelStartTime = performance.now() - 1000;
            
            levelManager.update(100, {});
            
            expect(levelManager.levelCompletionTime).toBeGreaterThan(1000);
        });

        test('should check level completion', () => {
            levelManager.levelInProgress = true;
            levelManager.checkLevelCompletion = jest.fn();
            
            levelManager.update(100, {});
            
            expect(levelManager.checkLevelCompletion).toHaveBeenCalledWith({});
        });

        test('should update performance metrics', () => {
            levelManager.levelInProgress = true;
            levelManager.updatePerformanceMetrics = jest.fn();
            
            levelManager.update(100, {});
            
            expect(levelManager.updatePerformanceMetrics).toHaveBeenCalledWith({});
        });
    });

    describe('checkLevelCompletion', () => {
        beforeEach(() => {
            levelManager.levelInProgress = true;
            levelManager.levelConfig = {
                totalEnemies: 10,
                totalWaves: 3,
                timeLimit: 120
            };
            levelManager.requiredEnemiesDefeated = 10;
            levelManager.requiredWaves = 3;
            levelManager.completeLevel = jest.fn();
            levelManager.failLevel = jest.fn();
        });

        test('should complete level when all criteria met', () => {
            levelManager.levelEnemiesDefeated = 10;
            levelManager.wavesCompleted = 3;
            
            levelManager.checkLevelCompletion({});
            
            expect(levelManager.completeLevel).toHaveBeenCalled();
            expect(levelManager.failLevel).not.toHaveBeenCalled();
        });

        test('should not complete level when criteria not met', () => {
            levelManager.levelEnemiesDefeated = 5;
            levelManager.wavesCompleted = 2;
            
            levelManager.checkLevelCompletion({});
            
            expect(levelManager.completeLevel).not.toHaveBeenCalled();
            expect(levelManager.failLevel).not.toHaveBeenCalled();
        });

        test('should fail level when time expires', () => {
            levelManager.levelCompletionTime = 121000; // 121 seconds, over 120 second limit
            
            levelManager.checkLevelCompletion({});
            
            expect(levelManager.completeLevel).not.toHaveBeenCalled();
            expect(levelManager.failLevel).toHaveBeenCalledWith('time_expired');
        });

        test('should fail level when player destroyed', () => {
            levelManager.checkLevelCompletion({ playerDestroyed: true });
            
            expect(levelManager.completeLevel).not.toHaveBeenCalled();
            expect(levelManager.failLevel).toHaveBeenCalledWith('player_destroyed');
        });

        test('should not check completion if level not in progress', () => {
            levelManager.levelInProgress = false;
            
            levelManager.checkLevelCompletion({});
            
            expect(levelManager.completeLevel).not.toHaveBeenCalled();
            expect(levelManager.failLevel).not.toHaveBeenCalled();
        });
    });

    describe('completeLevel', () => {
        beforeEach(() => {
            levelManager.levelInProgress = true;
            levelManager.levelStartTime = performance.now() - 60000; // 60 seconds ago
            levelManager.levelConfig = {
                completionReward: 100,
                difficultyMultiplier: 1.5
            };
            levelManager.currentScore = 500;
            levelManager.levelScore = 200;
            levelManager.levelEnemiesDefeated = 10;
            levelManager.perfectCompletion = true;
            levelManager.onLevelCompleteCallback = jest.fn();
        });

        test('should mark level as not in progress', () => {
            levelManager.completeLevel();
            
            expect(levelManager.levelInProgress).toBe(false);
        });

        test('should calculate completion time', () => {
            levelManager.completeLevel();
            
            expect(levelManager.levelCompletionTime).toBeGreaterThan(59000); // Approximately 60 seconds
            expect(levelManager.levelCompletionTime).toBeLessThan(61000);
        });

        test('should calculate level score', () => {
            levelManager.calculateLevelScore = jest.fn().mockReturnValue({
                totalScore: 300,
                bonuses: { speed: true, accuracy: false, perfect: true }
            });
            
            levelManager.completeLevel();
            
            expect(levelManager.calculateLevelScore).toHaveBeenCalledWith(expect.any(Number));
        });

        test('should update total score', () => {
            levelManager.calculateLevelScore = jest.fn().mockReturnValue({
                totalScore: 300,
                bonuses: { speed: true, accuracy: false, perfect: true }
            });
            
            levelManager.completeLevel();
            
            expect(levelManager.currentScore).toBe(800); // 500 + 300
            expect(levelManager.totalScore).toBe(800);
        });

        test('should trigger completion callback with correct data', () => {
            const mockScoreData = {
                totalScore: 300,
                bonuses: { speed: true, accuracy: false, perfect: true }
            };
            levelManager.calculateLevelScore = jest.fn().mockReturnValue(mockScoreData);
            
            levelManager.completeLevel();
            
            const expectedData = {
                levelNumber: 1,
                completed: true,
                completionTime: expect.any(Number),
                score: mockScoreData,
                enemiesDefeated: 10,
                perfectCompletion: true,
                bonuses: mockScoreData.bonuses,
                nextLevel: 2
            };
            
            expect(levelManager.onLevelCompleteCallback).toHaveBeenCalledWith(expectedData);
        });

        test('should return completion data', () => {
            const mockScoreData = {
                totalScore: 300,
                bonuses: { speed: true, accuracy: false, perfect: true }
            };
            levelManager.calculateLevelScore = jest.fn().mockReturnValue(mockScoreData);
            
            const result = levelManager.completeLevel();
            
            expect(result.levelNumber).toBe(1);
            expect(result.completed).toBe(true);
            expect(result.score).toBe(mockScoreData);
            expect(result.nextLevel).toBe(2);
        });
    });

    describe('failLevel', () => {
        beforeEach(() => {
            levelManager.levelInProgress = true;
            levelManager.levelStartTime = performance.now() - 30000; // 30 seconds ago
            levelManager.levelScore = 100;
            levelManager.levelEnemiesDefeated = 5;
            levelManager.onLevelCompleteCallback = jest.fn();
        });

        test('should mark level as not in progress', () => {
            levelManager.failLevel('test_reason');
            
            expect(levelManager.levelInProgress).toBe(false);
        });

        test('should calculate completion time', () => {
            levelManager.failLevel('test_reason');
            
            expect(levelManager.levelCompletionTime).toBeGreaterThan(29000); // Approximately 30 seconds
            expect(levelManager.levelCompletionTime).toBeLessThan(31000);
        });

        test('should trigger completion callback with failure data', () => {
            levelManager.failLevel('time_expired');
            
            const expectedData = {
                levelNumber: 1,
                completed: false,
                reason: 'time_expired',
                completionTime: expect.any(Number),
                score: 100,
                enemiesDefeated: 5,
                canRetry: true
            };
            
            expect(levelManager.onLevelCompleteCallback).toHaveBeenCalledWith(expectedData);
        });

        test('should return failure data', () => {
            const result = levelManager.failLevel('player_destroyed');
            
            expect(result.levelNumber).toBe(1);
            expect(result.completed).toBe(false);
            expect(result.reason).toBe('player_destroyed');
            expect(result.score).toBe(100);
            expect(result.enemiesDefeated).toBe(5);
            expect(result.canRetry).toBe(true);
        });
    });

    describe('calculateLevelScore', () => {
        beforeEach(() => {
            levelManager.levelConfig = {
                completionReward: 100,
                difficultyMultiplier: 1.5
            };
            levelManager.levelScore = 500;
            levelManager.perfectCompletion = true;
            levelManager.calculateTimeBonus = jest.fn().mockReturnValue(200);
            levelManager.calculateAccuracyBonus = jest.fn().mockReturnValue(150);
        });

        test('should calculate total score with all components', () => {
            const result = levelManager.calculateLevelScore(60);
            
            expect(result.enemyScore).toBe(500);
            expect(result.timeBonus).toBe(200);
            expect(result.accuracyBonus).toBe(150);
            expect(result.perfectBonus).toBe(250); // 500 * 0.5
            expect(result.completionBonus).toBe(100);
            expect(result.difficultyMultiplier).toBe(1.5);
            
            // Base total = 500 + 200 + 150 + 250 + 100 = 1200
            // With multiplier = 1200 * 1.5 = 1800
            expect(result.totalScore).toBe(1800);
        });

        test('should include bonus information', () => {
            const result = levelManager.calculateLevelScore(60);
            
            expect(result.bonuses.speed).toBe(true);
            expect(result.bonuses.accuracy).toBe(true);
            expect(result.bonuses.perfect).toBe(true);
        });

        test('should not include perfect bonus for imperfect completion', () => {
            levelManager.perfectCompletion = false;
            
            const result = levelManager.calculateLevelScore(60);
            
            expect(result.perfectBonus).toBe(0);
            expect(result.bonuses.perfect).toBe(false);
        });
    });

    describe('calculateTimeBonus', () => {
        test('should return maximum bonus for excellent time', () => {
            const bonus = levelManager.calculateTimeBonus(60, 120); // 60 is exactly 50% of 120
            
            expect(bonus).toBe(500); // Maximum bonus
        });

        test('should return partial bonus for good time', () => {
            const bonus = levelManager.calculateTimeBonus(90, 120); // 90 is 75% of 120
            
            expect(bonus).toBe(200);
        });

        test('should return small bonus for acceptable time', () => {
            const bonus = levelManager.calculateTimeBonus(110, 120); // 110 is ~92% of 120
            
            expect(bonus).toBe(50);
        });

        test('should return no bonus for slow completion', () => {
            const bonus = levelManager.calculateTimeBonus(130, 120); // Over time limit
            
            expect(bonus).toBe(0);
        });

        test('should calculate decreasing bonus within excellent range', () => {
            const bonus1 = levelManager.calculateTimeBonus(50, 120); // Very fast
            const bonus2 = levelManager.calculateTimeBonus(70, 120); // Still excellent but slower
            
            expect(bonus1).toBeGreaterThan(bonus2);
        });
    });

    describe('calculateAccuracyBonus', () => {
        test('should return current accuracy bonus value', () => {
            levelManager.accuracyBonus = 300;
            
            expect(levelManager.calculateAccuracyBonus()).toBe(300);
            
            levelManager.accuracyBonus = 150;
            
            expect(levelManager.calculateAccuracyBonus()).toBe(150);
        });
    });

    describe('updatePerformanceMetrics', () => {
        test('should set perfect completion to false when player takes damage', () => {
            levelManager.perfectCompletion = true;
            
            levelManager.updatePerformanceMetrics({ playerDamageTaken: true });
            
            expect(levelManager.perfectCompletion).toBe(false);
        });

        test('should not change perfect completion when no damage taken', () => {
            levelManager.perfectCompletion = true;
            
            levelManager.updatePerformanceMetrics({ playerDamageTaken: false });
            
            expect(levelManager.perfectCompletion).toBe(true);
        });

        test('should set accuracy bonus based on shot accuracy', () => {
            levelManager.updatePerformanceMetrics({ shotsFired: 10, shotsHit: 9 });
            
            expect(levelManager.accuracyBonus).toBe(300); // 90% accuracy
            
            levelManager.updatePerformanceMetrics({ shotsFired: 10, shotsHit: 7 });
            
            expect(levelManager.accuracyBonus).toBe(150); // 70% accuracy
            
            levelManager.updatePerformanceMetrics({ shotsFired: 10, shotsHit: 5 });
            
            expect(levelManager.accuracyBonus).toBe(50); // 50% accuracy
        });

        test('should not update accuracy bonus when shot data not provided', () => {
            levelManager.accuracyBonus = 100;
            
            levelManager.updatePerformanceMetrics({});
            
            expect(levelManager.accuracyBonus).toBe(100);
        });
    });

    describe('recordEnemyDefeat', () => {
        beforeEach(() => {
            levelManager.currentScore = 1000;
            levelManager.levelScore = 500;
            levelManager.enemiesDefeated = 5;
            levelManager.levelEnemiesDefeated = 3;
            levelManager.onScoreUpdateCallback = jest.fn();
        });

        test('should increment defeat counters', () => {
            levelManager.recordEnemyDefeat({}, 100);
            
            expect(levelManager.enemiesDefeated).toBe(6);
            expect(levelManager.levelEnemiesDefeated).toBe(4);
        });

        test('should add score value to scores', () => {
            levelManager.recordEnemyDefeat({}, 100);
            
            expect(levelManager.levelScore).toBe(600);
            expect(levelManager.currentScore).toBe(1100);
        });

        test('should trigger score update callback', () => {
            levelManager.recordEnemyDefeat({}, 100);
            
            expect(levelManager.onScoreUpdateCallback).toHaveBeenCalledWith({
                enemiesDefeated: 6,
                levelEnemiesDefeated: 4,
                currentScore: 1100,
                levelScore: 600,
                scoreGained: 100
            });
        });
    });

    describe('recordWaveCompletion', () => {
        beforeEach(() => {
            levelManager.currentScore = 1000;
            levelManager.levelScore = 500;
            levelManager.wavesCompleted = 2;
            levelManager.onScoreUpdateCallback = jest.fn();
        });

        test('should increment waves completed', () => {
            levelManager.recordWaveCompletion(3, 0);
            
            expect(levelManager.wavesCompleted).toBe(3);
        });

        test('should add wave bonus to scores when provided', () => {
            levelManager.recordWaveCompletion(3, 200);
            
            expect(levelManager.levelScore).toBe(700);
            expect(levelManager.currentScore).toBe(1200);
        });

        test('should not add to scores when no bonus provided', () => {
            levelManager.recordWaveCompletion(3, 0);
            
            expect(levelManager.levelScore).toBe(500);
            expect(levelManager.currentScore).toBe(1000);
        });

        test('should trigger score update callback', () => {
            levelManager.recordWaveCompletion(3, 200);
            
            expect(levelManager.onScoreUpdateCallback).toHaveBeenCalledWith({
                wavesCompleted: 3,
                currentScore: 1200,
                levelScore: 700,
                scoreGained: 200
            });
        });
    });

    describe('calculateBaseLevelReward', () => {
        test('should calculate correct reward for level 1', () => {
            const reward = levelManager.calculateBaseLevelReward(1);
            
            expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 1); // Level 1-5: multiplier 1
        });

        test('should calculate correct reward for level 5', () => {
            const reward = levelManager.calculateBaseLevelReward(5);
            
            expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 1); // Level 1-5: multiplier 1
        });

        test('should calculate correct reward for level 6', () => {
            const reward = levelManager.calculateBaseLevelReward(6);
            
            expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 2); // Level 6-10: multiplier 2
        });

        test('should calculate correct reward for level 11', () => {
            const reward = levelManager.calculateBaseLevelReward(11);
            
            expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 3); // Level 11-15: multiplier 3
        });
    });

    describe('getLevelStatus', () => {
        beforeEach(() => {
            levelManager.currentLevel = 5;
            levelManager.levelInProgress = true;
            levelManager.levelConfig = { test: 'config' };
            levelManager.levelCompletionTime = 60000;
            levelManager.currentScore = 1500;
            levelManager.levelScore = 500;
            levelManager.totalScore = 2000;
            levelManager.levelEnemiesDefeated = 8;
            levelManager.requiredEnemiesDefeated = 10;
            levelManager.wavesCompleted = 2;
            levelManager.requiredWaves = 3;
            levelManager.perfectCompletion = true;
            levelManager.speedBonus = 200;
            levelManager.accuracyBonus = 150;
        });

        test('should return complete level status information', () => {
            const status = levelManager.getLevelStatus();
            
            expect(status.currentLevel).toBe(5);
            expect(status.levelInProgress).toBe(true);
            expect(status.levelConfig).toEqual({ test: 'config' });
            expect(status.completionTime).toBe(60000);
            
            expect(status.score.current).toBe(1500);
            expect(status.score.level).toBe(500);
            expect(status.score.total).toBe(2000);
            
            expect(status.progress.enemiesDefeated).toBe(8);
            expect(status.progress.requiredEnemies).toBe(10);
            expect(status.progress.wavesCompleted).toBe(2);
            expect(status.progress.requiredWaves).toBe(3);
            
            expect(status.performance.perfectCompletion).toBe(true);
            expect(status.performance.speedBonus).toBe(200);
            expect(status.performance.accuracyBonus).toBe(150);
        });
    });

    describe('reset', () => {
        beforeEach(() => {
            levelManager.currentLevel = 10;
            levelManager.levelInProgress = true;
            levelManager.levelStartTime = 1000;
            levelManager.levelCompletionTime = 2000;
            levelManager.currentScore = 5000;
            levelManager.levelScore = 1000;
            levelManager.enemiesDefeated = 50;
            levelManager.levelEnemiesDefeated = 20;
            levelManager.totalScore = 5000;
            levelManager.perfectCompletion = false;
            levelManager.speedBonus = 300;
            levelManager.accuracyBonus = 200;
            levelManager.wavesCompleted = 5;
        });

        test('should reset all level manager state to defaults', () => {
            levelManager.reset();
            
            expect(levelManager.currentLevel).toBe(1);
            expect(levelManager.levelInProgress).toBe(false);
            expect(levelManager.levelStartTime).toBe(0);
            expect(levelManager.levelCompletionTime).toBe(0);
            expect(levelManager.currentScore).toBe(0);
            expect(levelManager.levelScore).toBe(0);
            expect(levelManager.enemiesDefeated).toBe(0);
            expect(levelManager.levelEnemiesDefeated).toBe(0);
            expect(levelManager.totalScore).toBe(0);
            expect(levelManager.perfectCompletion).toBe(true);
            expect(levelManager.speedBonus).toBe(0);
            expect(levelManager.accuracyBonus).toBe(0);
            expect(levelManager.wavesCompleted).toBe(0);
        });
    });

    describe('setOnLevelStart', () => {
        test('should set level start callback', () => {
            const mockCallback = jest.fn();
            
            levelManager.setOnLevelStart(mockCallback);
            
            expect(levelManager.onLevelStartCallback).toBe(mockCallback);
        });
    });

    describe('setOnLevelComplete', () => {
        test('should set level complete callback', () => {
            const mockCallback = jest.fn();
            
            levelManager.setOnLevelComplete(mockCallback);
            
            expect(levelManager.onLevelCompleteCallback).toBe(mockCallback);
        });
    });

    describe('setOnScoreUpdate', () => {
        test('should set score update callback', () => {
            const mockCallback = jest.fn();
            
            levelManager.setOnScoreUpdate(mockCallback);
            
            expect(levelManager.onScoreUpdateCallback).toBe(mockCallback);
        });
    });
});
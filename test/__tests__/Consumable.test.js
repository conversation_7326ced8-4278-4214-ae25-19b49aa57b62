import { jest } from '@jest/globals';
import { 
    Consumable, 
    EMPBlastConsumable, 
    TimeDilationConsumable, 
    ConsumableFactory 
} from '../../src/systems/Consumable.js';

// Mock GameEngine
class MockGameEngine {
    constructor() {
        this.gameObjectManager = {
            findByTag: jest.fn(() => [])
        };
    }
}

// Mock Enemy
class MockEnemy {
    constructor(id = 'enemy1') {
        this.id = id;
        this.active = true;
        this.isDestroyed = false;
        this.velocity = { x: 10, y: 5, set: jest.fn(), clone: jest.fn(() => ({ x: 10, y: 5 })) };
        this.weaponSystem = {
            disableForDuration: jest.fn()
        };
        this.isFrozen = false;
        this.freezeEndTime = null;
        this.originalVelocity = null;
    }

    destroy() {
        this.isDestroyed = true;
        this.active = false;
    }
}

// Mock Projectile
class MockProjectile {
    constructor(id = 'proj1') {
        this.id = id;
        this.active = true;
        this.isDestroyed = false;
        this.destroy = jest.fn(() => {
            this.isDestroyed = true;
            this.active = false;
        });
    }
}

describe('Consumable Base Class', () => {
    let consumable;

    beforeEach(() => {
        consumable = new Consumable('TEST_TYPE', 1000, 'Test consumable', 500);
    });

    test('should initialize with correct properties', () => {
        expect(consumable.type).toBe('TEST_TYPE');
        expect(consumable.cost).toBe(1000);
        expect(consumable.description).toBe('Test consumable');
        expect(consumable.cooldown).toBe(500);
        expect(consumable.isUsed).toBe(false);
        expect(consumable.usedAt).toBeNull();
        expect(consumable.id).toMatch(/^consumable_\d+$/);
    });

    test('should generate unique IDs', () => {
        const consumable2 = new Consumable('TEST_TYPE2', 500);
        expect(consumable.id).not.toBe(consumable2.id);
    });

    test('should format name correctly', () => {
        expect(consumable.name).toBe('Test Type');
    });

    test('should check purchase availability correctly', () => {
        const result1 = consumable.canPurchase(2000);
        expect(result1.canPurchase).toBe(true);
        expect(result1.reason).toBe('available');

        const result2 = consumable.canPurchase(500);
        expect(result2.canPurchase).toBe(false);
        expect(result2.reason).toBe('insufficient_tokens');
    });

    test('should provide correct display info', () => {
        const displayInfo = consumable.getDisplayInfo();
        expect(displayInfo.type).toBe('TEST_TYPE');
        expect(displayInfo.name).toBe('Test Type');
        expect(displayInfo.description).toBe('Test consumable');
        expect(displayInfo.cost).toBe(1000);
        expect(displayInfo.isUsed).toBe(false);
    });

    test('should clone correctly', () => {
        const clone = consumable.clone();
        expect(clone).toBeInstanceOf(Consumable);
        expect(clone.type).toBe(consumable.type);
        expect(clone.cost).toBe(consumable.cost);
        expect(clone.id).not.toBe(consumable.id); // Should have different ID
    });

    test('should activate successfully', async () => {
        const mockGameEngine = new MockGameEngine();
        const result = await consumable.activate(mockGameEngine);
        
        expect(result).toBe(true);
        expect(consumable.isUsed).toBe(true);
        expect(consumable.usedAt).toBeGreaterThan(0);
    });

    test('should not activate if already used', async () => {
        const mockGameEngine = new MockGameEngine();
        
        // First activation
        await consumable.activate(mockGameEngine);
        
        // Second activation should fail
        const result = await consumable.activate(mockGameEngine);
        expect(result).toBe(false);
    });
});

describe('EMPBlastConsumable', () => {
    let empBlast;
    let mockGameEngine;

    beforeEach(() => {
        empBlast = new EMPBlastConsumable();
        mockGameEngine = new MockGameEngine();
    });

    test('should initialize with correct properties', () => {
        expect(empBlast.type).toBe('EMP_BLAST');
        expect(empBlast.cost).toBe(1750);
        expect(empBlast.icon).toBe('⚡');
        expect(empBlast.color).toBe('#00aaff');
        expect(empBlast.disableDuration).toBe(5000);
    });

    test('should destroy enemy projectiles and disable enemy weapons', async () => {
        const mockProjectiles = [
            new MockProjectile('proj1'),
            new MockProjectile('proj2'),
            new MockProjectile('proj3')
        ];
        
        const mockEnemies = [
            new MockEnemy('enemy1'),
            new MockEnemy('enemy2')
        ];

        mockGameEngine.gameObjectManager.findByTag
            .mockReturnValueOnce(mockProjectiles) // First call for enemy_projectile
            .mockReturnValueOnce(mockEnemies);    // Second call for enemy

        const result = await empBlast.activate(mockGameEngine);

        expect(result).toBe(true);
        expect(empBlast.isUsed).toBe(true);

        // Check that all projectiles were destroyed
        mockProjectiles.forEach(projectile => {
            expect(projectile.destroy).toHaveBeenCalled();
        });

        // Check that enemy weapons were disabled
        mockEnemies.forEach(enemy => {
            expect(enemy.weaponSystem.disableForDuration).toHaveBeenCalledWith(5000);
        });
    });

    test('should handle empty projectile and enemy arrays', async () => {
        mockGameEngine.gameObjectManager.findByTag.mockReturnValue([]);

        const result = await empBlast.activate(mockGameEngine);

        expect(result).toBe(true);
        expect(empBlast.isUsed).toBe(true);
    });

    test('should handle errors gracefully', async () => {
        mockGameEngine.gameObjectManager.findByTag.mockImplementation(() => {
            throw new Error('Test error');
        });

        const result = await empBlast.activate(mockGameEngine);

        expect(result).toBe(false);
        expect(empBlast.isUsed).toBe(false);
    });
});

describe('TimeDilationConsumable', () => {
    let timeDilation;
    let mockGameEngine;

    beforeEach(() => {
        timeDilation = new TimeDilationConsumable();
        mockGameEngine = new MockGameEngine();
    });

    test('should initialize with correct properties', () => {
        expect(timeDilation.type).toBe('TIME_DILATION');
        expect(timeDilation.cost).toBe(1750);
        expect(timeDilation.icon).toBe('⏰');
        expect(timeDilation.color).toBe('#aa00ff');
        expect(timeDilation.freezeDuration).toBe(5000);
    });

    test('should freeze enemies and disable their weapons', async () => {
        const mockEnemies = [
            new MockEnemy('enemy1'),
            new MockEnemy('enemy2'),
            new MockEnemy('enemy3')
        ];

        mockGameEngine.gameObjectManager.findByTag.mockReturnValue(mockEnemies);

        const result = await timeDilation.activate(mockGameEngine);

        expect(result).toBe(true);
        expect(timeDilation.isUsed).toBe(true);

        // Check that all enemies were frozen
        mockEnemies.forEach(enemy => {
            expect(enemy.velocity.set).toHaveBeenCalledWith(0, 0);
            expect(enemy.isFrozen).toBe(true);
            expect(enemy.freezeEndTime).toBeGreaterThan(Date.now());
            expect(enemy.weaponSystem.disableForDuration).toHaveBeenCalledWith(5000);
        });
    });

    test('should handle enemies without weapon systems', async () => {
        const mockEnemies = [
            { ...new MockEnemy('enemy1'), weaponSystem: null },
            new MockEnemy('enemy2')
        ];

        mockGameEngine.gameObjectManager.findByTag.mockReturnValue(mockEnemies);

        const result = await timeDilation.activate(mockGameEngine);

        expect(result).toBe(true);
        expect(timeDilation.isUsed).toBe(true);

        // First enemy should still be frozen even without weapon system
        expect(mockEnemies[0].isFrozen).toBe(true);
        expect(mockEnemies[1].weaponSystem.disableForDuration).toHaveBeenCalled();
    });

    test('should handle errors gracefully', async () => {
        mockGameEngine.gameObjectManager.findByTag.mockImplementation(() => {
            throw new Error('Test error');
        });

        const result = await timeDilation.activate(mockGameEngine);

        expect(result).toBe(false);
        expect(timeDilation.isUsed).toBe(false);
    });
});

describe('ConsumableFactory', () => {
    test('should create EMP Blast consumable', () => {
        const consumable = ConsumableFactory.createConsumable('EMP_BLAST');
        expect(consumable).toBeInstanceOf(EMPBlastConsumable);
    });

    test('should create Time Dilation consumable', () => {
        const consumable = ConsumableFactory.createConsumable('TIME_DILATION');
        expect(consumable).toBeInstanceOf(TimeDilationConsumable);
    });

    test('should return null for unknown type', () => {
        const consumable = ConsumableFactory.createConsumable('UNKNOWN_TYPE');
        expect(consumable).toBeNull();
    });

    test('should create all consumables', () => {
        const consumables = ConsumableFactory.createAllConsumables();
        expect(consumables).toHaveLength(2);
        expect(consumables[0]).toBeInstanceOf(EMPBlastConsumable);
        expect(consumables[1]).toBeInstanceOf(TimeDilationConsumable);
    });

    test('should return correct consumable types', () => {
        const types = ConsumableFactory.getConsumableTypes();
        expect(types).toEqual(['EMP_BLAST', 'TIME_DILATION']);
    });
});

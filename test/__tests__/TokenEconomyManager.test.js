import { TokenEconomyManager } from '../../src/managers/TokenEconomyManager.js';
import { GAME_CONFIG } from '../../src/config/gameConfig.js';

describe('TokenEconomyManager', () => {
  let tokenEconomyManager;

  beforeEach(() => {
    tokenEconomyManager = new TokenEconomyManager();
    // Reset console mocks
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    test('should initialize with default values', () => {
      expect(tokenEconomyManager.playerBalance).toBe(0);
      expect(tokenEconomyManager.totalEarned).toBe(0);
      expect(tokenEconomyManager.totalSpent).toBe(0);
      expect(tokenEconomyManager.transactionHistory).toEqual([]);
      expect(tokenEconomyManager.maxHistorySize).toBe(100);
      expect(tokenEconomyManager.pendingRewards).toEqual([]);
      expect(tokenEconomyManager.rewardAnimations).toEqual([]);
      expect(tokenEconomyManager.debugMode).toBe(false);
    });

    test('should initialize performance metrics', () => {
      expect(tokenEconomyManager.performanceMetrics).toEqual({
        levelsCompleted: 0,
        totalScore: 0,
        averageCompletionTime: 0,
        perfectCompletions: 0,
        speedBonuses: 0,
        accuracyBonuses: 0
      });
    });

    test('should initialize reward multipliers', () => {
      expect(tokenEconomyManager.rewardMultipliers).toEqual({
        base: 1.0,
        speed: 1.5,
        accuracy: 1.3,
        perfect: 2.0,
        difficulty: 1.0
      });
    });
  });

  describe('calculateBaseReward', () => {
    test('should calculate base reward correctly for level 1', () => {
      const reward = tokenEconomyManager.calculateBaseReward(1);
      expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 1);
    });

    test('should calculate base reward correctly for level 5', () => {
      const reward = tokenEconomyManager.calculateBaseReward(5);
      expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 1);
    });

    test('should calculate base reward correctly for level 6', () => {
      const reward = tokenEconomyManager.calculateBaseReward(6);
      expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 2);
    });

    test('should calculate base reward correctly for level 10', () => {
      const reward = tokenEconomyManager.calculateBaseReward(10);
      expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 2);
    });

    test('should calculate base reward correctly for level 11', () => {
      const reward = tokenEconomyManager.calculateBaseReward(11);
      expect(reward).toBe(GAME_CONFIG.BASE_LEVEL_REWARD * 3);
    });
  });

  describe('calculateTimeBonus', () => {
    test('should return maximum bonus for very fast completion', () => {
      const bonus = tokenEconomyManager.calculateTimeBonus(30, 1);
      expect(bonus).toBe(35); // 30 + (1 * 5)
    });

    test('should return partial bonus for good completion time', () => {
      const bonus = tokenEconomyManager.calculateTimeBonus(50, 1);
      expect(bonus).toBe(23); // 20 + (1 * 3)
    });

    test('should return small bonus for acceptable completion time', () => {
      const bonus = tokenEconomyManager.calculateTimeBonus(65, 1);
      expect(bonus).toBe(11); // 10 + 1
    });

    test('should return no bonus for slow completion', () => {
      const bonus = tokenEconomyManager.calculateTimeBonus(80, 1);
      expect(bonus).toBe(0);
    });

    test('should scale bonus with level number', () => {
      const bonus1 = tokenEconomyManager.calculateTimeBonus(30, 1);
      const bonus5 = tokenEconomyManager.calculateTimeBonus(30, 5);
      expect(bonus5).toBeGreaterThan(bonus1);
    });
  });

  describe('calculateScoreBonus', () => {
    test('should calculate score bonus correctly', () => {
      const bonus = tokenEconomyManager.calculateScoreBonus(1000, 1);
      expect(bonus).toBe(10); // 1000 * 0.01 * 1.0
    });

    test('should apply level scaling to score bonus', () => {
      const bonus1 = tokenEconomyManager.calculateScoreBonus(1000, 1);
      const bonus10 = tokenEconomyManager.calculateScoreBonus(1000, 10);
      expect(bonus10).toBeGreaterThan(bonus1);
    });

    test('should cap level scaling at 2.0', () => {
      const bonus20 = tokenEconomyManager.calculateScoreBonus(1000, 20);
      const bonus30 = tokenEconomyManager.calculateScoreBonus(1000, 30);
      expect(bonus20).toBe(20); // 1000 * 0.01 * 2.0
      expect(bonus30).toBe(20); // 1000 * 0.01 * 2.0
    });
  });

  describe('calculateSpeedBonus', () => {
    test('should calculate speed bonus based on base reward', () => {
      const bonus = tokenEconomyManager.calculateSpeedBonus(100);
      expect(bonus).toBe(50); // 100 * (1.5 - 1.0)
    });

    test('should return 0 for zero base reward', () => {
      const bonus = tokenEconomyManager.calculateSpeedBonus(0);
      expect(bonus).toBe(0);
    });
  });

  describe('calculateAccuracyBonus', () => {
    test('should calculate accuracy bonus based on base reward', () => {
      const bonus = tokenEconomyManager.calculateAccuracyBonus(100);
      expect(bonus).toBe(30); // 100 * (1.3 - 1.0)
    });

    test('should return 0 for zero base reward', () => {
      const bonus = tokenEconomyManager.calculateAccuracyBonus(0);
      expect(bonus).toBe(0);
    });
  });

  describe('calculatePerfectBonus', () => {
    test('should calculate perfect bonus based on base reward', () => {
      const bonus = tokenEconomyManager.calculatePerfectBonus(100);
      expect(bonus).toBe(100); // 100 * (2.0 - 1.0)
    });

    test('should return 0 for zero base reward', () => {
      const bonus = tokenEconomyManager.calculatePerfectBonus(0);
      expect(bonus).toBe(0);
    });
  });

  describe('calculateDifficultyMultiplier', () => {
    test('should return base multiplier for early levels', () => {
      const multiplier = tokenEconomyManager.calculateDifficultyMultiplier(1);
      expect(multiplier).toBe(1.0);
    });

    test('should increase multiplier every 10 levels', () => {
      const multiplier1 = tokenEconomyManager.calculateDifficultyMultiplier(1);
      const multiplier10 = tokenEconomyManager.calculateDifficultyMultiplier(10);
      const multiplier11 = tokenEconomyManager.calculateDifficultyMultiplier(11);
      const multiplier20 = tokenEconomyManager.calculateDifficultyMultiplier(20);
      
      expect(multiplier10).toBe(1.0);
      expect(multiplier11).toBe(1.1);
      expect(multiplier20).toBe(1.1);
    });

    test('should cap multiplier at 3.0', () => {
      const multiplier201 = tokenEconomyManager.calculateDifficultyMultiplier(201);
      const multiplier300 = tokenEconomyManager.calculateDifficultyMultiplier(300);
      expect(multiplier201).toBe(3.0);
      expect(multiplier300).toBe(3.0); // Should be capped at 3.0
    });
  });

  describe('calculateLevelReward', () => {
    test('should return 0 for incomplete level', () => {
      const completionData = { completed: false };
      const result = tokenEconomyManager.calculateLevelReward(completionData);
      expect(result.totalReward).toBe(0);
      expect(result.breakdown.reason).toBe('level_not_completed');
    });

    test('should calculate total reward correctly for completed level', () => {
      const completionData = {
        completed: true,
        levelNumber: 1,
        completionTime: 60,
        score: { totalScore: 1000 },
        bonuses: {
          speed: true,
          accuracy: true,
          perfect: true
        }
      };
      
      const result = tokenEconomyManager.calculateLevelReward(completionData);
      expect(result.totalReward).toBeGreaterThan(0);
      expect(result.breakdown).toHaveProperty('baseReward');
      expect(result.breakdown).toHaveProperty('timeBonus');
      expect(result.breakdown).toHaveProperty('scoreBonus');
      expect(result.breakdown).toHaveProperty('speedBonus');
      expect(result.breakdown).toHaveProperty('accuracyBonus');
      expect(result.breakdown).toHaveProperty('perfectBonus');
      expect(result.breakdown).toHaveProperty('difficultyMultiplier');
      expect(result.breakdown).toHaveProperty('totalReward');
    });

    test('should not include bonuses when not earned', () => {
      const completionData = {
        completed: true,
        levelNumber: 1,
        completionTime: 60,
        score: { totalScore: 1000 },
        bonuses: {
          speed: false,
          accuracy: false,
          perfect: false
        }
      };
      
      const result = tokenEconomyManager.calculateLevelReward(completionData);
      expect(result.breakdown.speedBonus).toBe(0);
      expect(result.breakdown.accuracyBonus).toBe(0);
      expect(result.breakdown.perfectBonus).toBe(0);
    });
  });

  describe('awardTokens', () => {
    test('should award tokens and update balance', () => {
      const result = tokenEconomyManager.awardTokens(100, 'test_reward');
      
      expect(result.success).toBe(true);
      expect(tokenEconomyManager.playerBalance).toBe(100);
      expect(tokenEconomyManager.totalEarned).toBe(100);
      expect(result.newBalance).toBe(100);
      expect(result.transaction).toHaveProperty('id');
      expect(result.transaction.type).toBe('earned');
      expect(result.transaction.amount).toBe(100);
      expect(result.transaction.reason).toBe('test_reward');
    });

    test('should reject non-positive token amounts', () => {
      const result = tokenEconomyManager.awardTokens(0, 'test_reward');
      expect(result.success).toBe(false);
      expect(result.reason).toBe('invalid_amount');
      
      const result2 = tokenEconomyManager.awardTokens(-10, 'test_reward');
      expect(result2.success).toBe(false);
      expect(result2.reason).toBe('invalid_amount');
    });

    test('should add transaction to history', () => {
      tokenEconomyManager.awardTokens(100, 'test_reward');
      expect(tokenEconomyManager.transactionHistory.length).toBe(1);
      expect(tokenEconomyManager.transactionHistory[0].type).toBe('earned');
    });

    test('should add pending reward for visual feedback', () => {
      tokenEconomyManager.awardTokens(100, 'test_reward');
      expect(tokenEconomyManager.pendingRewards.length).toBe(1);
      expect(tokenEconomyManager.pendingRewards[0].amount).toBe(100);
      expect(tokenEconomyManager.pendingRewards[0].reason).toBe('test_reward');
    });

    test('should update performance metrics for level completion', () => {
      const metadata = {
        score: 1000,
        completionTime: 60,
        bonuses: {
          perfect: true,
          speed: true,
          accuracy: true
        }
      };
      
      tokenEconomyManager.awardTokens(100, 'level_completion', metadata);
      expect(tokenEconomyManager.performanceMetrics.levelsCompleted).toBe(1);
      expect(tokenEconomyManager.performanceMetrics.totalScore).toBe(1000);
      expect(tokenEconomyManager.performanceMetrics.perfectCompletions).toBe(1);
      expect(tokenEconomyManager.performanceMetrics.speedBonuses).toBe(1);
      expect(tokenEconomyManager.performanceMetrics.accuracyBonuses).toBe(1);
    });
  });

  describe('spendTokens', () => {
    beforeEach(() => {
      tokenEconomyManager.awardTokens(200, 'initial_balance');
    });

    test('should spend tokens and update balance', () => {
      const result = tokenEconomyManager.spendTokens(100, 'test_purchase');
      
      expect(result.success).toBe(true);
      expect(tokenEconomyManager.playerBalance).toBe(100);
      expect(tokenEconomyManager.totalSpent).toBe(100);
      expect(result.newBalance).toBe(100);
      expect(result.transaction).toHaveProperty('id');
      expect(result.transaction.type).toBe('spent');
      expect(result.transaction.amount).toBe(100);
      expect(result.transaction.reason).toBe('test_purchase');
    });

    test('should reject non-positive token amounts', () => {
      const result = tokenEconomyManager.spendTokens(0, 'test_purchase');
      expect(result.success).toBe(false);
      expect(result.reason).toBe('invalid_amount');
    });

    test('should reject spending more than available balance', () => {
      const result = tokenEconomyManager.spendTokens(300, 'test_purchase');
      expect(result.success).toBe(false);
      expect(result.reason).toBe('insufficient_balance');
      expect(result.required).toBe(300);
      expect(result.available).toBe(200);
    });

    test('should add transaction to history', () => {
      tokenEconomyManager.spendTokens(100, 'test_purchase');
      expect(tokenEconomyManager.transactionHistory.length).toBe(2); // Initial award + this spend
      expect(tokenEconomyManager.transactionHistory[1].type).toBe('spent');
    });
  });

  describe('canAfford', () => {
    beforeEach(() => {
      tokenEconomyManager.awardTokens(100, 'initial_balance');
    });

    test('should return true for affordable amounts', () => {
      expect(tokenEconomyManager.canAfford(50)).toBe(true);
      expect(tokenEconomyManager.canAfford(100)).toBe(true);
    });

    test('should return false for unaffordable amounts', () => {
      expect(tokenEconomyManager.canAfford(101)).toBe(false);
      expect(tokenEconomyManager.canAfford(200)).toBe(false);
    });

    test('should use wallet balance in debug mode', () => {
      tokenEconomyManager.debugMode = true;
      tokenEconomyManager.walletBalance = 50;
      
      const result = tokenEconomyManager.canAfford(30);
      expect(result).toBe(true);
    });

    test('should return false when wallet balance insufficient in debug mode', () => {
      tokenEconomyManager.debugMode = true;
      tokenEconomyManager.walletBalance = 20;
      
      const result = tokenEconomyManager.canAfford(30);
      expect(result).toBe(false);
    });

    test('should use wallet balance when wallet connected', () => {
      tokenEconomyManager.walletConnected = true;
      tokenEconomyManager.walletBalance = 75;
      
      const result = tokenEconomyManager.canAfford(50);
      expect(result).toBe(true);
    });

    test('should return false when wallet balance insufficient when connected', () => {
      tokenEconomyManager.walletConnected = true;
      tokenEconomyManager.walletBalance = 25;
      
      const result = tokenEconomyManager.canAfford(50);
      expect(result).toBe(false);
    });
  });

  describe('getBalance', () => {
    test('should return current balance', () => {
      expect(tokenEconomyManager.getBalance()).toBe(0);
      
      tokenEconomyManager.awardTokens(100, 'test');
      expect(tokenEconomyManager.getBalance()).toBe(100);
      
      tokenEconomyManager.spendTokens(30, 'test');
      expect(tokenEconomyManager.getBalance()).toBe(70);
    });

    test('should return wallet balance in debug mode', () => {
      tokenEconomyManager.debugMode = true;
      tokenEconomyManager.walletBalance = 500;
      
      expect(tokenEconomyManager.getBalance()).toBe(500);
    });

    test('should return wallet balance when wallet connected', () => {
      tokenEconomyManager.walletConnected = true;
      tokenEconomyManager.walletBalance = 750;
      
      expect(tokenEconomyManager.getBalance()).toBe(750);
    });
  });

  describe('spendFromWallet', () => {
    beforeEach(() => {
      // Mock localStorage
      global.localStorage = {
        getItem: jest.fn(),
        setItem: jest.fn(),
        removeItem: jest.fn(),
        clear: jest.fn()
      };
    });

    test('should spend from wallet in debug mode and deduct amount + gas fee', async () => {
      tokenEconomyManager.debugMode = true;
      tokenEconomyManager.walletBalance = 10;
      
      const result = await tokenEconomyManager.spendFromWallet(8, 'test_purchase');
      
      expect(result.success).toBe(true);
      expect(result.amount).toBe(8);
      expect(result.gasFee).toBe(0.00003937);
      expect(result.totalCost).toBe(8.00003937);
      expect(tokenEconomyManager.walletBalance).toBeCloseTo(1.99996063, 5); // 10 - 8.00003937
      expect(localStorage.setItem).toHaveBeenCalledWith('debugWalletBalance', expect.any(String));
    });

    test('should reject insufficient balance for amount + gas in debug mode', async () => {
      tokenEconomyManager.debugMode = true;
      tokenEconomyManager.walletBalance = 5;
      
      const result = await tokenEconomyManager.spendFromWallet(8, 'test_purchase');
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('insufficient_wallet_balance_for_gas');
      expect(tokenEconomyManager.walletBalance).toBe(5); // Balance unchanged
    });

    test('should reject when wallet not connected in non-debug mode', async () => {
      tokenEconomyManager.debugMode = false;
      tokenEconomyManager.walletConnected = false;
      
      const result = await tokenEconomyManager.spendFromWallet(10, 'test_purchase');
      
      expect(result.success).toBe(false);
      expect(result.reason).toBe('wallet_not_connected');
    });

    test('should generate unique transaction IDs', () => {
      const id1 = tokenEconomyManager.generateTransactionId();
      const id2 = tokenEconomyManager.generateTransactionId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^tx_\d+_[a-z0-9]+$/);
    });
  });

  describe('getStatistics', () => {
    beforeEach(() => {
      tokenEconomyManager.awardTokens(200, 'initial');
      tokenEconomyManager.spendTokens(50, 'purchase');
    });

    test('should return correct statistics', () => {
      const stats = tokenEconomyManager.getStatistics();
      
      expect(stats.currentBalance).toBe(150);
      expect(stats.totalEarned).toBe(200);
      expect(stats.totalSpent).toBe(50);
      expect(stats.netProfit).toBe(150);
      expect(stats.transactionCount).toBe(2);
      expect(stats.averageEarningPerLevel).toBe(0);
      expect(stats.performanceMetrics).toEqual(tokenEconomyManager.performanceMetrics);
    });

    test('should calculate average earning per level correctly', () => {
      // Create a fresh instance to avoid interference from beforeEach
      const freshManager = new TokenEconomyManager();
      freshManager.awardTokens(100, 'level_completion', { score: 1000 });
      freshManager.awardTokens(150, 'level_completion', { score: 1500 });

      const stats = freshManager.getStatistics();
      expect(stats.averageEarningPerLevel).toBe(125); // (100 + 150) / 2
    });
  });

  describe('getRecentTransactions', () => {
    beforeEach(() => {
      tokenEconomyManager.awardTokens(100, 'reward1');
      tokenEconomyManager.spendTokens(50, 'purchase1');
      tokenEconomyManager.awardTokens(200, 'reward2');
    });

    test('should return recent transactions in reverse chronological order', () => {
      const recent = tokenEconomyManager.getRecentTransactions(2);
      
      expect(recent.length).toBe(2);
      expect(recent[0].reason).toBe('reward2');
      expect(recent[1].reason).toBe('purchase1');
    });

    test('should return all transactions when count exceeds history size', () => {
      const recent = tokenEconomyManager.getRecentTransactions(10);
      expect(recent.length).toBe(3);
    });

    test('should default to 10 transactions when no count specified', () => {
      const recent = tokenEconomyManager.getRecentTransactions();
      expect(recent.length).toBe(3);
    });
  });

  describe('addTransaction', () => {
    test('should add transaction to history', () => {
      const transaction = { id: 'test', type: 'test', amount: 100 };
      tokenEconomyManager.addTransaction(transaction);
      
      expect(tokenEconomyManager.transactionHistory).toContain(transaction);
    });

    test('should limit history size to maxHistorySize', () => {
      // Fill up the history
      for (let i = 0; i < tokenEconomyManager.maxHistorySize; i++) {
        tokenEconomyManager.addTransaction({ id: `test${i}`, type: 'test', amount: 100 });
      }
      
      expect(tokenEconomyManager.transactionHistory.length).toBe(tokenEconomyManager.maxHistorySize);
      
      // Add one more transaction
      tokenEconomyManager.addTransaction({ id: 'newest', type: 'test', amount: 100 });
      
      // Should still be at max size
      expect(tokenEconomyManager.transactionHistory.length).toBe(tokenEconomyManager.maxHistorySize);
      // Should remove the oldest transaction
      expect(tokenEconomyManager.transactionHistory[0].id).not.toBe('test0');
      // Should contain the newest transaction
      expect(tokenEconomyManager.transactionHistory[tokenEconomyManager.maxHistorySize - 1].id).toBe('newest');
    });
  });

  describe('generateTransactionId', () => {
    test('should generate unique transaction IDs', () => {
      const id1 = tokenEconomyManager.generateTransactionId();
      const id2 = tokenEconomyManager.generateTransactionId();
      
      expect(id1).not.toBe(id2);
      expect(id1).toMatch(/^tx_\d+_[a-z0-9]+$/);
      expect(id2).toMatch(/^tx_\d+_[a-z0-9]+$/);
    });
  });

  describe('updatePerformanceMetrics', () => {
    test('should update metrics for level completion', () => {
      const metadata = {
        score: 1000,
        completionTime: 60,
        bonuses: {
          perfect: true,
          speed: true,
          accuracy: true
        }
      };
      
      tokenEconomyManager.updatePerformanceMetrics('level_completion', metadata);
      
      expect(tokenEconomyManager.performanceMetrics.levelsCompleted).toBe(1);
      expect(tokenEconomyManager.performanceMetrics.totalScore).toBe(1000);
      expect(tokenEconomyManager.performanceMetrics.averageCompletionTime).toBe(60);
      expect(tokenEconomyManager.performanceMetrics.perfectCompletions).toBe(1);
      expect(tokenEconomyManager.performanceMetrics.speedBonuses).toBe(1);
      expect(tokenEconomyManager.performanceMetrics.accuracyBonuses).toBe(1);
    });

    test('should calculate average completion time correctly', () => {
      // First completion
      tokenEconomyManager.updatePerformanceMetrics('level_completion', { completionTime: 60 });
      expect(tokenEconomyManager.performanceMetrics.averageCompletionTime).toBe(60);
      
      // Second completion
      tokenEconomyManager.updatePerformanceMetrics('level_completion', { completionTime: 90 });
      expect(tokenEconomyManager.performanceMetrics.averageCompletionTime).toBe(75); // (60 + 90) / 2
      
      // Third completion
      tokenEconomyManager.updatePerformanceMetrics('level_completion', { completionTime: 30 });
      expect(tokenEconomyManager.performanceMetrics.averageCompletionTime).toBe(60); // (60 + 90 + 30) / 3
    });

    test('should not update metrics for non-level completion reasons', () => {
      const initialMetrics = { ...tokenEconomyManager.performanceMetrics };
      tokenEconomyManager.updatePerformanceMetrics('other_reason', {});
      
      expect(tokenEconomyManager.performanceMetrics).toEqual(initialMetrics);
    });
  });

  describe('pending rewards management', () => {
    test('should add pending reward correctly', () => {
      tokenEconomyManager.addPendingReward(100, 'test_reward');
      
      expect(tokenEconomyManager.pendingRewards.length).toBe(1);
      expect(tokenEconomyManager.pendingRewards[0].amount).toBe(100);
      expect(tokenEconomyManager.pendingRewards[0].reason).toBe('test_reward');
      expect(tokenEconomyManager.pendingRewards[0].displayed).toBe(false);
    });

    test('should get only undisplayed pending rewards', () => {
      tokenEconomyManager.addPendingReward(100, 'reward1');
      tokenEconomyManager.addPendingReward(200, 'reward2');
      
      // Mark one as displayed
      tokenEconomyManager.pendingRewards[0].displayed = true;
      
      const pending = tokenEconomyManager.getPendingRewards();
      expect(pending.length).toBe(1);
      expect(pending[0].reason).toBe('reward2');
    });

    test('should mark reward as displayed', () => {
      tokenEconomyManager.addPendingReward(100, 'test_reward');
      const rewardId = tokenEconomyManager.pendingRewards[0].id;
      
      tokenEconomyManager.markRewardDisplayed(rewardId);
      expect(tokenEconomyManager.pendingRewards[0].displayed).toBe(true);
    });

    test('should clean up old displayed rewards', () => {
      // Add an old reward
      const oldReward = {
        id: 'old',
        amount: 100,
        reason: 'old',
        timestamp: Date.now() - 15000, // 15 seconds ago
        displayed: true
      };
      
      // Add a recent reward
      tokenEconomyManager.addPendingReward(100, 'new');
      
      // Manually add the old reward
      tokenEconomyManager.pendingRewards.push(oldReward);
      
      // Mark the new reward as displayed (this should trigger cleanup)
      const newRewardId = tokenEconomyManager.pendingRewards.find(r => r.reason === 'new').id;
      tokenEconomyManager.markRewardDisplayed(newRewardId);
      
      // Old reward should be removed
      expect(tokenEconomyManager.pendingRewards.find(r => r.id === 'old')).toBeUndefined();
      // New reward should still be there
      expect(tokenEconomyManager.pendingRewards.find(r => r.id === newRewardId)).toBeDefined();
    });
  });

  describe('reward animations', () => {
    test('should update reward animations', () => {
      // Add a pending reward
      tokenEconomyManager.addPendingReward(100, 'test_reward');
      
      // Update animations (should convert pending reward to animation)
      tokenEconomyManager.updateRewardAnimations(1000);
      
      expect(tokenEconomyManager.rewardAnimations.length).toBe(1);
      expect(tokenEconomyManager.rewardAnimations[0].amount).toBe(100);
      expect(tokenEconomyManager.rewardAnimations[0].reason).toBe('test_reward');
    });

    test('should remove completed animations', () => {
      // Add an animation that's already completed
      tokenEconomyManager.rewardAnimations.push({
        id: 'test',
        amount: 100,
        reason: 'test',
        elapsed: 4000,
        duration: 3000
      });
      
      tokenEconomyManager.updateRewardAnimations(1000);
      
      expect(tokenEconomyManager.rewardAnimations.length).toBe(0);
    });
  });

  describe('warp transaction methods', () => {
    beforeEach(() => {
      tokenEconomyManager.awardTokens(500, 'initial_balance');
    });

    describe('calculateBaseCost', () => {
      test('should calculate correct base cost for different warp types', () => {
        const basicCost = tokenEconomyManager.calculateBaseCost('basic', 1);
        const advancedCost = tokenEconomyManager.calculateBaseCost('advanced', 1);
        const ultimateCost = tokenEconomyManager.calculateBaseCost('ultimate', 1);
        
        expect(ultimateCost).toBeGreaterThan(advancedCost);
        expect(advancedCost).toBeGreaterThan(basicCost);
      });

      test('should scale cost with level', () => {
        const level1Cost = tokenEconomyManager.calculateBaseCost('basic', 1);
        const level5Cost = tokenEconomyManager.calculateBaseCost('basic', 5);
        
        expect(level5Cost).toBeGreaterThan(level1Cost);
      });
    });

    describe('applySessionDiscount', () => {
      test('should apply discount based on warp count', () => {
        const baseCost = 100;
        
        const noDiscount = tokenEconomyManager.applySessionDiscount(baseCost, 0);
        const smallDiscount = tokenEconomyManager.applySessionDiscount(baseCost, 3);
        const largeDiscount = tokenEconomyManager.applySessionDiscount(baseCost, 10);
        
        expect(noDiscount).toBe(100);
        expect(smallDiscount).toBeLessThan(100);
        expect(largeDiscount).toBeLessThan(smallDiscount);
      });
    });

    describe('getFinalCost', () => {
      test('should calculate final cost correctly', () => {
        const playerStats = {
          accuracy: 0.8,
          score: 10000,
          enemiesDefeated: 50,
          totalWarps: 5,
          successRate: 0.8
        };
        const result = tokenEconomyManager.getFinalCost('basic', 1, 3, playerStats);

        expect(result).toHaveProperty('cost');
        expect(result).toHaveProperty('details');
        expect(result.cost).toBeGreaterThan(0);
        expect(Number.isFinite(result.cost)).toBe(true);
      });
    });

    describe('validateTokenBalance', () => {
      test('should validate sufficient balance', () => {
        tokenEconomyManager.playerBalance = 500; // Set sufficient balance
        const result = tokenEconomyManager.validateTokenBalance(100);
        expect(result.isValid).toBe(true);
        expect(result.reason).toBe('valid');
      });

      test('should reject insufficient balance', () => {
        tokenEconomyManager.playerBalance = 500; // Set balance
        const result = tokenEconomyManager.validateTokenBalance(1000);
        expect(result.isValid).toBe(false);
        expect(result.reason).toBe('insufficient_balance');
        expect(result.required).toBe(1000);
        expect(result.available).toBe(500);
      });
    });

    describe('processWarpTransaction', () => {
      test('should process warp transaction successfully', () => {
        const result = tokenEconomyManager.processWarpTransaction('basic', 100);
        
        expect(result.success).toBe(true);
        expect(result.transaction).toBeDefined();
        expect(tokenEconomyManager.playerBalance).toBe(400); // 500 - 100
      });

      test('should fail if validation fails', () => {
        const result = tokenEconomyManager.processWarpTransaction('basic', 1000);
        
        expect(result.success).toBe(false);
        expect(result.reason).toBe('insufficient_balance');
        expect(tokenEconomyManager.playerBalance).toBe(500); // Unchanged
      });
    });

    describe('refundWarpTransaction', () => {
      test('should refund warp transaction successfully', () => {
        // First spend some tokens
        tokenEconomyManager.spendTokens(100, 'warp_test');
        const balanceAfterSpend = tokenEconomyManager.playerBalance;
        
        // Refund the transaction
        const result = tokenEconomyManager.refundWarpTransaction('basic', 100);
        
        expect(result.success).toBe(true);
        expect(tokenEconomyManager.playerBalance).toBe(balanceAfterSpend + 100);
      });
    });
  });

  describe('reset', () => {
    beforeEach(() => {
      tokenEconomyManager.awardTokens(100, 'test');
      tokenEconomyManager.spendTokens(50, 'test');
    });

    test('should reset all properties to initial state', () => {
      tokenEconomyManager.reset();
      
      expect(tokenEconomyManager.playerBalance).toBe(0);
      expect(tokenEconomyManager.totalEarned).toBe(0);
      expect(tokenEconomyManager.totalSpent).toBe(0);
      expect(tokenEconomyManager.transactionHistory).toEqual([]);
      expect(tokenEconomyManager.pendingRewards).toEqual([]);
      expect(tokenEconomyManager.rewardAnimations).toEqual([]);
      
      // Performance metrics should be reset
      expect(tokenEconomyManager.performanceMetrics.levelsCompleted).toBe(0);
      expect(tokenEconomyManager.performanceMetrics.totalScore).toBe(0);
      expect(tokenEconomyManager.performanceMetrics.averageCompletionTime).toBe(0);
      expect(tokenEconomyManager.performanceMetrics.perfectCompletions).toBe(0);
      expect(tokenEconomyManager.performanceMetrics.speedBonuses).toBe(0);
      expect(tokenEconomyManager.performanceMetrics.accuracyBonuses).toBe(0);
    });
  });

  describe('debug mode', () => {
    test('should set debug mode correctly', () => {
      tokenEconomyManager.setDebugMode(true);
      expect(tokenEconomyManager.debugMode).toBe(true);
      
      tokenEconomyManager.setDebugMode(false);
      expect(tokenEconomyManager.debugMode).toBe(false);
    });
  });
});
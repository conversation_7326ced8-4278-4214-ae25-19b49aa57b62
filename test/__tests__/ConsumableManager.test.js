import { jest } from '@jest/globals';
import { ConsumableManager } from '../../src/managers/ConsumableManager.js';
import { EMPBlastConsumable, TimeDilationConsumable, ConsumableFactory } from '../../src/systems/Consumable.js';

// Mock TokenManager
class MockTokenManager {
    constructor(initialBalance = 10000) {
        this.balance = initialBalance;
        this.transactions = [];
    }

    getBalance() {
        return this.balance;
    }

    spendTokens(amount, reason, metadata = {}) {
        if (this.balance >= amount) {
            this.balance -= amount;
            const transaction = {
                id: `tx_${Date.now()}`,
                type: 'spent',
                amount: amount,
                reason: reason,
                timestamp: Date.now(),
                balanceAfter: this.balance,
                metadata: metadata
            };
            this.transactions.push(transaction);
            return { success: true, transaction: transaction };
        } else {
            return { success: false, reason: 'insufficient_balance' };
        }
    }

    awardTokens(amount, reason, metadata = {}) {
        this.balance += amount;
        const transaction = {
            id: `tx_${Date.now()}`,
            type: 'earned',
            amount: amount,
            reason: reason,
            timestamp: Date.now(),
            balanceAfter: this.balance,
            metadata: metadata
        };
        this.transactions.push(transaction);
        return { success: true, transaction: transaction };
    }
}

// Mock GameEngine
class MockGameEngine {
    constructor() {
        this.gameObjectManager = {
            findByTag: jest.fn(() => [])
        };
    }
}

describe('ConsumableManager', () => {
    let consumableManager;
    let mockTokenManager;
    let mockGameEngine;

    beforeEach(() => {
        mockTokenManager = new MockTokenManager(10000);
        mockGameEngine = new MockGameEngine();
        consumableManager = new ConsumableManager(mockTokenManager, mockGameEngine);
        
        // Clear localStorage
        localStorage.clear();
    });

    afterEach(() => {
        localStorage.clear();
    });

    describe('initialization', () => {
        test('should initialize with correct default state', () => {
            expect(consumableManager.getCurrentConsumable()).toBeNull();
            expect(consumableManager.hasConsumable()).toBe(false);
            expect(consumableManager.getAvailableConsumables()).toHaveLength(2);
        });

        test('should load available consumables', () => {
            const available = consumableManager.getAvailableConsumables();
            expect(available).toHaveLength(2);
            expect(available[0]).toBeInstanceOf(EMPBlastConsumable);
            expect(available[1]).toBeInstanceOf(TimeDilationConsumable);
        });
    });

    describe('addConsumableToInventory', () => {
        test('should successfully add EMP Blast consumable to inventory', () => {
            const result = consumableManager.addConsumableToInventory('EMP_BLAST');
            
            expect(result.success).toBe(true);
            expect(result.consumable).toBeInstanceOf(EMPBlastConsumable);
            expect(consumableManager.hasConsumable()).toBe(true);
        });

        test('should successfully add Time Dilation consumable to inventory', () => {
            const result = consumableManager.addConsumableToInventory('TIME_DILATION');
            
            expect(result.success).toBe(true);
            expect(result.consumable).toBeInstanceOf(TimeDilationConsumable);
            expect(consumableManager.hasConsumable()).toBe(true);
        });


        test('should fail when inventory is full', () => {
            // Add first consumable to inventory
            consumableManager.addConsumableToInventory('EMP_BLAST');
            
            // Try to add second consumable to inventory
            const result = consumableManager.addConsumableToInventory('TIME_DILATION');
            
            expect(result.success).toBe(false);
            expect(result.reason).toBe('inventory_full');
            expect(result.message).toContain('one consumable at a time');
        });

        test('should fail for invalid consumable type', () => {
            const result = consumableManager.addConsumableToInventory('INVALID_TYPE');
            
            expect(result.success).toBe(false);
            expect(result.reason).toBe('invalid_consumable');
        });
    });

    describe('activateConsumable', () => {
        beforeEach(() => {
            // Add a consumable to inventory for activation tests
            consumableManager.addConsumableToInventory('EMP_BLAST');
        });

        test('should successfully activate consumable', async () => {
            const result = await consumableManager.activateConsumable();
            
            expect(result.success).toBe(true);
            expect(result.consumable).toBeInstanceOf(EMPBlastConsumable);
            expect(result.consumable.isUsed).toBe(true);
            expect(consumableManager.hasConsumable()).toBe(false);
        });

        test('should fail when no consumable available', async () => {
            // Clear current consumable
            consumableManager.currentConsumable = null;
            
            const result = await consumableManager.activateConsumable();
            
            expect(result.success).toBe(false);
            expect(result.reason).toBe('no_consumable');
        });

        test('should fail when consumable is already used', async () => {
            // Mark consumable as used
            consumableManager.currentConsumable.isUsed = true;
            
            const result = await consumableManager.activateConsumable();
            
            expect(result.success).toBe(false);
            expect(result.reason).toBe('already_used');
        });

        test('should respect activation cooldown', async () => {
            // Activate first consumable
            await consumableManager.activateConsumable();
            
            // Add and try to activate another immediately
            consumableManager.addConsumableToInventory('TIME_DILATION');
            const result = await consumableManager.activateConsumable();
            
            expect(result.success).toBe(false);
            expect(result.reason).toBe('cooldown');
        });
    });

    describe('inventory persistence', () => {
        test('should save inventory to localStorage', () => {
            consumableManager.addConsumableToInventory('EMP_BLAST');
            
            const savedData = JSON.parse(localStorage.getItem('consumable_inventory'));
            expect(savedData).toBeDefined();
            expect(savedData.currentConsumable).toBeDefined();
            expect(savedData.currentConsumable.type).toBe('EMP_BLAST');
        });

        test('should load inventory from localStorage', () => {
            // Manually set localStorage data
            const inventoryData = {
                currentConsumable: {
                    type: 'TIME_DILATION',
                    isUsed: false,
                    usedAt: null
                }
            };
            localStorage.setItem('consumable_inventory', JSON.stringify(inventoryData));
            
            // Create new manager to test loading
            const newManager = new ConsumableManager(mockTokenManager, mockGameEngine);
            
            expect(newManager.hasConsumable()).toBe(true);
            expect(newManager.getCurrentConsumable()).toBeInstanceOf(TimeDilationConsumable);
        });

        test('should handle corrupted localStorage data gracefully', () => {
            localStorage.setItem('consumable_inventory', 'invalid json');
            
            const newManager = new ConsumableManager(mockTokenManager, mockGameEngine);
            
            expect(newManager.hasConsumable()).toBe(false);
            expect(newManager.getCurrentConsumable()).toBeNull();
        });
    });

    describe('reset', () => {
        test('should reset all consumable state', () => {
            consumableManager.addConsumableToInventory('EMP_BLAST');
            consumableManager.lastActivationTime = Date.now();
            
            consumableManager.reset();
            
            expect(consumableManager.hasConsumable()).toBe(false);
            expect(consumableManager.getCurrentConsumable()).toBeNull();
            expect(consumableManager.lastActivationTime).toBe(0);
            expect(consumableManager.isActivating).toBe(false);
        });
    });

    describe('callbacks', () => {
        test('should trigger inventory update callback', () => {
            const callback = jest.fn();
            consumableManager.setOnInventoryUpdate(callback);
            
            consumableManager.addConsumableToInventory('EMP_BLAST');
            
            expect(callback).toHaveBeenCalledWith(expect.any(EMPBlastConsumable));
        });

        test('should trigger consumable used callback', async () => {
            const callback = jest.fn();
            consumableManager.setOnConsumableUsed(callback);
            
            consumableManager.addConsumableToInventory('EMP_BLAST');
            await consumableManager.activateConsumable();
            
            expect(callback).toHaveBeenCalledWith(expect.any(EMPBlastConsumable));
        });
    });
});

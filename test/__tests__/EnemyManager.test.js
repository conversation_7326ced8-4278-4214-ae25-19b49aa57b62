import { EnemyManager } from '../../src/managers/EnemyManager.js';
import { Enemy } from '../../src/entities/Enemy.js';
import { Boss } from '../../src/entities/Boss.js';
import { Vector2 } from '../../src/utils/Vector2.js';
import { GAME_CONFIG, ENEMY_TYPES } from '../../src/config/gameConfig.js';

// Mock the Enemy and Boss classes
jest.mock('../../src/entities/Enemy.js');
jest.mock('../../src/entities/Boss.js');

describe('EnemyManager', () => {
  let enemyManager;
  let mockGameObjectManager;

  beforeEach(() => {
    // Reset mocks
    Enemy.mockClear();
    Boss.mockClear();
    
    // Create mock game object manager
    mockGameObjectManager = {
      add: jest.fn(),
      remove: jest.fn()
    };
    
    // Create enemy manager instance
    enemyManager = new EnemyManager(
      GAME_CONFIG.CANVAS_WIDTH,
      GAME_CONFIG.CANVAS_HEIGHT,
      mockGameObjectManager
    );
    
    // Clear console mocks
    jest.clearAllMocks();
  });

  describe('Constructor', () => {
    test('should initialize with default values', () => {
      expect(enemyManager.canvasWidth).toBe(GAME_CONFIG.CANVAS_WIDTH);
      expect(enemyManager.canvasHeight).toBe(GAME_CONFIG.CANVAS_HEIGHT);
      expect(enemyManager.gameObjectManager).toBe(mockGameObjectManager);
      expect(enemyManager.activeEnemies).toEqual([]);
      expect(enemyManager.enemyPool).toEqual([]);
      expect(enemyManager.maxEnemies).toBe(50);
      expect(enemyManager.activeBosses).toEqual([]);
      expect(enemyManager.bossPool).toEqual([]);
      expect(enemyManager.maxBosses).toBe(3);
      expect(enemyManager.currentBoss).toBeNull();
      expect(enemyManager.bossEncounterActive).toBe(false);
    });

    test('should initialize wave management properties', () => {
      expect(enemyManager.currentWave).toBe(0);
      expect(enemyManager.waveInProgress).toBe(false);
      expect(enemyManager.waveStartTime).toBe(0);
      expect(enemyManager.waveConfig).toBeNull();
      expect(enemyManager.enemiesSpawnedInWave).toBe(0);
      expect(enemyManager.enemiesKilledInWave).toBe(0);
      expect(enemyManager.enemiesEscapedInWave).toBe(0);
      expect(enemyManager.patternSpawnCounts).toEqual({});
    });

    test('should initialize boss encounter properties', () => {
      expect(enemyManager.bossEncounterCooldown).toBe(30000);
      expect(enemyManager.lastBossEncounterTime).toBe(0);
      expect(enemyManager.bossDefeatCallback).toBeNull();
      expect(enemyManager.bossWarpCallback).toBeNull();
    });

    test('should initialize spawn timing properties', () => {
      expect(enemyManager.lastSpawnTime).toBe(0);
      expect(enemyManager.spawnCooldown).toBe(1000);
      expect(enemyManager.spawnTimer).toBe(0);
    });

    test('should initialize formation management', () => {
      expect(enemyManager.formations).toEqual([]);
      expect(enemyManager.formationSpawnQueue).toEqual([]);
    });

    test('should initialize environmental effects', () => {
      expect(enemyManager.currentEnvironment).toBe('space');
      expect(enemyManager.environmentalEffects).toBeDefined();
      expect(typeof enemyManager.environmentalEffects).toBe('object');
    });

    test('should initialize statistics', () => {
      expect(enemyManager.totalEnemiesSpawned).toBe(0);
      expect(enemyManager.totalEnemiesKilled).toBe(0);
      expect(enemyManager.totalScore).toBe(0);
    });

    test('should initialize collision grid', () => {
      expect(enemyManager.collisionGrid).toBeNull();
      expect(enemyManager.gridSize).toBe(64);
      expect(enemyManager.gridWidth).toBe(Math.ceil(GAME_CONFIG.CANVAS_WIDTH / 64));
      expect(enemyManager.gridHeight).toBe(Math.ceil(GAME_CONFIG.CANVAS_HEIGHT / 64));
    });
  });

  describe('setBossWarpManager', () => {
    test('should set boss warp manager', () => {
      const mockBossWarpManager = {};
      enemyManager.setBossWarpManager(mockBossWarpManager);
      expect(enemyManager.bossWarpManager).toBe(mockBossWarpManager);
    });
  });

  describe('update', () => {
    test('should update spawn timer', () => {
      const deltaTime = 100;
      enemyManager.spawnTimer = 0;
      
      enemyManager.update(deltaTime);
      
      expect(enemyManager.spawnTimer).toBe(deltaTime);
    });

    test('should call update methods', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      // Mock the internal update methods
      enemyManager.updateWaveManagement = jest.fn();
      enemyManager.updateEnemySpawning = jest.fn();
      enemyManager.updateActiveEnemies = jest.fn();
      enemyManager.updateActiveBosses = jest.fn();
      enemyManager.cleanupDestroyedEnemies = jest.fn();
      enemyManager.cleanupDestroyedBosses = jest.fn();
      enemyManager.updateFormations = jest.fn();
      enemyManager.checkWaveCompletion = jest.fn();
      enemyManager.checkBossEncounter = jest.fn();
      
      enemyManager.update(deltaTime, mockPlayerPosition);
      
      expect(enemyManager.updateWaveManagement).toHaveBeenCalledWith(deltaTime);
      expect(enemyManager.updateEnemySpawning).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
      expect(enemyManager.updateActiveEnemies).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
      expect(enemyManager.updateActiveBosses).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
      expect(enemyManager.cleanupDestroyedEnemies).toHaveBeenCalled();
      expect(enemyManager.cleanupDestroyedBosses).toHaveBeenCalled();
      expect(enemyManager.updateFormations).toHaveBeenCalledWith(deltaTime);
      expect(enemyManager.checkWaveCompletion).toHaveBeenCalled();
      expect(enemyManager.checkBossEncounter).toHaveBeenCalled();
    });
  });

  describe('updateWaveManagement', () => {
    test('should start next wave when no wave in progress and no active enemies', () => {
      enemyManager.startNextWave = jest.fn();
      
      enemyManager.updateWaveManagement(100);
      
      expect(enemyManager.startNextWave).toHaveBeenCalled();
    });

    test('should not start next wave when wave is in progress', () => {
      enemyManager.waveInProgress = true;
      enemyManager.startNextWave = jest.fn();
      
      enemyManager.updateWaveManagement(100);
      
      expect(enemyManager.startNextWave).not.toHaveBeenCalled();
    });

    test('should not start next wave when there are active enemies', () => {
      enemyManager.activeEnemies.push({});
      enemyManager.startNextWave = jest.fn();
      
      enemyManager.updateWaveManagement(100);
      
      expect(enemyManager.startNextWave).not.toHaveBeenCalled();
    });

    test('should update wave start time when wave is in progress', () => {
      enemyManager.waveInProgress = true;
      enemyManager.waveStartTime = 0;
      
      enemyManager.updateWaveManagement(100);
      
      expect(enemyManager.waveStartTime).toBe(100);
    });
  });

  describe('updateEnemySpawning', () => {
    beforeEach(() => {
      enemyManager.waveInProgress = true;
      enemyManager.waveConfig = {
        totalEnemies: 10
      };
      enemyManager.spawnTimer = 1500; // Above default cooldown
    });

    test('should not spawn when wave is not in progress', () => {
      enemyManager.waveInProgress = false;
      enemyManager.spawnEnemyFromWave = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.spawnEnemyFromWave).not.toHaveBeenCalled();
    });

    test('should not spawn when wave config is not set', () => {
      enemyManager.waveConfig = null;
      enemyManager.spawnEnemyFromWave = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.spawnEnemyFromWave).not.toHaveBeenCalled();
    });

    test('should not spawn when spawn timer is below cooldown', () => {
      enemyManager.spawnTimer = 500; // Below default cooldown
      enemyManager.spawnEnemyFromWave = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.spawnEnemyFromWave).not.toHaveBeenCalled();
    });

    test('should not spawn when max enemies reached', () => {
      enemyManager.activeEnemies = new Array(50).fill({}); // Max enemies
      enemyManager.spawnEnemyFromWave = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.spawnEnemyFromWave).not.toHaveBeenCalled();
    });

    test('should not spawn when all enemies spawned', () => {
      enemyManager.enemiesSpawnedInWave = 10; // Same as totalEnemies
      enemyManager.spawnEnemyFromWave = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.spawnEnemyFromWave).not.toHaveBeenCalled();
    });

    test('should spawn enemy when conditions are met', () => {
      enemyManager.spawnEnemyFromWave = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.spawnEnemyFromWave).toHaveBeenCalled();
      expect(enemyManager.spawnTimer).toBe(0);
    });

    test('should process formation spawn queue', () => {
      enemyManager.processFormationSpawnQueue = jest.fn();
      
      enemyManager.updateEnemySpawning(100);
      
      expect(enemyManager.processFormationSpawnQueue).toHaveBeenCalledWith(100);
    });
  });

  describe('updateActiveEnemies', () => {
    test('should update all active enemies', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      const mockEnemy1 = { active: true, update: jest.fn() };
      const mockEnemy2 = { active: true, update: jest.fn() };
      
      enemyManager.activeEnemies = [mockEnemy1, mockEnemy2];
      
      enemyManager.updateActiveEnemies(deltaTime, mockPlayerPosition);
      
      expect(mockEnemy1.update).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
      expect(mockEnemy2.update).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
    });

    test('should apply environmental effects to enemies', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      const mockEnemy = { active: true, update: jest.fn() };
      enemyManager.activeEnemies = [mockEnemy];
      
      enemyManager.applyEnvironmentalEffects = jest.fn();
      
      enemyManager.updateActiveEnemies(deltaTime, mockPlayerPosition);
      
      expect(enemyManager.applyEnvironmentalEffects).toHaveBeenCalledWith(mockEnemy);
    });

    test('should not update inactive enemies', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      const mockEnemy = { active: false, update: jest.fn() };
      enemyManager.activeEnemies = [mockEnemy];
      
      enemyManager.updateActiveEnemies(deltaTime, mockPlayerPosition);
      
      expect(mockEnemy.update).not.toHaveBeenCalled();
    });
  });

  describe('updateActiveBosses', () => {
    test('should update all active bosses', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      const mockBoss1 = { active: true, update: jest.fn() };
      const mockBoss2 = { active: true, update: jest.fn() };
      
      enemyManager.activeBosses = [mockBoss1, mockBoss2];
      
      enemyManager.updateActiveBosses(deltaTime, mockPlayerPosition);
      
      expect(mockBoss1.update).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
      expect(mockBoss2.update).toHaveBeenCalledWith(deltaTime, mockPlayerPosition);
    });

    test('should apply environmental effects to bosses', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      const mockBoss = { active: true, update: jest.fn() };
      enemyManager.activeBosses = [mockBoss];
      
      enemyManager.applyEnvironmentalEffects = jest.fn();
      
      enemyManager.updateActiveBosses(deltaTime, mockPlayerPosition);
      
      expect(enemyManager.applyEnvironmentalEffects).toHaveBeenCalledWith(mockBoss);
    });

    test('should check boss warp triggers', () => {
      const deltaTime = 100;
      const mockPlayerPosition = new Vector2(100, 100);
      
      const mockBoss = { active: true, update: jest.fn() };
      enemyManager.activeBosses = [mockBoss];
      
      enemyManager.checkBossWarpTriggers = jest.fn();
      
      enemyManager.updateActiveBosses(deltaTime, mockPlayerPosition);
      
      expect(enemyManager.checkBossWarpTriggers).toHaveBeenCalledWith(mockBoss);
    });
  });

  describe('cleanupDestroyedEnemies', () => {
    test('should remove destroyed enemies', () => {
      const mockEnemy1 = { 
        destroyed: true, 
        isDestroyed: true,
        scoreValue: 10,
        id: 'enemy1'
      };
      const mockEnemy2 = { 
        destroyed: false, 
        active: true,
        id: 'enemy2'
      };
      
      enemyManager.activeEnemies = [mockEnemy1, mockEnemy2];
      enemyManager.returnEnemyToPool = jest.fn();
      
      enemyManager.cleanupDestroyedEnemies();
      
      expect(enemyManager.activeEnemies).toEqual([mockEnemy2]);
      expect(enemyManager.enemiesKilledInWave).toBe(1);
      expect(enemyManager.totalEnemiesKilled).toBe(1);
      expect(enemyManager.totalScore).toBe(10);
      expect(enemyManager.returnEnemyToPool).toHaveBeenCalledWith(mockEnemy1);
    });

    test('should handle escaped enemies', () => {
      const mockEnemy = { 
        destroyed: true, 
        isDestroyed: false,
        id: 'enemy1'
      };
      
      enemyManager.activeEnemies = [mockEnemy];
      enemyManager.returnEnemyToPool = jest.fn();
      enemyManager.onEnemyEscaped = jest.fn();
      
      enemyManager.cleanupDestroyedEnemies();
      
      expect(enemyManager.enemiesEscapedInWave).toBe(1);
      expect(enemyManager.onEnemyEscaped).toHaveBeenCalledWith(mockEnemy);
      expect(enemyManager.returnEnemyToPool).toHaveBeenCalledWith(mockEnemy);
    });
  });

  describe('cleanupDestroyedBosses', () => {
    test('should remove destroyed bosses', () => {
      const mockBoss = { 
        destroyed: true, 
        isDestroyed: true,
        bossName: 'Test Boss',
        scoreValue: 100,
        id: 'boss1'
      };
      
      enemyManager.activeBosses = [mockBoss];
      enemyManager.currentBoss = mockBoss;
      enemyManager.returnBossToPool = jest.fn();
      enemyManager.endBossEncounter = jest.fn();
      
      enemyManager.cleanupDestroyedBosses();
      
      expect(enemyManager.activeEnemies).toEqual([]);
      expect(enemyManager.currentBoss).toBeNull();
      expect(enemyManager.totalEnemiesKilled).toBe(1);
      expect(enemyManager.totalScore).toBe(100);
      expect(enemyManager.returnBossToPool).toHaveBeenCalledWith(mockBoss);
    });

    test('should call boss defeat callback', () => {
      const mockBoss = { 
        destroyed: true, 
        isDestroyed: true,
        bossName: 'Test Boss',
        scoreValue: 100,
        id: 'boss1'
      };
      
      enemyManager.activeBosses = [mockBoss];
      enemyManager.currentBoss = mockBoss;
      enemyManager.bossDefeatCallback = jest.fn();
      enemyManager.returnBossToPool = jest.fn();
      enemyManager.endBossEncounter = jest.fn();
      
      enemyManager.cleanupDestroyedBosses();
      
      expect(enemyManager.bossDefeatCallback).toHaveBeenCalledWith(mockBoss);
    });
  });

  describe('startNextWave', () => {
    test('should increment wave number and set wave in progress', () => {
      enemyManager.currentWave = 0;
      enemyManager.generateWaveConfig = jest.fn().mockReturnValue({
        totalEnemies: 10,
        spawnPatterns: []
      });
      
      enemyManager.startNextWave();
      
      expect(enemyManager.currentWave).toBe(1);
      expect(enemyManager.waveInProgress).toBe(true);
      expect(enemyManager.waveStartTime).toBe(0);
    });

    test('should reset wave statistics', () => {
      enemyManager.enemiesSpawnedInWave = 5;
      enemyManager.enemiesKilledInWave = 3;
      enemyManager.enemiesEscapedInWave = 2;
      enemyManager.patternSpawnCounts = { 0: 2, 1: 3 };
      
      enemyManager.generateWaveConfig = jest.fn().mockReturnValue({
        totalEnemies: 10,
        spawnPatterns: []
      });
      
      enemyManager.startNextWave();
      
      expect(enemyManager.enemiesSpawnedInWave).toBe(0);
      expect(enemyManager.enemiesKilledInWave).toBe(0);
      expect(enemyManager.enemiesEscapedInWave).toBe(0);
      expect(enemyManager.patternSpawnCounts).toEqual({});
    });

    test('should generate wave configuration', () => {
      enemyManager.generateWaveConfig = jest.fn().mockReturnValue({
        totalEnemies: 10,
        spawnPatterns: []
      });
      
      enemyManager.startNextWave();
      
      expect(enemyManager.generateWaveConfig).toHaveBeenCalledWith(1);
      expect(enemyManager.waveConfig).toEqual({
        totalEnemies: 10,
        spawnPatterns: []
      });
    });

    test('should initialize pattern spawn counts', () => {
      enemyManager.generateWaveConfig = jest.fn().mockReturnValue({
        totalEnemies: 10,
        spawnPatterns: [{}, {}]
      });
      
      enemyManager.startNextWave();
      
      expect(enemyManager.patternSpawnCounts).toEqual({ 0: 0, 1: 0 });
    });

    test('should set spawn cooldown based on wave difficulty', () => {
      enemyManager.generateWaveConfig = jest.fn().mockReturnValue({
        totalEnemies: 10,
        spawnPatterns: []
      });
      
      // Test wave 1
      enemyManager.currentWave = 1;
      enemyManager.startNextWave();
      expect(enemyManager.spawnCooldown).toBe(1450); // 1500 - (1 * 50)
      
      // Test wave 10
      enemyManager.currentWave = 10;
      enemyManager.startNextWave();
      expect(enemyManager.spawnCooldown).toBe(1000); // 1500 - (10 * 50), capped at 200
    });
  });

  describe('generateWaveConfig', () => {
    test('should generate correct configuration for wave 1', () => {
      const config = enemyManager.generateWaveConfig(1);
      
      expect(config.waveNumber).toBe(1);
      expect(config.difficulty).toBe(1);
      expect(config.totalEnemies).toBe(5);
      expect(config.totalWaves).toBe(3);
      expect(config.hasBoss).toBe(false);
      expect(config.hasSpecialMechanics).toBe(false);
      expect(config.completionReward).toBeGreaterThan(0);
      expect(config.difficultyMultiplier).toBe(1.0);
    });

    test('should increase difficulty with wave number', () => {
      const config1 = enemyManager.generateWaveConfig(1);
      const config5 = enemyManager.generateWaveConfig(5);
      const config10 = enemyManager.generateWaveConfig(10);
      
      expect(config5.difficulty).toBeGreaterThan(config1.difficulty);
      expect(config10.difficulty).toBeGreaterThan(config5.difficulty);
      expect(config10.totalEnemies).toBeGreaterThan(config5.totalEnemies);
      expect(config5.totalEnemies).toBeGreaterThan(config1.totalEnemies);
    });

    test('should have boss every 10th level', () => {
      const config9 = enemyManager.generateWaveConfig(9);
      const config10 = enemyManager.generateWaveConfig(10);
      const config11 = enemyManager.generateWaveConfig(11);
      
      expect(config9.hasBoss).toBe(false);
      expect(config10.hasBoss).toBe(true);
      expect(config11.hasBoss).toBe(false);
    });

    test('should have special mechanics every 5th level', () => {
      const config4 = enemyManager.generateWaveConfig(4);
      const config5 = enemyManager.generateWaveConfig(5);
      const config6 = enemyManager.generateWaveConfig(6);
      
      expect(config4.hasSpecialMechanics).toBe(false);
      expect(config5.hasSpecialMechanics).toBe(true);
      expect(config6.hasSpecialMechanics).toBe(false);
    });

    test('should include enemy distribution', () => {
      const config = enemyManager.generateWaveConfig(1);
      
      expect(config.enemyDistribution).toBeDefined();
      expect(typeof config.enemyDistribution).toBe('object');
    });

    test('should include level conditions', () => {
      const config = enemyManager.generateWaveConfig(1);
      
      expect(config.conditions).toBeDefined();
      expect(config.conditions.primary).toBe('defeat_all_enemies');
      expect(Array.isArray(config.conditions.secondary)).toBe(true);
      expect(Array.isArray(config.conditions.bonus)).toBe(true);
    });

    test('should include visual and audio settings', () => {
      const config = enemyManager.generateWaveConfig(1);
      
      expect(config.backgroundMusic).toBeDefined();
      expect(typeof config.backgroundMusic).toBe('string');
      expect(Array.isArray(config.visualEffects)).toBe(true);
    });
  });

  describe('getWaveEnemyTypes', () => {
    test('should return air and water enemies for early waves', () => {
      const types = enemyManager.getWaveEnemyTypes(1);
      
      expect(types).toHaveLength(2);
      expect(types[0].type).toBe(ENEMY_TYPES.AIR);
      expect(types[0].weight).toBe(0.8);
      expect(types[1].type).toBe(ENEMY_TYPES.WATER);
      expect(types[1].weight).toBe(0.2);
    });

    test('should return more variety for mid waves', () => {
      const types = enemyManager.getWaveEnemyTypes(5);
      
      expect(types).toHaveLength(4);
      expect(types.some(t => t.type === ENEMY_TYPES.AIR)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.WATER)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.FIRE)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.EARTH)).toBe(true);
    });

    test('should return all types for later waves', () => {
      const types = enemyManager.getWaveEnemyTypes(10);
      
      expect(types).toHaveLength(6);
      expect(types.some(t => t.type === ENEMY_TYPES.AIR)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.WATER)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.FIRE)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.EARTH)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.CRYSTAL)).toBe(true);
      expect(types.some(t => t.type === ENEMY_TYPES.SHADOW)).toBe(true);
    });
  });

  describe('getPredefinedWavePattern', () => {
    test('should return correct pattern for wave 1', () => {
      const pattern = enemyManager.getPredefinedWavePattern(1, 5);
      
      expect(pattern).toHaveLength(1);
      expect(pattern[0].type).toBe('formation');
      expect(pattern[0].formation).toBe('line');
      expect(pattern[0].movementPattern).toBe('predefined');
      expect(pattern[0].predefinedPattern).toBe('straight_down');
    });

    test('should return correct pattern for wave 2', () => {
      const pattern = enemyManager.getPredefinedWavePattern(2, 6);
      
      expect(pattern).toHaveLength(1);
      expect(pattern[0].type).toBe('formation');
      expect(pattern[0].formation).toBe('v-formation');
      expect(pattern[0].movementPattern).toBe('predefined');
      expect(pattern[0].predefinedPattern).toBe('triangle_left_right');
    });

    test('should return multiple patterns for wave 3', () => {
      const pattern = enemyManager.getPredefinedWavePattern(3, 10);
      
      expect(pattern).toHaveLength(2);
      expect(pattern[0].type).toBe('formation');
      expect(pattern[0].formation).toBe('triangle');
      expect(pattern[1].type).toBe('scattered');
    });

    test('should cycle through complex patterns for waves 6+', () => {
      const pattern6 = enemyManager.getPredefinedWavePattern(6, 10);
      const pattern10 = enemyManager.getPredefinedWavePattern(10, 10);
      const pattern14 = enemyManager.getPredefinedWavePattern(14, 10);
      
      expect(pattern6).toHaveLength(2);
      expect(pattern10).toHaveLength(1);
      expect(pattern14).toHaveLength(3);
    });
  });

  describe('spawnEnemyFromWave', () => {
    beforeEach(() => {
      enemyManager.waveConfig = {
        enemyTypes: [
          { type: ENEMY_TYPES.AIR, weight: 0.7 },
          { type: ENEMY_TYPES.WATER, weight: 0.3 }
        ],
        spawnPatterns: [
          {
            type: 'formation',
            count: 5,
            formation: 'line',
            movementPattern: 'predefined',
            predefinedPattern: 'straight_down'
          }
        ]
      };
      
      enemyManager.spawnEnemy = jest.fn().mockReturnValue({});
      enemyManager.selectEnemyType = jest.fn().mockReturnValue(ENEMY_TYPES.AIR);
      enemyManager.selectSpawnPattern = jest.fn().mockReturnValue(enemyManager.waveConfig.spawnPatterns[0]);
      enemyManager.generateSpawnPosition = jest.fn().mockReturnValue({ x: 100, y: 50 });
    });

    test('should spawn enemy with correct type and position', () => {
      enemyManager.spawnEnemyFromWave();
      
      expect(enemyManager.selectEnemyType).toHaveBeenCalledWith(enemyManager.waveConfig.enemyTypes);
      expect(enemyManager.selectSpawnPattern).toHaveBeenCalledWith(enemyManager.waveConfig.spawnPatterns);
      expect(enemyManager.generateSpawnPosition).toHaveBeenCalledWith(enemyManager.waveConfig.spawnPatterns[0]);
      expect(enemyManager.spawnEnemy).toHaveBeenCalledWith(100, 50, ENEMY_TYPES.AIR);
    });

    test('should increment enemies spawned in wave', () => {
      enemyManager.enemiesSpawnedInWave = 0;
      enemyManager.spawnEnemyFromWave();
      
      expect(enemyManager.enemiesSpawnedInWave).toBe(1);
    });

    test('should update pattern spawn counts', () => {
      enemyManager.patternSpawnCounts = { 0: 0 };
      enemyManager.spawnEnemyFromWave();
      
      expect(enemyManager.patternSpawnCounts[0]).toBe(1);
    });
  });

  describe('spawnBossFromWave', () => {
    beforeEach(() => {
      enemyManager.spawnBoss = jest.fn().mockReturnValue({});
    });

    test('should spawn boss at center of screen', () => {
      enemyManager.spawnBossFromWave();
      
      expect(enemyManager.spawnBoss).toHaveBeenCalledWith(
        GAME_CONFIG.CANVAS_WIDTH / 2,
        100,
        ENEMY_TYPES.SHADOW
      );
    });

    test('should set current boss and start boss encounter', () => {
      const mockBoss = { id: 'boss1' };
      enemyManager.spawnBoss = jest.fn().mockReturnValue(mockBoss);
      
      enemyManager.spawnBossFromWave();
      
      expect(enemyManager.currentBoss).toBe(mockBoss);
      expect(enemyManager.bossEncounterActive).toBe(true);
      expect(enemyManager.lastBossEncounterTime).toBeGreaterThan(0);
    });
  });

  describe('selectEnemyType', () => {
    test('should select enemy type based on weights', () => {
      const enemyTypes = [
        { type: ENEMY_TYPES.AIR, weight: 0.7 },
        { type: ENEMY_TYPES.WATER, weight: 0.3 }
      ];
      
      // Mock Math.random to return a value that should select AIR
      const originalRandom = Math.random;
      Math.random = jest.fn().mockReturnValue(0.3);
      
      const selectedType = enemyManager.selectEnemyType(enemyTypes);
      
      expect(selectedType).toBe(ENEMY_TYPES.AIR);
      
      // Restore original Math.random
      Math.random = originalRandom;
    });
  });

  describe('selectSpawnPattern', () => {
    test('should select pattern with available slots', () => {
      const patterns = [
        { type: 'formation', count: 3 },
        { type: 'scattered', count: 2 }
      ];
      
      enemyManager.patternSpawnCounts = { 0: 2, 1: 0 };
      
      const selectedPattern = enemyManager.selectSpawnPattern(patterns);
      
      expect(selectedPattern).toBe(patterns[0]); // First pattern has available slot
    });

    test('should return null if no patterns have available slots', () => {
      const patterns = [
        { type: 'formation', count: 3 },
        { type: 'scattered', count: 2 }
      ];
      
      enemyManager.patternSpawnCounts = { 0: 3, 1: 2 };
      
      const selectedPattern = enemyManager.selectSpawnPattern(patterns);
      
      expect(selectedPattern).toBeNull();
    });
  });

  describe('generateSpawnPosition', () => {
    test('should generate position for line formation', () => {
      const pattern = {
        formation: 'line',
        predefinedPattern: 'straight_down'
      };
      
      const position = enemyManager.generateSpawnPosition(pattern);
      
      expect(position).toHaveProperty('x');
      expect(position).toHaveProperty('y');
      expect(position.y).toBeLessThan(0); // Above screen
    });

    test('should generate position for scattered formation', () => {
      const pattern = {
        type: 'scattered'
      };
      
      const position = enemyManager.generateSpawnPosition(pattern);
      
      expect(position).toHaveProperty('x');
      expect(position).toHaveProperty('y');
      expect(position.x).toBeGreaterThanOrEqual(0);
      expect(position.x).toBeLessThanOrEqual(GAME_CONFIG.CANVAS_WIDTH);
    });
  });

  describe('spawnEnemy', () => {
    test('should create new enemy if pool is empty', () => {
      const mockEnemy = { id: 'enemy1' };
      Enemy.mockReturnValue(mockEnemy);
      
      const result = enemyManager.spawnEnemy(100, 50, ENEMY_TYPES.AIR);
      
      expect(Enemy).toHaveBeenCalledWith(100, 50, ENEMY_TYPES.AIR);
      expect(enemyManager.activeEnemies).toContain(mockEnemy);
      expect(result).toBe(mockEnemy);
    });

    test('should reuse enemy from pool if available', () => {
      const mockEnemy = { id: 'enemy1', reset: jest.fn() };
      enemyManager.enemyPool = [mockEnemy];
      
      const result = enemyManager.spawnEnemy(100, 50, ENEMY_TYPES.AIR);
      
      expect(mockEnemy.reset).toHaveBeenCalled();
      expect(enemyManager.activeEnemies).toContain(mockEnemy);
      expect(enemyManager.enemyPool).not.toContain(mockEnemy);
      expect(result).toBe(mockEnemy);
    });
  });

  describe('spawnBoss', () => {
    test('should create new boss if pool is empty', () => {
      const mockBoss = { id: 'boss1' };
      Boss.mockReturnValue(mockBoss);
      
      const result = enemyManager.spawnBoss(100, 50, ENEMY_TYPES.SHADOW);
      
      expect(Boss).toHaveBeenCalledWith(100, 50, ENEMY_TYPES.SHADOW);
      expect(enemyManager.activeBosses).toContain(mockBoss);
      expect(result).toBe(mockBoss);
    });

    test('should reuse boss from pool if available', () => {
      const mockBoss = { id: 'boss1', reset: jest.fn() };
      enemyManager.bossPool = [mockBoss];
      
      const result = enemyManager.spawnBoss(100, 50, ENEMY_TYPES.SHADOW);
      
      expect(mockBoss.reset).toHaveBeenCalled();
      expect(enemyManager.activeBosses).toContain(mockBoss);
      expect(enemyManager.bossPool).not.toContain(mockBoss);
      expect(result).toBe(mockBoss);
    });
  });

  describe('returnEnemyToPool', () => {
    test('should return enemy to pool', () => {
      const mockEnemy = { id: 'enemy1' };
      
      enemyManager.returnEnemyToPool(mockEnemy);
      
      expect(enemyManager.enemyPool).toContain(mockEnemy);
    });

    test('should not exceed max pool size', () => {
      // Fill up the pool
      for (let i = 0; i < enemyManager.maxEnemies; i++) {
        enemyManager.enemyPool.push({ id: `enemy${i}` });
      }
      
      const mockEnemy = { id: 'new_enemy' };
      enemyManager.returnEnemyToPool(mockEnemy);
      
      expect(enemyManager.enemyPool.length).toBe(enemyManager.maxEnemies);
      expect(enemyManager.enemyPool).not.toContain(mockEnemy);
    });
  });

  describe('returnBossToPool', () => {
    test('should return boss to pool', () => {
      const mockBoss = { id: 'boss1' };
      
      enemyManager.returnBossToPool(mockBoss);
      
      expect(enemyManager.bossPool).toContain(mockBoss);
    });

    test('should not exceed max pool size', () => {
      // Fill up the pool
      for (let i = 0; i < enemyManager.maxBosses; i++) {
        enemyManager.bossPool.push({ id: `boss${i}` });
      }
      
      const mockBoss = { id: 'new_boss' };
      enemyManager.returnBossToPool(mockBoss);
      
      expect(enemyManager.bossPool.length).toBe(enemyManager.maxBosses);
      expect(enemyManager.bossPool).not.toContain(mockBoss);
    });
  });

  describe('checkWaveCompletion', () => {
    test('should complete wave when all enemies are defeated', () => {
      enemyManager.waveInProgress = true;
      enemyManager.waveConfig = { totalEnemies: 10 };
      enemyManager.enemiesKilledInWave = 10;
      enemyManager.wavesCompleted = 3;
      enemyManager.requiredWaves = 3;
      
      enemyManager.completeWave = jest.fn();
      
      enemyManager.checkWaveCompletion();
      
      expect(enemyManager.completeWave).toHaveBeenCalled();
    });

    test('should not complete wave when enemies remain', () => {
      enemyManager.waveInProgress = true;
      enemyManager.waveConfig = { totalEnemies: 10 };
      enemyManager.enemiesKilledInWave = 5;
      enemyManager.wavesCompleted = 3;
      enemyManager.requiredWaves = 3;
      
      enemyManager.completeWave = jest.fn();
      
      enemyManager.checkWaveCompletion();
      
      expect(enemyManager.completeWave).not.toHaveBeenCalled();
    });

    test('should not complete wave when waves remain', () => {
      enemyManager.waveInProgress = true;
      enemyManager.waveConfig = { totalEnemies: 10 };
      enemyManager.enemiesKilledInWave = 10;
      enemyManager.wavesCompleted = 2;
      enemyManager.requiredWaves = 3;
      
      enemyManager.completeWave = jest.fn();
      
      enemyManager.checkWaveCompletion();
      
      expect(enemyManager.completeWave).not.toHaveBeenCalled();
    });
  });

  describe('completeWave', () => {
    test('should end wave and calculate bonus', () => {
      enemyManager.waveInProgress = true;
      enemyManager.currentWave = 1;
      enemyManager.calculateWaveBonus = jest.fn().mockReturnValue(100);
      enemyManager.onWaveComplete = jest.fn();
      
      enemyManager.completeWave();
      
      expect(enemyManager.waveInProgress).toBe(false);
      expect(enemyManager.calculateWaveBonus).toHaveBeenCalled();
      expect(enemyManager.onWaveComplete).toHaveBeenCalledWith(1, 100);
    });
  });

  describe('calculateWaveBonus', () => {
    test('should calculate bonus based on wave number', () => {
      enemyManager.currentWave = 1;
      const bonus1 = enemyManager.calculateWaveBonus();
      
      enemyManager.currentWave = 5;
      const bonus5 = enemyManager.calculateWaveBonus();
      
      expect(bonus5).toBeGreaterThan(bonus1);
    });
  });

  describe('checkPlayerCollisions', () => {
    test('should check collisions between player and enemies', () => {
      const mockPlayer = { 
        position: { x: 100, y: 100 },
        collisionRadius: 20
      };
      
      const mockEnemy1 = { 
        position: { x: 110, y: 110 },
        collisionRadius: 20,
        active: true
      };
      
      const mockEnemy2 = { 
        position: { x: 200, y: 200 },
        collisionRadius: 20,
        active: true
      };
      
      enemyManager.activeEnemies = [mockEnemy1, mockEnemy2];
      enemyManager.handlePlayerEnemyCollision = jest.fn();
      
      const collisions = enemyManager.checkPlayerCollisions(mockPlayer);
      
      expect(collisions).toHaveLength(1);
      expect(enemyManager.handlePlayerEnemyCollision).toHaveBeenCalledWith(mockPlayer, mockEnemy1);
    });
  });

  describe('checkProjectileCollisions', () => {
    test('should check collisions between projectiles and enemies', () => {
      const mockProjectile1 = { 
        position: { x: 100, y: 100 },
        damage: 10,
        active: true
      };
      
      const mockProjectile2 = { 
        position: { x: 200, y: 200 },
        damage: 10,
        active: true
      };
      
      const mockEnemy = { 
        position: { x: 110, y: 110 },
        collisionRadius: 20,
        active: true,
        takeDamage: jest.fn()
      };
      
      enemyManager.activeEnemies = [mockEnemy];
      enemyManager.handleProjectileEnemyCollision = jest.fn();
      
      const collisions = enemyManager.checkProjectileCollisions([mockProjectile1, mockProjectile2]);
      
      expect(collisions).toHaveLength(1);
      expect(enemyManager.handleProjectileEnemyCollision).toHaveBeenCalledWith(mockProjectile1, mockEnemy, 10);
    });
  });

  describe('applyEnvironmentalEffects', () => {
    test('should apply environmental effects to enemy', () => {
      const mockEnemy = {
        type: ENEMY_TYPES.WATER,
        speed: 2,
        health: 100
      };
      
      enemyManager.currentEnvironment = 'underwater';
      
      enemyManager.applyEnvironmentalEffects(mockEnemy);
      
      // Enemy should be affected by environment
      expect(mockEnemy.speed).toBeDefined();
      expect(mockEnemy.health).toBeDefined();
    });
  });

  describe('setEnvironment', () => {
    test('should set current environment', () => {
      enemyManager.setEnvironment('underwater');
      
      expect(enemyManager.currentEnvironment).toBe('underwater');
    });
  });

  describe('getDefaultEnvironmentalEffects', () => {
    test('should return default environmental effects', () => {
      const effects = enemyManager.getDefaultEnvironmentalEffects();
      
      expect(effects).toHaveProperty('space');
      expect(effects).toHaveProperty('underwater');
      expect(effects).toHaveProperty('volcanic');
      expect(effects).toHaveProperty('crystal');
      expect(effects).toHaveProperty('forest');
      
      expect(effects.space).toHaveProperty('enemySpeedMultiplier');
      expect(effects.space).toHaveProperty('enemyHealthMultiplier');
      expect(effects.space).toHaveProperty('enemySpawnRateMultiplier');
      expect(effects.space).toHaveProperty('enemyTypeModifiers');
    });
  });

  describe('reset', () => {
    beforeEach(() => {
      // Set up some state
      enemyManager.currentWave = 5;
      enemyManager.waveInProgress = true;
      enemyManager.activeEnemies = [{ id: 'enemy1' }];
      enemyManager.activeBosses = [{ id: 'boss1' }];
      enemyManager.totalEnemiesSpawned = 10;
      enemyManager.totalEnemiesKilled = 8;
      enemyManager.totalScore = 100;
    });

    test('should reset all properties to initial state', () => {
      enemyManager.reset();
      
      expect(enemyManager.currentWave).toBe(0);
      expect(enemyManager.waveInProgress).toBe(false);
      expect(enemyManager.waveStartTime).toBe(0);
      expect(enemyManager.waveConfig).toBeNull();
      expect(enemyManager.activeEnemies).toEqual([]);
      expect(enemyManager.activeBosses).toEqual([]);
      expect(enemyManager.currentBoss).toBeNull();
      expect(enemyManager.bossEncounterActive).toBe(false);
      expect(enemyManager.enemiesSpawnedInWave).toBe(0);
      expect(enemyManager.enemiesKilledInWave).toBe(0);
      expect(enemyManager.enemiesEscapedInWave).toBe(0);
      expect(enemyManager.patternSpawnCounts).toEqual({});
      expect(enemyManager.totalEnemiesSpawned).toBe(0);
      expect(enemyManager.totalEnemiesKilled).toBe(0);
      expect(enemyManager.totalScore).toBe(0);
    });
  });

  describe('updateCanvasDimensions', () => {
    test('should update canvas dimensions and grid calculations', () => {
      const newWidth = 1024;
      const newHeight = 768;
      
      enemyManager.updateCanvasDimensions(newWidth, newHeight);
      
      expect(enemyManager.canvasWidth).toBe(newWidth);
      expect(enemyManager.canvasHeight).toBe(newHeight);
      expect(enemyManager.gridWidth).toBe(Math.ceil(newWidth / enemyManager.gridSize));
      expect(enemyManager.gridHeight).toBe(Math.ceil(newHeight / enemyManager.gridSize));
    });
  });

  describe('getStatistics', () => {
    beforeEach(() => {
      enemyManager.totalEnemiesSpawned = 20;
      enemyManager.totalEnemiesKilled = 15;
      enemyManager.totalScore = 1000;
      enemyManager.currentWave = 5;
      enemyManager.waveInProgress = true;
      enemyManager.waveConfig = { totalEnemies: 10 };
      enemyManager.enemiesSpawnedInWave = 8;
      enemyManager.enemiesKilledInWave = 6;
      enemyManager.enemiesEscapedInWave = 1;
    });

    test('should return correct statistics', () => {
      const stats = enemyManager.getStatistics();
      
      expect(stats.totalEnemiesSpawned).toBe(20);
      expect(stats.totalEnemiesKilled).toBe(15);
      expect(stats.totalScore).toBe(1000);
      expect(stats.killRate).toBe(0.75); // 15 / 20
      expect(stats.currentWave).toBe(5);
      expect(stats.waveInProgress).toBe(true);
      expect(stats.waveProgress).toEqual({
        spawned: 8,
        killed: 6,
        escaped: 1,
        total: 10,
        percentage: 70 // (6 + 1) / 10 * 100
      });
    });
  });

  describe('boss encounter methods', () => {
    beforeEach(() => {
      enemyManager.lastBossEncounterTime = Date.now() - 40000; // 40 seconds ago
    });

    describe('checkBossEncounter', () => {
      test('should start boss encounter when cooldown has passed', () => {
        enemyManager.startBossEncounter = jest.fn();
        
        enemyManager.checkBossEncounter();
        
        expect(enemyManager.startBossEncounter).toHaveBeenCalled();
      });

      test('should not start boss encounter when cooldown has not passed', () => {
        enemyManager.lastBossEncounterTime = Date.now() - 10000; // 10 seconds ago
        enemyManager.startBossEncounter = jest.fn();
        
        enemyManager.checkBossEncounter();
        
        expect(enemyManager.startBossEncounter).not.toHaveBeenCalled();
      });
    });

    describe('startBossEncounter', () => {
      test('should spawn boss and set encounter state', () => {
        enemyManager.spawnBossFromWave = jest.fn().mockReturnValue({ id: 'boss1' });
        
        enemyManager.startBossEncounter();
        
        expect(enemyManager.spawnBossFromWave).toHaveBeenCalled();
        expect(enemyManager.bossEncounterActive).toBe(true);
        expect(enemyManager.lastBossEncounterTime).toBeGreaterThan(0);
      });
    });

    describe('endBossEncounter', () => {
      test('should end boss encounter', () => {
        enemyManager.bossEncounterActive = true;
        
        enemyManager.endBossEncounter();
        
        expect(enemyManager.bossEncounterActive).toBe(false);
        expect(enemyManager.lastBossEncounterTime).toBeGreaterThan(0);
      });
    });
  });
});
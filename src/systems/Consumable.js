/**
 * Base Consumable class for single-use tactical items
 * Provides common functionality for consumable effects and activation
 */
export class Consumable {
    constructor(type, cost, description = '', cooldown = 0) {
        this.type = type;
        this.cost = cost;
        this.description = description;
        this.cooldown = cooldown; // Cooldown in milliseconds
        this.id = Consumable.generateId();
        
        // Visual properties
        this.icon = null;
        this.color = '#00ffff';
        this.glowColor = '#ffffff';
        this.name = type.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
        
        // State tracking
        this.isUsed = false;
        this.usedAt = null;
    }
    
    // Static ID generator
    static idCounter = 0;
    static generateId() {
        return `consumable_${++Consumable.idCounter}`;
    }
    
    /**
     * Activate the consumable effect
     * @param {GameEngine} gameEngine - The game engine instance
     * @returns {Promise<boolean>} True if successfully activated
     */
    async activate(gameEngine) {
        if (this.isUsed) {
            console.warn(`Consumable ${this.type} is already used`);
            return false;
        }
        
        this.isUsed = true;
        this.usedAt = Date.now();
        
        const result = await this.applyEffect(gameEngine);
        
        // If activation failed, reset the used state
        if (!result) {
            this.isUsed = false;
            this.usedAt = null;
        }
        
        return result;
    }
    
    /**
     * Apply the specific consumable effect (override in subclasses)
     * @param {GameEngine} gameEngine - The game engine instance
     * @returns {Promise<boolean>} True if successfully applied
     */
    async applyEffect(gameEngine) {
        // Override in subclasses
        return true;
    }
    
    /**
     * Check if this consumable can be purchased
     * @param {number} playerTokens - Current player token balance
     * @returns {object} Purchase availability info
     */
    canPurchase(playerTokens) {
        return {
            canPurchase: playerTokens >= this.cost,
            reason: playerTokens < this.cost ? 'insufficient_tokens' : 'available'
        };
    }
    
    /**
     * Get display information for UI
     * @returns {object} Display info
     */
    getDisplayInfo() {
        return {
            type: this.type,
            name: this.name,
            description: this.description,
            cost: this.cost,
            icon: this.icon,
            color: this.color,
            glowColor: this.glowColor,
            isUsed: this.isUsed
        };
    }
    
    /**
     * Create a copy of this consumable (for inventory management)
     * @returns {Consumable} New instance of this consumable
     */
    clone() {
        const ConsumableClass = this.constructor;
        if (ConsumableClass === Consumable) {
            // For base Consumable class, pass constructor parameters
            return new ConsumableClass(this.type, this.cost, this.description, this.cooldown);
        } else {
            // For subclasses, use default constructor
            return new ConsumableClass();
        }
    }
}

/**
 * EMP Blast consumable - Destroys all enemy projectiles and disables enemy fire
 */
export class EMPBlastConsumable extends Consumable {
    constructor() {
        super(
            'EMP_BLAST',
            1750,
            'Detonates an electromagnetic pulse, destroying all enemy projectiles and temporarily disabling enemy fire within the level.',
            0
        );
        
        this.icon = '⚡';
        this.color = '#00aaff';
        this.glowColor = '#88ccff';
        this.disableDuration = 5000; // 5 seconds in milliseconds
    }
    
    async applyEffect(gameEngine) {
        try {
            // Destroy all enemy projectiles
            const enemyProjectiles = gameEngine.gameObjectManager.findByTag('enemy_projectile');
            for (const projectile of enemyProjectiles) {
                if (projectile.active && !projectile.isDestroyed) {
                    projectile.destroy();
                }
            }
            
            // Disable enemy firing for 5 seconds
            const enemies = gameEngine.gameObjectManager.findByTag('enemy');
            for (const enemy of enemies) {
                if (enemy.active && !enemy.isDestroyed) {
                    // Disable enemy firing by setting a very long fire cooldown
                    enemy.fireCooldown = this.disableDuration;
                    enemy.empDisabled = true;
                    enemy.empDisableEndTime = Date.now() + this.disableDuration;
                }
            }
            
            // Visual effect (could be enhanced with particle system later)
            console.log('EMP Blast activated! All enemy projectiles destroyed and enemy fire disabled for 5 seconds.');
            
            return true;
        } catch (error) {
            console.error('Failed to apply EMP Blast effect:', error);
            return false;
        }
    }
}

/**
 * Time Dilation consumable - Freezes enemies for a duration
 */
export class TimeDilationConsumable extends Consumable {
    constructor() {
        super(
            'TIME_DILATION',
            1750,
            'Creates a time local distortion field allowing the player to fight faster than enemies can respond.',
            0
        );
        
        this.icon = '⏰';
        this.color = '#aa00ff';
        this.glowColor = '#cc88ff';
        this.freezeDuration = 5000; // 5 seconds in milliseconds
    }
    
    async applyEffect(gameEngine) {
        try {
            // Freeze all enemies for 5 seconds
            const enemies = gameEngine.gameObjectManager.findByTag('enemy');
            for (const enemy of enemies) {
                if (enemy.active && !enemy.isDestroyed) {
                    // Store original velocity and stop movement
                    if (!enemy.originalVelocity) {
                        enemy.originalVelocity = enemy.velocity.clone();
                    }
                    enemy.velocity.set(0, 0);

                    // Disable enemy AI/movement for duration
                    enemy.isFrozen = true;
                    enemy.freezeEndTime = Date.now() + this.freezeDuration;

                    // Disable enemy firing by setting a very long fire cooldown
                    enemy.fireCooldown = this.freezeDuration;
                    enemy.timeDilationDisabled = true;
                    enemy.timeDilationEndTime = Date.now() + this.freezeDuration;
                }
            }
            
            // Visual effect (could be enhanced with particle system later)
            console.log('Time Dilation activated! All enemies frozen for 5 seconds.');
            
            return true;
        } catch (error) {
            console.error('Failed to apply Time Dilation effect:', error);
            return false;
        }
    }
}

/**
 * Consumable factory for creating consumable instances
 */
export class ConsumableFactory {
    static createConsumable(type) {
        switch (type) {
            case 'EMP_BLAST':
                return new EMPBlastConsumable();
            case 'TIME_DILATION':
                return new TimeDilationConsumable();
            default:
                console.warn(`Unknown consumable type: ${type}`);
                return null;
        }
    }
    
    static createAllConsumables() {
        return [
            new EMPBlastConsumable(),
            new TimeDilationConsumable()
        ];
    }
    
    static getConsumableTypes() {
        return ['EMP_BLAST', 'TIME_DILATION'];
    }
}

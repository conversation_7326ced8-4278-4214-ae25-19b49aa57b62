/**
 * AudioManager - Central audio system for WarpSector
 * Handles sound effects, background music, ambient sounds, and volume control
 */
export class AudioManager {
    constructor() {
        // Audio context and configuration
        this.audioContext = null;
        this.masterVolume = 1.0;
        this.musicVolume = 0.7;
        this.sfxVolume = 0.8;
        this.ambientVolume = 0.3;
        
        // Audio storage and management
        this.audioBuffers = new Map();     // Pre-loaded audio files
        this.activeSounds = new Map();     // Currently playing sounds
        this.musicTracks = new Map();      // Background music tracks
        this.soundPool = new Map();        // Pooled sounds for performance
        
        // Feature flags
        this.enabled = true;
        this.musicEnabled = true;
        this.sfxEnabled = true;
        this.ambientEnabled = true;
        
        // Performance tracking
        this.activeSoundCount = 0;
        this.maxConcurrentSounds = 32;
        
        // Audio element fallback for older browsers
        this.audioElements = new Map();
        
        // Background music tracking for continuous playback
        this.backgroundMusicId = null;
        
        this.init();
    }
    
    async init() {
        try {
            // Initialize Web Audio API context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Set up audio processing chain immediately
            this.masterGain = this.audioContext.createGain();
            this.musicGain = this.audioContext.createGain();
            this.sfxGain = this.audioContext.createGain();
            this.ambientGain = this.audioContext.createGain();
            
            // Connect gain nodes to master
            this.musicGain.connect(this.masterGain);
            this.sfxGain.connect(this.masterGain);
            this.ambientGain.connect(this.masterGain);
            this.masterGain.connect(this.audioContext.destination);
            
            // Set initial volumes
            this.updateVolumeSettings();
            
            console.log('AudioManager initialized successfully');
            console.log('Audio context state:', this.audioContext.state);
            
            // Add event listener for audio context state changes
            this.audioContext.onstatechange = () => {
                console.log('Audio context state changed to:', this.audioContext.state);
            };
            
        } catch (error) {
            console.warn('Web Audio API not supported, falling back to HTML5 Audio:', error);
            this.audioContext = null;
        }
    }
    
    /**
     * Load and decode an audio file
     * @param {string} url - URL of the audio file
     * @param {string} name - Identifier for the audio file
     * @returns {Promise<boolean>} Success status
     */
    async loadAudioFile(url, name) {
        try {
            // If Web Audio API is available, use it for better performance
            if (this.audioContext) {
                const response = await fetch(url);
                const arrayBuffer = await response.arrayBuffer();
                const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
                this.audioBuffers.set(name, audioBuffer);
                return true;
            } else {
                // Fallback to HTML5 Audio
                const audio = new Audio();
                audio.preload = 'auto';
                audio.src = url;
                this.audioElements.set(name, audio);
                return true;
            }
        } catch (error) {
            console.error(`Failed to load audio file ${name}:`, error);
            return false;
        }
    }
    
    /**
     * Load multiple audio files
     * @param {Array<{url: string, name: string}>} audioFiles - Array of audio file definitions
     * @returns {Promise<void>}
     */
    async loadAudioFiles(audioFiles) {
        const loadPromises = audioFiles.map(file => this.loadAudioFile(file.url, file.name));
        const results = await Promise.allSettled(loadPromises);
        
        const loaded = results.filter(result => result.status === 'fulfilled' && result.value).length;
        console.log(`Loaded ${loaded}/${audioFiles.length} audio files`);
    }
    
    /**
     * Play a sound effect
     * @param {string} name - Sound identifier
     * @param {Object} options - Playback options
     * @param {number} options.volume - Volume (0-1)
     * @param {number} options.pitch - Pitch multiplier
     * @param {boolean} options.loop - Whether to loop the sound
     * @returns {string|null} Sound instance ID or null if failed
     */
    playSound(name, options = {}) {
        if (!this.enabled || !this.sfxEnabled) return null;
        if (this.activeSoundCount >= this.maxConcurrentSounds) return null;
        
        const volume = options.volume ?? 1.0;
        const pitch = options.pitch ?? 1.0;
        const loop = options.loop ?? false;
        
        try {
            if (this.audioContext) {
                // Use Web Audio API for better performance
                const buffer = this.audioBuffers.get(name);
                if (!buffer) {
                    console.warn(`Audio buffer not found: ${name}`);
                    return null;
                }
                
                const source = this.audioContext.createBufferSource();
                const gainNode = this.audioContext.createGain();
                
                source.buffer = buffer;
                source.playbackRate.value = pitch;
                gainNode.gain.value = volume;
                
                source.connect(gainNode);
                gainNode.connect(this.sfxGain);
                
                const soundId = `sfx_${Date.now()}_${Math.random()}`;
                this.activeSounds.set(soundId, { source, gainNode });
                this.activeSoundCount++;
                
                source.onended = () => {
                    this.activeSounds.delete(soundId);
                    this.activeSoundCount--;
                };
                
                source.start();
                return soundId;
            } else {
                // Fallback to HTML5 Audio
                const audio = this.audioElements.get(name);
                if (!audio) {
                    console.warn(`Audio element not found: ${name}`);
                    return null;
                }
                
                const clonedAudio = audio.cloneNode();
                clonedAudio.volume = volume * this.sfxVolume * this.masterVolume;
                clonedAudio.playbackRate = pitch;
                clonedAudio.loop = loop;
                
                const soundId = `sfx_${Date.now()}_${Math.random()}`;
                this.activeSounds.set(soundId, clonedAudio);
                this.activeSoundCount++;
                
                clonedAudio.addEventListener('ended', () => {
                    this.activeSounds.delete(soundId);
                    this.activeSoundCount--;
                });
                
                clonedAudio.play().catch(e => {
                    console.warn('Could not play sound:', e);
                    this.activeSounds.delete(soundId);
                    this.activeSoundCount--;
                });
                
                return soundId;
            }
        } catch (error) {
            console.error(`Error playing sound ${name}:`, error);
            return null;
        }
    }
    
    /**
     * Play background music
     * @param {string} name - Music track identifier
     * @param {boolean} loop - Whether to loop the music
     * @returns {string|null} Music instance ID or null if failed
     */
    playMusic(name, loop = true) {
        if (!this.enabled || !this.musicEnabled) return null;
        
        // Stop any currently playing music
        this.stopAllMusic();
        
        try {
            if (this.audioContext) {
                const buffer = this.audioBuffers.get(name);
                if (!buffer) {
                    console.warn(`Music buffer not found: ${name}`);
                    return null;
                }
                
                const source = this.audioContext.createBufferSource();
                const gainNode = this.audioContext.createGain();
                
                source.buffer = buffer;
                source.loop = loop;
                gainNode.gain.value = 1.0;
                
                source.connect(gainNode);
                gainNode.connect(this.musicGain);
                
                const musicId = `music_${Date.now()}_${Math.random()}`;
                this.musicTracks.set(musicId, { source, gainNode });
                
                source.start();
                return musicId;
            } else {
                // Fallback to HTML5 Audio
                const audio = this.audioElements.get(name);
                if (!audio) {
                    console.warn(`Music element not found: ${name}`);
                    return null;
                }
                
                const clonedAudio = audio.cloneNode();
                clonedAudio.volume = this.musicVolume * this.masterVolume;
                clonedAudio.loop = loop;
                
                const musicId = `music_${Date.now()}_${Math.random()}`;
                this.musicTracks.set(musicId, clonedAudio);
                
                clonedAudio.play().catch(e => {
                    console.warn('Could not play music:', e);
                    this.musicTracks.delete(musicId);
                });
                
                return musicId;
            }
        } catch (error) {
            console.error(`Error playing music ${name}:`, error);
            return null;
        }
    }
    
    /**
     * Play background music without stopping existing music (for continuous background music)
     * @param {string} name - Music track identifier
     * @param {boolean} loop - Whether to loop the music
     * @returns {string|null} Music instance ID or null if failed
     */
    playBackgroundMusic(name, loop = true) {
        if (!this.enabled || !this.musicEnabled) return null;
        
        // Stop any existing background music first
        if (this.backgroundMusicId) {
            this.stopMusic(this.backgroundMusicId);
            this.backgroundMusicId = null;
        }
        
        try {
            if (this.audioContext) {
                const buffer = this.audioBuffers.get(name);
                if (!buffer) {
                    console.warn(`Music buffer not found: ${name}`);
                    return null;
                }
                
                const source = this.audioContext.createBufferSource();
                const gainNode = this.audioContext.createGain();
                
                source.buffer = buffer;
                source.loop = loop;
                gainNode.gain.value = 1.0;
                
                source.connect(gainNode);
                gainNode.connect(this.musicGain);
                
                const musicId = `background_music_${Date.now()}_${Math.random()}`;
                this.musicTracks.set(musicId, { source, gainNode });
                this.backgroundMusicId = musicId;
                
                source.start();
                console.log(`Background music started: ${name} with ID: ${musicId}`);
                return musicId;
            } else {
                // Fallback to HTML5 Audio
                const audio = this.audioElements.get(name);
                if (!audio) {
                    console.warn(`Music element not found: ${name}`);
                    return null;
                }
                
                const clonedAudio = audio.cloneNode();
                clonedAudio.volume = this.musicVolume * this.masterVolume;
                clonedAudio.loop = loop;
                
                const musicId = `background_music_${Date.now()}_${Math.random()}`;
                this.musicTracks.set(musicId, clonedAudio);
                this.backgroundMusicId = musicId;
                
                clonedAudio.play().catch(e => {
                    console.warn('Could not play background music:', e);
                    this.musicTracks.delete(musicId);
                    this.backgroundMusicId = null;
                });
                
                console.log(`Background music started: ${name} with ID: ${musicId}`);
                return musicId;
            }
        } catch (error) {
            console.error(`Error playing background music ${name}:`, error);
            this.backgroundMusicId = null;
            return null;
        }
    }
    
    /**
     * Stop only the background music
     */
    stopBackgroundMusic() {
        if (this.backgroundMusicId) {
            this.stopMusic(this.backgroundMusicId);
            this.backgroundMusicId = null;
        }
    }
    
    /**
     * Stop all background music
     */
    stopAllMusic() {
        // Don't stop background music in stopAllMusic - use stopBackgroundMusic for that
        const backgroundMusic = this.backgroundMusicId ? this.musicTracks.get(this.backgroundMusicId) : null;
        
        for (const [musicId, music] of this.musicTracks) {
            // Skip background music if it exists
            if (musicId === this.backgroundMusicId) {
                continue;
            }
            
            if (this.audioContext) {
                const { source } = music;
                try {
                    source.stop();
                } catch (e) {
                    // Source might already be stopped
                }
            } else {
                music.pause();
                music.currentTime = 0;
            }
        }
        
        // Clear all tracks except background music
        if (this.backgroundMusicId && backgroundMusic) {
            this.musicTracks.clear();
            this.musicTracks.set(this.backgroundMusicId, backgroundMusic);
        } else {
            this.musicTracks.clear();
        }
    }
    
    /**
     * Stop a specific music track
     * @param {string} musicId - Music instance ID
     */
    stopMusic(musicId) {
        const music = this.musicTracks.get(musicId);
        if (!music) return;
        
        if (this.audioContext) {
            const { source } = music;
            try {
                source.stop();
            } catch (e) {
                // Source might already be stopped
            }
        } else {
            music.pause();
            music.currentTime = 0;
        }
        
        this.musicTracks.delete(musicId);
    }
    
    /**
     * Set volume for a specific audio type
     * @param {string} type - Audio type ('master', 'music', 'sfx', 'ambient')
     * @param {number} volume - Volume level (0-1)
     */
    setVolume(type, volume) {
        volume = Math.max(0, Math.min(1, volume)); // Clamp between 0 and 1
        
        switch (type) {
            case 'master':
                this.masterVolume = volume;
                break;
            case 'music':
                this.musicVolume = volume;
                break;
            case 'sfx':
                this.sfxVolume = volume;
                break;
            case 'ambient':
                this.ambientVolume = volume;
                break;
            default:
                console.warn(`Unknown volume type: ${type}`);
                return;
        }
        
        this.updateVolumeSettings();
    }
    
    /**
     * Update volume settings in audio nodes
     */
    updateVolumeSettings() {
        if (this.audioContext && this.masterGain && this.musicGain && this.sfxGain && this.ambientGain) {
            this.masterGain.gain.value = this.masterVolume;
            this.musicGain.gain.value = this.musicVolume;
            this.sfxGain.gain.value = this.sfxVolume;
            this.ambientGain.gain.value = this.ambientVolume;
        }
    }
    
    /**
     * Mute or unmute specific audio types
     * @param {string} type - Audio type ('music', 'sfx', 'ambient', 'all')
     * @param {boolean} mute - Whether to mute
     */
    mute(type, mute = true) {
        switch (type) {
            case 'music':
                this.musicEnabled = !mute;
                if (mute) {
                    this.stopAllMusic();
                }
                break;
            case 'sfx':
                this.sfxEnabled = !mute;
                break;
            case 'ambient':
                this.ambientEnabled = !mute;
                break;
            case 'all':
                this.enabled = !mute;
                if (mute) {
                    this.stopAllMusic();
                }
                break;
            default:
                console.warn(`Unknown mute type: ${type}`);
        }
    }
    
    /**
     * Get current volume settings
     * @returns {Object} Volume settings
     */
    getVolumeSettings() {
        return {
            master: this.masterVolume,
            music: this.musicVolume,
            sfx: this.sfxVolume,
            ambient: this.ambientVolume,
            musicEnabled: this.musicEnabled,
            sfxEnabled: this.sfxEnabled,
            ambientEnabled: this.ambientEnabled,
            enabled: this.enabled
        };
    }
    
    /**
     * Clean up audio resources
     */
    cleanup() {
        // Stop all music including background music
        for (const [musicId, music] of this.musicTracks) {
            if (this.audioContext) {
                const { source } = music;
                try {
                    source.stop();
                } catch (e) {
                    // Source might already be stopped
                }
            } else {
                music.pause();
                music.currentTime = 0;
            }
        }
        this.musicTracks.clear();
        this.backgroundMusicId = null;
        
        // Stop all active sounds
        for (const [soundId, sound] of this.activeSounds) {
            if (this.audioContext) {
                const { source } = sound;
                try {
                    source.stop();
                } catch (e) {
                    // Source might already be stopped
                }
            } else {
                sound.pause();
                sound.currentTime = 0;
            }
        }
        this.activeSounds.clear();
        this.activeSoundCount = 0;
        
        // Close audio context
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
    }
    
    /**
     * Handle user interaction for audio initialization (required by modern browsers)
     */
    async handleUserInteraction() {
        if (!this.audioContext) {
            console.warn('Audio context not available');
            return;
        }
        
        try {
            if (this.audioContext.state === 'suspended') {
                console.log('Resuming suspended audio context...');
                await this.audioContext.resume();
                console.log('Audio context resumed successfully');
            } else if (this.audioContext.state === 'closed') {
                console.warn('Audio context is closed, attempting to recreate...');
                // Try to recreate the audio context
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                await this.init();
            }
        } catch (error) {
            console.error('Error handling user interaction for audio:', error);
        }
    }
}
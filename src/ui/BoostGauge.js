/**
 * BoostGauge - UI component for displaying the player's boost level
 * Shows boost gauge with color coding (green → yellow → red) and refill/drain mechanics
 */
export class BoostGauge {
    constructor() {
        // UI state
        this.isVisible = false;
        this.boostLevel = 100; // Start with full boost (0-100%)
        this.boostActive = false;
        
        // Boost mechanics
        this.maxBoostLevel = 100;
        this.boostDrainRate = 20; // Percent per second when active
        this.boostRefillRate = 10; // Percent per second when not active
        
        // Visual properties
        this.width = 200;
        this.height = 20;
        this.backgroundColor = 'rgba(0, 0, 0, 0.7)';
        this.borderColor = '#ffffff';
        
        // Animation properties
        this.animationTime = 0;
        
    }
    
    /**
     * Show the boost gauge
     */
    show() {
        this.isVisible = true;
    }
    
    /**
     * Hide the boost gauge
     */
    hide() {
        this.isVisible = false;
    }
    
    /**
     * Set boost level (0-100)
     * @param {number} level - Boost level (0-100)
     */
    setBoostLevel(level) {
        this.boostLevel = Math.max(0, Math.min(this.maxBoostLevel, level));
    }
    
    /**
     * Get current boost level
     * @returns {number} Current boost level (0-100)
     */
    getBoostLevel() {
        return this.boostLevel;
    }
    
    /**
     * Check if boost is available (level > 0)
     * @returns {boolean} True if boost is available
     */
    isBoostAvailable() {
        return this.boostLevel > 0;
    }
    
    /**
     * Set boost active state
     * @param {boolean} active - Whether boost is active
     */
    setBoostActive(active) {
        this.boostActive = active;
    }
    
    /**
     * Update boost level based on time and active state
     * @param {number} deltaTime - Time elapsed since last update in milliseconds
     */
    update(deltaTime) {
        if (!this.isVisible) return;
        
        const deltaTimeSeconds = deltaTime / 1000;
        
        if (this.boostActive && this.boostLevel > 0) {
            // Drain boost when active
            this.boostLevel = Math.max(0, this.boostLevel - (this.boostDrainRate * deltaTimeSeconds));
            
            // Auto-deactivate boost when empty
            if (this.boostLevel <= 0) {
                this.boostActive = false;
            }
        } else if (!this.boostActive && this.boostLevel < this.maxBoostLevel) {
            // Refill boost when not active
            this.boostLevel = Math.min(this.maxBoostLevel, this.boostLevel + (this.boostRefillRate * deltaTimeSeconds));
        }
        
        // Update animation timer
        this.animationTime += deltaTime / 1000;
    }
    
    /**
     * Render the boost gauge on canvas
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} x - X position
     * @param {number} y - Y position
     * @param {number} width - Width of the gauge
     * @param {number} height - Height of the gauge
     */
    render(ctx, x, y, width, height) {
        if (!this.isVisible) return;
        
        // Update dimensions if provided
        if (width) this.width = width;
        if (height) this.height = height;
        
        // Calculate color based on boost level
        let fillColor = '#00ff00'; // Green
        if (this.boostLevel < 30) {
            fillColor = '#ff4444'; // Red
        } else if (this.boostLevel < 60) {
            fillColor = '#ffaa00'; // Orange/Yellow
        }
        
        // Draw boost label
        ctx.fillStyle = '#ffffff';
        ctx.font = 'bold 12px "Courier New", monospace';
        ctx.textAlign = 'left';
        ctx.shadowColor = '#000000';
        ctx.shadowBlur = 3;
        ctx.fillText('BOOST', x, y - 5);
        
        // Draw boost percentage
        ctx.textAlign = 'right';
        ctx.fillStyle = fillColor;
        ctx.fillText(`${Math.round(this.boostLevel)}%`, x + this.width, y - 5);
        ctx.shadowBlur = 0;
        
        // Draw gauge background
        ctx.fillStyle = this.backgroundColor;
        ctx.fillRect(x, y, this.width, this.height);
        
        // Draw gauge border
        ctx.strokeStyle = this.borderColor;
        ctx.lineWidth = 1;
        ctx.strokeRect(x, y, this.width, this.height);
        
        // Draw gauge fill
        const fillWidth = (this.width - 4) * (this.boostLevel / 100);
        if (fillWidth > 0) {
            // Create gradient for fill
            const gradient = ctx.createLinearGradient(x + 2, y, x + 2 + fillWidth, y);
            gradient.addColorStop(0, fillColor);
            gradient.addColorStop(1, fillColor + 'aa');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(x + 2, y + 2, fillWidth, this.height - 4);
            
            // Add glow effect when boost is active
            if (this.boostActive) {
                ctx.shadowColor = fillColor;
                ctx.shadowBlur = 10;
                ctx.fillRect(x + 2, y + 2, fillWidth, this.height - 4);
                ctx.shadowBlur = 0;
                
                // Draw shine effect
                const shineOffset = (this.animationTime * 100) % (this.width + 40) - 20;
                const shineGradient = ctx.createLinearGradient(
                    x + shineOffset, y,
                    x + shineOffset + 20, y
                );
                shineGradient.addColorStop(0, 'transparent');
                shineGradient.addColorStop(0.5, 'rgba(255, 255, 255, 0.3)');
                shineGradient.addColorStop(1, 'transparent');
                
                ctx.fillStyle = shineGradient;
                ctx.fillRect(x + 2, y + 2, fillWidth, this.height - 4);
            }
        }
    }
    
    /**
     * Destroy the boost gauge and clean up
     */
    destroy() {
        // Nothing to clean up for canvas-based rendering
    }
}
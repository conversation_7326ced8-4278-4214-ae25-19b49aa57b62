/**
 * EnemyScaling utility for managing enemy progression and stat scaling
 * Implements the tier-based difficulty system as documented in EnemyProgression.md
 */

/**
 * Get the difficulty tier for a given level
 * @param {number} level - Current level
 * @returns {number} Tier number (1-5)
 */
export function getTierForLevel(level) {
    if (level <= 5) return 1;
    if (level <= 10) return 2;
    if (level <= 15) return 3;
    if (level <= 20) return 4;
    return 5;
}

/**
 * Get the difficulty coefficient for a given level
 * @param {number} level - Current level
 * @returns {number} Difficulty coefficient
 */
export function getDifficultyCoefficient(level) {
    const tier = getTierForLevel(level);
    
    // Calculate coefficient based on tier and level position within tier
    switch (tier) {
        case 1: // Levels 1-5: 1.0 - 1.8
            return 1.0 + (level - 1) * 0.2;
        case 2: // Levels 6-10: 2.0 - 3.6
            return 2.0 + (level - 6) * 0.32;
        case 3: // Levels 11-15: 4.0 - 7.2
            return 4.0 + (level - 11) * 0.64;
        case 4: // Levels 16-20: 8.0 - 14.4
            return 8.0 + (level - 16) * 1.28;
        case 5: // Levels 21+: 16.0+
            return 16.0 + (level - 21) * 1.6;
        default:
            return 1.0;
    }
}

/**
 * Scale enemy stats based on level and difficulty coefficient
 * @param {object} baseStats - Base enemy stats
 * @param {number} level - Current level
 * @returns {object} Scaled enemy stats
 */
export function scaleEnemyStats(baseStats, level) {
    const coeff = getDifficultyCoefficient(level);
    const tier = getTierForLevel(level);
    
    // Apply scaling formulas as documented in EnemyProgression.md
    return {
        maxHealth: Math.round(baseStats.maxHealth * coeff),
        speed: baseStats.speed * coeff,
        damage: Math.round(baseStats.damage * coeff),
        fireRate: baseStats.fireRate / Math.sqrt(coeff),
        scoreValue: Math.round(baseStats.scoreValue * coeff),
        tier: tier,
        coefficient: coeff
    };
}

/**
 * Determine if an enemy should be elite based on level and random chance
 * @param {number} level - Current level
 * @returns {boolean} True if enemy should be elite
 */
export function shouldBeElite(level) {
    const tier = getTierForLevel(level);
    let eliteChance = 0;
    
    // Elite chance increases with tier
    switch (tier) {
        case 1: eliteChance = 0.0; break;
        case 2: eliteChance = 0.05; break;
        case 3: eliteChance = 0.1; break;
        case 4: eliteChance = 0.15; break;
        case 5: eliteChance = 0.25; break;
        default: eliteChance = 0.0;
    }
    
    return Math.random() < eliteChance;
}

/**
 * Get available behaviors for an enemy based on tier
 * @param {number} tier - Enemy tier
 * @param {boolean} isElite - Whether enemy is elite
 * @returns {Array} Array of available behavior names
 */
export function getAvailableBehaviors(tier, isElite = false) {
    const behaviors = ['basic_movement', 'single_shot'];
    
    // Add behaviors based on tier
    if (tier >= 2) {
        behaviors.push('spread_shot', 'leading_shots');
    }
    
    if (tier >= 3) {
        behaviors.push('homing_projectiles', 'shield_allies');
    }
    
    if (tier >= 4) {
        behaviors.push('complex_patterns', 'evasion_dash', 'formation_flying');
    }
    
    if (tier >= 5) {
        behaviors.push('multi_phase_attack', 'teleport', 'summon_allies');
    }
    
    // Elite enemies get all lower tier behaviors plus special ones
    if (isElite) {
        if (!behaviors.includes('homing_projectiles')) behaviors.push('homing_projectiles');
        if (!behaviors.includes('evasion_dash')) behaviors.push('evasion_dash');
        behaviors.push('unpredictable_movement', 'enhanced_stats');
    }
    
    return behaviors;
}

/**
 * Get enemy type modifier based on environment
 * @param {string} enemyType - Type of enemy
 * @param {string} environment - Current environment
 * @returns {number} Multiplier for enemy stats
 */
export function getEnvironmentTypeModifier(enemyType, environment) {
    const modifiers = {
        space: { air: 1.2, crystal: 1.1 },
        water: { water: 1.5, earth: 0.8, fire: 0.5, air: 0.7 }, // merged underwater + ice
        fire: { fire: 1.6, earth: 1.3 }, // was volcanic
        crystal: { crystal: 1.8, shadow: 1.2 },
        earth: { earth: 1.4, shadow: 1.3, fire: 1.2 }, // merged forest + desert
        shadow: { shadow: 1.3 }
    };
    
    return modifiers[environment]?.[enemyType] || 1.0;
}

/**
 * Apply environmental modifiers to enemy stats
 * @param {object} stats - Base enemy stats
 * @param {string} enemyType - Type of enemy
 * @param {string} environment - Current environment
 * @returns {object} Modified stats
 */
export function applyEnvironmentalModifiers(stats, enemyType, environment) {
    const modifier = getEnvironmentTypeModifier(enemyType, environment);
    
    return {
        ...stats,
        maxHealth: Math.round(stats.maxHealth * modifier),
        damage: Math.round(stats.damage * modifier),
        // Speed is not affected by environment to maintain game balance
    };
}

/**
 * Get fire pattern for enemy based on tier and type
 * @param {number} tier - Enemy tier
 * @param {string} enemyType - Type of enemy
 * @returns {string} Fire pattern name
 */
export function getFirePattern(tier, enemyType) {
    // Some enemy types have preferred patterns
    if (enemyType === 'fire' && tier >= 2) return 'burst';
    if (enemyType === 'crystal' && tier >= 3) return 'spiral';
    if (enemyType === 'water' && tier >= 4) return 'shotgun';
    
    // Default patterns by tier
    switch (tier) {
        case 1: return 'single';
        case 2: return 'spread';
        case 3: return 'homing';
        case 4: return 'complex';
        case 5: return 'elite';
        default: return 'single';
    }
}

/**
 * Get movement pattern for enemy based on tier and type
 * @param {number} tier - Enemy tier
 * @param {string} enemyType - Type of enemy
 * @returns {string} Movement pattern name
 */
export function getMovementPattern(tier, enemyType) {
    // Some enemy types have preferred movement patterns
    if (enemyType === 'air') return 'zigzag';
    if (enemyType === 'shadow' && tier >= 3) return 'teleport';
    if (enemyType === 'water') return 'wave';
    
    // Default patterns by tier
    switch (tier) {
        case 1: return 'straight';
        case 2: return 'sine';
        case 3: return 'adaptive';
        case 4: return 'evasive';
        case 5: return 'unpredictable';
        default: return 'straight';
    }
}

/**
 * Calculate enemy formation based on tier
 * @param {number} tier - Enemy tier
 * @returns {string} Formation type
 */
export function getFormationType(tier) {
    switch (tier) {
        case 1: return 'line';
        case 2: return 'grid';
        case 3: return 'v_formation';
        case 4: return 'delta';
        case 5: return 'circle';
        default: return 'line';
    }
}

/**
 * Get special abilities for enemy based on tier and elite status
 * @param {number} tier - Enemy tier
 * @param {boolean} isElite - Whether enemy is elite
 * @returns {Array} Array of special ability names
 */
export function getSpecialAbilities(tier, isElite = false) {
    const abilities = [];
    
    switch (tier) {
        case 1:
            // No special abilities for tier 1
            break;
        case 2:
            abilities.push('leading_shots');
            break;
        case 3:
            abilities.push('shield_allies', 'homing_projectiles');
            break;
        case 4:
            abilities.push('evasion_dash', 'formation_flying', 'complex_patterns');
            break;
        case 5:
            abilities.push('multi_phase_attack', 'teleport', 'summon_allies', 'damage_resistance');
            break;
    }
    
    // Elite enemies get additional abilities
    if (isElite) {
        abilities.push('enhanced_stats', 'unpredictable_movement');
        if (tier >= 3) abilities.push('area_damage');
        if (tier >= 4) abilities.push('temporal_shield');
    }
    
    return abilities;
}

/**
 * Get color variations for elite enemies
 * @param {string} baseColor - Base color of enemy
 * @param {number} tier - Enemy tier
 * @returns {string} Elite color variation
 */
export function getEliteColor(baseColor, tier) {
    // Elite enemies have a golden glow that intensifies with tier
    const glowIntensity = Math.min(255, 100 + tier * 30);
    return `rgba(255, 215, 0, ${glowIntensity / 255})`;
}

/**
 * Calculate spawn rate modifier based on tier
 * @param {number} tier - Enemy tier
 * @returns {number} Spawn rate multiplier
 */
export function getSpawnRateModifier(tier) {
    switch (tier) {
        case 1: return 1.0;
        case 2: return 1.2;
        case 3: return 1.4;
        case 4: return 1.6;
        case 5: return 2.0;
        default: return 1.0;
    }
}

/**
 * Get enemy description based on tier and type
 * @param {number} tier - Enemy tier
 * @param {string} enemyType - Type of enemy
 * @param {boolean} isElite - Whether enemy is elite
 * @returns {string} Description string
 */
export function getEnemyDescription(tier, enemyType, isElite = false) {
    const tierNames = ['', 'Basic', 'Adaptive', 'Tactical', 'Complex', 'Elite'];
    const typeNames = {
        air: 'Air',
        water: 'Water',
        fire: 'Fire',
        earth: 'Earth',
        crystal: 'Crystal',
        shadow: 'Shadow'
    };
    
    const tierName = tierNames[tier] || 'Unknown';
    const typeName = typeNames[enemyType] || 'Unknown';
    
    if (isElite) {
        return `Elite ${typeName} (${tierName} tier)`;
    }
    
    return `${typeName} (${tierName} tier)`;
}
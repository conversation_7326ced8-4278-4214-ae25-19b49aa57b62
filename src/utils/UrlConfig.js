/**
 * URL Configuration Utility
 * Provides dynamic URL detection for both local development and production deployment
 * Handles mobile compatibility by detecting the current hostname and protocol
 */
export class UrlConfig {
    constructor() {
        // Cache the base URL to avoid repeated calculations
        this._baseUrl = null;
    }

    /**
     * Get the base API URL based on current hostname and deployment environment
     * @returns {string} The appropriate base URL for API calls
     */
    getApiBaseUrl() {
        if (this._baseUrl) {
            return this._baseUrl;
        }

        const { protocol, hostname, port } = window.location;
        
        // Production deployment
        if (hostname === 'warpsector.merchgenieai.com') {
            this._baseUrl = `${protocol}//warpsector.merchgenieai.com`;
        }
        // Local development - use current hostname with port 3001
        else {
            // Use the current hostname (handles both localhost and network IPs like *************)
            this._baseUrl = `${protocol}//${hostname}:3001`;
        }
        
        console.log(`URL Config: Using base URL ${this._baseUrl} for hostname ${hostname}`);
        return this._baseUrl;
    }

    /**
     * Get the full URL for a given API endpoint
     * @param {string} endpoint - The API endpoint (e.g., '/api/environments')
     * @returns {string} The complete URL
     */
    getApiUrl(endpoint) {
        const baseUrl = this.getApiBaseUrl();
        // Ensure endpoint starts with '/'
        const cleanEndpoint = endpoint.startsWith('/') ? endpoint : `/${endpoint}`;
        return `${baseUrl}${cleanEndpoint}`;
    }

    /**
     * Get the full URL for an image path
     * @param {string} imagePath - The image path (may be relative or absolute)
     * @returns {string} The complete image URL
     */
    getImageUrl(imagePath) {
        if (!imagePath) return '';
        
        // If it's already a full URL, return as-is
        if (imagePath.startsWith('http://') || imagePath.startsWith('https://')) {
            return imagePath;
        }
        
        // If it's a relative path starting with /api/, prepend the base URL
        if (imagePath.startsWith('/api/')) {
            const baseUrl = this.getApiBaseUrl();
            return `${baseUrl}${imagePath}`;
        }
        
        // For other relative paths, assume they're local assets
        return imagePath;
    }

    /**
     * Reset the cached base URL (useful for testing or dynamic environment changes)
     */
    reset() {
        this._baseUrl = null;
    }

    /**
     * Get current configuration info for debugging
     * @returns {object} Configuration information
     */
    getConfigInfo() {
        const { protocol, hostname, port } = window.location;
        return {
            currentHostname: hostname,
            currentProtocol: protocol,
            currentPort: port,
            apiBaseUrl: this.getApiBaseUrl(),
            isProduction: hostname === 'warpsector.merchgenieai.com',
            isLocalDevelopment: hostname !== 'warpsector.merchgenieai.com'
        };
    }
}

// Create a singleton instance for global use
export const urlConfig = new UrlConfig();
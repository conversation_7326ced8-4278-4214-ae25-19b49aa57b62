/**
 * Audio configuration for War<PERSON><PERSON><PERSON>
 * Defines all audio files and their paths
 */

export const AUDIO_CONFIG = {
    // Music tracks
    MUSIC: {
        // Themes
        SPACE_THEME: 'assets/audio/music/themes/water_theme.mp3',
        WATER_THEME: 'assets/audio/music/themes/water_theme.mp3',
        FIRE_INTENSITY: 'assets/audio/music/themes/water_theme.mp3',
        AIR_AMBIENT: 'assets/audio/music/themes/water_theme.mp3',
        EARTH_RHYTHM: 'assets/audio/music/themes/water_theme.mp3',
        CRYSTAL_HARMONY: 'assets/audio/music/themes/water_theme.mp3',
        SHADOW_MYSTERY: 'assets/audio/music/themes/water_theme.mp3',
        
        // Boss music
        BOSS_THEME: 'assets/audio/music/boss/boss_theme.mp3',
        
        // Menu music
        MENU_THEME: 'assets/audio/music/themes/water_theme.mp3'
    },
    
    // Sound effects
    SFX: {
        // Weapon sounds
        PLAYER_SHOT: 'assets/audio/sfx/weapons/player_shot.wav',
        AIR_SHOT: 'assets/audio/sfx/weapons/shadow_shot.wav',
        WATER_SHOT: 'assets/audio/sfx/weapons/shadow_shot.wav',
        FIRE_SHOT: 'assets/audio/sfx/weapons/shadow_shot.wav',
        EARTH_SHOT: 'assets/audio/sfx/weapons/shadow_shot.wav',
        CRYSTAL_SHOT: 'assets/audio/sfx/weapons/shadow_shot.wav',
        SHADOW_SHOT: 'assets/audio/sfx/weapons/shadow_shot.wav',
        
        // Explosion sounds
        ENEMY_EXPLOSION: 'assets/audio/sfx/explosions/enemy_explosion.wav',
        PLAYER_EXPLOSION: 'assets/audio/sfx/explosions/boss_explosion.wav',
        BOSS_EXPLOSION: 'assets/audio/sfx/explosions/boss_explosion.wav',
        
        // UI sounds
        BUTTON_CLICK: 'assets/audio/sfx/ui/menu_open.wav',
        MENU_OPEN: 'assets/audio/sfx/ui/menu_open.wav',
        MENU_CLOSE: 'assets/audio/sfx/ui/menu_open.wav',
        ACHIEVEMENT: 'assets/audio/sfx/ui/achievement.wav',
        
        // Power-up sounds
        COLLECT_POWERUP: 'assets/audio/sfx/ui/collect_powerup.wav',
        WEAPON_UPGRADE: 'assets/audio/sfx/powerups/weapon_upgrade.wav',
        EXTRA_LIFE: 'assets/audio/sfx/powerups/weapon_upgrade.wav',
        
        // Formation sounds
        FORMATION_SPAWN: 'assets/audio/sfx/formations/formation_spawn.wav',
        FORMATION_TURN: 'assets/audio/sfx/formations/formation_turn.wav',
        DIVE_ATTACK: 'assets/audio/sfx/formations/formation_spawn.wav',
        
        // Ambient sounds
        SPACE_AMBIENT: 'assets/audio/sfx/ambient/space_ambient.wav',
        WARP_AMBIENT: 'assets/audio/sfx/ambient/warp_ambient.wav'
    }
};

// Audio file mapping for easy loading
export const AUDIO_FILES = [
    // Music files
    { url: AUDIO_CONFIG.MUSIC.SPACE_THEME, name: 'space_theme' },
    { url: AUDIO_CONFIG.MUSIC.WATER_THEME, name: 'water_theme' },
    { url: AUDIO_CONFIG.MUSIC.FIRE_INTENSITY, name: 'fire_intensity' },
    { url: AUDIO_CONFIG.MUSIC.AIR_AMBIENT, name: 'air_ambient' },
    { url: AUDIO_CONFIG.MUSIC.EARTH_RHYTHM, name: 'earth_rhythm' },
    { url: AUDIO_CONFIG.MUSIC.CRYSTAL_HARMONY, name: 'crystal_harmony' },
    { url: AUDIO_CONFIG.MUSIC.SHADOW_MYSTERY, name: 'shadow_mystery' },
    { url: AUDIO_CONFIG.MUSIC.BOSS_THEME, name: 'boss_theme' },
    { url: AUDIO_CONFIG.MUSIC.MENU_THEME, name: 'menu_theme' },
    
    // SFX files
    { url: AUDIO_CONFIG.SFX.PLAYER_SHOT, name: 'player_shot' },
    { url: AUDIO_CONFIG.SFX.AIR_SHOT, name: 'air_shot' },
    { url: AUDIO_CONFIG.SFX.WATER_SHOT, name: 'water_shot' },
    { url: AUDIO_CONFIG.SFX.FIRE_SHOT, name: 'fire_shot' },
    { url: AUDIO_CONFIG.SFX.EARTH_SHOT, name: 'earth_shot' },
    { url: AUDIO_CONFIG.SFX.CRYSTAL_SHOT, name: 'crystal_shot' },
    { url: AUDIO_CONFIG.SFX.SHADOW_SHOT, name: 'shadow_shot' },
    { url: AUDIO_CONFIG.SFX.ENEMY_EXPLOSION, name: 'enemy_explosion' },
    { url: AUDIO_CONFIG.SFX.PLAYER_EXPLOSION, name: 'player_explosion' },
    { url: AUDIO_CONFIG.SFX.BOSS_EXPLOSION, name: 'boss_explosion' },
    { url: AUDIO_CONFIG.SFX.BUTTON_CLICK, name: 'button_click' },
    { url: AUDIO_CONFIG.SFX.MENU_OPEN, name: 'menu_open' },
    { url: AUDIO_CONFIG.SFX.MENU_CLOSE, name: 'menu_close' },
    { url: AUDIO_CONFIG.SFX.ACHIEVEMENT, name: 'achievement' },
    { url: AUDIO_CONFIG.SFX.COLLECT_POWERUP, name: 'collect_powerup' },
    { url: AUDIO_CONFIG.SFX.WEAPON_UPGRADE, name: 'weapon_upgrade' },
    { url: AUDIO_CONFIG.SFX.EXTRA_LIFE, name: 'extra_life' },
    { url: AUDIO_CONFIG.SFX.FORMATION_SPAWN, name: 'formation_spawn' },
    { url: AUDIO_CONFIG.SFX.FORMATION_TURN, name: 'formation_turn' },
    { url: AUDIO_CONFIG.SFX.DIVE_ATTACK, name: 'dive_attack' },
    { url: AUDIO_CONFIG.SFX.SPACE_AMBIENT, name: 'space_ambient' },
    { url: AUDIO_CONFIG.SFX.WARP_AMBIENT, name: 'warp_ambient' }
];
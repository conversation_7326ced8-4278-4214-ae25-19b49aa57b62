import { GameObject } from '../utils/GameObject.js';
import { Vector2 } from '../utils/Vector2.js';
import { PowerUpFactory } from '../systems/PowerUp.js';

/**
 * PowerUpCollectible - A collectible power-up that appears in the game world
 * Player must collide with it to collect the power-up effect
 */
export class PowerUpCollectible extends GameObject {
    constructor(x, y, powerUpType) {
        super(x, y);
        
        this.powerUpType = powerUpType;
        this.powerUp = PowerUpFactory.createPowerUp(powerUpType);
        this.collected = false;
        
        // Add power-up collectible tag
        this.addTag('powerup-collectible');
        
        // Visual properties
        this.radius = 15;
        this.collisionRadius = 15; // Set collision radius for collision detection
        this.color = this.powerUp.color;
        this.glowColor = this.powerUp.glowColor;
        this.pulseAmount = 0;
        this.pulseDirection = 1;
        this.pulseSpeed = 0.05;
        
        // Movement properties
        this.velocity = new Vector2(0, 0.5); // Start with slow drift downward
        this.lifetime = 10000; // 10 seconds before disappearing
        this.timeAlive = 0;
        
        // Downward gravitation properties
        this.downwardGravity = 50; // Constant downward acceleration
        this.maxDownwardSpeed = 150; // Maximum downward speed
        
        // Player gravitation properties
        this.gravitationSpeed = 100; // Speed at which power-up gravitates toward player
        this.gravitationDistance = 200; // Distance at which power-up starts gravitating
        
        // Enhanced bloom properties
        this.bloomIntensity = 0.8;
        this.bloomRadius = 25;
        this.bloomPulseSpeed = 0.03;
        this.bloomPulseAmount = 0;
        
        this.updateCollisionBounds();
    }
    
    /**
     * Update the collectible
     * @param {number} deltaTime - Time elapsed since last update
     */
    update(deltaTime) {
        if (this.collected) return;
        
        super.update(deltaTime);
        
        // Update pulse animation
        this.pulseAmount += this.pulseSpeed * this.pulseDirection * (deltaTime / 16);
        if (this.pulseAmount >= 1) {
            this.pulseAmount = 1;
            this.pulseDirection = -1;
        } else if (this.pulseAmount <= 0) {
            this.pulseAmount = 0;
            this.pulseDirection = 1;
        }
        
        // Update bloom pulse animation
        this.bloomPulseAmount += this.bloomPulseSpeed * (deltaTime / 16);
        if (this.bloomPulseAmount >= 1) {
            this.bloomPulseAmount = 1;
            this.bloomPulseSpeed = -this.bloomPulseSpeed;
        } else if (this.bloomPulseAmount <= 0) {
            this.bloomPulseAmount = 0;
            this.bloomPulseSpeed = Math.abs(this.bloomPulseSpeed);
        }
        
        // Apply downward gravity
        this.applyDownwardGravity(deltaTime);
        
        // Gravitate toward player if within range
        this.gravitateTowardPlayer(deltaTime);
        
        // Update lifetime
        this.timeAlive += deltaTime;
        if (this.timeAlive >= this.lifetime) {
            this.destroy();
        }
        
        // Check if out of bounds
        this.checkBounds();
    }
    
    /**
     * Make power-up gravitate toward the player if within range
     * @param {number} deltaTime - Time elapsed since last update
     */
    applyDownwardGravity(deltaTime) {
        // Apply constant downward acceleration
        this.velocity.y += this.downwardGravity * deltaTime / 1000;
        
        // Clamp to maximum downward speed
        if (this.velocity.y > this.maxDownwardSpeed) {
            this.velocity.y = this.maxDownwardSpeed;
        }
    }
    
    gravitateTowardPlayer(deltaTime) {
        // Find player ship
        const gameObjectManager = window.gameEngine?.gameObjectManager;
        if (!gameObjectManager) return;
        
        const players = gameObjectManager.findByTag('player');
        if (players.length === 0) return;
        
        const player = players[0];
        const distance = this.position.distanceTo(player.position);
        
        // If player is within gravitation range, move toward player
        if (distance < this.gravitationDistance) {
            // Calculate direction to player
            const direction = player.position.subtract(this.position).normalize();
            
            // Gravitation strength increases as player gets closer (inverse relationship)
            const strength = 1.0 - (distance / this.gravitationDistance);
            const gravitationForce = this.gravitationSpeed * strength;
            
            // Apply gravitation velocity
            const gravitationVelocity = direction.multiply(gravitationForce * deltaTime / 1000);
            this.velocity.addInPlace(gravitationVelocity);
        }
    }
    
    /**
     * Check if collectible is out of bounds
     */
    checkBounds() {
        const canvas = window.gameEngine?.canvas;
        if (!canvas) return;
        
        if (this.position.y > canvas.height + 50) {
            this.destroy();
        }
    }
    
    /**
     * Render the collectible
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} interpolation - Interpolation value
     */
    render(ctx, interpolation = 0) {
        if (this.collected) return;
        
        const renderPos = this.position.add(this.velocity.multiply(interpolation / 1000));
        
        ctx.save();
        ctx.translate(renderPos.x, renderPos.y);
        
        // Draw enhanced bloom effect with multiple layers
        const bloomPulseFactor = 1 + this.bloomPulseAmount * 0.4;
        const baseGlowRadius = this.bloomRadius * bloomPulseFactor;
        
        // Outer bloom layer (most transparent)
        const outerBloomRadius = baseGlowRadius * 1.5;
        const outerGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, outerBloomRadius);
        outerGradient.addColorStop(0, this.glowColor + '20'); // 20% opacity
        outerGradient.addColorStop(0.5, this.color + '10'); // 10% opacity
        outerGradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = outerGradient;
        ctx.beginPath();
        ctx.arc(0, 0, outerBloomRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Middle bloom layer
        const middleBloomRadius = baseGlowRadius * 1.2;
        const middleGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, middleBloomRadius);
        middleGradient.addColorStop(0, this.glowColor + '40'); // 40% opacity
        middleGradient.addColorStop(0.7, this.color + '20'); // 20% opacity
        middleGradient.addColorStop(1, 'transparent');
        
        ctx.fillStyle = middleGradient;
        ctx.beginPath();
        ctx.arc(0, 0, middleBloomRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Inner bloom layer (most intense)
        const innerBloomRadius = baseGlowRadius;
        const innerGradient = ctx.createRadialGradient(0, 0, 0, 0, 0, innerBloomRadius);
        innerGradient.addColorStop(0, this.glowColor);
        innerGradient.addColorStop(0.5, this.color + '80'); // 80% opacity
        innerGradient.addColorStop(1, this.color + '20'); // 20% opacity
        
        ctx.fillStyle = innerGradient;
        ctx.beginPath();
        ctx.arc(0, 0, innerBloomRadius, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw main collectible
        ctx.fillStyle = this.color;
        ctx.beginPath();
        ctx.arc(0, 0, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Draw icon with enhanced glow
        ctx.shadowColor = this.glowColor;
        ctx.shadowBlur = 10 * bloomPulseFactor;
        ctx.font = 'bold 20px Arial';
        ctx.fillStyle = 'white';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.fillText(this.powerUp.icon, 0, 0);
        
        // Reset shadow
        ctx.shadowBlur = 0;
        
        ctx.restore();
    }
    
    /**
     * Collect the power-up
     * @returns {PowerUp} The power-up that was collected
     */
    collect() {
        if (this.collected) return null;
        
        this.collected = true;
        this.destroy();
        return this.powerUp;
    }
    
    /**
     * Reset for object pooling
     */
    reset() {
        super.reset();
        this.collected = false;
        this.timeAlive = 0;
        this.pulseAmount = 0;
        this.pulseDirection = 1;
        this.bloomPulseAmount = 0;
        this.bloomPulseSpeed = 0.03;
        this.velocity = new Vector2(0, 0.5);
    }
}
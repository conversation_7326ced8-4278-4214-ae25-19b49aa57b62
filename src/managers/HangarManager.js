/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON> - Manages persistent upgrades for PlayerShip and WingmanShip
 * Handles upgrade purchases, stat calculations, and integration with save system
 */

/**
 * Upgrade definitions for PlayerShip
 */
export const PLAYER_UPGRADES = {
    ENGINE_TUNING: {
        id: 'engine_tuning',
        name: 'Engine Tuning',
        description: 'Increases ship speed and acceleration',
        maxLevel: 10,
        baseCost: 2500,
        costMultiplier: 1.5,
        affects: ['maxSpeed', 'acceleration'],
        baseValues: { maxSpeed: 300, acceleration: 800 },
        maxMultiplier: 2.0
    },
    HULL_PLATING: {
        id: 'hull_plating', 
        name: 'Hull Plating',
        description: 'Increases maximum health',
        maxLevel: 10,
        baseCost: 3000,
        costMultiplier: 1.6,
        affects: ['maxHealth'],
        baseValues: { maxHealth: 100 },
        maxMultiplier: 2.5
    },
    REACTOR_CORE: {
        id: 'reactor_core',
        name: 'Reactor Core', 
        description: 'Increases weapon fire rate',
        maxLevel: 10,
        baseCost: 4000,
        costMultiplier: 1.7,
        affects: ['fireRate'],
        baseValues: { fireRate: 250 }, // milliseconds between shots
        maxMultiplier: 0.4 // Lower is better for fire rate
    },
    FIREPOWER: {
        id: 'firepower',
        name: 'Firepower',
        description: 'Increases projectile damage',
        maxLevel: 10,
        baseCost: 3500,
        costMultiplier: 1.6,
        affects: ['projectileDamage'],
        baseValues: { projectileDamage: 25 },
        maxMultiplier: 3.0
    },
    EXTRA_LIVES: {
        id: 'extra_lives',
        name: 'Extra Lives',
        description: 'Increases starting lives',
        maxLevel: 5,
        baseCost: 10000,
        costMultiplier: 2.0,
        affects: ['maxLives'],
        baseValues: { maxLives: 3 },
        maxMultiplier: 1.67 // Up to 5 lives total
    }
};


/**
 * HangarManager class
 */
export class HangarManager {
    constructor(tokenEconomyManager, orangeSDKManager) {
        this.tokenEconomyManager = tokenEconomyManager;
        this.orangeSDKManager = orangeSDKManager;
        
        // Initialize upgrade levels from save data
        this.playerUpgrades = this.loadPlayerUpgrades();
        
        // Event listeners for UI updates
        this.upgradeListeners = [];
    }

    /**
     * Load player upgrades from save system
     * @returns {object} Player upgrade levels
     */
    loadPlayerUpgrades() {
        const saveData = this.orangeSDKManager?.playerData?.hangarUpgrades?.player || {};
        const upgrades = {};
        
        // Initialize all upgrades to level 0 if not present
        for (const [key, upgrade] of Object.entries(PLAYER_UPGRADES)) {
            upgrades[upgrade.id] = saveData[upgrade.id] || 0;
        }
        
        return upgrades;
    }


    /**
     * Reload upgrade data from save system
     */
    reloadUpgradeData() {
        this.playerUpgrades = this.loadPlayerUpgrades();
    }

    /**
     * Save upgrades to persistent storage
     */
    saveUpgrades() {
        if (!this.orangeSDKManager) return;
        
        // Ensure hangarUpgrades exists in player data
        if (!this.orangeSDKManager.playerData.hangarUpgrades) {
            this.orangeSDKManager.playerData.hangarUpgrades = {};
        }
        
        this.orangeSDKManager.playerData.hangarUpgrades.player = { ...this.playerUpgrades };
        
        // Trigger save
        this.orangeSDKManager.savePlayerData();
    }

    /**
     * Calculate the cost of upgrading a specific upgrade to the next level
     * @param {object} upgradeConfig - Upgrade configuration
     * @param {number} currentLevel - Current upgrade level
     * @returns {number} Cost in WISH tokens
     */
    calculateUpgradeCost(upgradeConfig, currentLevel) {
        if (currentLevel >= upgradeConfig.maxLevel) return 0;
        
        return Math.floor(upgradeConfig.baseCost * Math.pow(upgradeConfig.costMultiplier, currentLevel));
    }

    /**
     * Purchase an upgrade for the player ship
     * @param {string} upgradeId - ID of the upgrade to purchase
     * @returns {object} Purchase result
     */
    purchasePlayerUpgrade(upgradeId) {
        const upgrade = Object.values(PLAYER_UPGRADES).find(u => u.id === upgradeId);
        if (!upgrade) {
            return { success: false, reason: 'invalid_upgrade' };
        }

        const currentLevel = this.playerUpgrades[upgradeId];
        if (currentLevel >= upgrade.maxLevel) {
            return { success: false, reason: 'max_level_reached' };
        }

        const cost = this.calculateUpgradeCost(upgrade, currentLevel);

        // Check if player has enough tokens
        const currentBalance = this.tokenEconomyManager.getBalance();
        const canAfford = this.tokenEconomyManager.canAfford(cost);

        console.log(`[HangarManager] Upgrade purchase check: ${upgradeId}, Cost: ${cost}, Balance: ${currentBalance}, CanAfford: ${canAfford}`);

        if (!canAfford) {
            console.log(`[HangarManager] Purchase failed - insufficient tokens. Required: ${cost}, Available: ${currentBalance}`);
            return { success: false, reason: 'insufficient_tokens', cost: cost, balance: currentBalance };
        }

        // Spend tokens
        console.log(`[HangarManager] Attempting to spend ${cost} tokens for ${upgradeId}`);
        const spendResult = this.tokenEconomyManager.spendTokens(cost, 'hangar_upgrade', {
            upgradeId: upgradeId,
            upgradeLevel: currentLevel + 1,
            upgradeName: upgrade.name
        });

        console.log(`[HangarManager] Spend result:`, spendResult);

        if (!spendResult.success) {
            console.log(`[HangarManager] Transaction failed:`, spendResult.reason);
            return { success: false, reason: 'transaction_failed' };
        }

        // Apply upgrade
        this.playerUpgrades[upgradeId] = currentLevel + 1;
        console.log('Post-increment level for hull_plating:', this.playerUpgrades['hull_plating']);
        this.saveUpgrades();

        // Notify listeners
        console.log('Notifying for hull_plating level:', currentLevel + 1);
        this.notifyUpgradeListeners(upgradeId, currentLevel + 1);
        document.dispatchEvent(new CustomEvent('token-transaction', { detail: spendResult }));

        return {
            success: true,
            upgradeId: upgradeId,
            newLevel: currentLevel + 1,
            cost: cost,
            transactionId: spendResult.transactionId
        };
    }


    /**
     * Calculate the effective stats for the player ship based on upgrades
     * @returns {object} Calculated stats
     */
    calculatePlayerStats() {
        const stats = {};
        
        for (const [key, upgrade] of Object.entries(PLAYER_UPGRADES)) {
            const level = this.playerUpgrades[upgrade.id];
            const progress = level / upgrade.maxLevel;
            
            for (const stat of upgrade.affects) {
                const baseValue = upgrade.baseValues[stat];
                const maxMultiplier = upgrade.maxMultiplier;
                
                if (stat === 'fireRate') {
                    // For fire rate, lower is better (faster firing)
                    stats[stat] = baseValue * (1 - (progress * (1 - maxMultiplier)));
                } else {
                    // For other stats, higher is better
                    stats[stat] = baseValue * (1 + (progress * (maxMultiplier - 1)));
                }
            }
        }
        
        return stats;
    }


    /**
     * Get upgrade information for UI display
     * @returns {Array} Array of upgrade information objects
     */
    getUpgradeInfo() {
        const upgrades = PLAYER_UPGRADES;
        const levels = this.playerUpgrades;
        
        return Object.values(upgrades).map(upgrade => {
            const currentLevel = levels[upgrade.id];
            const nextCost = this.calculateUpgradeCost(upgrade, currentLevel);
            const canAfford = this.tokenEconomyManager.canAfford(nextCost);
            const isMaxLevel = currentLevel >= upgrade.maxLevel;
            
            return {
                id: upgrade.id,
                name: upgrade.name,
                description: upgrade.description,
                currentLevel: currentLevel,
                maxLevel: upgrade.maxLevel,
                nextCost: isMaxLevel ? 0 : nextCost,
                canAfford: canAfford,
                isMaxLevel: isMaxLevel,
                affects: upgrade.affects
            };
        });
    }

    /**
     * Add listener for upgrade events
     * @param {function} listener - Callback function
     */
    addUpgradeListener(listener) {
        this.upgradeListeners.push(listener);
    }

    /**
     * Remove upgrade listener
     * @param {function} listener - Callback function to remove
     */
    removeUpgradeListener(listener) {
        const index = this.upgradeListeners.indexOf(listener);
        if (index > -1) {
            this.upgradeListeners.splice(index, 1);
        }
    }

    /**
     * Notify all upgrade listeners of changes
     * @param {string} upgradeId - ID of upgraded item
     * @param {number} newLevel - New upgrade level
     */
    notifyUpgradeListeners(upgradeId, newLevel) {
        console.log('Notifying upgrade listeners for:', upgradeId, 'level:', newLevel);
        for (const listener of this.upgradeListeners) {
            try {
                listener(upgradeId, newLevel);
            } catch (error) {
                console.error('Error in upgrade listener:', error);
            }
        }
    }

    /**
     * Get total upgrade investment for display
     * @returns {object} Investment summary
     */
    getUpgradeInvestment() {
        let totalSpent = 0;
        let playerLevels = 0;
        
        // Calculate player upgrade investment
        for (const [key, upgrade] of Object.entries(PLAYER_UPGRADES)) {
            const level = this.playerUpgrades[upgrade.id];
            playerLevels += level;
            
            for (let i = 0; i < level; i++) {
                totalSpent += this.calculateUpgradeCost(upgrade, i);
            }
        }
        
        return {
            totalSpent: totalSpent,
            playerUpgradeLevels: playerLevels,
            totalUpgradeLevels: playerLevels
        };
    }
}

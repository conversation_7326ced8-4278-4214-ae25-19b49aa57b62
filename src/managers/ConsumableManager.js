import { ConsumableFactory } from '../systems/Consumable.js';

/**
 * ConsumableManager - Manages player's consumable inventory and activation
 * Handles purchase, storage, activation, and persistence of consumable items
 */
export class ConsumableManager {
    constructor(tokenManager, gameEngine) {
        this.tokenManager = tokenManager;
        this.gameEngine = gameEngine;
        
        // Player inventory - can only hold one consumable at a time
        this.currentConsumable = null;
        
        // Available consumables for purchase
        this.availableConsumables = ConsumableFactory.createAllConsumables();
        
        // Activation state
        this.isActivating = false;
        this.lastActivationTime = 0;
        this.activationCooldown = 1000; // 1 second cooldown between activations
        
        // Callbacks for UI updates
        this.onInventoryUpdateCallback = null;
        this.onConsumableUsedCallback = null;
        
        // Load saved inventory
        this.loadInventory();
    }
    
    /**
     * Purchase a consumable and add it to inventory
     * @param {string} consumableType - Type of consumable to purchase
     * @returns {object} Purchase result
     */
    addConsumableToInventory(consumableType) {
        // Check if player already has a consumable
        if (this.currentConsumable) {
            return {
                success: false,
                reason: 'inventory_full',
                message: 'You can only carry one consumable at a time. Use your current consumable first.'
            };
        }
        
        // Find the consumable template
        const consumableTemplate = this.availableConsumables.find(c => c.type === consumableType);
        if (!consumableTemplate) {
            return {
                success: false,
                reason: 'invalid_consumable',
                message: 'Unknown consumable type'
            };
        }
        
        // Create new consumable instance and add to inventory
        this.currentConsumable = consumableTemplate.clone();
        this.saveInventory();
        
        // Trigger inventory update callback
        this.triggerInventoryUpdate();
        
        console.log(`Added consumable to inventory: ${consumableType}`);
        
        return {
            success: true,
            consumable: this.currentConsumable
        };
    }
    
    /**
     * Activate the current consumable
     * @returns {Promise<object>} Activation result
     */
    async activateConsumable() {
        // Check if player has a consumable
        if (!this.currentConsumable) {
            return {
                success: false,
                reason: 'no_consumable',
                message: 'No consumable available'
            };
        }
        
        // Check if already activating
        if (this.isActivating) {
            return {
                success: false,
                reason: 'already_activating',
                message: 'Consumable activation in progress'
            };
        }
        
        // Check cooldown
        const now = Date.now();
        if (now - this.lastActivationTime < this.activationCooldown) {
            return {
                success: false,
                reason: 'cooldown',
                message: 'Consumable activation on cooldown'
            };
        }
        
        // Check if consumable is already used
        if (this.currentConsumable.isUsed) {
            return {
                success: false,
                reason: 'already_used',
                message: 'Consumable already used'
            };
        }
        
        this.isActivating = true;
        this.lastActivationTime = now;
        
        try {
            // Activate the consumable
            const activationResult = await this.currentConsumable.activate(this.gameEngine);
            
            if (activationResult) {
                // Remove consumable from inventory after successful use
                const usedConsumable = this.currentConsumable;
                this.currentConsumable = null;
                this.saveInventory();
                
                // Trigger callbacks
                this.triggerInventoryUpdate();
                this.triggerConsumableUsed(usedConsumable);
                
                console.log(`Activated consumable: ${usedConsumable.type}`);
                
                return {
                    success: true,
                    consumable: usedConsumable,
                    message: `${usedConsumable.name} activated successfully!`
                };
            } else {
                return {
                    success: false,
                    reason: 'activation_failed',
                    message: 'Failed to activate consumable'
                };
            }
        } catch (error) {
            console.error('Error activating consumable:', error);
            return {
                success: false,
                reason: 'activation_error',
                message: 'Error occurred during activation'
            };
        } finally {
            this.isActivating = false;
        }
    }
    
    /**
     * Get current consumable info
     * @returns {object|null} Current consumable or null
     */
    getCurrentConsumable() {
        return this.currentConsumable;
    }
    
    /**
     * Check if player has a consumable
     * @returns {boolean} True if player has a consumable
     */
    hasConsumable() {
        return this.currentConsumable !== null;
    }
    
    /**
     * Get available consumables for purchase
     * @returns {Array} Array of available consumables
     */
    getAvailableConsumables() {
        return this.availableConsumables;
    }
    
    /**
     * Save inventory to localStorage
     */
    saveInventory() {
        try {
            const inventoryData = {
                currentConsumable: this.currentConsumable ? {
                    type: this.currentConsumable.type,
                    isUsed: this.currentConsumable.isUsed,
                    usedAt: this.currentConsumable.usedAt
                } : null
            };
            
            localStorage.setItem('consumable_inventory', JSON.stringify(inventoryData));
        } catch (error) {
            console.warn('Failed to save consumable inventory:', error);
        }
    }
    
    /**
     * Load inventory from localStorage
     */
    loadInventory() {
        try {
            const savedData = localStorage.getItem('consumable_inventory');
            if (savedData) {
                const inventoryData = JSON.parse(savedData);
                
                if (inventoryData.currentConsumable) {
                    // Recreate consumable from saved data
                    this.currentConsumable = ConsumableFactory.createConsumable(inventoryData.currentConsumable.type);
                    if (this.currentConsumable) {
                        this.currentConsumable.isUsed = inventoryData.currentConsumable.isUsed;
                        this.currentConsumable.usedAt = inventoryData.currentConsumable.usedAt;
                    }
                }
            }
        } catch (error) {
            console.warn('Failed to load consumable inventory:', error);
            this.currentConsumable = null;
        }
    }
    
    /**
     * Reset inventory (for new game)
     */
    reset() {
        this.currentConsumable = null;
        this.isActivating = false;
        this.lastActivationTime = 0;
        this.saveInventory();
        this.triggerInventoryUpdate();
    }
    
    /**
     * Set callback for inventory updates
     * @param {Function} callback - Callback function
     */
    setOnInventoryUpdate(callback) {
        this.onInventoryUpdateCallback = callback;
    }
    
    /**
     * Set callback for consumable used events
     * @param {Function} callback - Callback function
     */
    setOnConsumableUsed(callback) {
        this.onConsumableUsedCallback = callback;
    }
    
    /**
     * Trigger inventory update callback
     */
    triggerInventoryUpdate() {
        if (this.onInventoryUpdateCallback) {
            this.onInventoryUpdateCallback(this.currentConsumable);
        }
    }
    
    /**
     * Trigger consumable used callback
     * @param {Consumable} consumable - The used consumable
     */
    triggerConsumableUsed(consumable) {
        if (this.onConsumableUsedCallback) {
            this.onConsumableUsedCallback(consumable);
        }
    }
}

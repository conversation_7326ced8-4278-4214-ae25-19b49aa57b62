/**
 * ETHTestModeManager - Manages ETH test mode for WISH token testing
 * Intercepts token operations and substitutes ETH balance for testing purposes
 */
export class ETHTestModeManager {
    constructor(tokenManager) {
        this.tokenManager = tokenManager;
        this.isTestMode = false;
        this.originalGetBalance = null;
        this.originalSpendTokens = null;
        this.originalAwardTokens = null;
        this.originalCanAfford = null;
        this.originalGetBalanceDisplay = null;
    }
    
    /**
     * Enable test mode - substitute ETH balance for WISH tokens
     */
    async enableTestMode() {
        if (this.isTestMode) return;
        
        console.log('🧪 Enabling ETH Test Mode...');
        
        // Check if wallet is connected and verify chain ID
        try {
            const walletProvider = window.ethereum;
            
            if (walletProvider && walletProvider.request) {
                console.log('🔍 Checking current chain for ETH Test Mode...');
                
                const currentChainId = await walletProvider.request({ method: 'eth_chainId' });
                const currentChainIdDecimal = parseInt(currentChainId, 16);
                
                console.log(`🔗 Current Chain ID: ${currentChainIdDecimal}`);
                
                // Verify we're on the correct chain for testing
                if (currentChainIdDecimal === 31337) {
                    console.log('✅ Connected to Hardhat local development network (Chain ID: 31337)');
                } else if (currentChainIdDecimal === 1337) {
                    console.log('✅ Connected to Ganache local development network (Chain ID: 1337)');
                } else if (currentChainIdDecimal === 1) {
                    console.warn('⚠️ Connected to Ethereum Mainnet (Chain ID: 1) - ETH Test Mode expects local development network');
                    console.warn('⚠️ Please switch to Chain ID 31337 (Hardhat) or 1337 (Ganache) for proper testing');
                } else {
                    console.log(`⚠️ Connected to network with Chain ID: ${currentChainIdDecimal} (expected 31337 or 1337 for local development)`);
                }
                
                // Ensure wallet is properly initialized
                if (!this.tokenManager.walletConnected) {
                    console.log('🔄 Wallet not connected, attempting to initialize...');
                    this.tokenManager.initializeWallet();
                }
                
                // Sync wallet balance to ensure we have the latest ETH balance
                if (this.tokenManager.walletConnected) {
                    console.log('🔄 Syncing wallet balance for ETH Test Mode...');
                    await this.tokenManager.syncWalletBalance();
                    console.log('✅ Wallet balance synced:', this.tokenManager.getWalletBalance(), 'ETH');
                } else {
                    console.warn('⚠️ Wallet still not connected after initialization attempt');
                }
                
            } else if (this.tokenManager.debugMode) {
                // Allow debug mode to enable ETH Test Mode without MetaMask
                console.log('🐛 Debug mode: Enabling ETH Test Mode without MetaMask');
                console.log('🐛 Using debug wallet balance for ETH Test Mode');
                
                // Ensure wallet is properly initialized in debug mode
                if (!this.tokenManager.walletConnected) {
                    console.log('🔄 Debug wallet not connected, attempting to initialize...');
                    this.tokenManager.initializeWallet();
                }
                
                // Sync debug wallet balance
                if (this.tokenManager.walletConnected) {
                    console.log('🔄 Syncing debug wallet balance for ETH Test Mode...');
                    await this.tokenManager.syncWalletBalance();
                    console.log('✅ Debug wallet balance synced:', this.tokenManager.getWalletBalance(), 'ETH');
                } else {
                    console.warn('⚠️ Debug wallet still not connected after initialization attempt');
                }
                
            } else {
                console.warn('⚠️ MetaMask not detected - ETH Test Mode requires MetaMask connection via OrangeID');
                console.warn('⚠️ Please connect your wallet using OrangeID first');
                return;
            }
        } catch (error) {
            console.error('❌ Failed to verify chain for ETH Test Mode:', error);
            console.log('❌ Not enabling ETH Test Mode due to verification failure');
            return;
        }
        
        this.isTestMode = true;
        
        console.log('✅ ETH Test Mode enabled');
        
        // Store original methods (only the ones we actually override)
        this.originalGetBalance = this.tokenManager.getBalance;
        this.originalCanAfford = this.tokenManager.canAfford;
        this.originalGetBalanceDisplay = this.tokenManager.getBalanceDisplay;
        
        // Override getBalance to return ETH balance instead of WISH balance
        this.tokenManager.getBalance = () => {
            const ethBalance = this.tokenManager.getWalletBalance();
            console.log(`🧪 Test Mode: Returning ETH balance ${ethBalance} instead of WISH balance`);
            return ethBalance; // Returns ETH balance instead of WISH balance
        };
        
        // Override getBalanceDisplay to show ETH instead of WISH
        this.tokenManager.getBalanceDisplay = () => {
            const ethBalance = this.tokenManager.getWalletBalance();
            const display = `${ethBalance.toFixed(4)} ETH (Test Mode)`;
            console.log(`🧪 Test Mode Balance Display: ${display}`);
            return display;
        };
        
        // Override canAfford to check ETH balance instead of WISH balance
        this.tokenManager.canAfford = async (amount) => {
            const ethBalance = this.tokenManager.getWalletBalance();
            return ethBalance >= amount;
        };
        
        // Log that test mode is active and ready
        console.log('✅ ETH Test Mode: Balance checks now use ETH instead of WISH tokens');
        console.log('✅ ETH Test Mode: Transactions will process real ETH via wallet provider');
        
        // NOTE: We do NOT override spendTokens or awardTokens
        // The existing transaction flow should handle ETH transactions naturally
        // by using the overridden getBalance() and canAfford() methods above
        
        console.log('✅ ETH Test Mode enabled - using ETH balance as WISH tokens');
    }
    
    /**
     * Disable test mode - restore original token manager methods
     */
    disableTestMode() {
        if (!this.isTestMode) return;
        
        this.isTestMode = false;
        
        // Restore original methods (only the ones we actually override)
        if (this.originalGetBalance) {
            this.tokenManager.getBalance = this.originalGetBalance;
        }
        if (this.originalCanAfford) {
            this.tokenManager.canAfford = this.originalCanAfford;
        }
        if (this.originalGetBalanceDisplay) {
            this.tokenManager.getBalanceDisplay = this.originalGetBalanceDisplay;
        }
        
        console.log('ETH Test Mode disabled - restored WISH token system');
    }
    
    /**
     * Apply test mode discount to base price (90% discount)
     * @param {number} basePrice - Original price
     * @returns {number} Discounted price
     */
    applyTestModeDiscount(basePrice) {
        return Math.round(basePrice * 0.1); // 90% discount
    }
    
    /**
     * Toggle test mode on/off
     */
    toggleTestMode() {
        if (this.isTestMode) {
            this.disableTestMode();
        } else {
            this.enableTestMode();
        }
        return this.isTestMode;
    }
    
    /**
     * Get current test mode status
     * @returns {boolean} True if test mode is enabled
     */
    getTestModeStatus() {
        return this.isTestMode;
    }
}
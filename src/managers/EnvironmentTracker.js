/**
 * EnvironmentTracker - Manages tracking and storage of generated environments
 * Handles environment creation tracking, user attribution, and environment listings
 * Now uses server-side storage instead of localStorage
 */
import { urlConfig } from '../utils/UrlConfig.js';

export class EnvironmentTracker {
    constructor() {
        // Environment storage (cache)
        this.environments = new Map(); // Map of environment ID to environment data
        this.userEnvironments = new Map(); // Map of user ID to array of environment IDs
        this.environmentStats = new Map(); // Map of environment ID to usage stats
        
        // Server API endpoint
        this.serverUrl = urlConfig.getApiBaseUrl();
        
        // Load initial data from server
        this.loadFromServer();
    }
    
    /**
     * Calculate environment price based on difficulty and popularity
     * @param {object} environment - Environment object
     * @returns {number} Price in tokens
     */
    calculateEnvironmentPrice(environment) {
        const basePrices = {
            1: 5000,   // Easy (100x multiplier applied)
            2: 7500,   // Easy-Medium (100x multiplier applied)
            3: 10000,  // Medium (100x multiplier applied)
            4: 15000,  // Medium-Hard (100x multiplier applied)
            5: 20000   // Hard (100x multiplier applied)
        };

        const basePrice = basePrices[environment.difficulty] || 10000;

        // Add popularity bonus (more popular = more expensive)
        const popularityMultiplier = 1 + (environment.timesUsed * 0.01);

        return Math.round(basePrice * popularityMultiplier);
    }
    
    /**
     * Load all environment data from server
     */
    async loadFromServer() {
        try {
            const response = await fetch(`${this.serverUrl}/api/environments`);
            if (response.ok) {
                const environments = await response.json();
                
                // Clear local cache
                this.environments.clear();
                this.userEnvironments.clear();
                this.environmentStats.clear();
                
                // Populate local cache
                environments.forEach(env => {
                    this.environments.set(env.id, env);
                    
                    // Track user's environments
                    if (!this.userEnvironments.has(env.creatorUserId)) {
                        this.userEnvironments.set(env.creatorUserId, []);
                    }
                    this.userEnvironments.get(env.creatorUserId).push(env.id);
                    
                    // Initialize stats
                    this.environmentStats.set(env.id, {
                        views: env.timesUsed || 0,
                        purchases: env.totalPurchases || 0,
                        revenue: env.totalRevenue || 0,
                        ratings: [],
                        averageRating: 0
                    });
                });
                
                console.log(`Loaded ${environments.length} environments from server`);
            } else {
                console.error('Failed to load environments from server:', response.statusText);
            }
        } catch (error) {
            console.error('Error loading environments from server:', error);
        }
    }
    
    /**
     * Track a new environment creation
     * @param {object} environmentData - Environment data from EnvironmentCreator
     * @param {string} creatorUserId - ID of the user who created the environment
     * @param {string} imageFileName - Name of the saved image file
     * @returns {string} Environment ID
     */
    async trackEnvironment(environmentData, creatorUserId, imageFileName) {
        try {
            const response = await fetch(`${this.serverUrl}/api/environments`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    environmentData,
                    creatorUserId,
                    imageFileName
                })
            });
            
            if (!response.ok) {
                console.error('Failed to save environment to server:', response.statusText);
                return null;
            }
            
            const result = await response.json();
            
            if (result.success) {
                // Add to local cache
                this.environments.set(result.environmentId, result.environment);
                
                // Track user's environments
                if (!this.userEnvironments.has(creatorUserId)) {
                    this.userEnvironments.set(creatorUserId, []);
                }
                this.userEnvironments.get(creatorUserId).push(result.environmentId);
                
                // Initialize stats
                this.environmentStats.set(result.environmentId, {
                    views: 0,
                    purchases: 0,
                    revenue: 0,
                    ratings: [],
                    averageRating: 0
                });
                
                console.log(`Environment tracked: ${result.environmentId} by user ${creatorUserId}`);
                return result.environmentId;
            } else {
                console.error('Failed to track environment:', result.error);
                return null;
            }
        } catch (error) {
            console.error('Error tracking environment:', error);
            return null;
        }
    }
    
    /**
     * Get environment by ID
     * @param {string} environmentId - Environment ID
     * @returns {object|null} Environment data or null if not found
     */
    async getEnvironment(environmentId) {
        try {
            const response = await fetch(`${this.serverUrl}/api/environments/${environmentId}`);
            if (response.ok) {
                const environment = await response.json();
                // Calculate and add price to environment if it doesn't exist
                if (!environment.price) {
                    environment.price = this.calculateEnvironmentPrice(environment);
                }
                return environment;
            } else if (response.status === 404) {
                return null;
            } else {
                console.error('Failed to get environment from server:', response.statusText);
                return null;
            }
        } catch (error) {
            console.error('Error getting environment:', error);
            return null;
        }
    }
    
    /**
     * Get environments created by a specific user
     * @param {string} userId - User ID
     * @returns {Array} Array of environment objects
     */
    async getUserEnvironments(userId) {
        try {
            const response = await fetch(`${this.serverUrl}/api/environments/user/${userId}`);
            if (response.ok) {
                const environments = await response.json();
                return environments;
            } else {
                console.error('Failed to get user environments from server:', response.statusText);
                return [];
            }
        } catch (error) {
            console.error('Error getting user environments:', error);
            return [];
        }
    }
    
    /**
     * Get random selection of environments for genie menu
     * @param {number} count - Number of environments to return (default: 5)
     * @param {string} excludeUserId - User ID to exclude from selection (optional)
     * @returns {Array} Array of environment objects
     */
    async getRandomEnvironments(count = 5, excludeUserId = null) {
        try {
            const response = await fetch(`${this.serverUrl}/api/environments/random?count=${count}`);
            if (response.ok) {
                const environments = await response.json();
                // Calculate and add price to each environment if it doesn't exist
                environments.forEach(env => {
                    if (!env.price) {
                        env.price = this.calculateEnvironmentPrice(env);
                    }
                });
                return environments;
            } else {
                console.error('Failed to get random environments from server:', response.statusText);
                return [];
            }
        } catch (error) {
            console.error('Error getting random environments:', error);
            // Check if it's a network error
            if (error instanceof TypeError && error.message.includes('fetch')) {
                console.error('Network error: Could not connect to environment server. Please check if the server is running on ' + this.serverUrl);
            }
            return [];
        }
    }
    
    /**
     * Record environment usage
     * @param {string} environmentId - Environment ID
     * @param {string} userId - User who used the environment
     */
    async recordEnvironmentUsage(environmentId, userId) {
        try {
            const response = await fetch(`${this.serverUrl}/api/environments/${environmentId}/usage`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ userId })
            });
            
            if (response.ok) {
                // Update local cache
                const environment = this.environments.get(environmentId);
                if (environment) {
                    environment.timesUsed++;
                    environment.lastUsed = Date.now();
                }
                
                const stats = this.environmentStats.get(environmentId);
                if (stats) {
                    stats.views++;
                }
            } else {
                console.error('Failed to record environment usage:', response.statusText);
            }
        } catch (error) {
            console.error('Error recording environment usage:', error);
        }
    }
    
    /**
     * Record environment purchase
     * @param {string} environmentId - Environment ID
     * @param {string} purchaserUserId - User who purchased the environment
     * @param {number} cost - Cost of the purchase
     */
    async recordEnvironmentPurchase(environmentId, purchaserUserId, cost) {
        try {
            // Validate cost is a proper number before sending to server
            const numericCost = Number(cost);
            if (isNaN(numericCost) || numericCost <= 0) {
                console.error(`Invalid cost for environment purchase: ${cost}`);
                return false;
            }
            
            const response = await fetch(`${this.serverUrl}/api/environments/${environmentId}/purchase`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ purchaserUserId, cost: numericCost })
            });
            
            if (response.ok) {
                // Update local cache
                const environment = this.environments.get(environmentId);
                if (environment) {
                    environment.totalPurchases++;
                    environment.totalRevenue += numericCost;
                }
                
                const stats = this.environmentStats.get(environmentId);
                if (stats) {
                    stats.purchases++;
                    stats.revenue += numericCost;
                }
                
                console.log(`Environment ${environmentId} purchased by ${purchaserUserId} for ${numericCost} tokens`);
                return true;
            } else {
                console.error('Failed to record environment purchase:', response.statusText);
                return false;
            }
        } catch (error) {
            console.error('Error recording environment purchase:', error);
            return false;
        }
    }
    
    /**
     * Get environment statistics
     * @param {string} environmentId - Environment ID
     * @returns {object|null} Environment statistics or null if not found
     */
    getEnvironmentStats(environmentId) {
        return this.environmentStats.get(environmentId) || null;
    }
    
    /**
     * Get top environments by usage
     * @param {number} count - Number of top environments to return
     * @returns {Array} Array of environment objects sorted by usage
     */
    getTopEnvironments(count = 10) {
        return Array.from(this.environments.values())
            .filter(env => env.isActive)
            .sort((a, b) => b.timesUsed - a.timesUsed)
            .slice(0, count);
    }
    
    /**
     * Generate unique environment ID
     * @returns {string} Environment ID
     */
    generateEnvironmentId() {
        return `env_${this.nextEnvironmentId++}_${Date.now()}`;
    }
    
    /**
     * Calculate difficulty based on gameplay modifiers
     * @param {object} gameplayModifiers - Gameplay modifiers object
     * @returns {number} Difficulty score (1-5)
     */
    calculateDifficulty(gameplayModifiers) {
        let difficulty = 3; // Base difficulty
        
        // Calculate average modifier from enemyTypeModifiers
        const enemyTypeModifiers = gameplayModifiers.enemyTypeModifiers || {};
        const modifierValues = Object.values(enemyTypeModifiers).filter(val => typeof val === 'number');
        const averageModifier = modifierValues.length > 0 ?
            modifierValues.reduce((sum, val) => sum + val, 0) / modifierValues.length : 1.0;
        
        // Adjust difficulty based on average modifier
        difficulty += (averageModifier - 1.0) * 3;
        
        return Math.max(1, Math.min(5, Math.round(difficulty)));
    }
    
    /**
     * Shuffle array using Fisher-Yates algorithm
     * @param {Array} array - Array to shuffle
     * @returns {Array} Shuffled array
     */
    shuffleArray(array) {
        for (let i = array.length - 1; i > 0; i--) {
            const j = Math.floor(Math.random() * (i + 1));
            [array[i], array[j]] = [array[j], array[i]];
        }
        return array;
    }
    
    /**
     * Clean up old environments to prevent memory issues
     */
    cleanupOldEnvironments() {
        if (this.environments.size <= this.maxStoredEnvironments) return;
        
        // Get environments sorted by last used (oldest first)
        const sortedEnvironments = Array.from(this.environments.values())
            .sort((a, b) => (a.lastUsed || 0) - (b.lastUsed || 0));
        
        // Remove oldest environments
        const toRemove = sortedEnvironments.slice(0, this.environments.size - this.maxStoredEnvironments);
        toRemove.forEach(env => {
            this.environments.delete(env.id);
            this.environmentStats.delete(env.id);
            
            // Remove from user environments
            const userEnvs = this.userEnvironments.get(env.creatorUserId);
            if (userEnvs) {
                const index = userEnvs.indexOf(env.id);
                if (index > -1) {
                    userEnvs.splice(index, 1);
                }
            }
        });
        
        console.log(`Cleaned up ${toRemove.length} old environments`);
    }
    
    /**
     * Save data to localStorage
     */
    saveToStorage() {
        try {
            const data = {
                environments: Array.from(this.environments.entries()),
                userEnvironments: Array.from(this.userEnvironments.entries()),
                environmentStats: Array.from(this.environmentStats.entries()),
                nextEnvironmentId: this.nextEnvironmentId
            };
            
            localStorage.setItem('environmentTracker', JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save environment tracker data to localStorage:', error);
        }
    }
    
    /**
     * Load data from localStorage
     */
    loadFromStorage() {
        try {
            const data = localStorage.getItem('environmentTracker');
            if (!data) return;
            
            const parsed = JSON.parse(data);
            
            this.environments = new Map(parsed.environments || []);
            this.userEnvironments = new Map(parsed.userEnvironments || []);
            this.environmentStats = new Map(parsed.environmentStats || []);
            this.nextEnvironmentId = parsed.nextEnvironmentId || 1;
            
            console.log(`Loaded ${this.environments.size} environments from storage`);
        } catch (error) {
            console.warn('Failed to load environment tracker data from localStorage:', error);
        }
    }
    
    /**
     * Get summary statistics
     * @returns {object} Summary statistics
     */
    getSummaryStats() {
        const totalEnvironments = this.environments.size;
        const totalUsers = this.userEnvironments.size;
        const totalUsage = Array.from(this.environments.values()).reduce((sum, env) => sum + env.timesUsed, 0);
        const totalRevenue = Array.from(this.environments.values()).reduce((sum, env) => sum + env.totalRevenue, 0);
        
        return {
            totalEnvironments,
            totalUsers,
            totalUsage,
            totalRevenue,
            averageUsagePerEnvironment: totalEnvironments > 0 ? totalUsage / totalEnvironments : 0
        };
    }
}

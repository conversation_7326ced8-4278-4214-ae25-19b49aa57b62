/**
 * Reward<PERSON>anager - <PERSON>les creator rewards and revenue distribution
 * Manages the distribution of rewards to environment creators when their environments are purchased
 */
export class RewardManager {
    constructor(tokenManager) {
        this.tokenManager = tokenManager;
        
        // Reward configuration
        this.creatorRewardPercentage = 0.5; // 50% of purchase cost goes to creator
        this.platformFeePercentage = 0.1;   // 10% platform fee
        this.remainingPercentage = 0.4;     // 40% goes to general pool
        
        // Server configuration
        this.serverUrl = 'http://localhost:3001/api';
        
        // Reward tracking
        this.pendingRewards = new Map(); // Map of userId to pending reward amount
        this.rewardHistory = new Map();  // Map of userId to array of reward transactions
        this.totalRewardsDistributed = 0;
        this.totalPlatformFees = 0;
        
        // Load from storage
        this.loadFromStorage();
    }
    
    /**
     * Process a reward for an environment creator
     * @param {string} creatorUserId - ID of the environment creator
     * @param {string} purchaserUserId - ID of the user who purchased the environment
     * @param {string} environmentId - ID of the purchased environment
     * @param {number} purchaseAmount - Amount paid for the environment
     * @param {object} environmentData - Environment data for context
     * @returns {object} Reward processing result
     */
    processCreatorReward(creatorUserId, purchaserUserId, environmentId, purchaseAmount, environmentData = {}) {
        try {
            // Calculate reward amounts
            const creatorReward = Math.floor(purchaseAmount * this.creatorRewardPercentage);
            const platformFee = Math.floor(purchaseAmount * this.platformFeePercentage);
            const remainingAmount = purchaseAmount - creatorReward - platformFee;
            
            // Create reward transaction
            const rewardTransaction = {
                id: this.generateRewardId(),
                creatorUserId: creatorUserId,
                purchaserUserId: purchaserUserId,
                environmentId: environmentId,
                environmentName: environmentData.name || 'Unknown Environment',
                purchaseAmount: purchaseAmount,
                creatorReward: creatorReward,
                platformFee: platformFee,
                remainingAmount: remainingAmount,
                timestamp: Date.now(),
                status: 'pending',
                processedAt: null
            };
            
            // Add to pending rewards
            if (!this.pendingRewards.has(creatorUserId)) {
                this.pendingRewards.set(creatorUserId, 0);
            }
            this.pendingRewards.set(creatorUserId, this.pendingRewards.get(creatorUserId) + creatorReward);
            
            // Add to reward history
            if (!this.rewardHistory.has(creatorUserId)) {
                this.rewardHistory.set(creatorUserId, []);
            }
            this.rewardHistory.get(creatorUserId).push(rewardTransaction);
            
            // Update totals
            this.totalPlatformFees += platformFee;
            
            // Save to storage
            this.saveToStorage();
            
            console.log(`Creator reward processed: ${creatorReward} tokens for ${creatorUserId}`);
            
            return {
                success: true,
                transaction: rewardTransaction,
                creatorReward: creatorReward,
                platformFee: platformFee,
                remainingAmount: remainingAmount
            };
            
        } catch (error) {
            console.error('Error processing creator reward:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }
    
    /**
     * Distribute pending rewards to a creator
     * @param {string} creatorUserId - ID of the creator to pay
     * @returns {object} Distribution result
     */
    async distributePendingRewards(creatorUserId) {
        try {
            const pendingAmount = this.pendingRewards.get(creatorUserId) || 0;
            
            if (pendingAmount <= 0) {
                return {
                    success: false,
                    reason: 'no_pending_rewards',
                    message: 'No pending rewards to distribute'
                };
            }
            
            // Use server API to distribute rewards to the creator
            console.log(`💰 Requesting distribution of ${pendingAmount} ETH to creator ${creatorUserId} via secure server API`);
            
            // Call server API to distribute rewards
            const response = await fetch(`${this.serverUrl}/rewards/distribute`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer secure-token-for-development'
                },
                body: JSON.stringify({
                    creatorUserId: creatorUserId,
                    amount: pendingAmount.toString(),
                    reason: `creator_reward_${creatorUserId}`
                })
            });
            
            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Server API call failed: ${response.status} ${response.statusText} - ${errorData.error || 'Unknown error'}`);
            }
            
            const serverResult = await response.json();
            
            if (serverResult && serverResult.success) {
                // Also award tokens to creator's game balance (for tracking) via server API
                const awardResponse = await fetch(`${this.serverUrl}/tokens/award`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': 'Bearer secure-token-for-development'
                    },
                    body: JSON.stringify({
                        userId: creatorUserId,
                        amount: pendingAmount.toString(),
                        reason: 'creator_reward',
                        metadata: {
                            creatorUserId: creatorUserId,
                            rewardAmount: pendingAmount,
                            blockchainHash: serverResult.walletTransaction.transactionHash,
                            fromHotWallet: true
                        }
                    })
                });
                
                let awardResult = null;
                if (awardResponse.ok) {
                    awardResult = await awardResponse.json();
                }
                
                // Update pending rewards
                this.pendingRewards.set(creatorUserId, 0);
                
                // Update reward history status
                const creatorHistory = this.rewardHistory.get(creatorUserId) || [];
                creatorHistory.forEach(transaction => {
                    if (transaction.status === 'pending') {
                        transaction.status = 'distributed';
                        transaction.processedAt = Date.now();
                        transaction.blockchainHash = serverResult.walletTransaction.transactionHash;
                        transaction.distributedVia = 'server_api';
                    }
                });
                
                // Update totals
                this.totalRewardsDistributed += pendingAmount;
                
                // Save to storage
                this.saveToStorage();
                
                console.log(`✅ Distributed ${pendingAmount} ETH to creator ${creatorUserId} via secure server API`);
                
                return {
                    success: true,
                    amount: pendingAmount,
                    newBalance: awardResult ? awardResult.newBalance : this.tokenManager.getBalance(),
                    blockchainHash: serverResult.walletTransaction.transactionHash,
                    transactionId: serverResult.distributionId,
                    serverTransactionId: serverResult.walletTransaction.transactionId
                };
            } else {
                throw new Error(serverResult?.message || 'Failed to distribute rewards via server API');
            }
            
        } catch (error) {
            console.error('Error distributing pending rewards:', error);
            return {
                success: false,
                reason: 'distribution_error',
                message: error.message
            };
        }
    }
    
    /**
     * Get pending rewards for a creator
     * @param {string} creatorUserId - ID of the creator
     * @returns {number} Pending reward amount
     */
    getPendingRewards(creatorUserId) {
        return this.pendingRewards.get(creatorUserId) || 0;
    }
    
    /**
     * Get reward history for a creator
     * @param {string} creatorUserId - ID of the creator
     * @returns {Array} Array of reward transactions
     */
    getRewardHistory(creatorUserId) {
        return this.rewardHistory.get(creatorUserId) || [];
    }
    
    /**
     * Get total earnings for a creator
     * @param {string} creatorUserId - ID of the creator
     * @returns {object} Earnings summary
     */
    getCreatorEarnings(creatorUserId) {
        const history = this.getRewardHistory(creatorUserId);
        const pending = this.getPendingRewards(creatorUserId);
        
        const totalEarned = history.reduce((sum, transaction) => {
            return sum + (transaction.status === 'distributed' ? transaction.creatorReward : 0);
        }, 0);
        
        const totalPending = pending;
        const totalTransactions = history.length;
        
        return {
            totalEarned: totalEarned,
            totalPending: totalPending,
            totalLifetime: totalEarned + totalPending,
            totalTransactions: totalTransactions,
            averageReward: totalTransactions > 0 ? (totalEarned + totalPending) / totalTransactions : 0
        };
    }
    
    /**
     * Get top earning creators
     * @param {number} limit - Number of top creators to return
     * @returns {Array} Array of creator earnings data
     */
    getTopCreators(limit = 10) {
        const creatorEarnings = [];
        
        // Calculate earnings for all creators
        for (const creatorUserId of this.rewardHistory.keys()) {
            const earnings = this.getCreatorEarnings(creatorUserId);
            creatorEarnings.push({
                creatorUserId: creatorUserId,
                ...earnings
            });
        }
        
        // Sort by total lifetime earnings and return top creators
        return creatorEarnings
            .sort((a, b) => b.totalLifetime - a.totalLifetime)
            .slice(0, limit);
    }
    
    /**
     * Auto-distribute rewards for creators with significant pending amounts
     * @param {number} threshold - Minimum pending amount to trigger auto-distribution
     * @returns {Array} Array of distribution results
     */
    autoDistributeRewards(threshold = 100) {
        const results = [];
        
        for (const [creatorUserId, pendingAmount] of this.pendingRewards.entries()) {
            if (pendingAmount >= threshold) {
                const result = this.distributePendingRewards(creatorUserId);
                results.push({
                    creatorUserId: creatorUserId,
                    ...result
                });
            }
        }
        
        return results;
    }
    
    /**
     * Generate unique reward ID
     * @returns {string} Reward ID
     */
    generateRewardId() {
        return `reward_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    
    /**
     * Get system-wide reward statistics
     * @returns {object} Reward system statistics
     */
    getSystemStats() {
        const totalCreators = this.rewardHistory.size;
        const totalPendingRewards = Array.from(this.pendingRewards.values()).reduce((sum, amount) => sum + amount, 0);
        const totalTransactions = Array.from(this.rewardHistory.values()).reduce((sum, history) => sum + history.length, 0);
        
        return {
            totalCreators: totalCreators,
            totalRewardsDistributed: this.totalRewardsDistributed,
            totalPendingRewards: totalPendingRewards,
            totalPlatformFees: this.totalPlatformFees,
            totalTransactions: totalTransactions,
            averageRewardPerTransaction: totalTransactions > 0 ? (this.totalRewardsDistributed + totalPendingRewards) / totalTransactions : 0,
            creatorRewardPercentage: this.creatorRewardPercentage,
            platformFeePercentage: this.platformFeePercentage
        };
    }
    
    /**
     * Save data to localStorage
     */
    saveToStorage() {
        try {
            const data = {
                pendingRewards: Array.from(this.pendingRewards.entries()),
                rewardHistory: Array.from(this.rewardHistory.entries()),
                totalRewardsDistributed: this.totalRewardsDistributed,
                totalPlatformFees: this.totalPlatformFees
            };
            
            localStorage.setItem('rewardManager', JSON.stringify(data));
        } catch (error) {
            console.warn('Failed to save reward manager data to localStorage:', error);
        }
    }
    
    /**
     * Load data from localStorage
     */
    loadFromStorage() {
        try {
            const data = localStorage.getItem('rewardManager');
            if (!data) return;
            
            const parsed = JSON.parse(data);
            
            this.pendingRewards = new Map(parsed.pendingRewards || []);
            this.rewardHistory = new Map(parsed.rewardHistory || []);
            this.totalRewardsDistributed = parsed.totalRewardsDistributed || 0;
            this.totalPlatformFees = parsed.totalPlatformFees || 0;
            
            console.log(`Loaded reward data for ${this.rewardHistory.size} creators`);
        } catch (error) {
            console.warn('Failed to load reward manager data from localStorage:', error);
        }
    }
}

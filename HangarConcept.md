# Hangar: Persistent Upgrade System

## 1. Overview

The Hangar is a new game screen accessible from the main menu. It serves as the central hub for players to spend WISH tokens on **permanent, persistent upgrades** for their Player Ship and Wingman. This system is designed to provide a deep and rewarding progression path, giving players a tangible sense of growing power and a strong incentive to earn and spend WISH tokens.

By investing in their ship, players can achieve higher scores, clear difficult levels more easily, and gain a significant competitive advantage in OrangeSDK tournaments.

## 2. Core Objectives

*   **Create Incremental Progress:** Give players a feeling of constant growth and improvement, session after session.
*   **Drive Token Velocity:** Establish a primary, high-value sink for WISH tokens, encouraging both earning and purchasing.
*   **Enhance Strategic Depth:** Allow players to customize their ship's capabilities to match their playstyle.
*   **Boost Player Retention:** Provide a long-term goal for players to work towards, keeping them engaged with the game.

## 3. Player Ship Upgrades

The Player Ship will have a dedicated section in the Hangar with the following upgradeable attributes. Each attribute will have multiple tiers (e.g., 10 levels), with the WISH token cost increasing exponentially with each tier.

| Attribute | Code Reference (`PlayerShip.js`) | Description | Benefit for Player |
| :--- | :--- | :--- | :--- |
| **Engine Tuning** | `maxSpeed`, `acceleration` | Increases the ship's top speed (up to 2x normal at maximum stat upgrade) and how quickly it reaches that speed. Also adds a fine control key that allows the player to move at normal speed while the key is pressed. | Improved mobility, easier to dodge projectiles and collect power-ups. |
| **Hull Plating** | `maxHealth` | Increases the ship's maximum health, allowing it to withstand more damage. | Increased survivability, higher chance of achieving "perfect completion" bonuses. |
| **Reactor Core** | `weaponSystem.fireRate` | Permanently decreases the delay between shots, like the rapid-fire power-up, increasing the rate of fire. Does not stack with in game rapid-fire. | Higher damage-per-second (DPS), faster enemy clearing. |
| **Firepower**| `weaponSystem.projectileDamage` | Increases the damage dealt by each projectile. | Higher DPS, essential for taking down high-health enemies and bosses. |
| **Extra Lives** | `maxLives` | Increases the number of lives a player starts with (Up to 5 maximum with full upgrade). | More chances to complete difficult levels and achieve high scores. |

## 4. Wingman Upgrades

The Wingman is a critical support unit and can be upgraded separately, providing another strategic layer for token investment.

| Attribute | Code Reference (`WingmanShip.js`) | Description | Benefit for Player |
| :--- | :--- | :--- | :--- |
| **Wingman Armor** | `maxHealth` | Increases the Wingman's survivability in combat. | Wingman lasts longer, providing more consistent fire support. |
| **Wingman FirePower**| `fireRate` | Increases the Wingman's rate of fire. | Increased overall DPS for the player. |

## 5. Economic Impact

The Hangar system transforms the WISH token from a session-based convenience into a long-term investment asset.

*   **Creates a Powerful Token Sink:** The tiered, increasingly expensive upgrades will create a continuous demand for WISH tokens.
*   **Incentivizes High-Performance Play:** Players will be motivated to play well to earn more tokens for upgrades.
*   **Drives Token Purchases:** To gain a competitive edge in tournaments, players will be strongly incentivized to purchase WISH tokens to accelerate their progression.

This system directly addresses the core objective of giving players a compelling reason to spend WISH tokens to compete in external tournaments. A fully upgraded ship will be a formidable force, capable of achieving scores that are simply out of reach for a non-upgraded ship. The enemies should also 
import { Powerups, POWER_TYPES } from './powerups.js';
import { play, SFX } from './sound.js';

export class Game {
  constructor(canvas) {
    this.c = canvas;
    this.ctx = canvas.getContext('2d');
    this.w = canvas.width; this.h = canvas.height;

    // Game state
    this.resetAll();

    // Timing
    this.last = 0;
    this.timeScale = 1;
    this.t = 0;
    this.nextDive = 2.5;
  }

  resetAll() {
    this.player = { 
      x: this.w/2, 
      y: this.h - 60, 
      w: 36, 
      h: 20, 
      speed: 360, 
      lives: 3, 
      hp: 3, // 3 hits per life
      maxHp: 3,
      invuln: 0, 
      shield: 0, 
      fireCd: 0, 
      bombs: 1, 
      score: 0 
    };
    this.bullets = [];
    this.enemyBullets = [];
    this.enemies = [];
    this.explosions = [];
    this.effects = [];
    this.powerups = [];
    this.level = 1;
    this.state = 'playing';
    this.scroll = 0;
    this.power = Powerups.none();
    this.timeScale = 1;
    this.t = 0;
    this.nextDive = 2.0;
    this.moveX = 0;
    this.moveY = 0;
    this.spawnWave();
  }

  spawnWave() {
    const cols = Math.min(10, 6 + this.level);
    const rows = Math.min(5, 3 + Math.floor(this.level/2));
    const spacingX = 60, spacingY = 50;
    const startX = (this.w - (cols-1)*spacingX)/2;
    const startY = 100;
    this.enemies.length = 0;
    for (let r=0;r<rows;r++){
      for (let c=0;c<cols;c++){
        const type = (r % 3 === 0) ? 'tank' : (r % 2 === 0 ? 'shooter' : 'basic');
        this.enemies.push({
          x: startX + c*spacingX,
          y: startY + r*spacingY,
          w: 32, h: 22,
          vx: 40 + this.level * 6,
          type,
          hp: type==='tank'?3:(type==='shooter'?2:1),
          fireCd: 1000 + Math.random()*1200,
          baseY: startY + r*spacingY,
          bobAmp: 6 + Math.random()*8,
          bobPhase: Math.random()*Math.PI*2,
          state: 'formation'
        });
      }
    }
    this.waveDir = 1;
    this.edgeReached = false;
    play(SFX.START);
  }

  onResize() {
    // Keep fixed internal resolution but scale via CSS; nothing to do here.
  }

  grantPower(type) {
    this.power = Powerups.create(type);
    this.power.start(this);
  }

  update(ts) {
    if (!this.last) this.last = ts;
    let dt = Math.min(1/30, (ts - this.last)/1000);
    this.last = ts;
    dt *= this.timeScale;
    this.t += dt;
    if (this.state !== 'playing') return;

    const p = this.player;
    // Timers
    p.invuln = Math.max(0, p.invuln - dt);
    p.shield = Math.max(0, p.shield - dt);
    if (this.power.tick) this.power.tick(this, dt);

    // Movement (set by input system)
    const dx = (this.moveX || 0) * p.speed * dt;
    const dy = (this.moveY || 0) * p.speed * dt;
    p.x = Math.max(20, Math.min(this.w-20, p.x + dx));
    p.y = Math.max(this.h - 100, Math.min(this.h - 20, p.y + dy));

    // Shooting
    p.fireCd -= dt;
    if (this.wantFire && p.fireCd <= 0) {
      this.fire();
    }

    // Update bullets
    this.bullets = this.bullets.filter(b => {
      b.x += b.vx * dt; b.y += b.vy * dt;
      b.life -= dt;
      return b.y > -30 && b.y < this.h + 30 && b.life > 0;
    });

    // Enemies movement
    let minX = Infinity, maxX = -Infinity;
    this.enemies.forEach(e => {
      if (e.state === 'formation') {
        e.x += e.vx * dt * this.waveDir;
        e.y = e.baseY + Math.sin(this.t*2 + e.bobPhase) * e.bobAmp;
      } else if (e.state === 'dive') {
        const dx = this.player.x - e.x, dy = this.player.y - e.y;
        const len = Math.hypot(dx,dy) || 1;
        e.vx += (dx/len) * 180 * dt;
        e.vy += (dy/len) * 180 * dt;
        e.x += e.vx * dt; e.y += e.vy * dt;
        if (e.y > this.h + 40 || e.x<-40 || e.x>this.w+40) {
          e.state = 'formation'; e.baseY = 80 + Math.random()*60; e.y = e.baseY; e.vx = 40 + this.level*6; e.vy = 0;
        }
      }
      minX = Math.min(minX, e.x - e.w/2);
      maxX = Math.max(maxX, e.x + e.w/2);
      e.fireCd -= dt*1000;
      if (e.type !== 'basic' && e.fireCd <= 0) {
        e.fireCd = 1000 + Math.random()*1400 - this.level*20;
        const tx = this.player.x - e.x, ty = this.player.y - e.y;
        const l = Math.hypot(tx,ty) || 1, sp = 180 + this.level*20;
        this.enemyBullets.push({ x:e.x, y:e.y+10, vx:(tx/l)*sp, vy:(ty/l)*sp, r:3, dmg:1 });
        if (Math.random()<0.15) this.enemyBullets.push({ x:e.x+8, y:e.y+10, vx:(tx/l)*sp + 40, vy:(ty/l)*sp + 10, r:3, dmg:1 });
      }
    });
    if (minX < 20 || maxX > this.w-20) { this.waveDir *= -1; this.enemies.forEach(e=> { if (e.state==='formation') e.baseY += 18; }); }

    // Enemy bullets
    this.enemyBullets = this.enemyBullets.filter(b=>{
      b.x += b.vx*dt; b.y += b.vy*dt;
      return b.y < this.h + 40 && b.x>-40 && b.x<this.w+40;
    });

    // Collisions: bullets vs enemies
    for (let i=this.bullets.length-1;i>=0;i--){
      const b = this.bullets[i];
      for (let j=this.enemies.length-1;j>=0;j--){
        const e = this.enemies[j];
        if (overlapCircleRect(b.x,b.y,b.r, e.x-e.w/2, e.y-e.h/2, e.w, e.h)) {
          if (!b.pierce) this.bullets.splice(i,1);
          e.hp -= b.dmg || 1;
          if (this.power.onHit) this.power.onHit(this, e, b);
          if (e.hp<=0){
            this.enemies.splice(j,1);
            this.player.score += 50;
            if (Math.random()<0.18) this.dropPower(e.x, e.y);
            play(SFX.EXPLODE);
          } else {
            play(SFX.HIT);
          }
          break;
        }
      }
    }

    // Enemy bullets vs player
    for (let i=this.enemyBullets.length-1;i>=0;i--){
      const b = this.enemyBullets[i];
      if (circleRect(b.x,b.y,b.r, this.player)) {
        if (p.shield>0){ this.enemyBullets.splice(i,1); play(SFX.SHIELD); continue; }
        if (p.invuln>0){ this.enemyBullets.splice(i,1); continue; }
        this.enemyBullets.splice(i,1);
        this.hitPlayer();
      }
    }

    // Powerups pickup
    for (let i=this.powerups.length-1;i>=0;i--){
      const pu = this.powerups[i];
      pu.y += pu.vy*dt; pu.x += pu.vx*dt; pu.vy += 20*dt;
      if (pu.y>this.h+20) { this.powerups.splice(i,1); continue; }
      if (circleRect(pu.x, pu.y, 10, {x:p.x, y:p.y, w:p.w, h:p.h})) {
        if (pu.type === 'EXTRA_LIFE') {
          this.player.lives++;
          this.player.hp = this.player.maxHp; // Full heal on extra life
        } else if (pu.type === 'SHIELD_DROP') {
          this.player.shield = 8; // 8 seconds of shield
        } else {
          this.grantPower(pu.type);
        }
        this.powerups.splice(i,1);
        play(SFX.PICK);
      }
    }

    // Occasional dive attack
    this.nextDive -= dt;
    if (this.nextDive <= 0) {
      const candidates = this.enemies.filter(e=>e.state==='formation');
      const pick = candidates[Math.floor(Math.random()*candidates.length)];
      if (pick) { pick.state = 'dive'; pick.vy = 220; pick.vx = (this.player.x - pick.x)>0 ? 80 : -80; }
      this.nextDive = 2 + Math.max(0.8, 4 - this.level*0.2) * Math.random();
    }

    // Next wave
    if (this.enemies.length===0) {
      this.level++;
      this.player.bombs = Math.min(3, this.player.bombs+1);
      this.spawnWave();
    }
  }

  fire() {
    const p = this.player;
    const cd = this.power.fireRate || 0.18;
    p.fireCd = cd;
    const pattern = this.power.firePattern ? this.power.firePattern(this) : [{vx:0, vy:-460, dmg:1}];
    for (const pat of pattern) {
      this.bullets.push({ x:p.x, y:p.y-16, vx: pat.vx, vy: pat.vy, r:4, dmg: pat.dmg||1, life: 2.5, pierce: pat.pierce||false, homing: pat.homing||false, color: pat.color||'#fff' });
    }
    play(SFX.SHOOT);
  }

  useBomb() {
    if (this.player.bombs<=0) return;
    this.player.bombs--;
    // Mega bomb: clears bullets, damages all enemies
    this.enemyBullets.length = 0;
    this.enemies.forEach(e=> e.hp -= 2);
    this.enemies = this.enemies.filter(e=> {
      if (e.hp<=0){ this.player.score += 50; if (Math.random()<0.12) this.dropPower(e.x,e.y); }
      return e.hp>0;
    });
    play(SFX.BOMB);
  }

  hitPlayer() {
    if (this.player.invuln>0) return;
    
    this.player.hp--;
    play(SFX.PLAYER_HIT);
    
    if (this.player.hp <= 0) {
      this.player.lives--;
      if (this.player.lives > 0) {
        this.player.hp = this.player.maxHp; // Reset HP on new life
      }
    }
    
    this.player.invuln = 2.0;
    
    if (this.player.lives <= 0) {
      this.state = 'over';
      play(SFX.GAMEOVER);
    }
  }

  dropPower(x,y) {
    const rand = Math.random();
    
    // Extra life drop (1 in 250 enemies)
    if (rand < 0.004) { // 0.4% = 1 in 250
      const ang = (Math.random()*Math.PI/2) + Math.PI*5/4;
      this.powerups.push({ 
        x, y, 
        vx: Math.cos(ang)*60, 
        vy: Math.sin(ang)*60, 
        type: 'EXTRA_LIFE' 
      });
      return;
    }
    
    // Shield drop (1 in 100 enemies)
    if (rand < 0.01 + 0.004) { // 1% = 1 in 100 (excluding life drops)
      const ang = (Math.random()*Math.PI/2) + Math.PI*5/4;
      this.powerups.push({ 
        x, y, 
        vx: Math.cos(ang)*60, 
        vy: Math.sin(ang)*60, 
        type: 'SHIELD_DROP' 
      });
      return;
    }
    
    // Regular powerups
    const choices = [
      POWER_TYPES.SPREAD, POWER_TYPES.LASER, POWER_TYPES.SHIELD,
      POWER_TYPES.SLOWMO, POWER_TYPES.DRONE, POWER_TYPES.HOMING,
      POWER_TYPES.PIERCE, POWER_TYPES.MAGNET
    ];
    const type = choices[Math.floor(Math.random()*choices.length)];
    const ang = (Math.random()*Math.PI/2) + Math.PI*5/4;
    this.powerups.push({ x, y, vx: Math.cos(ang)*60, vy: Math.sin(ang)*60, type });
  }

  render() {
    const ctx = this.ctx;
    const { w,h } = this;
    ctx.clearRect(0,0,w,h);

    // Starfield
    drawStars(ctx, this.last*0.04, w, h);

    // Player
    drawPlayer(ctx, this.player);

    // Bullets
    ctx.fillStyle = '#fff';
    this.bullets.forEach(b=>{
      ctx.fillStyle = b.color || '#fff';
      ctx.shadowColor = ctx.fillStyle; ctx.shadowBlur = 10;
      ctx.beginPath(); ctx.arc(b.x,b.y,b.r,0,Math.PI*2); ctx.fill();
      ctx.shadowBlur = 0;
    });

    // Enemies
    this.enemies.forEach(e=>{
      drawEnemy(ctx,e);
    });

    // Enemy bullets
    ctx.fillStyle = '#ff4444';
    this.enemyBullets.forEach(b=>{
      ctx.beginPath(); ctx.arc(b.x,b.y,b.r,0,Math.PI*2); ctx.fill();
    });

    // Powerups
    this.powerups.forEach(pu=>{
      drawPower(ctx, pu.type, pu.x, pu.y);
    });

    // Over state overlay
    if (this.state==='over') {
      ctx.fillStyle='rgba(0,0,0,.6)';
      ctx.fillRect(0,0,w,h);
      ctx.fillStyle='#fff';
      ctx.font='800 36px Noto Sans';
      ctx.textAlign='center';
      ctx.fillText('Game Over', w/2, h/2 - 20);
      ctx.font='600 18px Noto Sans';
      ctx.fillText('Press R to restart', w/2, h/2 + 14);
    }
  }
}

// Helpers and drawing
function drawPlayer(ctx, p){
  ctx.save();
  ctx.translate(p.x, p.y);
  
  // Main body with metallic gradient
  const grad = ctx.createLinearGradient(0, -12, 0, 12);
  grad.addColorStop(0, '#f0f0f0');
  grad.addColorStop(0.5, '#c0c0c0');
  grad.addColorStop(1, '#808080');
  
  // Core body
  ctx.fillStyle = grad;
  ctx.beginPath();
  ctx.moveTo(-18, 10);
  ctx.quadraticCurveTo(-20, 0, -18, -8);
  ctx.lineTo(-8, -12);
  ctx.lineTo(8, -12);
  ctx.lineTo(18, -8);
  ctx.quadraticCurveTo(20, 0, 18, 10);
  ctx.closePath();
  ctx.fill();
  
  // Engine glow
  const engineGlow = ctx.createRadialGradient(0, 12, 0, 0, 12, 8);
  engineGlow.addColorStop(0, '#00c2ff');
  engineGlow.addColorStop(1, 'transparent');
  ctx.fillStyle = engineGlow;
  ctx.fillRect(-8, 8, 16, 8);
  
  // Cockpit with glass effect
  const cockpitGrad = ctx.createLinearGradient(-8, -12, 8, -4);
  cockpitGrad.addColorStop(0, '#00c2ff');
  cockpitGrad.addColorStop(0.5, '#0099cc');
  cockpitGrad.addColorStop(1, '#006699');
  ctx.fillStyle = cockpitGrad;
  ctx.beginPath();
  ctx.ellipse(0, -8, 8, 4, 0, 0, Math.PI*2);
  ctx.fill();
  
  // Wing details
  ctx.strokeStyle = '#555';
  ctx.lineWidth = 1;
  ctx.beginPath();
  ctx.moveTo(-15, 2);
  ctx.lineTo(-20, 8);
  ctx.moveTo(15, 2);
  ctx.lineTo(20, 8);
  ctx.stroke();
  
  // Shield
  if (p.shield>0) {
    ctx.strokeStyle='rgba(0,194,255,0.6)';
    ctx.lineWidth=3;
    ctx.shadowColor='#00c2ff';
    ctx.shadowBlur=20;
    ctx.beginPath();
    ctx.arc(0, 0, 28, 0, Math.PI*2);
    ctx.stroke();
    ctx.shadowBlur=0;
  }
  
  // Invuln blink
  if (p.invuln>0 && Math.floor(performance.now()/100)%2===0) {
    ctx.globalAlpha=0.4;
    ctx.fillStyle='#fff';
    ctx.fillRect(-18,-12,36,24);
  }
  ctx.restore();
}

function drawEnemy(ctx,e){
  ctx.save();
  ctx.translate(e.x,e.y);
  
  const isTank = e.type==='tank';
  const isShooter = e.type==='shooter';
  
  // Dynamic color based on type
  let color1, color2, glowColor;
  if (isTank) {
    color1 = '#ff6b47';
    color2 = '#ff3300';
    glowColor = '#ff3300';
  } else if (isShooter) {
    color1 = '#47ff6b';
    color2 = '#00ff33';
    glowColor = '#00ff33';
  } else {
    color1 = '#4799ff';
    color2 = '#0066ff';
    glowColor = '#0066ff';
  }
  
  // Main body with metallic finish
  const grad = ctx.createLinearGradient(-e.w/2, -e.h/2, e.w/2, e.h/2);
  grad.addColorStop(0, '#ffffff');
  grad.addColorStop(0.3, color1);
  grad.addColorStop(1, color2);
  
  ctx.fillStyle = grad;
  ctx.shadowColor = glowColor;
  ctx.shadowBlur = 15;
  
  if (isTank) {
    // Tank: bulky with armor plating
    ctx.beginPath();
    ctx.moveTo(-e.w/2, -e.h/2);
    ctx.lineTo(e.w/2, -e.h/2);
    ctx.lineTo(e.w/2+4, 0);
    ctx.lineTo(e.w/2, e.h/2);
    ctx.lineTo(-e.w/2, e.h/2);
    ctx.lineTo(-e.w/2-4, 0);
    ctx.closePath();
    ctx.fill();
    
    // Armor plating
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 2;
    ctx.stroke();
    
    // Heavy weapon
    ctx.fillStyle = '#222';
    ctx.fillRect(-4, -e.h/2-4, 8, 4);
  } else if (isShooter) {
    // Shooter: sleek and angular
    ctx.beginPath();
    ctx.moveTo(-e.w/2, 0);
    ctx.lineTo(-e.w/4, -e.h/2);
    ctx.lineTo(e.w/4, -e.h/2);
    ctx.lineTo(e.w/2, 0);
    ctx.lineTo(e.w/4, e.h/2);
    ctx.lineTo(-e.w/4, e.h/2);
    ctx.closePath();
    ctx.fill();
    
    // Wing details
    ctx.strokeStyle = '#333';
    ctx.lineWidth = 1;
    ctx.beginPath();
    ctx.moveTo(-e.w/3, -e.h/4);
    ctx.lineTo(-e.w/2-2, -e.h/4);
    ctx.moveTo(e.w/3, -e.h/4);
    ctx.lineTo(e.w/2+2, -e.h/4);
    ctx.stroke();
  } else {
    // Basic: classic but modernized
    ctx.beginPath();
    ctx.moveTo(-e.w/2, -e.h/3);
    ctx.quadraticCurveTo(-e.w/3, -e.h/2, 0, -e.h/2);
    ctx.quadraticCurveTo(e.w/3, -e.h/2, e.w/2, -e.h/3);
    ctx.lineTo(e.w/2, e.h/3);
    ctx.quadraticCurveTo(e.w/3, e.h/2, 0, e.h/2);
    ctx.quadraticCurveTo(-e.w/3, e.h/2, -e.w/2, e.h/3);
    ctx.closePath();
    ctx.fill();
  }
  
  // Engine glow
  const engineGrad = ctx.createRadialGradient(0, e.h/2, 0, 0, e.h/2+5, 10);
  engineGrad.addColorStop(0, glowColor);
  engineGrad.addColorStop(1, 'transparent');
  ctx.fillStyle = engineGrad;
  ctx.fillRect(-e.w/3, e.h/2, e.w*2/3, 5);
  
  // Health bar with glow
  const hpw = (e.hp / (isTank?3:(isShooter?2:1))) * e.w;
  ctx.fillStyle = glowColor;
  ctx.fillRect(-e.w/2, e.h/2+4, hpw, 3);
  
  // Energy core
  const energyGrad = ctx.createRadialGradient(0, 0, 0, 0, 0, 6);
  energyGrad.addColorStop(0, '#ffffff');
  energyGrad.addColorStop(0.5, color1);
  energyGrad.addColorStop(1, 'transparent');
  ctx.fillStyle = energyGrad;
  ctx.beginPath();
  ctx.arc(0, 0, 6, 0, Math.PI*2);
  ctx.fill();
  
  ctx.shadowBlur = 0;
  ctx.restore();
}

function drawPower(ctx, type, x,y){
  ctx.save(); ctx.translate(x,y);
  
  if (type === 'EXTRA_LIFE') {
    // Heart shape for extra life
    ctx.fillStyle='#ff0066';
    ctx.beginPath();
    ctx.moveTo(0, 5);
    ctx.bezierCurveTo(-8, -5, -8, -12, 0, -12);
    ctx.bezierCurveTo(8, -12, 8, -5, 0, 5);
    ctx.fill();
    
    // Glow effect
    ctx.shadowColor = '#ff0066';
    ctx.shadowBlur = 15;
    ctx.fill();
    ctx.shadowBlur = 0;
  } else if (type === 'SHIELD_DROP') {
    // Shield icon
    ctx.fillStyle='#00c2ff';
    ctx.beginPath();
    ctx.arc(0, 0, 12, 0, Math.PI*2); 
    ctx.fill();
    
    // Inner shield symbol
    ctx.strokeStyle='#fff';
    ctx.lineWidth = 2;
    ctx.beginPath();
    ctx.arc(0, 0, 6, 0, Math.PI*2);
    ctx.stroke();
    
    // Glow effect
    ctx.shadowColor = '#00c2ff';
    ctx.shadowBlur = 15;
    ctx.fill();
    ctx.shadowBlur = 0;
  } else {
    // Regular powerups
    ctx.fillStyle='#fff';
    ctx.beginPath(); ctx.arc(0,0,12,0,Math.PI*2); ctx.fill();
    ctx.fillStyle={
      SPREAD:'#ffcc00', LASER:'#ff3d81', SHIELD:'#00c2ff',
      SLOWMO:'#a0a0ff', DRONE:'#00ff9c', HOMING:'#ff8844',
      PIERCE:'#b088ff', MAGNET:'#44d1ff'
    }[type] || '#fff';
    ctx.beginPath(); ctx.arc(0,0,8,0,Math.PI*2); ctx.fill();
  }
  ctx.restore();
}

// Collision utils
function circleRect(cx,cy,r, rect){
  const rx = rect.x-rect.w/2, ry = rect.y-rect.h/2, rw = rect.w, rh = rect.h;
  const dx = Math.max(rx, Math.min(cx, rx+rw));
  const dy = Math.max(ry, Math.min(cy, ry+rh));
  const dist = Math.hypot(cx-dx, cy-dy);
  return dist <= r;
}
function overlapCircleRect(cx,cy,r, rx,ry,rw,rh){
  const dx = Math.max(rx, Math.min(cx, rx+rw));
  const dy = Math.max(ry, Math.min(cy, ry+rh));
  return Math.hypot(cx-dx, cy-dy) <= r;
}

function drawStars(ctx, t, w, h){
  ctx.save();
  const layers = [
    { count: 50, speed: 20, size: 1, color:'#666' },
    { count: 40, speed: 40, size: 2, color:'#888' },
    { count: 25, speed: 80, size: 2, color:'#00c2ff' },
  ];
  layers.forEach((L, li)=>{
    ctx.globalAlpha = li===2 ? 0.7 : 0.5;
    for (let i=0;i<L.count;i++){
      const y = (i*200 + (t%200000)) % (h+200) - 100;
      const x = (i*73 % w);
      ctx.fillStyle = L.color;
      ctx.fillRect(x, (y + L.speed*(t/16)) % (h+200) - 100, L.size, L.size);
    }
  });
  ctx.restore();
}
import { ethers } from 'ethers';
import dotenv from 'dotenv';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Load environment variables from root directory
dotenv.config({ path: path.resolve(__dirname, '../../.env') });

/**
 * WalletManager handles secure wallet operations on the server side
 * This prevents private keys from being exposed in client-side code
 */
class WalletManager {
    constructor() {
        // Initialize provider and wallet
        this.provider = new ethers.JsonRpcProvider(process.env.ETHEREUM_NETWORK_URL || 'http://localhost:8545');
        
        // Initialize hot wallet from environment variables
        if (process.env.HOT_WALLET_PRIVATE_KEY && process.env.HOT_WALLET_ADDRESS) {
            this.hotWallet = new ethers.Wallet(process.env.HOT_WALLET_PRIVATE_KEY, this.provider);
            this.hotWalletAddress = process.env.HOT_WALLET_ADDRESS;
            
            console.log('🔐 Hot wallet initialized securely on server');
        } else {
            console.error('❌ Hot wallet configuration missing in environment variables');
            this.hotWallet = null;
            this.hotWalletAddress = null;
        }
    }
    
    /**
     * Get hot wallet balance
     * @returns {Promise<string>} Balance in ETH
     */
    async getHotWalletBalance() {
        if (!this.hotWallet) {
            throw new Error('Hot wallet not initialized');
        }
        
        try {
            const balance = await this.provider.getBalance(this.hotWalletAddress);
            return ethers.formatEther(balance);
        } catch (error) {
            console.error('❌ Failed to get hot wallet balance:', error);
            throw new Error('Failed to get hot wallet balance');
        }
    }
    
    /**
     * Send ETH from hot wallet to a recipient
     * @param {string} toAddress - Recipient address
     * @param {string} amount - Amount in ETH
     * @param {string} reason - Reason for the transaction (for logging)
     * @returns {Promise<object>} Transaction result
     */
    async sendFromHotWallet(toAddress, amount, reason = 'unspecified') {
        if (!this.hotWallet) {
            throw new Error('Hot wallet not initialized');
        }
        
        try {
            console.log(`💰 Sending ${amount} ETH from hot wallet to ${toAddress} for ${reason}`);
            
            // Convert amount to wei
            const amountWei = ethers.parseEther(amount.toString());
            
            // Get current balance before transaction
            const balanceBefore = await this.provider.getBalance(this.hotWalletAddress);
            console.log(`💰 Hot wallet balance before transaction: ${ethers.formatEther(balanceBefore)} ETH`);
            
            // Send transaction
            const tx = await this.hotWallet.sendTransaction({
                to: toAddress,
                value: amountWei
            });
            
            console.log(`📤 Transaction sent: ${tx.hash}`);
            
            // Wait for transaction confirmation
            const receipt = await tx.wait();
            console.log(`✅ Transaction confirmed in block ${receipt.blockNumber}`);
            
            // Get balance after transaction
            const balanceAfter = await this.provider.getBalance(this.hotWalletAddress);
            console.log(`💰 Hot wallet balance after transaction: ${ethers.formatEther(balanceAfter)} ETH`);
            
            return {
                success: true,
                transactionHash: tx.hash,
                blockNumber: receipt.blockNumber,
                from: this.hotWalletAddress,
                to: toAddress,
                amount: amount,
                reason: reason,
                gasUsed: receipt.gasUsed.toString(),
                balanceBefore: ethers.formatEther(balanceBefore),
                balanceAfter: ethers.formatEther(balanceAfter)
            };
        } catch (error) {
            console.error('❌ Failed to send transaction from hot wallet:', error);
            throw new Error(`Failed to send transaction: ${error.message}`);
        }
    }
    
    /**
     * Validate Ethereum address
     * @param {string} address - Ethereum address to validate
     * @returns {boolean} True if valid
     */
    isValidAddress(address) {
        try {
            return ethers.isAddress(address);
        } catch (error) {
            return false;
        }
    }
    
    /**
     * Get hot wallet address (public information)
     * @returns {string|null} Hot wallet address
     */
    getHotWalletAddress() {
        return this.hotWalletAddress;
    }
    
    /**
     * Check if wallet is properly initialized
     * @returns {boolean} True if wallet is ready
     */
    isWalletReady() {
        return this.hotWallet !== null && this.hotWalletAddress !== null;
    }
}

// Export singleton instance
export default new WalletManager();
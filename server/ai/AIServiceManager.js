import { EnvironmentCreator } from './EnvironmentCreator.js';
import { Environment } from './Environment.js';

/**
 * AI Service Manager - Coordinates all AI services (no fallbacks allowed)
 * Manages environment generation and AI service integration
 */
export class AIServiceManager {
    constructor() {
        this.environmentCreator = new EnvironmentCreator();
        this.environment = new Environment();
        this.isInitialized = false;
    }

    /**
     * Initialize all AI services
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            
            // Initialize environment creator (which initializes both Fal.ai and LLM clients)
            await this.environmentCreator.initialize();
            
            // Initialize environment system
            await this.environment.initialize();
            
            this.isInitialized = true;
            
        } catch (error) {
            console.error('Failed to initialize AI Service Manager:', error);
            // No fallbacks allowed - rethrow the error
            throw error;
        }
    }

    /**
     * Generate and set a new environment based on user input
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Complete environment data
     */
    async generateAndSetEnvironment(userInput) {
        
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Check if server is available
            if (!await this.isServerAvailable()) {
                throw new Error('AI services server (localhost:3001) is not available');
            }
            
            // Step 1: Create complete environment data using EnvironmentCreator
            const environmentData = await this.environmentCreator.createEnvironment(userInput);
            
            // Step 2: Set the environment in the Environment system
            await this.environment.setEnvironment(environmentData, environmentData.backgroundImage);
            
            return environmentData;
            
        } catch (error) {
            
            // Check if it's a connection error
            if (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('localhost:3001')) {
                throw new Error(`AI services connection failed: ${error.message}. Please ensure the AI server is running on localhost:3001`);
            }
            
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to generate and set environment: ${error.message}`);
        }
    }

    /**
     * Generate environment details only (without image generation)
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Environment details with prompts and modifiers
     */
    async generateEnvironmentDetails(userInput) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            
            const details = await this.environmentCreator.generateEnvironmentDetails(userInput);
            
            return details;
            
        } catch (error) {
            console.error('Error generating environment details:', error);
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to generate environment details: ${error.message}`);
        }
    }

    /**
     * Set environment from pre-generated details
     * @param {object} environmentDetails - Pre-generated environment details
     * @returns {Promise<object>} - Complete environment data
     */
    async setEnvironmentFromDetails(environmentDetails) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            
            // Step 1: Generate image from the details
            const backgroundImage = await this.environmentCreator.generateImageFromDetails(environmentDetails);
            
            // Step 2: Set the environment in the Environment system
            await this.environment.setEnvironment(environmentDetails, backgroundImage);
            
            return {
                ...environmentDetails,
                backgroundImage: backgroundImage
            };
            
        } catch (error) {
            console.error('Error setting environment from details:', error);
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to set environment from details: ${error.message}`);
        }
    }

    /**
     * Create and set default space environment
     * @returns {Promise<object>} - Default environment data
     */
    async createDefaultEnvironment() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            
            const defaultEnvironment = await this.environmentCreator.createDefaultEnvironment();
            await this.environment.setEnvironment(defaultEnvironment, defaultEnvironment.backgroundImage);
            
            return defaultEnvironment;
            
        } catch (error) {
            console.error('Error creating default environment:', error);
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to create default environment: ${error.message}`);
        }
    }

    /**
     * Get current gameplay modifiers from environment
     * @returns {object} - Current gameplay modifiers
     */
    getCurrentGameplayModifiers() {
        return this.environment.getGameplayModifiers();
    }

    /**
     * Get current environment type
     * @returns {string} - Current environment type
     */
    getCurrentEnvironmentType() {
        return this.environment.getEnvironmentType();
    }

    /**
     * Get current environment name
     * @returns {string} - Current environment name
     */
    getCurrentEnvironmentName() {
        return this.environment.getEnvironmentName();
    }

    /**
     * Get current environment description
     * @returns {string} - Current environment description
     */
    getCurrentEnvironmentDescription() {
        return this.environment.getEnvironmentDescription();
    }

    /**
     * Check if current environment is compatible with enemy type
     * @param {string} enemyType - Type of enemy to check
     * @returns {boolean} - True if compatible
     */
    isEnemyCompatible(enemyType) {
        return this.environment.isEnemyCompatible(enemyType);
    }

    /**
     * Get current environment hazards
     * @returns {Array} - Array of environment hazards
     */
    getCurrentEnvironmentHazards() {
        return this.environment.getEnvironmentHazards();
    }

    /**
     * Apply environment effects to enemy stats
     * @param {object} enemyStats - Base enemy stats
     * @returns {object} - Modified enemy stats
     */
    applyEnvironmentEffects(enemyStats) {
        return this.environment.applyEnvironmentEffects(enemyStats);
    }

    /**
     * Update environment system (for transitions and animations)
     * @param {number} deltaTime - Time since last update in milliseconds
     */
    update(deltaTime) {
        this.environment.updateTransition(deltaTime);
    }

    /**
     * Render environment background
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} canvasWidth - Canvas width
     * @param {number} canvasHeight - Canvas height
     */
    render(ctx, canvasWidth, canvasHeight) {
        this.environment.render(ctx, canvasWidth, canvasHeight);
    }

    /**
     * Reset to default environment
     */
    async resetToDefault() {
        try {
            this.environment.resetToDefault();
        } catch (error) {
            console.error('Error resetting to default environment:', error);
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to reset to default environment: ${error.message}`);
        }
    }

    /**
     * Clear all caches
     */
    clearCaches() {
        try {
            this.environmentCreator.clearCache();
            this.llmClient?.clearExpiredCache();
        } catch (error) {
            console.error('Error clearing caches:', error);
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to clear caches: ${error.message}`);
        }
    }
    
    /**
     * Check if the AI services server is available
     * @returns {Promise<boolean>} True if server is available
     */
    async isServerAvailable() {
        try {
            // Try to fetch a simple health check or ping endpoint
            const response = await fetch('http://localhost:3001/health', {
                method: 'GET',
                timeout: 5000 // 5 second timeout
            });
            
            return response.ok;
        } catch (error) {
            return false;
        }
    }

    /**
     * Get AI service manager status
     * @returns {object} - Status information
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            environmentStatus: this.environment.getStatus(),
            environmentCreatorStatus: this.environmentCreator.getStatus()
        };
    }

    /**
     * Get cache statistics
     * @returns {object} - Cache statistics
     */
    getCacheStats() {
        return this.environmentCreator.getCacheStats();
    }

    /**
     * Clean up resources
     */
    async destroy() {
        try {
            
            // Clear caches
            this.clearCaches();
            
            // Reset environment
            this.environment.resetToDefault();
            
            this.isInitialized = false;
            
        } catch (error) {
            console.error('Error destroying AI Service Manager:', error);
            // No fallbacks allowed - rethrow the error
            throw new Error(`Failed to destroy AI Service Manager: ${error.message}`);
        }
    }
}
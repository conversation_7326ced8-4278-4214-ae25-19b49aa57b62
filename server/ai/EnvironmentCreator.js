import { FalaiClient } from './FalaiClient.js';
import { LLMClient } from './LLMClient.js';

/**
 * EnvironmentCreator class for generating environment details and coordinating AI services
 * Takes user input and creates both the Fal.ai prompt and gameplay modifiers
 */
export class EnvironmentCreator {
    constructor() {
        this.falaiClient = new FalaiClient();
        this.llmClient = new LLMClient();
        this.isInitialized = false;
        this.cache = new Map();
        this.cacheExpiry = 0; // No cache expiry, cache lasts until level completion
    }

    /**
     * Initialize the AI clients
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            await this.falaiClient.initialize();
            await this.llmClient.initialize();
            this.isInitialized = true;
        } catch (error) {
            console.error('Failed to initialize EnvironmentCreator:', error);
            throw error;
        }
    }

    /**
     * Create a new environment based on user input
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Complete environment data with image and modifiers
     */
    async createEnvironment(userInput) {
        
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Check cache first
        const cacheKey = `env_create:${userInput}`;
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }

        try {
            
            // Use the combined environment generation endpoint
            const response = await fetch('http://localhost:3001/api/generate-environment', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    environmentDescription: userInput
                }),
            });

            
            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to generate environment');
            }

            const result = await response.json();
            
            // Load the generated image
            const backgroundImage = await this.loadImageFromUrl(result.imageData.images[0].url);
            
            // Create complete environment data
            const environmentData = {
                type: this.determineEnvironmentType(result),
                name: result.name || this.generateEnvironmentName(userInput),
                description: result.description || userInput,
                imagePrompt: result.imagePrompt,
                gameplayModifiers: result.gameplayModifiers,
                imageData: result.imageData,
                backgroundImage: backgroundImage,
                // Add tracking metadata
                createdAt: Date.now(),
                imageFileName: this.generateImageFileName(userInput)
            };

            // Cache the result
            this.setToCache(cacheKey, environmentData);

            return environmentData;
            
        } catch (error) {
            throw new Error(`Failed to create environment: ${error.message}`);
        }
    }

    /**
     * Create environment with default space environment
     * @returns {Promise<object>} - Default environment data
     */
    async createDefaultEnvironment() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Check cache first
        const cacheKey = 'env_default';
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }

        try {
            
            // Use the dedicated default environment endpoint
            const response = await fetch('http://localhost:3001/api/default-environment', {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to generate default environment');
            }

            const result = await response.json();
            
            // Load the generated image
            const backgroundImage = await this.loadImageFromUrl(result.imageData.images[0].url);
            
            // Create complete environment data
            const environmentData = {
                type: "space",
                name: "Deep Space",
                description: "Classic space environment perfect for intense dogfights",
                imagePrompt: result.imagePrompt,
                gameplayModifiers: result.gameplayModifiers,
                imageData: result.imageData,
                backgroundImage: backgroundImage
            };
            
            // Cache the result
            this.setToCache(cacheKey, environmentData);
            
            return environmentData;
            
        } catch (error) {
            console.error('Error creating default environment:', error);
            throw new Error(`Failed to create default environment: ${error.message}`);
        }
    }

    /**
     * Generate environment details only (without image generation)
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Environment details with prompts and modifiers
     */
    async generateEnvironmentDetails(userInput) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Check cache first
        const cacheKey = `env_details:${userInput}`;
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }

        try {
            
            // Use LLM to generate both image prompt and gameplay modifiers
            const llmResponse = await this.llmClient.generateRealityWarpPrompt({}, {}, 'basic', userInput);
            
            const environmentDetails = {
                type: this.determineEnvironmentType(llmResponse),
                name: llmResponse.name || this.generateEnvironmentName(userInput),
                description: llmResponse.description || userInput,
                imagePrompt: llmResponse.imagePrompt,
                gameplayModifiers: llmResponse.gameplayModifiers
            };
            
            // Cache the result
            this.setToCache(cacheKey, environmentDetails);
            
            return environmentDetails;
            
        } catch (error) {
            console.error('Error generating environment details:', error);
            throw new Error(`Failed to generate environment details: ${error.message}`);
        }
    }

    /**
     * Generate image from pre-existing environment details
     * @param {object} environmentDetails - Environment details with image prompt
     * @returns {Promise<HTMLImageElement>} - Generated background image
     */
    async generateImageFromDetails(environmentDetails) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            
            // Use Fal.ai to generate the background image
            const imageData = await this.falaiClient.generateImage(environmentDetails.imagePrompt, {
                negative_prompt: "blurry, low quality, text, watermark, signature, distorted, ugly"
            });
            
            // Load the generated image
            const backgroundImage = await this.loadImageFromUrl(imageData.images[0].url);
            
            return backgroundImage;
            
        } catch (error) {
            console.error('Error generating image from details:', error);
            throw new Error(`Failed to generate image from details: ${error.message}`);
        }
    }

    /**
     * Determine environment type from LLM response
     * @param {object} llmResponse - LLM response object
     * @returns {string} - Determined environment type
     */
    determineEnvironmentType(llmResponse) {
        // Extract type from the LLM response environmentType field
        if (llmResponse.gameplayModifiers && llmResponse.gameplayModifiers.environmentType) {
            const envType = llmResponse.gameplayModifiers.environmentType;
            // If it's an array, get the first element
            if (Array.isArray(envType) && envType.length > 0) {
                const firstType = envType[0];
                if (typeof firstType === 'object' && firstType.type) {
                    // Extract type from the format {"type": "water|fire|air|earth|crystal|shadow"}
                    const typeString = firstType.type;
                    // Split by | and return the first valid type
                    const types = typeString.split('|');
                    if (types.length > 0) {
                        // Validate that the type is one of our allowed types
                        const validTypes = ['water', 'fire', 'air', 'earth', 'crystal', 'shadow'];
                        const firstValidType = types.find(type => validTypes.includes(type));
                        if (firstValidType) {
                            return firstValidType;
                        }
                    }
                }
            }
        }

        // No fallbacks - if we can't determine type from LLM response, return space as default
        return 'space';
    }

    /**
     * Generate a nice environment name from user input
     * @param {string} userInput - User's description
     * @returns {string} - Generated environment name
     */
    generateEnvironmentName(userInput) {
        const type = this.determineEnvironmentType(userInput);
        const typeNames = {
            space: 'Deep Space',
            alien: 'Alien World',
            cyberpunk: 'Neo-Tokyo',
            volcanic: 'Volcanic Wasteland',
            ice: 'Frozen Planet',
            jungle: 'Alien Jungle'
        };

        return typeNames[type] || 'Custom Environment';
    }

    /**
     * Generate image file name from user input
     * @param {string} userInput - User's description
     * @returns {string} - Generated image file name
     */
    generateImageFileName(userInput) {
        // Create a safe filename from user input
        const safeName = userInput
            .toLowerCase()
            .replace(/[^a-z0-9\s]/g, '') // Remove special characters
            .replace(/\s+/g, '_') // Replace spaces with underscores
            .substring(0, 50); // Limit length

        const timestamp = Date.now();
        return `env_${safeName}_${timestamp}.png`;
    }

    /**
     * Load image from URL
     * @param {string} url - Image URL
     * @returns {Promise<HTMLImageElement>} - Loaded image
     */
    loadImageFromUrl(url) {
        return new Promise((resolve, reject) => {
            const img = new Image();
            img.crossOrigin = 'anonymous';
            
            img.onload = () => {
                resolve(img);
            };
            
            img.onerror = () => {
                reject(new Error(`Failed to load image: ${url}`));
            };
            
            img.src = url;
        });
    }

    /**
     * Get data from cache if it exists and hasn't expired
     * @param {string} key - Cache key
     * @returns {object|null} - Cached data or null
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached) {
            return cached.data;
        }
        return null;
    }

    /**
     * Set data in cache with timestamp
     * @param {string} key - Cache key
     * @param {object} data - Data to cache
     */
    setToCache(key, data) {
        this.cache.set(key, {
            data: data
        });
    }

    /**
     * Clear expired cache entries
     */
    clearExpiredCache() {
        // No time-based cache expiry, cache cleared only at level completion
        // This method is kept for API compatibility but does nothing
    }

    /**
     * Get cache statistics
     * @returns {object} - Cache stats
     */
    getCacheStats() {
        return {
            totalEntries: this.cache.size,
            expiryTime: this.cacheExpiry,
            keys: Array.from(this.cache.keys())
        };
    }

    /**
     * Clear all cache entries
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get creator status
     * @returns {object} - Creator status
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            cacheStats: this.getCacheStats(),
            falaiClientInitialized: this.falaiClient.isInitialized,
            llmClientInitialized: this.llmClient.isInitialized
        };
    }
}
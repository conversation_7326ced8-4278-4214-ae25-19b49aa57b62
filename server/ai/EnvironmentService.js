/**
 * Environment Service - Client-side service for environment generation
 * Communicates with the server API for all AI-related operations
 * No AI code runs in the browser - only API calls
 */
export class EnvironmentService {
    constructor() {
        this.apiBaseUrl = 'http://localhost:3001/api';
        this.isInitialized = false;
    }

    /**
     * Initialize the environment service
     */
    async initialize() {
        if (this.isInitialized) {
            return;
        }

        try {
            // Check if the server is available
            const response = await fetch(`${this.apiBaseUrl}/health`);
            if (response.ok) {
                this.isInitialized = true;
                console.log('Environment service initialized successfully');
            } else {
                throw new Error('Server health check failed');
            }
        } catch (error) {
            console.error('Failed to initialize environment service:', error);
            throw new Error('Failed to connect to environment service server');
        }
    }

    /**
     * Generate and set a new environment based on user input
     * @param {string} userInput - User's description of the desired environment
     * @returns {Promise<object>} - Complete environment data
     */
    async generateAndSetEnvironment(userInput) {
        console.log('🔍 [ENV SERVICE DEBUG] generateAndSetEnvironment called with input:', userInput);
        console.log('🔍 [ENV SERVICE DEBUG] isInitialized:', this.isInitialized);
        
        if (!this.isInitialized) {
            console.log('🔍 [ENV SERVICE DEBUG] Initializing EnvironmentService...');
            await this.initialize();
        }

        try {
            console.log('🔍 [ENV SERVICE DEBUG] Making API request to server...');
            console.log('🔍 [ENV SERVICE DEBUG] Request URL: http://localhost:3001/api/generate-environment');
            console.log('🔍 [ENV SERVICE DEBUG] Request body:', { environmentDescription: userInput });
            
            // Call the server API to generate the environment
            const response = await fetch(`${this.apiBaseUrl}/generate-environment`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    environmentDescription: userInput
                }),
            });

            console.log('🔍 [ENV SERVICE DEBUG] Server response status:', response.status);
            
            if (!response.ok) {
                const errorData = await response.json();
                console.error('❌ [ENV SERVICE DEBUG] Server error response:', errorData);
                throw new Error(errorData.error || 'Failed to generate environment');
            }

            const result = await response.json();
            console.log('✅ [ENV SERVICE DEBUG] Environment generated successfully:', result);
            
            // Update the image URL to use the local server path if available
            if (result.imageData && result.imageData.images && result.imageData.images[0] && result.imageData.images[0].url) {
                // If the URL is already a local path (starts with /api/), use it as is
                // Otherwise, it's an external URL that we'll keep as fallback
                const imageUrl = result.imageData.images[0].url;
                if (imageUrl.startsWith('/api/')) {
                    console.log('🔍 [ENV SERVICE DEBUG] Using local image URL:', imageUrl);
                    result.imageData.images[0].localUrl = `http://localhost:3001${imageUrl}`;
                } else {
                    console.log('🔍 [ENV SERVICE DEBUG] Using external image URL:', imageUrl);
                }
            }
            
            return result;
            
        } catch (error) {
            console.error('❌ [ENV SERVICE DEBUG] Error generating environment:', error);
            
            // Check if it's a connection error
            if (error.message.includes('fetch') || error.message.includes('network') || error.message.includes('localhost:3001')) {
                console.error('❌ [ENV SERVICE DEBUG] Network error detected - server may be unavailable');
                throw new Error(`Environment service connection failed: ${error.message}. Please ensure the server is running on localhost:3001`);
            }
            
            throw new Error(`Failed to generate environment: ${error.message}`);
        }
    }

    /**
     * Get the default environment
     * @returns {Promise<object>} - Default environment data
     */
    async getDefaultEnvironment() {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            console.log('Getting default environment from server...');
            
            const response = await fetch(`${this.apiBaseUrl}/default-environment`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                },
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to get default environment');
            }

            const result = await response.json();
            console.log('Default environment retrieved successfully');
            return result;
            
        } catch (error) {
            console.error('Error getting default environment:', error);
            throw new Error(`Failed to get default environment: ${error.message}`);
        }
    }

    /**
     * Check if the server is available
     * @returns {Promise<boolean>} True if server is available
     */
    async isServerAvailable() {
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`, {
                method: 'GET',
                timeout: 5000 // 5 second timeout
            });
            
            return response.ok;
        } catch (error) {
            console.warn('Environment service server health check failed:', error);
            return false;
        }
    }

    /**
     * Get service status
     * @returns {object} - Status information
     */
    getStatus() {
        return {
            isInitialized: this.isInitialized,
            apiBaseUrl: this.apiBaseUrl
        };
    }
}
/**
 * Environment class for managing game environments
 * Handles visual assets and gameplay modifiers for different environments
 */
export class Environment {
    constructor() {
        this.currentEnvironment = null;
        this.backgroundImage = null;
        this.gameplayModifiers = null;
        this.isTransitioning = false;
        this.transitionProgress = 0;
        this.transitionDuration = 0; // No transition duration, immediate transitions
        this.previousBackground = null;
        
        // Default space environment
        this.defaultEnvironment = {
            type: "space",
            name: "Deep Space",
            description: "Classic space environment perfect for intense dogfights",
            imagePrompt: "Epic space battlefield with distant stars, nebulae, and galaxies in portrait orientation. Deep cosmic background with subtle starfield, perfect for a vertical space shooter game.",
            gameplayModifiers: {
                enemySpeedMultiplier: 1.0,
                enemyHealthMultiplier: 1.0,
                enemySpawnRateMultiplier: 1.0,
                enemyProjectileSpeedMultiplier: 1.0,
                environmentEffects: ["Normal space environment"],
                compatibleEnemyTypes: ["water", "fire", "air", "earth", "crystal", "shadow"],
                environmentHazards: [],
                enemyTypeModifiers: {
                    water: 1.0,
                    fire: 1.0,
                    air: 1.0,
                    earth: 1.0,
                    crystal: 1.0,
                    shadow: 1.0
                }
            }
        };
    }

    /**
     * Initialize the environment with default space environment
     */
    async initialize() {
        this.currentEnvironment = { ...this.defaultEnvironment };
        this.gameplayModifiers = { ...this.defaultEnvironment.gameplayModifiers };
    }

    /**
     * Set a new environment with generated image and modifiers
     * @param {object} environmentData - Environment data from AI generation
     * @param {HTMLImageElement} backgroundImage - Generated background image
     */
    async setEnvironment(environmentData, backgroundImage) {
        if (this.isTransitioning) {
            return;
        }


        // Store previous background for transition
        this.previousBackground = this.backgroundImage;
        
        // Set new environment data
        this.currentEnvironment = {
            type: environmentData.type || "custom",
            name: environmentData.name || "Custom Environment",
            description: environmentData.description || "Custom generated environment",
            imagePrompt: environmentData.imagePrompt || ""
        };
        
        this.gameplayModifiers = environmentData.gameplayModifiers || this.defaultEnvironment.gameplayModifiers;
        this.backgroundImage = backgroundImage;
        
        // Start transition effect
        this.startTransition();
    }

    /**
     * Start visual transition effect
     */
    startTransition() {
        this.isTransitioning = true;
        this.transitionProgress = 0;
    }

    /**
     * Update transition effect
     * @param {number} deltaTime - Time since last update in milliseconds
     */
    updateTransition(deltaTime) {
        if (!this.isTransitioning) return;

        // Immediate transition - skip all transition effects
        this.transitionProgress = 1.0;
        this.isTransitioning = false;
        this.previousBackground = null;
    }

    /**
     * Get current gameplay modifiers
     * @returns {object} - Current gameplay modifiers
     */
    getGameplayModifiers() {
        return this.gameplayModifiers || this.defaultEnvironment.gameplayModifiers;
    }

    /**
     * Get current environment type
     * @returns {string} - Current environment type
     */
    getEnvironmentType() {
        return this.currentEnvironment?.type || this.defaultEnvironment.type;
    }

    /**
     * Get current environment name
     * @returns {string} - Current environment name
     */
    getEnvironmentName() {
        return this.currentEnvironment?.name || this.defaultEnvironment.name;
    }

    /**
     * Get current environment description
     * @returns {string} - Current environment description
     */
    getEnvironmentDescription() {
        return this.currentEnvironment?.description || this.defaultEnvironment.description;
    }

    /**
     * Check if environment is compatible with enemy type
     * @param {string} enemyType - Type of enemy to check
     * @returns {boolean} - True if compatible
     */
    isEnemyCompatible(enemyType) {
        const modifiers = this.getGameplayModifiers();
        return modifiers.compatibleEnemyTypes.includes(enemyType);
    }

    /**
     * Get environment hazards
     * @returns {Array} - Array of environment hazards
     */
    getEnvironmentHazards() {
        const modifiers = this.getGameplayModifiers();
        return modifiers.environmentHazards || [];
    }

    /**
     * Apply environment effects to enemy stats
     * @param {object} enemyStats - Base enemy stats
     * @returns {object} - Modified enemy stats
     */
    applyEnvironmentEffects(enemyStats) {
        const modifiers = this.getGameplayModifiers();
        const enemyType = enemyStats.type;
        const baseTypeModifier = modifiers.enemyTypeModifiers?.[enemyType] ?? 1.0;

        // Enhance modifier effects by 50% to make them more pronounced
        const enhancedTypeModifier = 1 + (baseTypeModifier - 1) * 1.5;
        const enhancedSpeedMultiplier = 1 + (modifiers.enemySpeedMultiplier - 1) * 1.5;
        const enhancedHealthMultiplier = 1 + (modifiers.enemyHealthMultiplier - 1) * 1.5;
        const enhancedProjectileSpeedMultiplier = 1 + (modifiers.enemyProjectileSpeedMultiplier - 1) * 1.5;

        return {
            speed: enemyStats.speed * enhancedSpeedMultiplier * enhancedTypeModifier,
            health: enemyStats.health * enhancedHealthMultiplier * enhancedTypeModifier,
            projectileSpeed: enemyStats.projectileSpeed * enhancedProjectileSpeedMultiplier * enhancedTypeModifier
        };
    }

    /**
     * Render the environment background
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} canvasWidth - Canvas width
     * @param {number} canvasHeight - Canvas height
     */
    render(ctx, canvasWidth, canvasHeight) {
        if (this.isTransitioning) {
            // Render transition effect (immediate)
            this.renderTransition(ctx, canvasWidth, canvasHeight);
        } else if (this.backgroundImage) {
            // Render current background - starfield will be rendered by GameEngine on top
            this.renderBackground(ctx, canvasWidth, canvasHeight, this.backgroundImage, 1.0);
        }
        // No else case - starfield is now handled by GameEngine for consistent layering
    }

    /**
     * Render background image
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} canvasWidth - Canvas width
     * @param {number} canvasHeight - Canvas height
     * @param {HTMLImageElement} image - Background image
     * @param {number} alpha - Opacity (0-1)
     */
    renderBackground(ctx, canvasWidth, canvasHeight, image, alpha) {
        ctx.save();
        ctx.globalAlpha = alpha;
        
        // Calculate scaling to cover entire canvas while maintaining aspect ratio
        const imageAspect = image.width / image.height;
        const canvasAspect = canvasWidth / canvasHeight;
        
        let drawWidth, drawHeight, drawX, drawY;
        
        if (imageAspect > canvasAspect) {
            // Image is wider than canvas
            drawHeight = canvasHeight;
            drawWidth = drawHeight * imageAspect;
            drawX = (canvasWidth - drawWidth) / 2;
            drawY = 0;
        } else {
            // Image is taller than canvas
            drawWidth = canvasWidth;
            drawHeight = drawWidth / imageAspect;
            drawX = 0;
            drawY = (canvasHeight - drawHeight) / 2;
        }
        
        ctx.drawImage(image, drawX, drawY, drawWidth, drawHeight);
        ctx.restore();
    }

    /**
     * Render transition effect between backgrounds
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} canvasWidth - Canvas width
     * @param {number} canvasHeight - Canvas height
     */
    renderTransition(ctx, canvasWidth, canvasHeight) {
        // Immediate transition - render only the new background
        if (this.backgroundImage) {
            this.renderBackground(ctx, canvasWidth, canvasHeight, this.backgroundImage, 1.0);
        }
    }

    /**
     * Render default starfield background
     * @param {CanvasRenderingContext2D} ctx - Canvas context
     * @param {number} canvasWidth - Canvas width
     * @param {number} canvasHeight - Canvas height
     */
    renderDefaultStarfield(ctx, canvasWidth, canvasHeight) {
        // Simple star field effect
        ctx.fillStyle = '#000011';
        ctx.fillRect(0, 0, canvasWidth, canvasHeight);
        
        // This would normally use the starfield from GameEngine
        // For now, just render a dark background
        ctx.fillStyle = '#ffffff';
        for (let i = 0; i < 100; i++) {
            const x = Math.random() * canvasWidth;
            const y = Math.random() * canvasHeight;
            const size = Math.random() * 1.5 + 0.5;
            ctx.fillRect(x, y, size, size);
        }
    }

    /**
     * Ease-in-out cubic function for smooth transitions
     * @param {number} t - Progress (0-1)
     * @returns {number} - Eased progress
     */
    easeInOutCubic(t) {
        return t < 0.5 
            ? 4 * t * t * t 
            : 1 - Math.pow(-2 * t + 2, 3) / 2;
    }

    /**
     * Reset to default space environment
     */
    resetToDefault() {
        this.currentEnvironment = { ...this.defaultEnvironment };
        this.gameplayModifiers = { ...this.defaultEnvironment.gameplayModifiers };
        this.backgroundImage = null;
        this.isTransitioning = false;
        this.transitionProgress = 0;
        this.previousBackground = null;
    }

    /**
     * Get environment status
     * @returns {object} - Current environment status
     */
    getStatus() {
        return {
            type: this.getEnvironmentType(),
            name: this.getEnvironmentName(),
            description: this.getEnvironmentDescription(),
            isTransitioning: this.isTransitioning,
            transitionProgress: this.transitionProgress,
            hasCustomBackground: this.backgroundImage !== null,
            gameplayModifiers: this.getGameplayModifiers()
        };
    }
}
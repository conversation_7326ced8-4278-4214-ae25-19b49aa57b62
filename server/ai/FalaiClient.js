/**
 * Fal.ai API client for battlefield generation
 * Handles image generation for game environments via server API
 */
export class FalaiClient {
    constructor() {
        this.apiBaseUrl = 'http://localhost:3001/api';
        this.isInitialized = false;
    }

    /**
     * Initialize the Fal.ai client
     */
    async initialize() {
        // Check if the server is available
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            if (response.ok) {
                this.isInitialized = true;
            } else {
                throw new Error('Server health check failed');
            }
        } catch (error) {
            console.error('Failed to initialize Fal.ai client:', error);
            throw new Error('Failed to connect to AI service server');
        }
    }

    /**
     * Generate an image based on a prompt
     * @param {string} prompt - The text prompt for image generation
     * @param {object} options - Additional options for image generation
     * @returns {Promise<object>} - Generated image data
     */
    async generateImage(prompt, options = {}) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const response = await fetch(`${this.apiBaseUrl}/falai/generate-image`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt,
                    options
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to generate image');
            }

            const result = await response.json();
            return result;
        } catch (error) {
            console.error('Error generating image:', error);
            throw new Error(`Failed to generate image: ${error.message}`);
        }
    }

    /**
     * Generate an environment background image using LLM client for prompt generation
     * @param {LLMClient} llmClient - LLM client instance
     * @param {string} environmentDescription - User input or default environment type
     * @returns {Promise<{imageData: object, gameplayModifiers: object}>} - Generated image data and gameplay modifiers
     */
    async generateEnvironment(llmClient, environmentDescription = "space") {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            // Use LLM client to generate both the image prompt and gameplay modifiers
            const llmResponse = await llmClient.generateRealityWarpPrompt({}, {}, 'basic', environmentDescription);
            
            const { imagePrompt, gameplayModifiers } = llmResponse;
            
            // Generate the image using the LLM-generated prompt
            const imageData = await this.generateImage(imagePrompt, {
                negative_prompt: "blurry, low quality, text, watermark, signature, distorted, ugly"
            });
            
            return {
                imageData,
                gameplayModifiers
            };
        } catch (error) {
            console.error('Error generating environment:', error);
            throw new Error(`Failed to generate environment: ${error.message}`);
        }
    }

    /**
     * Submit a request to the queue for async processing
     * @param {string} prompt - The text prompt for image generation
     * @param {string} webhookUrl - Optional webhook URL for results
     * @returns {Promise<string>} - Request ID
     */
    async submitQueueRequest(prompt, webhookUrl = null) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const input = {
            prompt: prompt,
            image_size: {
                width: 768,  // 9:16 aspect ratio for portrait mode
                height: 1360
            },
            num_inference_steps: 16,
            num_images: 1,
            enable_safety_checker: true,
            output_format: "jpeg"
        };

        const requestConfig = {
            input: input
        };

        if (webhookUrl) {
            requestConfig.webhookUrl = webhookUrl;
        }

        try {
            const { request_id } = await fal.queue.submit(this.model, requestConfig);
            return request_id;
        } catch (error) {
            console.error('Error submitting queue request:', error);
            throw new Error(`Failed to submit queue request: ${error.message}`);
        }
    }

    /**
     * Get the status of a queued request
     * @param {string} requestId - The request ID to check
     * @returns {Promise<object>} - Request status
     */
    async getQueueStatus(requestId) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const status = await fal.queue.status(this.model, {
                requestId: requestId,
                logs: true,
            });
            return status;
        } catch (error) {
            console.error('Error getting queue status:', error);
            throw new Error(`Failed to get queue status: ${error.message}`);
        }
    }

    /**
     * Get the result of a completed queued request
     * @param {string} requestId - The request ID to retrieve results for
     * @returns {Promise<object>} - Request result
     */
    async getQueueResult(requestId) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        try {
            const result = await fal.queue.result(this.model, {
                requestId: requestId
            });
            return result.data;
        } catch (error) {
            console.error('Error getting queue result:', error);
            throw new Error(`Failed to get queue result: ${error.message}`);
        }
    }
}
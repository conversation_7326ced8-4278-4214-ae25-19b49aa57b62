/**
 * LLM API client for environment analysis and prompt generation
 * Uses Groq API for fast language model processing via server API
 */
export class LLMClient {
    constructor() {
        this.apiBaseUrl = 'http://localhost:3001/api';
        this.model = "llama-3.1-8b-instant";
        this.isInitialized = false;
        this.cache = new Map();
        this.cacheExpiry = 0; // No cache expiry, cache lasts until level completion
        
        // Debug and logging configuration
        this.debugMode = false;
        this.logEnabled = true;
        this.performanceTracking = true;
        
        // Performance metrics
        this.performanceMetrics = {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0,
            responseTimes: [],
            lastResponseTime: 0,
            totalProcessingTime: 0
        };
        
        // Reality warp specific metrics
        this.realityWarpMetrics = {
            totalGenerations: 0,
            successfulGenerations: 0,
            failedGenerations: 0,
            averageQualityScore: 0,
            qualityScores: [],
            cacheHits: 0,
            cacheMisses: 0
        };
        
        // Response validation metrics
        this.validationMetrics = {
            totalResponses: 0,
            validResponses: 0,
            invalidResponses: 0,
            parsingFailures: 0,
            extractionFailures: 0
        };
        
        // Debug log buffer
        this.debugLog = [];
        this.maxDebugLogSize = 100;
    }

    /**
     * Initialize the Groq client
     */
    async initialize() {
        // Check if the server is available
        try {
            const response = await fetch(`${this.apiBaseUrl}/health`);
            if (response.ok) {
                this.isInitialized = true;
            } else {
                throw new Error('Server health check failed');
            }
        } catch (error) {
            console.error('Failed to initialize Groq client:', error);
            throw new Error('Failed to connect to AI service server');
        }
    }

    /**
     * Generate a chat completion
     * @param {string} prompt - The prompt to send to the LLM
     * @param {object} options - Additional options for the completion
     * @returns {Promise<string>} - The generated response
     */
    async generateCompletion(prompt, options = {}) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        const defaultOptions = {
            model: this.model,
            temperature: 0.7,
            max_tokens: 1000,
            ...options
        };

        const startTime = performance.now();
        this.performanceMetrics.totalRequests++;

        try {
            const response = await fetch(`${this.apiBaseUrl}/groq/generate-completion`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    prompt,
                    options: defaultOptions
                }),
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.error || 'Failed to generate completion');
            }

            const result = await response.json();
            const responseText = result.response || "";
            const responseTime = performance.now() - startTime;
            
            this.performanceMetrics.successfulRequests++;
            this.performanceMetrics.lastResponseTime = responseTime;
            this.performanceMetrics.responseTimes.push(responseTime);
            if (this.performanceMetrics.responseTimes.length > 50) {
                this.performanceMetrics.responseTimes.shift();
            }
            
            // Calculate average response time
            const avgTime = this.performanceMetrics.responseTimes.reduce((a, b) => a + b, 0) / this.performanceMetrics.responseTimes.length;
            this.performanceMetrics.averageResponseTime = avgTime;
            
            this.debugLog.push({
                timestamp: Date.now(),
                event: 'completion',
                responseTime: responseTime,
                promptLength: prompt.length,
                responseLength: responseText.length,
                success: true
            });
            
            return responseText;
        } catch (error) {
            const responseTime = performance.now() - startTime;
            this.performanceMetrics.failedRequests++;
            this.performanceMetrics.lastResponseTime = responseTime;
            
            this.error('Error generating completion:', error);
            this.debugLog.push({
                timestamp: Date.now(),
                event: 'completion',
                responseTime: responseTime,
                error: error.message,
                success: false
            });
            
            throw new Error(`Failed to generate completion: ${error.message}`);
        }
    }


    /**
     * Parse the LLM response to extract structured environment data
     * @param {string} response - Raw response from LLM
     * @returns {object} - Parsed environment data
     */
    parseEnvironmentResponse(response) {
        try {
            // Try to parse as JSON directly
            const parsed = JSON.parse(response);
            return this.validateEnvironmentData(parsed);
        } catch (error) {
            
            // Try to extract JSON from the response
            const jsonMatch = response.match(/\{[\s\S]*\}/);
            if (jsonMatch) {
                try {
                    const parsed = JSON.parse(jsonMatch[0]);
                    return this.validateEnvironmentData(parsed);
                } catch (parseError) {
                }
            }
            
            // Fallback to default environment
            return this.getDefaultEnvironmentData();
        }
    }

    /**
     * Validate and clean environment data
     * @param {object} data - Raw environment data
     * @returns {object} - Validated environment data
     */
    validateEnvironmentData(data) {
        const enemyTypeModifiers = data.gameplayModifiers?.enemyTypeModifiers || {};
        
        // Calculate global modifiers based on enemy type modifiers
        const modifierValues = Object.values(enemyTypeModifiers).filter(val => typeof val === 'number');
        const averageModifier = modifierValues.length > 0 ?
            modifierValues.reduce((sum, val) => sum + val, 0) / modifierValues.length : 1.0;
        
        return {
            type: data.type || "space",
            imagePrompt: data.imagePrompt || "Epic space battlefield",
            gameplayModifiers: {
                enemySpeedMultiplier: Math.max(0.5, Math.min(2.0, averageModifier)),
                enemyHealthMultiplier: Math.max(0.5, Math.min(2.0, averageModifier)),
                enemySpawnRateMultiplier: Math.max(0.5, Math.min(2.0, averageModifier)),
                enemyProjectileSpeedMultiplier: Math.max(0.5, Math.min(2.0, averageModifier)),
                environmentEffects: Array.isArray(data.gameplayModifiers?.environmentEffects) ? data.gameplayModifiers.environmentEffects : [],
                compatibleEnemyTypes: Array.isArray(data.gameplayModifiers?.compatibleEnemyTypes) ? data.gameplayModifiers.compatibleEnemyTypes : ["water", "fire", "air", "earth", "crystal", "shadow"],
                environmentHazards: Array.isArray(data.gameplayModifiers?.environmentHazards) ? data.gameplayModifiers.environmentHazards.map(hazard => ({
                    type: hazard.type || "none",
                    damagePerSecond: Math.max(0, Math.min(10, hazard.damagePerSecond || 0)),
                    slowEffect: Math.max(0.0, Math.min(1.0, hazard.slowEffect || 0.0))
                })) : [],
                enemyTypeModifiers: enemyTypeModifiers
            }
        };
    }

    /**
     * Get default environment data as fallback
     * @returns {object} - Default environment data
     */
    getDefaultEnvironmentData() {
        return {
            type: "space",
            imagePrompt: "Epic space battlefield with distant stars, nebulae, and galaxies in portrait orientation. Deep cosmic background with subtle starfield, perfect for a vertical space shooter game.",
            gameplayModifiers: {
                enemySpeedMultiplier: 1.0,
                enemyHealthMultiplier: 1.0,
                enemySpawnRateMultiplier: 1.0,
                enemyProjectileSpeedMultiplier: 1.0,
                environmentEffects: ["Normal space environment"],
                compatibleEnemyTypes: ["water", "fire", "air", "earth", "crystal", "shadow"],
                environmentHazards: [],
                enemyTypeModifiers: {
                    water: 1.0,
                    fire: 1.0,
                    air: 1.0,
                    earth: 1.0,
                    crystal: 1.0,
                    shadow: 1.0
                }
            }
        };
    }

    /**
     * Create a cache key for storing results
     * @param {string} type - Type of request
     * @param {string} input - Main input string
     * @returns {string} - Cache key
     */
    createCacheKey(type, input) {
        return `${type}:${input}`;
    }

    /**
     * Get data from cache if it exists and hasn't expired
     * @param {string} key - Cache key
     * @returns {object|null} - Cached data or null
     */
    getFromCache(key) {
        const cached = this.cache.get(key);
        if (cached) {
            return cached.data;
        }
        return null;
    }

    /**
     * Set data in cache with timestamp
     * @param {string} key - Cache key
     * @param {object} data - Data to cache
     */
    setToCache(key, data) {
        this.cache.set(key, {
            data: data
        });
    }

    /**
     * Clear expired cache entries
     */
    clearExpiredCache() {
        // No time-based cache expiry, cache cleared only at level completion
        // This method is kept for API compatibility but does nothing
    }

    /**
     * Get cache statistics
     * @returns {object} - Cache stats
     */
    getCacheStats() {
        return {
            totalEntries: this.cache.size,
            expiryTime: this.cacheExpiry,
            keys: Array.from(this.cache.keys())
        };
    }
    /**
     * Generate a reality warp prompt with context and player stats
     * @param {object} levelContext - Level context information
     * @param {object} playerStats - Player performance statistics
     * @returns {Promise<string>} Generated reality warp prompt
     */
    async generateRealityWarpPrompt(levelContext, playerStats, environmentDescription = null) {
        if (!this.isInitialized) {
            await this.initialize();
        }

        // Create cache key
        const cacheKey = `warp:reality_warp:${JSON.stringify(levelContext)}:${JSON.stringify(playerStats)}:${environmentDescription || 'default'}`;
        
        // Check cache first
        const cachedResult = this.getFromCache(cacheKey);
        if (cachedResult) {
            return cachedResult;
        }

        // Generate prompt
        const prompt = this.createRealityWarpPrompt(levelContext, playerStats, environmentDescription);
        
        try {
            const response = await this.generateCompletion(prompt, {
                temperature: 0.8,
                max_tokens: 2000
            });

            // If environmentDescription is provided, parse as environment data
            if (environmentDescription) {
                const parsedResponse = this.parseEnvironmentResponse(response);
                // Cache the result
                this.setToCache(cacheKey, parsedResponse);
                return parsedResponse;
            }
            
            // Extract and validate the image generation prompt
            const imagePrompt = this.extractImageGenerationPrompt(response);
            
            // Cache the result
            this.setToCache(cacheKey, imagePrompt);
            
            return imagePrompt;
        } catch (error) {
            // If environmentDescription is provided, return default environment data
            if (environmentDescription) {
                return this.getDefaultEnvironmentData();
            }
            // Return fallback prompt
            return this.getRealityWarpFallbackPrompt();
        }
    }

    /**
     * Create a reality warp prompt for the LLM
     * @param {object} levelContext - Level context information
     * @param {object} playerStats - Player performance statistics
     * @returns {string} Formatted prompt for LLM
     */
    createRealityWarpPrompt(levelContext, playerStats, environmentDescription = null) {
        const warpDescriptions = {
            basic: "Reality Shift - Basic reality manipulation that subtly alters the battlefield",
            advanced: "Dimensional Warp - Advanced dimensional manipulation that dramatically changes the environment",
            ultimate: "Reality Overload - Ultimate reality manipulation that completely transforms the battlefield"
        };

        const level = levelContext.level || 1;
        const score = levelContext.score || 0;
        const enemiesDefeated = levelContext.enemiesDefeated || 0;
        const difficulty = levelContext.difficulty || 'normal';

        // If environmentDescription is provided, use it as the primary focus
        if (environmentDescription) {
            return `You are an expert graphic designer for a vertical space shooter game. Based on the environment description, responde with:

1. A detailed Fal.ai image generation prompt for creating a background image
2. JSON configuration for gameplay modifiers that describe how this environment affects enemies and gameplay

Environment description: "${environmentDescription}"

**Game Context:**
- Level: ${level}
- Player Score: ${score}
- Enemies Defeated: ${enemiesDefeated}
- Difficulty: ${difficulty}
- Warp Type: reality_warp

**Player Performance Context:**
- Accuracy: ${(playerStats.accuracy * 100).toFixed(1)}%
- Level Progress: ${(playerStats.levelProgress * 100).toFixed(1)}%
- Performance: ${playerStats.performance || 'normal'}

**Visual Requirements:**
- Create an epic, dynamic visual representation of the environment
- Style: Cinematic, dramatic, sci-fi aesthetic
- Color palette: Based on environment and warp type
- Composition: Dynamic, flowing, otherworldly
- Atmosphere: Mysterious, powerful, reality-bending

**Enemy Types and Strengths/Vulnerabilities**:
- **Water**: Strong against Fire, weak against Earth.
- **Fire**: Strong against Air, weak against Water.
- **Air**: Strong against Earth, weak against Fire.
- **Earth**: Strong against Water, weak against Air.
- **Crystal**: Resistant to most types, but vulnerable to sound and focused attacks.
- **Shadow**: Fast and evasive, but vulnerable to laser blasts and light environments.

**Response Format:**

Please respond in the following JSON format. ONLY provide enemyTypeModifiers - the system will calculate all other modifiers based on these values:

{
  "imagePrompt": "Describe the environment, not the image format, the medium, etc. JUST WHAT IS SHOWN IN THE IMAGE.",
  "gameplayModifiers": {
    "environmentEffects": [
      "Effect description 1 - these are the gameplay modifiers such as slows down enemies if an ice environment",
      "Effect description 2"
    ],
    "compatibleEnemyTypes": ["water", "fire", "air", "earth", "crystal", "shadow"],
    "environmentType": [
      {
        "type": "water|fire|air|earth|crystal|shadow"
      }
    ],
    "enemyTypeModifiers": {
      "water": 0.5-2.0,
      "fire": 0.5-2.0,
      "air": 0.5-2.0,
      "earth": 0.5-2.0,
      "crystal": 0.5-2.0,
      "shadow": 0.5-2.0
    }
  }
}

The image description should be detailed, creative, and high-quality.`;
        }

        // If no environmentDescription is provided, use the original reality warp prompt
        return `You are an expert AI artist for a vertical space shooter game. Describe the scene representing a stunning visual representation of a ${warpDescriptions.reality_warp}.

**Game Context:**
- Level: ${level}
- Player Score: ${score}
- Enemies Defeated: ${enemiesDefeated}
- Difficulty: ${difficulty}
- Warp Type: reality_warp

**Player Performance Context:**
- Accuracy: ${(playerStats.accuracy * 100).toFixed(1)}%
- Level Progress: ${(playerStats.levelProgress * 100).toFixed(1)}%
- Performance: ${playerStats.performance || 'normal'}

**Visual Requirements:**
- Create an epic, dynamic visual effect for the reality warp
- Style: Cinematic, dramatic, sci-fi aesthetic
- Composition: Dynamic, flowing, otherworldly
- Atmosphere: Mysterious, powerful, reality-bending

**Image Prompt Guidelines:**
- Include dramatic lighting effects
- Add particle effects and energy streams
- Create depth with layered elements
- Use vibrant colors that match the warp type
- Include subtle hints of the game environment
- Make it visually impressive for a space shooter

**Response Format:**
Please respond in the following JSON format:
{
  "imagePrompt": "A detailed description of the visual effect for the reality warp",
  "negativePrompt": "What to avoid in the image",
  "styleGuidance": "Additional style instructions",
  "qualityIndicators": ["visual quality indicators"],
  "contextualElements": ["game-specific visual elements"]
}

The image description should be detailed, creative, and high-quality`;
    }

    /**
     * Extract image generation prompt from LLM JSON response
     * @param {string} llmResponse - Raw response from LLM
     * @returns {string} Clean image generation prompt
     */
    extractImageGenerationPrompt(llmResponse) {
        try {
            // Try to parse as JSON directly
            const parsed = JSON.parse(llmResponse);
            if (parsed.imagePrompt) {
                return this.cleanImagePrompt(parsed.imagePrompt);
            }
        } catch (error) {
        }

        // Try to extract JSON from the response
        const jsonMatch = llmResponse.match(/\{[\s\S]*\}/);
        if (jsonMatch) {
            try {
                const parsed = JSON.parse(jsonMatch[0]);
                if (parsed.imagePrompt) {
                    return this.cleanImagePrompt(parsed.imagePrompt);
                }
            } catch (parseError) {
                console.error('Failed to parse extracted JSON:', parseError);
            }
        }

        // Fallback: try to extract prompt text directly
        const promptPatterns = [
            /imagePrompt[:\s]*["']([^"']+)["']/i,
            /prompt[:\s]*["']([^"']+)["']/i,
            /"imagePrompt"[^:]*:[^:]*["']([^"']+)["']/i
        ];

        for (const pattern of promptPatterns) {
            const match = llmResponse.match(pattern);
            if (match && match[1]) {
                return this.cleanImagePrompt(match[1]);
            }
        }

        // Last resort: return the raw response (cleaned)
        return this.cleanImagePrompt(llmResponse);
    }

    /**
     * Clean and format an image prompt
     * @param {string} prompt - Raw image prompt
     * @returns {string} Cleaned image prompt
     */
    cleanImagePrompt(prompt) {
        if (!prompt || typeof prompt !== 'string') {
            return "Epic space battlefield with dramatic reality warp effects";
        }

        // Remove JSON formatting artifacts
        let cleaned = prompt
            .replace(/^[^{]*"imagePrompt"[^:]*:[^:]*["']?/, '')  // Remove JSON prefix
            .replace(/["']?[^"']*["']?$/, '')  // Remove JSON suffix
            .replace(/\\n/g, ' ')  // Replace escaped newlines
            .replace(/\s+/g, ' ')  // Normalize whitespace
            .trim();

        // Ensure it starts with a descriptive phrase
        if (!cleaned.match(/^(Epic|Dramatic|Stunning|Beautiful|Incredible|Amazing)/i)) {
            cleaned = `Epic ${cleaned}`;
        }

        return cleaned || "Epic space battlefield with dramatic reality warp effects";
    }

    /**
     * Validate LLM response format for reality warp
     * @param {string} response - Raw response from LLM
     * @returns {object} Validation result
     */
    validateRealityWarpResponse(response) {
        const result = {
            isValid: false,
            hasImagePrompt: false,
            hasNegativePrompt: false,
            hasStyleGuidance: false,
            qualityScore: 0,
            errors: []
        };

        try {
            // Try to parse as JSON
            const parsed = JSON.parse(response);
            
            result.hasImagePrompt = !!parsed.imagePrompt;
            result.hasNegativePrompt = !!parsed.negativePrompt;
            result.hasStyleGuidance = !!parsed.styleGuidance;
            
            // Calculate quality score
            let score = 0;
            if (result.hasImagePrompt) score += 40;
            if (result.hasNegativePrompt) score += 20;
            if (result.hasStyleGuidance) score += 20;
            if (parsed.qualityIndicators && Array.isArray(parsed.qualityIndicators)) {
                score += Math.min(20, parsed.qualityIndicators.length * 5);
            }
            
            result.qualityScore = score;
            result.isValid = score >= 40; // Minimum acceptable quality
            
        } catch (error) {
            result.errors.push('JSON parsing failed');
            result.isValid = false;
        }

        return result;
    }

    /**
     * Get example reality warp prompt for guidance
     * @returns {string} Example prompt
     */
    getRealityWarpExamplePrompt() {
        const examples = {
            basic: {
                imagePrompt: "Epic cosmic reality shift with swirling blue and purple energy fields, distorted space-time effects, floating crystalline fragments, and ethereal particle streams creating a dynamic battlefield transformation",
                negativePrompt: "blurry, low quality, simple, boring, static, plain, dull",
                styleGuidance: "Cinematic lighting, dramatic composition, vibrant colors, high detail, sci-fi aesthetic",
                qualityIndicators: ["high detail", "dynamic composition", "vibrant colors", "cinematic lighting"],
                contextualElements: ["space battlefield", "energy effects", "particle systems", "distorted reality"]
            },
            advanced: {
                imagePrompt: "Stunning dimensional warp with electric blue and green energy vortexes, tearing fabric of space-time, dimensional rifts revealing otherworldly landscapes, intense energy beams warping the battlefield",
                negativePrompt: "soft, muted colors, simple design, static elements, low energy",
                styleGuidance: "High contrast lighting, dramatic perspective, intense energy effects, otherworldly atmosphere",
                qualityIndicators: ["high contrast", "intense energy", "dramatic perspective", "otherworldly"],
                contextualElements: ["dimensional rifts", "energy vortexes", "warped space-time", "battlefield distortion"]
            },
            ultimate: {
                imagePrompt: "Breathtaking reality overload with golden and orange explosive energy waves, reality shattering into crystalline fragments, time distortion effects, cosmic storm elements, and overwhelming power visualization",
                negativePrompt: "subtle, calm, simple, muted, low energy, basic",
                styleGuidance: "Explosive composition, extreme lighting effects, cosmic scale, overwhelming power visualization",
                qualityIndicators: ["explosive composition", "extreme lighting", "cosmic scale", "overwhelming power"],
                contextualElements: ["reality shattering", "cosmic storm", "time distortion", "crystalline fragments"]
            }
        };

        return examples.reality_warp;
    }

    /**
     * Get fallback prompt for reality warp generation
     * @returns {string} Fallback prompt
     */
    getRealityWarpFallbackPrompt() {
        const fallbacks = {
            basic: "Epic cosmic reality shift with swirling blue and purple energy fields, distorted space-time effects, and ethereal particle streams",
            advanced: "Stunning dimensional warp with electric blue and green energy vortexes, tearing fabric of space-time, and dimensional rifts",
            ultimate: "Breathtaking reality overload with golden and orange explosive energy waves, reality shattering into crystalline fragments"
        };

        return fallbacks.reality_warp;
    }

    /**
     * Get reality warp specific cache statistics
     * @returns {object} Cache statistics
     */
    getRealityWarpCacheStats() {
        const warpCacheKeys = Array.from(this.cache.keys()).filter(key => key.startsWith('warp:'));
        
        return {
            totalRealityWarpEntries: warpCacheKeys.length,
            realityWarpCacheKeys: warpCacheKeys,
            totalCacheEntries: this.cache.size,
            cacheHitRate: warpCacheKeys.length > 0 ? 0.8 : 0 // Simulated hit rate
        };
    }

    /**
     * Clear reality warp specific cache entries
     */
    clearRealityWarpCache() {
        const warpCacheKeys = Array.from(this.cache.keys()).filter(key => key.startsWith('warp:'));
        
        warpCacheKeys.forEach(key => {
            this.cache.delete(key);
        });
        
        console.log(`Cleared ${warpCacheKeys.length} reality warp cache entries`);
    }

    /**
     * Log reality warp prompt generation metrics
     * @param {boolean} success - Whether generation was successful
     * @param {number} responseTime - Response time in milliseconds
     * @param {number} qualityScore - Quality score of response
     */
    logRealityWarpMetrics(success, responseTime, qualityScore = 0) {
        // Update metrics
        this.realityWarpMetrics.totalGenerations++;
        if (success) {
            this.realityWarpMetrics.successfulGenerations++;
        } else {
            this.realityWarpMetrics.failedGenerations++;
        }
        
        this.realityWarpMetrics.qualityScores.push(qualityScore);
        if (this.realityWarpMetrics.qualityScores.length > 20) {
            this.realityWarpMetrics.qualityScores.shift();
        }
        
        // Calculate average quality score
        const avgQuality = this.realityWarpMetrics.qualityScores.reduce((a, b) => a + b, 0) / this.realityWarpMetrics.qualityScores.length;
        this.realityWarpMetrics.averageQualityScore = avgQuality;
        
        const metrics = {
            timestamp: Date.now(),
            warpType: 'reality_warp',
            success: success,
            responseTime: responseTime,
            qualityScore: qualityScore,
            cacheHit: this.getFromCache(`warp:reality_warp:*`) !== null,
            averageQualityScore: avgQuality
        };

        this.log('[RealityWarpMetrics]', metrics);
        this.debugLog.push({
            timestamp: Date.now(),
            event: 'realityWarpMetrics',
            metrics: metrics
        });
    }

    /**
     * Log response validation metrics
     * @param {object} validation - Validation result
     * @param {string} response - Raw response
     */
    logValidationMetrics(validation, response) {
        this.validationMetrics.totalResponses++;
        
        if (validation.isValid) {
            this.validationMetrics.validResponses++;
        } else {
            this.validationMetrics.invalidResponses++;
            if (validation.errors.includes('JSON parsing failed')) {
                this.validationMetrics.parsingFailures++;
            }
            if (validation.errors.includes('Extraction failed')) {
                this.validationMetrics.extractionFailures++;
            }
        }
        
        this.debugLog.push({
            timestamp: Date.now(),
            event: 'validation',
            validation: validation,
            responseLength: response.length
        });
    }

    /**
     * Enable or disable debug mode
     * @param {boolean} enabled - Whether debug mode is enabled
     */
    setDebugMode(enabled) {
        this.debugMode = enabled;
        this.log(`Debug mode ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Enable or disable logging
     * @param {boolean} enabled - Whether logging is enabled
     */
    setLogging(enabled) {
        this.logEnabled = enabled;
        this.log(`Logging ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Enable or disable performance tracking
     * @param {boolean} enabled - Whether performance tracking is enabled
     */
    setPerformanceTracking(enabled) {
        this.performanceTracking = enabled;
        this.log(`Performance tracking ${enabled ? 'enabled' : 'disabled'}`);
    }

    /**
     * Get performance metrics
     * @returns {object} Performance metrics
     */
    getPerformanceMetrics() {
        const successRate = this.performanceMetrics.totalRequests > 0
            ? (this.performanceMetrics.successfulRequests / this.performanceMetrics.totalRequests) * 100
            : 0;

        return {
            totalRequests: this.performanceMetrics.totalRequests,
            successfulRequests: this.performanceMetrics.successfulRequests,
            failedRequests: this.performanceMetrics.failedRequests,
            successRate: Math.round(successRate),
            averageResponseTime: Math.round(this.performanceMetrics.averageResponseTime),
            lastResponseTime: Math.round(this.performanceMetrics.lastResponseTime),
            responseTimes: this.performanceMetrics.responseTimes.slice(-10) // Last 10 response times
        };
    }

    /**
     * Get reality warp metrics
     * @returns {object} Reality warp metrics
     */
    getRealityWarpMetrics() {
        const successRate = this.realityWarpMetrics.totalGenerations > 0
            ? (this.realityWarpMetrics.successfulGenerations / this.realityWarpMetrics.totalGenerations) * 100
            : 0;

        return {
            totalGenerations: this.realityWarpMetrics.totalGenerations,
            successfulGenerations: this.realityWarpMetrics.successfulGenerations,
            failedGenerations: this.realityWarpMetrics.failedGenerations,
            successRate: Math.round(successRate),
            averageQualityScore: Math.round(this.realityWarpMetrics.averageQualityScore),
            qualityScores: this.realityWarpMetrics.qualityScores.slice(-10), // Last 10 quality scores
            cacheHitRate: this.realityWarpMetrics.cacheHits / (this.realityWarpMetrics.cacheHits + this.realityWarpMetrics.cacheMisses) || 0
        };
    }

    /**
     * Get validation metrics
     * @returns {object} Validation metrics
     */
    getValidationMetrics() {
        const validityRate = this.validationMetrics.totalResponses > 0
            ? (this.validationMetrics.validResponses / this.validationMetrics.totalResponses) * 100
            : 0;

        return {
            totalResponses: this.validationMetrics.totalResponses,
            validResponses: this.validationMetrics.validResponses,
            invalidResponses: this.validationMetrics.invalidResponses,
            validityRate: Math.round(validityRate),
            parsingFailures: this.validationMetrics.parsingFailures,
            extractionFailures: this.validationMetrics.extractionFailures
        };
    }

    /**
     * Get all metrics
     * @returns {object} All metrics
     */
    getAllMetrics() {
        return {
            performance: this.getPerformanceMetrics(),
            realityWarp: this.getRealityWarpMetrics(),
            validation: this.getValidationMetrics(),
            cache: this.getCacheStats(),
            debugMode: this.debugMode,
            loggingEnabled: this.logEnabled,
            performanceTracking: this.performanceTracking
        };
    }

    /**
     * Get debug log entries
     * @param {number} count - Number of recent entries to return
     * @returns {Array} Debug log entries
     */
    getDebugLog(count = 20) {
        return this.debugLog.slice(-count).reverse();
    }

    /**
     * Clear debug log
     */
    clearDebugLog() {
        this.debugLog = [];
        this.log('Debug log cleared');
    }

    /**
     * Log message (conditional based on logEnabled)
     * @param {...any} args - Arguments to log
     */
    log(...args) {
        if (this.logEnabled) {
            console.log('[LLMClient]', ...args);
        }
    }

    /**
     * Log error message (conditional based on logEnabled)
     * @param {...any} args - Arguments to log
     */
    error(...args) {
        if (this.logEnabled) {
            console.error('[LLMClient]', ...args);
        }
    }

    /**
     * Log debug message (conditional based on debugMode)
     * @param {...any} args - Arguments to log
     */
    debug(...args) {
        if (this.debugMode) {
            console.debug('[LLMClient]', ...args);
        }
    }
}
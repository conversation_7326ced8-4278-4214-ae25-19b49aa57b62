"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TOKEN_EXPIRATION_SECONDS = void 0;
exports.getTemporaryAuthToken = getTemporaryAuthToken;
const config_1 = require("./config");
const request_1 = require("./request");
const utils_1 = require("./utils");
exports.TOKEN_EXPIRATION_SECONDS = 120;
/**
 * Get a token to connect to the realtime endpoint.
 */
function getTemporaryAuthToken(app, config) {
    return __awaiter(this, void 0, void 0, function* () {
        const appId = (0, utils_1.parseEndpointId)(app);
        const token = yield (0, request_1.dispatchRequest)({
            method: "POST",
            targetUrl: `${(0, config_1.getRestApiUrl)()}/tokens/`,
            config,
            input: {
                allowed_apps: [appId.alias],
                token_expiration: exports.TOKEN_EXPIRATION_SECONDS,
            },
        });
        // keep this in case the response was wrapped (old versions of the proxy do that)
        // should be safe to remove in the future
        if (typeof token !== "string" && token["detail"]) {
            return token["detail"];
        }
        return token;
    });
}
//# sourceMappingURL=auth.js.map
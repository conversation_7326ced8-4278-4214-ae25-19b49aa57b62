import { RequiredConfig } from "./config";
/**
 * File support for the client. This interface establishes the contract for
 * uploading files to the server and transforming the input to replace file
 * objects with URLs.
 */
export interface StorageClient {
    /**
     * Upload a file to the server. Returns the URL of the uploaded file.
     * @param file the file to upload
     * @param options optional parameters, such as custom file name
     * @returns the URL of the uploaded file
     */
    upload: (file: Blob) => Promise<string>;
    /**
     * Transform the input to replace file objects with URLs. This is used
     * to transform the input before sending it to the server and ensures
     * that the server receives URLs instead of file objects.
     *
     * @param input the input to transform.
     * @returns the transformed input.
     */
    transformInput: (input: Record<string, any>) => Promise<Record<string, any>>;
}
type StorageClientDependencies = {
    config: RequiredConfig;
};
export declare function createStorageClient({ config, }: StorageClientDependencies): StorageClient;
export {};

{"version": 3, "file": "storage.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/storage.ts"], "names": [], "mappings": ";;;;;;;;;;;AAuLA,kDAkDC;AAzOD,qCAAyD;AACzD,uCAA4C;AAC5C,mCAAwC;AAqCxC;;;;;;GAMG;AACH,SAAS,2BAA2B,CAAC,WAAmB;;IACtD,MAAM,CAAC,CAAC,EAAE,QAAQ,CAAC,GAAG,WAAW,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC;IAC7C,OAAO,MAAA,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,CAAC,CAAC,mCAAI,KAAK,CAAC;AAC5C,CAAC;AAED;;;GAGG;AACH,SAAe,cAAc,CAC3B,IAAU,EACV,MAAsB,EACtB,WAAmB;;QAEnB,MAAM,QAAQ,GACZ,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,2BAA2B,CAAC,WAAW,CAAC,EAAE,CAAC;QAE3E,OAAO,MAAM,IAAA,yBAAe,EAA2C;YACrE,MAAM,EAAE,MAAM;YACd,0EAA0E;YAC1E,SAAS,EAAE,GAAG,IAAA,sBAAa,GAAE,kDAAkD;YAC/E,KAAK,EAAE;gBACL,YAAY,EAAE,WAAW;gBACzB,SAAS,EAAE,QAAQ;aACpB;YACD,MAAM;SACP,CAAC,CAAC;IACL,CAAC;CAAA;AAED;;;GAGG;AACH,SAAe,uBAAuB,CACpC,IAAU,EACV,MAAsB,EACtB,WAAmB;;QAEnB,MAAM,QAAQ,GACZ,IAAI,CAAC,IAAI,IAAI,GAAG,IAAI,CAAC,GAAG,EAAE,IAAI,2BAA2B,CAAC,WAAW,CAAC,EAAE,CAAC;QAE3E,OAAO,MAAM,IAAA,yBAAe,EAA2C;YACrE,MAAM,EAAE,MAAM;YACd,SAAS,EAAE,GAAG,IAAA,sBAAa,GAAE,4DAA4D;YACzF,KAAK,EAAE;gBACL,YAAY,EAAE,WAAW;gBACzB,SAAS,EAAE,QAAQ;aACpB;YACD,MAAM;SACP,CAAC,CAAC;IACL,CAAC;CAAA;AAOD,SAAe,iBAAiB;yDAC9B,SAAiB,EACjB,KAAW,EACX,MAAsB,EACtB,KAAK,GAAG,CAAC;QAET,IAAI,KAAK,KAAK,CAAC,EAAE,CAAC;YAChB,MAAM,IAAI,KAAK,CAAC,uCAAuC,CAAC,CAAC;QAC3D,CAAC;QAED,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC;QAE1C,IAAI,CAAC;YACH,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE;gBACtC,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,KAAK;aACZ,CAAC,CAAC;YAEH,OAAO,CAAC,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAoB,CAAC;QAC9D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,OAAO,MAAM,iBAAiB,CAAC,SAAS,EAAE,KAAK,EAAE,MAAM,EAAE,KAAK,GAAG,CAAC,CAAC,CAAC;QACtE,CAAC;IACH,CAAC;CAAA;AAED,SAAe,eAAe,CAC5B,IAAU,EACV,MAAsB;;QAEtB,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC;QAC1C,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,0BAA0B,CAAC;QAC5D,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,GAC5C,MAAM,uBAAuB,CAAC,IAAI,EAAE,MAAM,EAAE,WAAW,CAAC,CAAC;QAE3D,kCAAkC;QAClC,MAAM,SAAS,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,CAAC;QACnC,MAAM,MAAM,GAAG,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,IAAI,GAAG,SAAS,CAAC,CAAC;QAEhD,MAAM,SAAS,GAAG,IAAI,GAAG,CAAC,SAAS,CAAC,CAAC;QAErC,MAAM,SAAS,GAAsB,EAAE,CAAC;QAExC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,MAAM,EAAE,CAAC,EAAE,EAAE,CAAC;YAChC,MAAM,KAAK,GAAG,CAAC,GAAG,SAAS,CAAC;YAC5B,MAAM,GAAG,GAAG,IAAI,CAAC,GAAG,CAAC,KAAK,GAAG,SAAS,EAAE,IAAI,CAAC,IAAI,CAAC,CAAC;YAEnD,MAAM,KAAK,GAAG,IAAI,CAAC,KAAK,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAErC,MAAM,UAAU,GAAG,CAAC,GAAG,CAAC,CAAC;YACzB,gDAAgD;YAChD,MAAM,aAAa,GAAG,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,IAAI,UAAU,GAAG,SAAS,CAAC,MAAM,EAAE,CAAC;YAElG,SAAS,CAAC,IAAI,CAAC,MAAM,iBAAiB,CAAC,aAAa,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,CAAC;QACxE,CAAC;QAED,sBAAsB;QACtB,MAAM,WAAW,GAAG,GAAG,SAAS,CAAC,MAAM,GAAG,SAAS,CAAC,QAAQ,YAAY,SAAS,CAAC,MAAM,EAAE,CAAC;QAC3F,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,WAAW,EAAE;YACxC,MAAM,EAAE,MAAM;YACd,OAAO,EAAE;gBACP,cAAc,EAAE,kBAAkB;aACnC;YACD,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC;gBACnB,KAAK,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC/B,UAAU,EAAE,KAAK,CAAC,UAAU;oBAC5B,IAAI,EAAE,KAAK,CAAC,IAAI;iBACjB,CAAC,CAAC;aACJ,CAAC;SACH,CAAC,CAAC;QACH,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;QAEhC,OAAO,GAAG,CAAC;IACb,CAAC;CAAA;AASD,SAAgB,mBAAmB,CAAC,EAClC,MAAM,GACoB;IAC1B,MAAM,GAAG,GAAkB;QACzB,MAAM,EAAE,CAAO,IAAU,EAAE,EAAE;YAC3B,oDAAoD;YACpD,IAAI,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,IAAI,GAAG,IAAI,EAAE,CAAC;gBACjC,OAAO,MAAM,eAAe,CAAC,IAAI,EAAE,MAAM,CAAC,CAAC;YAC7C,CAAC;YAED,MAAM,WAAW,GAAG,IAAI,CAAC,IAAI,IAAI,0BAA0B,CAAC;YAE5D,MAAM,EAAE,KAAK,EAAE,eAAe,EAAE,GAAG,MAAM,CAAC;YAC1C,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,EAAE,GAAG,MAAM,cAAc,CACnE,IAAI,EACJ,MAAM,EACN,WAAW,CACZ,CAAC;YACF,MAAM,QAAQ,GAAG,MAAM,KAAK,CAAC,SAAS,EAAE;gBACtC,MAAM,EAAE,KAAK;gBACb,IAAI,EAAE,IAAI;gBACV,OAAO,EAAE;oBACP,cAAc,EAAE,IAAI,CAAC,IAAI,IAAI,0BAA0B;iBACxD;aACF,CAAC,CAAC;YACH,MAAM,eAAe,CAAC,QAAQ,CAAC,CAAC;YAChC,OAAO,GAAG,CAAC;QACb,CAAC,CAAA;QAED,8DAA8D;QAC9D,cAAc,EAAE,CAAO,KAAU,EAAgB,EAAE;YACjD,IAAI,KAAK,CAAC,OAAO,CAAC,KAAK,CAAC,EAAE,CAAC;gBACzB,OAAO,OAAO,CAAC,GAAG,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,EAAE,EAAE,CAAC,GAAG,CAAC,cAAc,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC;YACpE,CAAC;iBAAM,IAAI,KAAK,YAAY,IAAI,EAAE,CAAC;gBACjC,OAAO,MAAM,GAAG,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;YACjC,CAAC;iBAAM,IAAI,IAAA,qBAAa,EAAC,KAAK,CAAC,EAAE,CAAC;gBAChC,MAAM,WAAW,GAAG,KAA4B,CAAC;gBACjD,MAAM,QAAQ,GAAG,MAAM,CAAC,OAAO,CAAC,WAAW,CAAC,CAAC,GAAG,CAC9C,KAA4C,EAAE,0CAAvC,CAAC,GAAG,EAAE,KAAK,CAAC;oBACjB,OAAO,CAAC,GAAG,EAAE,MAAM,GAAG,CAAC,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;gBAChD,CAAC,CAAA,CACF,CAAC;gBACF,MAAM,OAAO,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC;gBAC5C,OAAO,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,CAAC;YACrC,CAAC;YACD,4EAA4E;YAC5E,OAAO,KAAK,CAAC;QACf,CAAC,CAAA;KACF,CAAC;IACF,OAAO,GAAG,CAAC;AACb,CAAC", "sourcesContent": ["import { getRestApiUrl, RequiredConfig } from \"./config\";\nimport { dispatchRequest } from \"./request\";\nimport { isPlainObject } from \"./utils\";\n/**\n * File support for the client. This interface establishes the contract for\n * uploading files to the server and transforming the input to replace file\n * objects with URLs.\n */\nexport interface StorageClient {\n  /**\n   * Upload a file to the server. Returns the URL of the uploaded file.\n   * @param file the file to upload\n   * @param options optional parameters, such as custom file name\n   * @returns the URL of the uploaded file\n   */\n  upload: (file: Blob) => Promise<string>;\n\n  /**\n   * Transform the input to replace file objects with URLs. This is used\n   * to transform the input before sending it to the server and ensures\n   * that the server receives URLs instead of file objects.\n   *\n   * @param input the input to transform.\n   * @returns the transformed input.\n   */\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  transformInput: (input: Record<string, any>) => Promise<Record<string, any>>;\n}\n\ntype InitiateUploadResult = {\n  file_url: string;\n  upload_url: string;\n};\n\ntype InitiateUploadData = {\n  file_name: string;\n  content_type: string | null;\n};\n\n/**\n * Get the file extension from the content type. This is used to generate\n * a file name if the file name is not provided.\n *\n * @param contentType the content type of the file.\n * @returns the file extension or `bin` if the content type is not recognized.\n */\nfunction getExtensionFromContentType(contentType: string): string {\n  const [_, fileType] = contentType.split(\"/\");\n  return fileType.split(/[-;]/)[0] ?? \"bin\";\n}\n\n/**\n * Initiate the upload of a file to the server. This returns the URL to upload\n * the file to and the URL of the file once it is uploaded.\n */\nasync function initiateUpload(\n  file: Blob,\n  config: RequiredConfig,\n  contentType: string,\n): Promise<InitiateUploadResult> {\n  const filename =\n    file.name || `${Date.now()}.${getExtensionFromContentType(contentType)}`;\n\n  return await dispatchRequest<InitiateUploadData, InitiateUploadResult>({\n    method: \"POST\",\n    // NOTE: We want to test V3 without making it the default at the API level\n    targetUrl: `${getRestApiUrl()}/storage/upload/initiate?storage_type=fal-cdn-v3`,\n    input: {\n      content_type: contentType,\n      file_name: filename,\n    },\n    config,\n  });\n}\n\n/**\n * Initiate the multipart upload of a file to the server. This returns the URL to upload\n * the file to and the URL of the file once it is uploaded.\n */\nasync function initiateMultipartUpload(\n  file: Blob,\n  config: RequiredConfig,\n  contentType: string,\n): Promise<InitiateUploadResult> {\n  const filename =\n    file.name || `${Date.now()}.${getExtensionFromContentType(contentType)}`;\n\n  return await dispatchRequest<InitiateUploadData, InitiateUploadResult>({\n    method: \"POST\",\n    targetUrl: `${getRestApiUrl()}/storage/upload/initiate-multipart?storage_type=fal-cdn-v3`,\n    input: {\n      content_type: contentType,\n      file_name: filename,\n    },\n    config,\n  });\n}\n\ntype MultipartObject = {\n  partNumber: number;\n  etag: string;\n};\n\nasync function partUploadRetries(\n  uploadUrl: string,\n  chunk: Blob,\n  config: RequiredConfig,\n  tries = 3,\n): Promise<MultipartObject> {\n  if (tries === 0) {\n    throw new Error(\"Part upload failed, retries exhausted\");\n  }\n\n  const { fetch, responseHandler } = config;\n\n  try {\n    const response = await fetch(uploadUrl, {\n      method: \"PUT\",\n      body: chunk,\n    });\n\n    return (await responseHandler(response)) as MultipartObject;\n  } catch (error) {\n    return await partUploadRetries(uploadUrl, chunk, config, tries - 1);\n  }\n}\n\nasync function multipartUpload(\n  file: Blob,\n  config: RequiredConfig,\n): Promise<string> {\n  const { fetch, responseHandler } = config;\n  const contentType = file.type || \"application/octet-stream\";\n  const { upload_url: uploadUrl, file_url: url } =\n    await initiateMultipartUpload(file, config, contentType);\n\n  // Break the file into 10MB chunks\n  const chunkSize = 10 * 1024 * 1024;\n  const chunks = Math.ceil(file.size / chunkSize);\n\n  const parsedUrl = new URL(uploadUrl);\n\n  const responses: MultipartObject[] = [];\n\n  for (let i = 0; i < chunks; i++) {\n    const start = i * chunkSize;\n    const end = Math.min(start + chunkSize, file.size);\n\n    const chunk = file.slice(start, end);\n\n    const partNumber = i + 1;\n    // {uploadUrl}/{part_number}?uploadUrlParams=...\n    const partUploadUrl = `${parsedUrl.origin}${parsedUrl.pathname}/${partNumber}${parsedUrl.search}`;\n\n    responses.push(await partUploadRetries(partUploadUrl, chunk, config));\n  }\n\n  // Complete the upload\n  const completeUrl = `${parsedUrl.origin}${parsedUrl.pathname}/complete${parsedUrl.search}`;\n  const response = await fetch(completeUrl, {\n    method: \"POST\",\n    headers: {\n      \"Content-Type\": \"application/json\",\n    },\n    body: JSON.stringify({\n      parts: responses.map((mpart) => ({\n        partNumber: mpart.partNumber,\n        etag: mpart.etag,\n      })),\n    }),\n  });\n  await responseHandler(response);\n\n  return url;\n}\n\n// eslint-disable-next-line @typescript-eslint/no-explicit-any\ntype KeyValuePair = [string, any];\n\ntype StorageClientDependencies = {\n  config: RequiredConfig;\n};\n\nexport function createStorageClient({\n  config,\n}: StorageClientDependencies): StorageClient {\n  const ref: StorageClient = {\n    upload: async (file: Blob) => {\n      // Check for 90+ MB file size to do multipart upload\n      if (file.size > 90 * 1024 * 1024) {\n        return await multipartUpload(file, config);\n      }\n\n      const contentType = file.type || \"application/octet-stream\";\n\n      const { fetch, responseHandler } = config;\n      const { upload_url: uploadUrl, file_url: url } = await initiateUpload(\n        file,\n        config,\n        contentType,\n      );\n      const response = await fetch(uploadUrl, {\n        method: \"PUT\",\n        body: file,\n        headers: {\n          \"Content-Type\": file.type || \"application/octet-stream\",\n        },\n      });\n      await responseHandler(response);\n      return url;\n    },\n\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    transformInput: async (input: any): Promise<any> => {\n      if (Array.isArray(input)) {\n        return Promise.all(input.map((item) => ref.transformInput(item)));\n      } else if (input instanceof Blob) {\n        return await ref.upload(input);\n      } else if (isPlainObject(input)) {\n        const inputObject = input as Record<string, any>;\n        const promises = Object.entries(inputObject).map(\n          async ([key, value]): Promise<KeyValuePair> => {\n            return [key, await ref.transformInput(value)];\n          },\n        );\n        const results = await Promise.all(promises);\n        return Object.fromEntries(results);\n      }\n      // Return the input as is if it's neither an object nor a file/blob/data URI\n      return input;\n    },\n  };\n  return ref;\n}\n"]}
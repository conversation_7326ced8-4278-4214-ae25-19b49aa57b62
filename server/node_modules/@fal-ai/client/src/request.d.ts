import { RequiredConfig } from "./config";
import { ResponseH<PERSON><PERSON> } from "./response";
import { type RetryOptions } from "./retry";
import { RunOptions, UrlOptions } from "./types/common";
type RequestOptions = {
    responseHandler?: ResponseHandler<any>;
    /**
     * Retry configuration for this specific request.
     * If not specified, uses the default retry configuration from the client config.
     */
    retry?: Partial<RetryOptions>;
};
type RequestParams<Input = any> = {
    method?: string;
    targetUrl: string;
    input?: Input;
    config: RequiredConfig;
    options?: RequestOptions & RequestInit;
    headers?: Record<string, string>;
};
export declare function dispatchRequest<Input, Output>(params: RequestParams<Input>): Promise<Output>;
/**
 * Builds the final url to run the function based on its `id` or alias and
 * a the options from `RunOptions<Input>`.
 *
 * @private
 * @param id the function id or alias
 * @param options the run options
 * @returns the final url to run the function
 */
export declare function buildUrl<Input>(id: string, options?: RunOptions<Input> & UrlOptions): string;
export {};

{"version": 3, "file": "client.js", "sourceRoot": "", "sources": ["../../../../../libs/client/src/types/client.ts"], "names": [], "mappings": "", "sourcesContent": ["import { EndpointTypeMap } from \"./endpoints\";\n\n// eslint-disable-next-line @typescript-eslint/ban-types\nexport type EndpointType = keyof EndpointTypeMap | (string & {});\n\n// Get input type based on endpoint ID\nexport type InputType<T extends string> = T extends keyof EndpointTypeMap\n  ? EndpointTypeMap[T][\"input\"]\n  : Record<string, any>;\n\n// Get output type based on endpoint ID\nexport type OutputType<T extends string> = T extends keyof EndpointTypeMap\n  ? EndpointTypeMap[T][\"output\"]\n  : any;\n"]}
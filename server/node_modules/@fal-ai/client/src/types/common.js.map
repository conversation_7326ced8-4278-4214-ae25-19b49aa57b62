{"version": 3, "file": "common.js", "sourceRoot": "", "sources": ["../../../../../libs/client/src/types/common.ts"], "names": [], "mappings": ";;AA0FA,sCAEC;AAED,wDAEC;AAND,SAAgB,aAAa,CAAC,GAAQ;IACpC,OAAO,GAAG,IAAI,GAAG,CAAC,MAAM,IAAI,GAAG,CAAC,YAAY,CAAC;AAC/C,CAAC;AAED,SAAgB,sBAAsB,CAAC,GAAQ;IAC7C,OAAO,aAAa,CAAC,GAAG,CAAC,IAAI,GAAG,CAAC,MAAM,KAAK,WAAW,CAAC;AAC1D,CAAC", "sourcesContent": ["/**\n * Represents an API result, containing the data,\n *  the request ID and any other relevant information.\n */\nexport type Result<T> = {\n  data: T;\n  requestId: string;\n};\n\n/**\n * The function input and other configuration when running\n * the function, such as the HTTP method to use.\n */\nexport type RunOptions<Input> = {\n  /**\n   * The function input. It will be submitted either as query params\n   * or the body payload, depending on the `method`.\n   */\n  readonly input?: Input;\n\n  /**\n   * The HTTP method, defaults to `post`;\n   */\n  readonly method?: \"get\" | \"post\" | \"put\" | \"delete\" | string;\n\n  /**\n   * The abort signal to cancel the request.\n   */\n  readonly abortSignal?: AbortSignal;\n};\n\nexport type UrlOptions = {\n  /**\n   * If `true`, the function will use the queue to run the function\n   * asynchronously and return the result in a separate call. This\n   * influences how the URL is built.\n   */\n  readonly subdomain?: string;\n\n  /**\n   * The query parameters to include in the URL.\n   */\n  readonly query?: Record<string, string>;\n\n  /**\n   * The path to append to the function URL.\n   */\n  path?: string;\n};\n\nexport type RequestLog = {\n  message: string;\n  level: \"STDERR\" | \"STDOUT\" | \"ERROR\" | \"INFO\" | \"WARN\" | \"DEBUG\";\n  source: \"USER\";\n  timestamp: string; // Using string to represent date-time format, but you could also use 'Date' type if you're going to construct Date objects.\n};\n\nexport type Metrics = {\n  inference_time: number | null;\n};\n\ninterface BaseQueueStatus {\n  status: \"IN_QUEUE\" | \"IN_PROGRESS\" | \"COMPLETED\";\n  request_id: string;\n  response_url: string;\n  status_url: string;\n  cancel_url: string;\n}\n\nexport interface InQueueQueueStatus extends BaseQueueStatus {\n  status: \"IN_QUEUE\";\n  queue_position: number;\n}\n\nexport interface InProgressQueueStatus extends BaseQueueStatus {\n  status: \"IN_PROGRESS\";\n  logs: RequestLog[];\n}\n\nexport interface CompletedQueueStatus extends BaseQueueStatus {\n  status: \"COMPLETED\";\n  logs: RequestLog[];\n  metrics?: Metrics;\n}\n\nexport type QueueStatus =\n  | InProgressQueueStatus\n  | CompletedQueueStatus\n  | InQueueQueueStatus;\n\nexport function isQueueStatus(obj: any): obj is QueueStatus {\n  return obj && obj.status && obj.response_url;\n}\n\nexport function isCompletedQueueStatus(obj: any): obj is CompletedQueueStatus {\n  return isQueueStatus(obj) && obj.status === \"COMPLETED\";\n}\n\nexport type ValidationErrorInfo = {\n  msg: string;\n  loc: Array<string | number>;\n  type: string;\n};\n\n/**\n * Represents the response from a WebHook request.\n * This is a union type that varies based on the `status` property.\n *\n * @template Payload - The type of the payload in the response. It defaults to `any`,\n * allowing for flexibility in specifying the structure of the payload.\n */\nexport type WebHookResponse<Payload = any> =\n  | {\n      /** Indicates a successful response. */\n      status: \"OK\";\n      /** The payload of the response, structure determined by the Payload type. */\n      payload: Payload;\n      /** Error is never present in a successful response. */\n      error: never;\n      /** The unique identifier for the request. */\n      request_id: string;\n    }\n  | {\n      /** Indicates an unsuccessful response. */\n      status: \"ERROR\";\n      /** The payload of the response, structure determined by the Payload type. */\n      payload: Payload;\n      /** Description of the error that occurred. */\n      error: string;\n      /** The unique identifier for the request. */\n      request_id: string;\n    };\n"]}
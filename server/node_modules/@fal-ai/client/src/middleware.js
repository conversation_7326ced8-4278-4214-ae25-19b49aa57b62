"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TARGET_URL_HEADER = void 0;
exports.withMiddleware = withMiddleware;
exports.withProxy = withProxy;
/**
 * Setup a execution chain of middleware functions.
 *
 * @param middlewares one or more middleware functions.
 * @returns a middleware function that executes the given middlewares in order.
 */
function withMiddleware(...middlewares) {
    const isDefined = (middleware) => typeof middleware === "function";
    return (config) => __awaiter(this, void 0, void 0, function* () {
        let currentConfig = Object.assign({}, config);
        for (const middleware of middlewares.filter(isDefined)) {
            currentConfig = yield middleware(currentConfig);
        }
        return currentConfig;
    });
}
exports.TARGET_URL_HEADER = "x-fal-target-url";
function withProxy(config) {
    const passthrough = (requestConfig) => Promise.resolve(requestConfig);
    // when running on the server, we don't need to proxy the request
    if (typeof window === "undefined") {
        return passthrough;
    }
    // if x-fal-target-url is already set, we skip it
    return (requestConfig) => requestConfig.headers && exports.TARGET_URL_HEADER in requestConfig
        ? passthrough(requestConfig)
        : Promise.resolve(Object.assign(Object.assign({}, requestConfig), { url: config.targetUrl, headers: Object.assign(Object.assign({}, (requestConfig.headers || {})), { [exports.TARGET_URL_HEADER]: requestConfig.url }) }));
}
//# sourceMappingURL=middleware.js.map
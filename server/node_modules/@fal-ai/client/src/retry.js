"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DEFAULT_RETRY_OPTIONS = exports.DEFAULT_RETRYABLE_STATUS_CODES = void 0;
exports.isRetryableError = isRetryableError;
exports.calculateBackoffDelay = calculateBackoffDelay;
exports.executeWithRetry = executeWithRetry;
const response_1 = require("./response");
const utils_1 = require("./utils");
/**
 * Base retryable status codes for most requests
 */
exports.DEFAULT_RETRYABLE_STATUS_CODES = [429, 502, 503, 504];
exports.DEFAULT_RETRY_OPTIONS = {
    maxRetries: 3,
    baseDelay: 1000,
    maxDelay: 30000,
    backoffMultiplier: 2,
    retryableStatusCodes: exports.DEFAULT_RETRYABLE_STATUS_CODES,
    enableJitter: true,
};
/**
 * Determines if an error is retryable based on the status code
 */
function isRetryableError(error, retryableStatusCodes) {
    return (error instanceof response_1.ApiError && retryableStatusCodes.includes(error.status));
}
/**
 * Calculates the backoff delay for a given attempt using exponential backoff
 */
function calculateBackoffDelay(attempt, baseDelay, maxDelay, backoffMultiplier, enableJitter) {
    const exponentialDelay = Math.min(baseDelay * Math.pow(backoffMultiplier, attempt), maxDelay);
    if (enableJitter) {
        // Add ±25% jitter to prevent thundering herd
        const jitter = 0.25 * exponentialDelay * (Math.random() * 2 - 1);
        return Math.max(0, exponentialDelay + jitter);
    }
    return exponentialDelay;
}
/**
 * Executes an operation with retry logic and returns both result and metrics
 */
function executeWithRetry(operation, options, onRetry) {
    return __awaiter(this, void 0, void 0, function* () {
        const metrics = {
            totalAttempts: 0,
            totalDelay: 0,
        };
        let lastError;
        for (let attempt = 0; attempt <= options.maxRetries; attempt++) {
            metrics.totalAttempts++;
            try {
                const result = yield operation();
                return { result, metrics };
            }
            catch (error) {
                lastError = error;
                metrics.lastError = error;
                if (attempt === options.maxRetries ||
                    !isRetryableError(error, options.retryableStatusCodes)) {
                    throw error;
                }
                const delay = calculateBackoffDelay(attempt, options.baseDelay, options.maxDelay, options.backoffMultiplier, options.enableJitter);
                metrics.totalDelay += delay;
                if (onRetry) {
                    onRetry(attempt + 1, error, delay);
                }
                yield (0, utils_1.sleep)(delay);
            }
        }
        throw lastError;
    });
}
//# sourceMappingURL=retry.js.map
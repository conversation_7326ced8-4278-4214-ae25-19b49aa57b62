"use strict";
var __awaiter = (this && this.__awaiter) || function (thisArg, _arguments, P, generator) {
    function adopt(value) { return value instanceof P ? value : new P(function (resolve) { resolve(value); }); }
    return new (P || (P = Promise))(function (resolve, reject) {
        function fulfilled(value) { try { step(generator.next(value)); } catch (e) { reject(e); } }
        function rejected(value) { try { step(generator["throw"](value)); } catch (e) { reject(e); } }
        function step(result) { result.done ? resolve(result.value) : adopt(result.value).then(fulfilled, rejected); }
        step((generator = generator.apply(thisArg, _arguments || [])).next());
    });
};
var __rest = (this && this.__rest) || function (s, e) {
    var t = {};
    for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p) && e.indexOf(p) < 0)
        t[p] = s[p];
    if (s != null && typeof Object.getOwnPropertySymbols === "function")
        for (var i = 0, p = Object.getOwnPropertySymbols(s); i < p.length; i++) {
            if (e.indexOf(p[i]) < 0 && Object.prototype.propertyIsEnumerable.call(s, p[i]))
                t[p[i]] = s[p[i]];
        }
    return t;
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.dispatchRequest = dispatchRequest;
exports.buildUrl = buildUrl;
const retry_1 = require("./retry");
const runtime_1 = require("./runtime");
const utils_1 = require("./utils");
const isCloudflareWorkers = typeof navigator !== "undefined" &&
    (navigator === null || navigator === void 0 ? void 0 : navigator.userAgent) === "Cloudflare-Workers";
function dispatchRequest(params) {
    return __awaiter(this, void 0, void 0, function* () {
        var _a;
        const { targetUrl, input, config, options = {} } = params;
        const { credentials: credentialsValue, requestMiddleware, responseHandler, fetch, } = config;
        const retryOptions = Object.assign(Object.assign({}, config.retry), (options.retry || {}));
        const executeRequest = () => __awaiter(this, void 0, void 0, function* () {
            var _a, _b, _c;
            const userAgent = (0, runtime_1.isBrowser)() ? {} : { "User-Agent": (0, runtime_1.getUserAgent)() };
            const credentials = typeof credentialsValue === "function"
                ? credentialsValue()
                : credentialsValue;
            const { method, url, headers } = yield requestMiddleware({
                method: ((_b = (_a = params.method) !== null && _a !== void 0 ? _a : options.method) !== null && _b !== void 0 ? _b : "post").toUpperCase(),
                url: targetUrl,
                headers: params.headers,
            });
            const authHeader = credentials
                ? { Authorization: `Key ${credentials}` }
                : {};
            const requestHeaders = Object.assign(Object.assign(Object.assign(Object.assign({}, authHeader), { Accept: "application/json", "Content-Type": "application/json" }), userAgent), (headers !== null && headers !== void 0 ? headers : {}));
            const { responseHandler: customResponseHandler, retry: _ } = options, requestInit = __rest(options, ["responseHandler", "retry"]);
            const response = yield fetch(url, Object.assign(Object.assign(Object.assign(Object.assign({}, requestInit), { method, headers: Object.assign(Object.assign({}, requestHeaders), ((_c = requestInit.headers) !== null && _c !== void 0 ? _c : {})) }), (!isCloudflareWorkers && { mode: "cors" })), { signal: options.signal, body: method.toLowerCase() !== "get" && input
                    ? JSON.stringify(input)
                    : undefined }));
            const handleResponse = customResponseHandler !== null && customResponseHandler !== void 0 ? customResponseHandler : responseHandler;
            return yield handleResponse(response);
        });
        let lastError;
        for (let attempt = 0; attempt <= retryOptions.maxRetries; attempt++) {
            try {
                return yield executeRequest();
            }
            catch (error) {
                lastError = error;
                const shouldNotRetry = attempt === retryOptions.maxRetries ||
                    !(0, retry_1.isRetryableError)(error, retryOptions.retryableStatusCodes) ||
                    ((_a = options.signal) === null || _a === void 0 ? void 0 : _a.aborted);
                if (shouldNotRetry) {
                    throw error;
                }
                const delay = (0, retry_1.calculateBackoffDelay)(attempt, retryOptions.baseDelay, retryOptions.maxDelay, retryOptions.backoffMultiplier, retryOptions.enableJitter);
                yield (0, utils_1.sleep)(delay);
            }
        }
        throw lastError;
    });
}
/**
 * Builds the final url to run the function based on its `id` or alias and
 * a the options from `RunOptions<Input>`.
 *
 * @private
 * @param id the function id or alias
 * @param options the run options
 * @returns the final url to run the function
 */
function buildUrl(id, options = {}) {
    var _a, _b;
    const method = ((_a = options.method) !== null && _a !== void 0 ? _a : "post").toLowerCase();
    const path = ((_b = options.path) !== null && _b !== void 0 ? _b : "").replace(/^\//, "").replace(/\/{2,}/, "/");
    const input = options.input;
    const params = Object.assign(Object.assign({}, (options.query || {})), (method === "get" ? input : {}));
    const queryParams = Object.keys(params).length > 0
        ? `?${new URLSearchParams(params).toString()}`
        : "";
    // if a fal url is passed, just use it
    if ((0, utils_1.isValidUrl)(id)) {
        const url = id.endsWith("/") ? id : `${id}/`;
        return `${url}${path}${queryParams}`;
    }
    const appId = (0, utils_1.ensureEndpointIdFormat)(id);
    const subdomain = options.subdomain ? `${options.subdomain}.` : "";
    const url = `https://${subdomain}fal.run/${appId}/${path}`;
    return `${url.replace(/\/$/, "")}${queryParams}`;
}
//# sourceMappingURL=request.js.map
import { type FalClient } from "./client";
import { Config } from "./config";
export { createFalClient, type FalClient } from "./client";
export { withMiddleware, withProxy } from "./middleware";
export type { RequestMiddleware } from "./middleware";
export type { QueueClient } from "./queue";
export type { RealtimeClient } from "./realtime";
export { ApiError, ValidationError } from "./response";
export type { ResponseHandler } from "./response";
export { isRetryableError } from "./retry";
export type { RetryOptions } from "./retry";
export type { StorageClient } from "./storage";
export type { FalStream, StreamingClient } from "./streaming";
export * from "./types/common";
export type { QueueStatus, ValidationErrorInfo, WebHookResponse, } from "./types/common";
export { parseEndpointId } from "./utils";
type SingletonFalClient = {
    config(config: Config): void;
} & FalClient;
/**
 * Creates a singleton instance of the client. This is useful as a compatibility
 * layer for existing code that uses the clients version prior to 1.0.0.
 */
export declare const fal: SingletonFalClient;

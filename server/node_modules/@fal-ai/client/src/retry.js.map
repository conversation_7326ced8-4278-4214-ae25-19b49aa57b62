{"version": 3, "file": "retry.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/retry.ts"], "names": [], "mappings": ";;;;;;;;;;;;AA6BA,4CAOC;AAKD,sDAmBC;AAcD,4CAgDC;AA1HD,yCAAsC;AACtC,mCAAgC;AAWhC;;GAEG;AACU,QAAA,8BAA8B,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;AAEtD,QAAA,qBAAqB,GAAiB;IACjD,UAAU,EAAE,CAAC;IACb,SAAS,EAAE,IAAI;IACf,QAAQ,EAAE,KAAK;IACf,iBAAiB,EAAE,CAAC;IACpB,oBAAoB,EAAE,sCAA8B;IACpD,YAAY,EAAE,IAAI;CACnB,CAAC;AAEF;;GAEG;AACH,SAAgB,gBAAgB,CAC9B,KAAU,EACV,oBAA8B;IAE9B,OAAO,CACL,KAAK,YAAY,mBAAQ,IAAI,oBAAoB,CAAC,QAAQ,CAAC,KAAK,CAAC,MAAM,CAAC,CACzE,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,qBAAqB,CACnC,OAAe,EACf,SAAiB,EACjB,QAAgB,EAChB,iBAAyB,EACzB,YAAqB;IAErB,MAAM,gBAAgB,GAAG,IAAI,CAAC,GAAG,CAC/B,SAAS,GAAG,IAAI,CAAC,GAAG,CAAC,iBAAiB,EAAE,OAAO,CAAC,EAChD,QAAQ,CACT,CAAC;IAEF,IAAI,YAAY,EAAE,CAAC;QACjB,6CAA6C;QAC7C,MAAM,MAAM,GAAG,IAAI,GAAG,gBAAgB,GAAG,CAAC,IAAI,CAAC,MAAM,EAAE,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;QACjE,OAAO,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,gBAAgB,GAAG,MAAM,CAAC,CAAC;IAChD,CAAC;IAED,OAAO,gBAAgB,CAAC;AAC1B,CAAC;AAWD;;GAEG;AACH,SAAsB,gBAAgB,CACpC,SAA2B,EAC3B,OAAqB,EACrB,OAA8D;;QAE9D,MAAM,OAAO,GAAiB;YAC5B,aAAa,EAAE,CAAC;YAChB,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,IAAI,SAAc,CAAC;QAEnB,KAAK,IAAI,OAAO,GAAG,CAAC,EAAE,OAAO,IAAI,OAAO,CAAC,UAAU,EAAE,OAAO,EAAE,EAAE,CAAC;YAC/D,OAAO,CAAC,aAAa,EAAE,CAAC;YAExB,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,SAAS,EAAE,CAAC;gBACjC,OAAO,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC;YAC7B,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,SAAS,GAAG,KAAK,CAAC;gBAClB,OAAO,CAAC,SAAS,GAAG,KAAK,CAAC;gBAE1B,IACE,OAAO,KAAK,OAAO,CAAC,UAAU;oBAC9B,CAAC,gBAAgB,CAAC,KAAK,EAAE,OAAO,CAAC,oBAAoB,CAAC,EACtD,CAAC;oBACD,MAAM,KAAK,CAAC;gBACd,CAAC;gBAED,MAAM,KAAK,GAAG,qBAAqB,CACjC,OAAO,EACP,OAAO,CAAC,SAAS,EACjB,OAAO,CAAC,QAAQ,EAChB,OAAO,CAAC,iBAAiB,EACzB,OAAO,CAAC,YAAY,CACrB,CAAC;gBAEF,OAAO,CAAC,UAAU,IAAI,KAAK,CAAC;gBAE5B,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,OAAO,GAAG,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC;gBACrC,CAAC;gBAED,MAAM,IAAA,aAAK,EAAC,KAAK,CAAC,CAAC;YACrB,CAAC;QACH,CAAC;QAED,MAAM,SAAS,CAAC;IAClB,CAAC;CAAA", "sourcesContent": ["import { ApiError } from \"./response\";\nimport { sleep } from \"./utils\";\n\nexport type RetryOptions = {\n  maxRetries: number;\n  baseDelay: number;\n  maxDelay: number;\n  backoffMultiplier: number;\n  retryableStatusCodes: number[];\n  enableJitter: boolean;\n};\n\n/**\n * Base retryable status codes for most requests\n */\nexport const DEFAULT_RETRYABLE_STATUS_CODES = [429, 502, 503, 504];\n\nexport const DEFAULT_RETRY_OPTIONS: RetryOptions = {\n  maxRetries: 3,\n  baseDelay: 1000,\n  maxDelay: 30000,\n  backoffMultiplier: 2,\n  retryableStatusCodes: DEFAULT_RETRYABLE_STATUS_CODES,\n  enableJitter: true,\n};\n\n/**\n * Determines if an error is retryable based on the status code\n */\nexport function isRetryableError(\n  error: any,\n  retryableStatusCodes: number[],\n): boolean {\n  return (\n    error instanceof ApiError && retryableStatusCodes.includes(error.status)\n  );\n}\n\n/**\n * Calculates the backoff delay for a given attempt using exponential backoff\n */\nexport function calculateBackoffDelay(\n  attempt: number,\n  baseDelay: number,\n  maxDelay: number,\n  backoffMultiplier: number,\n  enableJitter: boolean,\n): number {\n  const exponentialDelay = Math.min(\n    baseDelay * Math.pow(backoffMultiplier, attempt),\n    maxDelay,\n  );\n\n  if (enableJitter) {\n    // Add ±25% jitter to prevent thundering herd\n    const jitter = 0.25 * exponentialDelay * (Math.random() * 2 - 1);\n    return Math.max(0, exponentialDelay + jitter);\n  }\n\n  return exponentialDelay;\n}\n\n/**\n * Retry metrics for tracking retry attempts\n */\nexport interface RetryMetrics {\n  totalAttempts: number;\n  totalDelay: number;\n  lastError?: any;\n}\n\n/**\n * Executes an operation with retry logic and returns both result and metrics\n */\nexport async function executeWithRetry<T>(\n  operation: () => Promise<T>,\n  options: RetryOptions,\n  onRetry?: (attempt: number, error: any, delay: number) => void,\n): Promise<{ result: T; metrics: RetryMetrics }> {\n  const metrics: RetryMetrics = {\n    totalAttempts: 0,\n    totalDelay: 0,\n  };\n\n  let lastError: any;\n\n  for (let attempt = 0; attempt <= options.maxRetries; attempt++) {\n    metrics.totalAttempts++;\n\n    try {\n      const result = await operation();\n      return { result, metrics };\n    } catch (error) {\n      lastError = error;\n      metrics.lastError = error;\n\n      if (\n        attempt === options.maxRetries ||\n        !isRetryableError(error, options.retryableStatusCodes)\n      ) {\n        throw error;\n      }\n\n      const delay = calculateBackoffDelay(\n        attempt,\n        options.baseDelay,\n        options.maxDelay,\n        options.backoffMultiplier,\n        options.enableJitter,\n      );\n\n      metrics.totalDelay += delay;\n\n      if (onRetry) {\n        onRetry(attempt + 1, error, delay);\n      }\n\n      await sleep(delay);\n    }\n  }\n\n  throw lastError;\n}\n"]}
export type RetryOptions = {
    maxRetries: number;
    baseDelay: number;
    maxDelay: number;
    backoffMultiplier: number;
    retryableStatusCodes: number[];
    enableJitter: boolean;
};
/**
 * Base retryable status codes for most requests
 */
export declare const DEFAULT_RETRYABLE_STATUS_CODES: number[];
export declare const DEFAULT_RETRY_OPTIONS: RetryOptions;
/**
 * Determines if an error is retryable based on the status code
 */
export declare function isRetryableError(error: any, retryableStatusCodes: number[]): boolean;
/**
 * Calculates the backoff delay for a given attempt using exponential backoff
 */
export declare function calculateBackoffDelay(attempt: number, baseDelay: number, maxDelay: number, backoffMultiplier: number, enableJitter: boolean): number;
/**
 * Retry metrics for tracking retry attempts
 */
export interface RetryMetrics {
    totalAttempts: number;
    totalDelay: number;
    lastError?: any;
}
/**
 * Executes an operation with retry logic and returns both result and metrics
 */
export declare function executeWithRetry<T>(operation: () => Promise<T>, options: RetryOptions, onRetry?: (attempt: number, error: any, delay: number) => void): Promise<{
    result: T;
    metrics: RetryMetrics;
}>;

{"version": 3, "file": "middleware.js", "sourceRoot": "", "sources": ["../../../../libs/client/src/middleware.ts"], "names": [], "mappings": ";;;;;;;;;;;;AAyBA,wCAaC;AAQD,8BAmBC;AA9CD;;;;;GAKG;AACH,SAAgB,cAAc,CAC5B,GAAG,WAAgC;IAEnC,MAAM,SAAS,GAAG,CAAC,UAA6B,EAAW,EAAE,CAC3D,OAAO,UAAU,KAAK,UAAU,CAAC;IAEnC,OAAO,CAAO,MAAqB,EAAE,EAAE;QACrC,IAAI,aAAa,qBAAQ,MAAM,CAAE,CAAC;QAClC,KAAK,MAAM,UAAU,IAAI,WAAW,CAAC,MAAM,CAAC,SAAS,CAAC,EAAE,CAAC;YACvD,aAAa,GAAG,MAAM,UAAU,CAAC,aAAa,CAAC,CAAC;QAClD,CAAC;QACD,OAAO,aAAa,CAAC;IACvB,CAAC,CAAA,CAAC;AACJ,CAAC;AAMY,QAAA,iBAAiB,GAAG,kBAAkB,CAAC;AAEpD,SAAgB,SAAS,CAAC,MAA0B;IAClD,MAAM,WAAW,GAAG,CAAC,aAA4B,EAAE,EAAE,CACnD,OAAO,CAAC,OAAO,CAAC,aAAa,CAAC,CAAC;IACjC,iEAAiE;IACjE,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE,CAAC;QAClC,OAAO,WAAW,CAAC;IACrB,CAAC;IACD,iDAAiD;IACjD,OAAO,CAAC,aAAa,EAAE,EAAE,CACvB,aAAa,CAAC,OAAO,IAAI,yBAAiB,IAAI,aAAa;QACzD,CAAC,CAAC,WAAW,CAAC,aAAa,CAAC;QAC5B,CAAC,CAAC,OAAO,CAAC,OAAO,iCACV,aAAa,KAChB,GAAG,EAAE,MAAM,CAAC,SAAS,EACrB,OAAO,kCACF,CAAC,aAAa,CAAC,OAAO,IAAI,EAAE,CAAC,KAChC,CAAC,yBAAiB,CAAC,EAAE,aAAa,CAAC,GAAG,OAExC,CAAC;AACX,CAAC", "sourcesContent": ["/**\n * A request configuration object.\n *\n * **Note:** This is a simplified version of the `RequestConfig` type from the\n * `fetch` API. It contains only the properties that are relevant for the\n * fal client. It also works around the fact that the `fetch` API `Request`\n * does not support mutability, its clone method has critical limitations\n * to our use case.\n */\nexport type RequestConfig = {\n  url: string;\n  method: string;\n  headers?: Record<string, string | string[]>;\n};\n\nexport type RequestMiddleware = (\n  request: RequestConfig,\n) => Promise<RequestConfig>;\n\n/**\n * Setup a execution chain of middleware functions.\n *\n * @param middlewares one or more middleware functions.\n * @returns a middleware function that executes the given middlewares in order.\n */\nexport function withMiddleware(\n  ...middlewares: RequestMiddleware[]\n): RequestMiddleware {\n  const isDefined = (middleware: RequestMiddleware): boolean =>\n    typeof middleware === \"function\";\n\n  return async (config: RequestConfig) => {\n    let currentConfig = { ...config };\n    for (const middleware of middlewares.filter(isDefined)) {\n      currentConfig = await middleware(currentConfig);\n    }\n    return currentConfig;\n  };\n}\n\nexport type RequestProxyConfig = {\n  targetUrl: string;\n};\n\nexport const TARGET_URL_HEADER = \"x-fal-target-url\";\n\nexport function withProxy(config: RequestProxyConfig): RequestMiddleware {\n  const passthrough = (requestConfig: RequestConfig) =>\n    Promise.resolve(requestConfig);\n  // when running on the server, we don't need to proxy the request\n  if (typeof window === \"undefined\") {\n    return passthrough;\n  }\n  // if x-fal-target-url is already set, we skip it\n  return (requestConfig) =>\n    requestConfig.headers && TARGET_URL_HEADER in requestConfig\n      ? passthrough(requestConfig)\n      : Promise.resolve({\n          ...requestConfig,\n          url: config.targetUrl,\n          headers: {\n            ...(requestConfig.headers || {}),\n            [TARGET_URL_HEADER]: requestConfig.url,\n          },\n        });\n}\n"]}
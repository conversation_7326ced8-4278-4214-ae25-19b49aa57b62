"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __exportStar = (this && this.__exportStar) || function(m, exports) {
    for (var p in m) if (p !== "default" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.fal = exports.parseEndpointId = exports.isRetryableError = exports.ValidationError = exports.ApiError = exports.withProxy = exports.withMiddleware = exports.createFalClient = void 0;
const client_1 = require("./client");
var client_2 = require("./client");
Object.defineProperty(exports, "createFalClient", { enumerable: true, get: function () { return client_2.createFalClient; } });
var middleware_1 = require("./middleware");
Object.defineProperty(exports, "withMiddleware", { enumerable: true, get: function () { return middleware_1.withMiddleware; } });
Object.defineProperty(exports, "withProxy", { enumerable: true, get: function () { return middleware_1.withProxy; } });
var response_1 = require("./response");
Object.defineProperty(exports, "ApiError", { enumerable: true, get: function () { return response_1.ApiError; } });
Object.defineProperty(exports, "ValidationError", { enumerable: true, get: function () { return response_1.ValidationError; } });
var retry_1 = require("./retry");
Object.defineProperty(exports, "isRetryableError", { enumerable: true, get: function () { return retry_1.isRetryableError; } });
__exportStar(require("./types/common"), exports);
var utils_1 = require("./utils");
Object.defineProperty(exports, "parseEndpointId", { enumerable: true, get: function () { return utils_1.parseEndpointId; } });
/**
 * Creates a singleton instance of the client. This is useful as a compatibility
 * layer for existing code that uses the clients version prior to 1.0.0.
 */
exports.fal = (function createSingletonFalClient() {
    let currentInstance = (0, client_1.createFalClient)();
    return {
        config(config) {
            currentInstance = (0, client_1.createFalClient)(config);
        },
        get queue() {
            return currentInstance.queue;
        },
        get realtime() {
            return currentInstance.realtime;
        },
        get storage() {
            return currentInstance.storage;
        },
        get streaming() {
            return currentInstance.streaming;
        },
        run(id, options) {
            return currentInstance.run(id, options);
        },
        subscribe(endpointId, options) {
            return currentInstance.subscribe(endpointId, options);
        },
        stream(endpointId, options) {
            return currentInstance.stream(endpointId, options);
        },
    };
})();
//# sourceMappingURL=index.js.map
export declare function utf8Count(str: string): number;
export declare function utf8EncodeJs(str: string, output: Uint8Array, outputOffset: number): void;
export declare function utf8EncodeTE(str: string, output: Uint8Array, outputOffset: number): void;
export declare function utf8Encode(str: string, output: Uint8Array, outputOffset: number): void;
export declare function utf8DecodeJs(bytes: Uint8Array, inputOffset: number, byteLength: number): string;
export declare function utf8DecodeTD(bytes: Uint8Array, inputOffset: number, byteLength: number): string;
export declare function utf8Decode(bytes: Uint8Array, inputOffset: number, byteLength: number): string;

{"name": "@msgpack/msgpack", "version": "3.1.2", "description": "MessagePack for ECMA-262/JavaScript/TypeScript", "author": "The MessagePack community", "license": "ISC", "main": "./dist.cjs/index.cjs", "module": "./dist.esm/index.mjs", "cdn": "./dist.umd/msgpack.min.js", "unpkg": "./dist.umd/msgpack.min.js", "types": "./dist.esm/index.d.ts", "sideEffects": false, "scripts": {"build": "npm publish --dry-run", "prepare": "npm run clean && webpack --bail && tsc --build tsconfig.dist.cjs.json tsconfig.dist.esm.json && tsimp tools/fix-ext.mts --mjs dist.esm/*.js dist.esm/*/*.js && tsimp tools/fix-ext.mts --cjs dist.cjs/*.js dist.cjs/*/*.js", "prepublishOnly": "npm run test:dist", "clean": "rim<PERSON>f build dist dist.*", "test": "mocha 'test/**/*.test.ts'", "test:dist": "npm run lint && npm run test && npm run test:deno", "test:cover": "npm run cover:clean && npx nyc --no-clean npm run 'test' && npm run cover:report", "test:node_with_strip_types": "node --experimental-strip-types test/deno_test.ts", "test:deno": "deno test --allow-read test/deno_*.ts", "test:bun": "bun test test/bun.spec.ts", "test:fuzz": "npm exec --yes -- jsfuzz@git+https://gitlab.com/gitlab-org/security-products/analyzers/fuzzers/jsfuzz.git#39e6cf16613a0e30c7a7953f62e64292dbd5d3f3 --fuzzTime 60 --no-versifier test/decode.jsfuzz.js corpus", "cover:clean": "rimraf .nyc_output coverage/", "cover:report": "npx nyc report --reporter=text-summary --reporter=html --reporter=json", "test:browser": "karma start --single-run", "test:browser:firefox": "karma start --single-run --browsers FirefoxHeadless", "test:browser:chrome": "karma start --single-run --browsers ChromeHeadless", "test:watch:browser": "karma start --browsers ChromeHeadless,FirefoxHeadless", "test:watch:nodejs": "mocha -w 'test/**/*.test.ts'", "lint": "eslint src test", "lint:fix": "prettier --loglevel=warn --write 'src/**/*.ts' 'test/**/*.ts' && eslint --fix --ext .ts src test", "lint:print-config": "eslint --print-config .eslintrc.js", "update-dependencies": "npx rimraf node_modules/ package-lock.json ; npm install ; npm audit fix --force ; git restore package.json ; npm install"}, "homepage": "https://msgpack.org/", "repository": {"type": "git", "url": "https://github.com/msgpack/msgpack-javascript.git"}, "bugs": {"url": "https://github.com/msgpack/msgpack-javascript/issues"}, "keywords": ["msgpack", "MessagePack", "serialization", "universal"], "engines": {"node": ">= 18"}, "devDependencies": {"@eslint/compat": "latest", "@eslint/eslintrc": "latest", "@eslint/js": "latest", "@types/lodash": "latest", "@types/mocha": "latest", "@types/node": "latest", "@typescript-eslint/eslint-plugin": "latest", "@typescript-eslint/parser": "latest", "assert": "latest", "benchmark": "latest", "buffer": "latest", "core-js": "latest", "eslint": "latest", "eslint-config-prettier": "latest", "eslint-plugin-import": "latest", "eslint-plugin-tsdoc": "latest", "ieee754": "latest", "karma": "latest", "karma-chrome-launcher": "latest", "karma-cli": "latest", "karma-firefox-launcher": "latest", "karma-mocha": "latest", "karma-sourcemap-loader": "latest", "karma-webpack": "latest", "lodash": "latest", "mocha": "latest", "msg-timestamp": "latest", "msgpack-test-js": "latest", "prettier": "latest", "rimraf": "latest", "ts-loader": "latest", "ts-node": "latest", "tsimp": "latest", "typescript": "latest", "webpack": "latest", "webpack-cli": "latest"}, "files": ["src/**/*.*", "dist.cjs/**/*.*", "dist.esm/**/*.*", "dist.umd/**/*.*", "mod.ts"]}
{"version": 3, "file": "stream.cjs", "sources": ["../src/stream.ts"], "sourcesContent": ["import type {EventSourceParser, ParsedEvent} from './types.js'\nimport {createParser} from './parse.js'\n\n/**\n * A TransformStream that ingests a stream of strings and produces a stream of ParsedEvents.\n *\n * @example\n * ```\n * const eventStream =\n *   response.body\n *     .pipeThrough(new TextDecoderStream())\n *     .pipeThrough(new EventSourceParserStream())\n * ```\n * @public\n */\nexport class EventSourceParserStream extends TransformStream<string, ParsedEvent> {\n  constructor() {\n    let parser!: EventSourceParser\n\n    super({\n      start(controller) {\n        parser = createParser((event) => {\n          if (event.type === 'event') {\n            controller.enqueue(event)\n          }\n        })\n      },\n      transform(chunk) {\n        parser.feed(chunk)\n      },\n    })\n  }\n}\n\nexport type {ParsedEvent} from './types.js'\n"], "names": ["EventSourceParserStream", "TransformStream", "constructor", "parser", "start", "controller", "create<PERSON><PERSON><PERSON>", "event", "type", "enqueue", "transform", "chunk", "feed"], "mappings": ";;;;;;AAeO,MAAMA,gCAAgCC,eAAqC,CAAA;EAChFC,WAAcA,CAAA,EAAA;IACR,IAAAC,MAAA;IAEE,KAAA,CAAA;MACJC,MAAMC,UAAY,EAAA;QACPF,MAAA,GAAAG,KAAAA,CAAAA,YAAA,CAAcC,KAAU,IAAA;UAC3B,IAAAA,KAAA,CAAMC,SAAS,OAAS,EAAA;YAC1BH,UAAA,CAAWI,QAAQF,KAAK,CAAA;UAC1B;QAAA,CACD,CAAA;MACH,CAAA;MACAG,UAAUC,KAAO,EAAA;QACfR,MAAA,CAAOS,KAAKD,KAAK,CAAA;MACnB;IAAA,CACD,CAAA;EACH;AACF;"}
# Warp Sector Deployment Guide

This guide explains how to deploy the Warp Sector game to your VPS using the provided scripts.

## Prerequisites

- VPS access with root privileges
- Domain name configured to point to your VPS (e.g., `warpsector.merchgenieai.com`)
- SSH key authentication set up for the VPS
- Node.js 18+ support on the VPS

## Deployment Scripts

### 1. Upload Script (`upload_warpsector.sh`)

This script uploads your local game files to the VPS while preserving important configuration files.

**Usage:**
```bash
./upload_warpsector.sh
```

**What it does:**
- Creates a backup of the current deployment (if backup script exists)
- Uploads all game files to `/opt/warpsector` on the VPS
- Preserves `.env` files and PM2 configuration
- Uploads the deployment script to the VPS

### 2. Deployment Script (`deploy_warpsector.sh`)

This script runs on the VPS to set up and deploy the game.

**Usage (on VPS):**
```bash
cd /opt/warpsector
./deploy_warpsector.sh
```

**What it does:**
- Installs Node.js 18+ if not present
- Installs PM2 for process management
- Installs frontend and server dependencies
- Builds the frontend application
- Sets up PM2 configurations for both server and static file serving
- Configures Nginx reverse proxy
- Sets up SSL with Let's Encrypt (if certbot is available)

### 3. Backup Script (`backup_vps.sh`)

This script creates backups of your current deployment.

**Usage:**
```bash
./backup_vps.sh
```

**What it does:**
- Creates timestamped backups of the deployment directory
- Backs up PM2 process configurations
- Keeps only the last 5 backups to save space

## Deployment Process

### Initial Deployment

1. **Configure your environment variables:**
   ```bash
   cp .env.example .env
   # Edit .env with your API keys:
   # - FAL_KEY: Your Fal.ai API key for image generation
   # - GROQ_API_KEY: Your Groq API key for LLM services
   ```

2. **Upload the game files:**
   ```bash
   ./upload_warpsector.sh
   ```

3. **Connect to VPS and deploy:**
   ```bash
   ssh root@**************
   cd /opt/warpsector
   ./deploy_warpsector.sh
   ```

### Updates

For subsequent updates, simply run the upload script again and then the deployment script on the VPS. The scripts will detect existing installations and handle updates appropriately.

## Architecture

The deployment sets up two PM2 processes:

1. **warpsector-server**: Runs the Node.js backend server on port 3001
   - Handles AI image generation via Fal.ai
   - Provides LLM services via Groq
   - Manages game environment generation

2. **warpsector-static**: Serves the built frontend files on port 3000
   - Serves the Vite-built static files from `dist/` directory

Nginx acts as a reverse proxy:
- `/` → serves static frontend files (port 3000)
- `/api/` → proxies to backend server (port 3001)
- `/ws` → WebSocket support for real-time features

## Monitoring

Check application status:
```bash
pm2 status
pm2 logs warpsector-server
pm2 logs warpsector-static
```

## Troubleshooting

### Common Issues

1. **Build fails**: Check that all dependencies are installed locally first
2. **Server won't start**: Verify `.env` file has correct API keys
3. **Nginx configuration fails**: Check domain DNS settings and Nginx syntax
4. **SSL setup fails**: Ensure domain points to VPS IP and certbot is installed

### Logs

- Application logs: `pm2 logs [process-name]`
- Nginx logs: `/var/log/nginx/error.log`
- System logs: `journalctl -u nginx`

## Security Notes

- Environment variables are preserved during updates
- SSL is automatically configured with Let's Encrypt
- API keys should never be committed to version control
- Regular backups are created automatically

## Domain Configuration

Ensure your domain's DNS A record points to `**************` before running the deployment script for SSL setup.
# Game Improvement Roadmap

## 1. Executive Summary

This document provides a comprehensive analysis and a strategic roadmap for improving the game's core engagement, challenge, appeal, and WISH token economy. The review identified a critical lack of persistent player progression as the primary weakness. To address this, we propose a multi-faceted approach centered around a persistent "Hangar" upgrade system, complemented by a robust enemy progression system, an enhanced "Reality Warp" feature, and new, diverse token sinks.

The ultimate goal is to create a compelling gameplay loop where players are motivated to earn and spend WISH tokens to gain a tangible competitive advantage, particularly in the context of OrangeSDK-run tournaments.

## 2. Analysis of Weaknesses

The initial codebase review, supported by existing documentation like [`CodebaseReview.md`](CodebaseReview.md:1), revealed several key areas for improvement:

*   **Lack of Persistent Progression:** The most significant weakness. The core gameplay loop of [`LevelManager.js`](src/managers/LevelManager.js:1) is session-based. While [`TokenEconomyManager.js`](src/managers/TokenEconomyManager.js:1) tracks a token balance, there is no system for permanently improving the player's ship or abilities. This leads to a lack of long-term goals.
*   **Underwhelming Token Economy:** The [`TokenEconomyManager.js`](src/managers/TokenEconomyManager.js:1) The primary token sink being the session-based powerups and the Reality Warp [`RealityWarpManager.js`](src/managers/RealityWarpManager.js:1), makes accumulating and spending tokens feel less rewarding, long term, thwarting any sense of progress.
*   **Static Enemy Difficulty:** The [`Enemy.js`](src/entities/Enemy.js:1) entities and the [`EnemyManager.js`](src/managers/EnemyManager.js:1) system appear to rely on static configurations. Without scaling difficulty, the game quickly becomes monotonous, and player upgrades (if they existed) would feel less necessary.
*   **Limited Strategic Depth:** The [`RealityWarpManager.js`](src/managers/RealityWarpManager.js:1) concept is unique but currently lacks the strategic impact and player choice to make it a compelling core feature.

## 3. Proposed Solutions & New Features

To address these weaknesses, we propose the following interconnected systems and enhancements.

### 3.1. The Hangar: Persistent Player Upgrades

**Concept:** A new, persistent system where players can permanently upgrade their `PlayerShip` and `WingmanShip` using WISH tokens. This is the cornerstone of the long-term progression loop.

**Details:**
*   **Documented In:** [`HangarConcept.md`](HangarConcept.md:1)
*   **Core Idea:** Players access a "Hangar" screen between levels where they can spend WISH tokens on a variety of upgrades.
*   **Upgrade Categories:**
    *   **Offensive:** Damage, Fire Rate
    *   **Defensive:** Max Health, Shield Strength
    *   **Mobility:** Max Speed, Acceleration, Handling.
    *   **Wingman-Specific:** Health, Damage/Fire Rate.
*   **Implementation:** Requires a new `HangarManager.js`, UI screens for purchasing and managing upgrades, and modifications to [`PlayerShip.js`](src/entities/PlayerShip.js:1) and [`WingmanShip.js`](src/entities/WingmanShip.js:1) to apply these upgrades. The upgrade data must be serialized and saved in the player's profile.

**Impact:** This directly addresses the lack of persistent progression, giving players a long-term reason to earn and spend WISH tokens. It makes players feel a tangible increase in power over time.

### 3.2. Enemy Progression & Scaling System

**Concept:** A dynamic system that scales enemy stats, behaviors, and types in tandem with the player's progress through the levels, creating a continuous and escalating challenge.

**Details:**
*   **Documented In:** [`EnemyProgression.md`](EnemyProgression.md:1)
*   **Core Idea:** Enemy attributes (Health, Speed, Damage) scale based on a difficulty coefficient derived from `LevelManager.currentLevel`. New enemy types and complex attack patterns are introduced at specific "tiers" (e.g., every 5 levels).
*   **Scaling Logic:** A new utility, `src/utils/EnemyScaling.js`, would manage the calculation of difficulty coefficients and tier assignments. [`EnemyManager.js`](src/managers/EnemyManager.js:1) would use this to spawn appropriately scaled enemies.
*   **Behavioral AI:** Enemies gain new abilities and better patterns at higher tiers, such as leading shots, firing in complex patterns, shielding allies, or employing evasion special formations, such as a "delta" formation.

**Impact:** This system creates a compelling *need* for the Hangar upgrades. As enemies become tougher and more complex, player upgrades become essential for survival and success, validating the investment of WISH tokens.

### 3.3. New Token Sinks

**Concept:** Introduce new ways for players to spend WISH tokens, catering to different playstyles and ensuring the economy remains active and engaging.

**Details:**
*   **Documented In:** [`EconomyImprovements.md`](EconomyImprovements.md:1)
*   **Consumables:** Single-use, purchasable items that provide a temporary, tactical advantage during a level. This gives players a new way to use their WISH tokens for a direct, in-level benefit. Use the 'E' key, (or on mobile, tap the consumable icon in the HUD) to use them.

**Impact:** Diversifies the token economy, provides more player choice, and offers long-term collection goals. Consumables add tactical depth.

## 4. Synergistic Gameplay Loop

These systems are designed to work together, creating a powerful and engaging core loop:

1.  **Play:** The player enters a level. The enemy difficulty is scaled based on their current progress ([`EnemyProgression.md`](EnemyProgression.md:1)).
2.  **Strategize:** Before or during the level, the player can spend WISH tokens on a powerful Reality Warp or activate a consumable to gain a tactical advantage.
3.  **Earn:** By defeating the scaled enemies, the player earns WISH tokens.
4.  **Upgrade:** After the level, the player takes their hard-earned WISH tokens to the Hangar ([`HangarConcept.md`](HangarConcept.md:1)) to purchase permanent upgrades for their ship, making them stronger for the next challenge.
5.  **Repeat & Compete:** This loop repeats, with the player becoming progressively more powerful but also facing proportionally harder enemies. This continuous cycle of challenge and reward is designed to prepare players for high-stakes competition in OrangeSDK tournaments, where their investment in upgrades and strategic use of warps will be a key determinant of success.

## 5. Implementation Roadmap

1.  **Phase 1: Core Progression (Highest Priority)**
    *   Implement the "Hangar" system ([`HangarConcept.md`](HangarConcept.md:1)).
        *   Create `HangarManager.js`.
        *   Design and implement the Hangar UI.
        *   Modify [`PlayerShip.js`](src/entities/PlayerShip.js:1) and [`WingmanShip.js`](src/entities/WingmanShip.js:1) to be upgradeable.
        *   Integrate with the save system.
    *   Implement the "Enemy Progression" system ([`EnemyProgression.md`](EnemyProgression.md:1)).
        *   Create `src/utils/EnemyScaling.js`.
        *   Modify [`EnemyManager.js`](src/managers/EnemyManager.js:1) and [`Enemy.js`](src/entities/Enemy.js:1) to use the scaling logic.
        *   Implement tier-based enemy AI and abilities.

2.  **Phase 2: Economy & Polish**
    *   Implement "Consumables" as a new token sink ([`EconomyImprovements.md`](EconomyImprovements.md:1)).
        *   Create a `ConsumableManager.js`.
        *   Design and implement a loadout/inventory UI.


## 6. Conclusion

By implementing this roadmap, the game will be transformed from a potentially repetitive experience into a deeply engaging and strategically rich title. The introduction of persistent upgrades, scaling enemies, and a diverse token economy directly addresses the core request to give players compelling reasons to spend WISH tokens to gain a competitive edge. This holistic approach ensures that player investment, both in time and tokens, is met with meaningful progression and a greater chance of success in competitive play.
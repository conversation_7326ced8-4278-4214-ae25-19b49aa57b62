#!/bin/bash
set -e

# Backup script for VPS deployments
echo "Creating backup of current deployment..."

# Variables
VPS_IP="**************"
VPS_USER="root"
REMOTE_DIR="/opt/warpsector"
BACKUP_DIR="/opt/backups/warpsector"
DATE=$(date +%Y%m%d_%H%M%S)

# Create backup directory on VPS
echo "Creating backup directory..."
ssh $VPS_USER@$VPS_IP "mkdir -p $BACKUP_DIR"

# Check if the deployment directory exists
if ssh $VPS_USER@$VPS_IP "[ -d '$REMOTE_DIR' ]"; then
    echo "Backing up current deployment to $BACKUP_DIR/backup_$DATE..."
    
    # Create a tar archive of the current deployment
    ssh $VPS_USER@$VPS_IP "cd $(dirname $REMOTE_DIR) && tar -czf $BACKUP_DIR/backup_$DATE.tar.gz $(basename $REMOTE_DIR)"
    
    # Also backup PM2 process list and logs if they exist
    ssh $VPS_USER@$VPS_IP "pm2 save && pm2 resurrect > $BACKUP_DIR/pm2_processes_$DATE.json 2>/dev/null || true"
    
    echo "Backup created successfully: $BACKUP_DIR/backup_$DATE.tar.gz"
    
    # Keep only the last 5 backups to save space
    echo "Cleaning up old backups (keeping last 5)..."
    ssh $VPS_USER@$VPS_IP "cd $BACKUP_DIR && ls -t backup_*.tar.gz | tail -n +6 | xargs rm -f"
    
    # List current backups
    echo "Current backups:"
    ssh $VPS_USER@$VPS_IP "ls -la $BACKUP_DIR/backup_*.tar.gz 2>/dev/null || echo 'No backups found'"
    
else
    echo "No existing deployment found at $REMOTE_DIR. Skipping backup."
fi

echo "Backup process completed."
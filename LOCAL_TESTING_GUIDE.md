# ETH Test Mode - Local Development Testing Guide

## Overview
This guide helps you test the ETH Test Mode feature with your local development node and MetaMask wallet.

## Prerequisites
1. Local Ethereum development node running (e.g., Hardhat, Ganache)
2. MetaMask configured to connect to your local node
3. Test ETH in your MetaMask wallet on the local network

## Setup Steps

### 1. Configure MetaMask for Local Development
1. Open MetaMask
2. Click on the network dropdown (usually shows "Ethereum Mainnet")
3. Click "Add Network"
4. Add your local development network:
   - **Network Name**: Local Development
   - **RPC URL**: http://localhost:8545 (or your node's URL)
   - **Chain ID**: 31337 (Hardhat) or 1337 (Ganache)
   - **Currency Symbol**: ETH
5. Save and switch to this network

### 2. Ensure Your Local Node Has Test ETH
Make sure your development accounts have ETH balance:
```bash
# For Hardhat
npx hardhat node

# For Ganache
ganache --deterministic --accounts 10 --host 0.0.0.0
```

### 3. Connect Your Wallet to the Game
1. Start the game: `npm start`
2. Open the game in your browser
3. Click "ETH Test Mode 🔗" button in the main menu
4. The game should detect your MetaMask wallet automatically

## Testing ETH Test Mode

### Expected Behavior:
1. **Balance Display**: Should show your actual ETH balance (e.g., "5.5000 ETH (Test Mode)")
2. **Prices**: All items in Genie Shop should show 90% discounted prices
3. **Spending**: Should check against your real ETH balance
4. **No Debug Tokens**: Should NOT award 50,000 debug tokens when balance is low

### Debugging Steps:

#### Check Wallet Connection:
Open browser console and look for these logs:
```
🔧 Initializing wallet connection...
✅ MetaMask/Wallet detected (window.ethereum)
✅ Already connected to wallet: 0xYourAddress
✅ Wallet connected: 0xYourAddress
✅ Wallet balance synced: X.XX ETH for address: 0xYourAddress
```

#### Check Test Mode Activation:
```
🧪 Enabling ETH Test Mode...
🧪 Test Mode: Returning ETH balance X.XX instead of WISH balance
✅ ETH Test Mode enabled - using ETH balance as WISH tokens
```

#### Check Balance Display:
```
🧪 Test Mode Balance Display: X.XXXX ETH (Test Mode)
```

## Common Issues and Solutions

### Issue 1: "No wallet detected"
**Solution**: Ensure MetaMask is installed and unlocked. Check browser console for wallet detection logs.

### Issue 2: "Failed to sync wallet balance"
**Solution**: 
1. Check if your local node is running
2. Verify MetaMask is connected to the correct network
3. Check browser console for detailed error messages

### Issue 3: Balance shows 0 ETH
**Solution**:
1. Ensure your MetaMask account has ETH on the local network
2. Check the wallet address in MetaMask matches the one shown in console logs
3. Verify your local node is mining/sending transactions

### Issue 4: OrangeID errors in console
**Solution**: These are expected in local development. The game will fall back to direct MetaMask connection.

### Issue 5: Prices not showing 90% discount
**Solution**: Ensure test mode is enabled (button should show "ETH Test Mode ✅")

## Manual Testing Checklist

- [ ] MetaMask connected to local development network
- [ ] Wallet has test ETH balance
- [ ] Game detects wallet connection
- [ ] ETH Test Mode button works
- [ ] Balance displays ETH instead of WISH
- [ ] Genie Shop prices show 90% discount
- [ ] Can "purchase" items if you have enough ETH
- [ ] No debug tokens awarded when balance is low
- [ ] Can disable test mode and return to normal

## Console Commands for Testing

```javascript
// Check current wallet status
game.gameEngine.tokenManager.isWalletConnected()

// Check wallet balance
game.gameEngine.tokenManager.getWalletBalance()

// Check if test mode is active
game.gameEngine.ethTestModeManager.getTestModeStatus()

// Manually enable test mode
game.gameEngine.ethTestModeManager.enableTestMode()

// Check current balance display
game.gameEngine.tokenManager.getBalanceDisplay()
```

## Network Configuration

Ensure your local node is accessible from the browser:
- **Hardhat**: Usually runs on `http://localhost:8545`
- **Ganache**: Usually runs on `http://localhost:8545`
- **Chain ID**: 31337 (Hardhat) or 1337 (Ganache)

## Support

If you continue to have issues:
1. Check the browser console for detailed error messages
2. Ensure your local node is properly configured
3. Verify MetaMask network settings
4. Test with a simple ETH balance check in MetaMask first
#!/bin/bash
set -e

# Warp Sector Deployment Script for VPS
echo "Starting Warp Sector deployment..."

# Define variables
DEPLOY_DIR="/opt/warpsector"
DOMAIN="warpsector.merchgenieai.com"
NODE_VERSION="18"

# Create deployment directory
echo "Creating deployment directory at $DEPLOY_DIR..."
mkdir -p $DEPLOY_DIR
cd $DEPLOY_DIR

# Check if this is an update or a fresh install
IS_UPDATE=false
if pm2 list | grep -q "warpsector"; then
  IS_UPDATE=true
  echo "Detected existing installation. Running in update mode."
fi

# Install Node.js if not already installed
if ! command -v node &> /dev/null || [ "$(node -v | cut -d. -f1 | tr -d 'v')" -lt "$NODE_VERSION" ]; then
  echo "Installing Node.js $NODE_VERSION..."
  curl -fsSL https://deb.nodesource.com/setup_$NODE_VERSION.x | bash -
  apt-get install -y nodejs
fi

# Install PM2 if not already installed
if ! command -v pm2 &> /dev/null; then
  echo "Installing PM2..."
  npm install -g pm2
fi

echo "Installing frontend dependencies..."
# Install frontend dependencies
npm install

echo "Installing server dependencies..."
# Install server dependencies
cd server
npm install
cd ..

# Build the frontend application
echo "Building frontend application..."
npm run build

# Create necessary directories for server data if they don't exist
echo "Setting up server directories..."
mkdir -p "$DEPLOY_DIR/server/images"
mkdir -p "$DEPLOY_DIR/server/environments"

# Copy .env file if it exists in the project root
if [ -f "$DEPLOY_DIR/.env" ]; then
  echo "Using existing .env file..."
else
  echo "Creating new .env file..."
  cp "$DEPLOY_DIR/.env.example" "$DEPLOY_DIR/.env"
  echo "Please configure your .env file with the required API keys:"
  echo "- FAL_KEY: Your Fal.ai API key"
  echo "- GROQ_API_KEY: Your Groq API key"
fi

# Create PM2 configuration for the server
echo "Creating PM2 configuration for server..."
cat > ecosystem.server.config.cjs << EOL
module.exports = {
  apps: [{
    name: "warpsector-server",
    script: "server/index.js",
    cwd: "$DEPLOY_DIR",
    env_file: ".env",
    env: {
      NODE_ENV: "production",
      PORT: 3001
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: "1G"
  }]
};
EOL

# Create PM2 configuration for serving static files
echo "Creating PM2 configuration for static files..."
cat > ecosystem.static.config.cjs << EOL
module.exports = {
  apps: [{
    name: "warpsector-static",
    script: "npx",
    args: ["serve", "-s", "dist", "-l", "3000"],
    cwd: "$DEPLOY_DIR",
    env: {
      NODE_ENV: "production"
    },
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: "500M"
  }]
};
EOL

# Start or restart the applications
if [ "$IS_UPDATE" = true ]; then
  echo "Restarting applications with PM2..."
  pm2 restart warpsector-server
  pm2 restart warpsector-static
else
  echo "Starting applications with PM2..."
  pm2 start ecosystem.server.config.cjs
  pm2 start ecosystem.static.config.cjs
fi

# Save PM2 process list to restart on reboot
pm2 save

# Only set up Nginx if this is a fresh install
if [ "$IS_UPDATE" = false ]; then
  # Set up Nginx configuration for the subdomain
  echo "Setting up Nginx configuration..."
  cat > /etc/nginx/sites-available/$DOMAIN << EOL
server {
    listen 80;
    server_name $DOMAIN;

    # Serve static files from the dist directory
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_cache_bypass \$http_upgrade;
    }

    # Proxy API requests to the backend server
    location /api/ {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
    }

    # WebSocket support for real-time features
    location /ws {
        proxy_pass http://localhost:3001;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_read_timeout 86400;
    }
}
EOL

  # Enable the site
  ln -sf /etc/nginx/sites-available/$DOMAIN /etc/nginx/sites-enabled/

  # Check nginx configuration
  nginx -t

  # Reload Nginx configuration
  systemctl reload nginx

  # Set up SSL with Let's Encrypt (requires certbot)
  if command -v certbot &> /dev/null; then
    echo "Setting up SSL with Let's Encrypt..."
    certbot --nginx -d $DOMAIN --non-interactive --agree-tos --email <EMAIL>
  else
    echo "Certbot not found. Skipping SSL setup."
    echo "To install Certbot and set up SSL:"
    echo "apt-get install -y certbot python3-certbot-nginx"
    echo "Then run: certbot --nginx -d $DOMAIN"
  fi
else
  echo "Skipping Nginx setup as this is an update."
fi

echo "Deployment complete! Warp Sector should be available at https://$DOMAIN"
echo "Note: If you encounter any issues, check the logs with:"
echo "pm2 logs warpsector-server"
echo "pm2 logs warpsector-static"
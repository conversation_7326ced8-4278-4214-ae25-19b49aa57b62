{"version": 3, "sources": ["../lib/cookie/index.ts", "../lib/pathMatch.ts", "../lib/getPublicSuffix.ts", "../lib/permuteDomain.ts", "../lib/store.ts", "../lib/utils.ts", "../lib/memstore.ts", "../lib/validators.ts", "../lib/version.ts", "../lib/cookie/constants.ts", "../lib/cookie/canonicalDomain.ts", "../lib/cookie/formatDate.ts", "../lib/cookie/parseDate.ts", "../lib/cookie/cookie.ts", "../lib/cookie/cookieCompare.ts", "../lib/cookie/defaultPath.ts", "../lib/cookie/domainMatch.ts", "../lib/cookie/secureContext.ts", "../lib/cookie/cookieJar.ts", "../lib/cookie/permutePath.ts"], "sourcesContent": ["export { MemoryCookieStore, type MemoryCookieStoreIndex } from '../memstore.js'\nexport { pathMatch } from '../pathMatch.js'\nexport { permuteDomain } from '../permuteDomain.js'\nexport {\n  getPublicSuffix,\n  type GetPublicSuffixOptions,\n} from '../getPublicSuffix.js'\nexport { Store } from '../store.js'\nexport { ParameterError } from '../validators.js'\nexport { version } from '../version.js'\nexport { type Callback, type ErrorCallback, type Nullable } from '../utils.js'\nexport { canonicalDomain } from './canonicalDomain.js'\nexport {\n  PrefixSecurityEnum,\n  type SerializedCookie,\n  type SerializedCookieJar,\n} from './constants.js'\nexport {\n  Cookie,\n  type CreateCookieOptions,\n  type ParseCookieOptions,\n} from './cookie.js'\nexport { cookieCompare } from './cookieCompare.js'\nexport {\n  CookieJar,\n  type CreateCookieJarOptions,\n  type GetCookiesOptions,\n  type SetCookieOptions,\n} from './cookieJar.js'\nexport { defaultPath } from './defaultPath.js'\nexport { domainMatch } from './domainMatch.js'\nexport { formatDate } from './formatDate.js'\nexport { parseDate } from './parseDate.js'\nexport { permutePath } from './permutePath.js'\n\nimport { Cookie, ParseCookieOptions } from './cookie.js'\n\n/**\n * {@inheritDoc Cookie.parse}\n * @public\n */\nexport function parse(\n  str: string,\n  options?: ParseCookieOptions,\n): Cookie | undefined {\n  return Cookie.parse(str, options)\n}\n\n/**\n * {@inheritDoc Cookie.fromJSON}\n * @public\n */\nexport function fromJSON(str: unknown): Cookie | undefined {\n  return Cookie.fromJSON(str)\n}\n", "/**\n * Answers \"does the request-path path-match a given cookie-path?\" as per {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.1.4 | RFC6265 Section 5.1.4}.\n * This is essentially a prefix-match where cookiePath is a prefix of reqPath.\n *\n * @remarks\n * A request-path path-matches a given cookie-path if at least one of\n * the following conditions holds:\n *\n * - The cookie-path and the request-path are identical.\n * - The cookie-path is a prefix of the request-path, and the last character of the cookie-path is %x2F (\"/\").\n * - The cookie-path is a prefix of the request-path, and the first character of the request-path that is not included in the cookie-path is a %x2F (\"/\") character.\n *\n * @param reqPath - the path of the request\n * @param cookiePath - the path of the cookie\n * @public\n */\nexport function pathMatch(reqPath: string, cookiePath: string): boolean {\n  // \"o  The cookie-path and the request-path are identical.\"\n  if (cookiePath === reqPath) {\n    return true\n  }\n\n  const idx = reqPath.indexOf(cookiePath)\n  if (idx === 0) {\n    // \"o  The cookie-path is a prefix of the request-path, and the last\n    // character of the cookie-path is %x2F (\"/\").\"\n    if (cookiePath[cookiePath.length - 1] === '/') {\n      return true\n    }\n\n    // \" o  The cookie-path is a prefix of the request-path, and the first\n    // character of the request-path that is not included in the cookie- path\n    // is a %x2F (\"/\") character.\"\n    if (reqPath.startsWith(cookiePath) && reqPath[cookiePath.length] === '/') {\n      return true\n    }\n  }\n\n  return false\n}\n", "import { getDomain } from 'tldts'\n\n// RFC 6761\nconst SPECIAL_USE_DOMAINS = ['local', 'example', 'invalid', 'localhost', 'test']\n\nconst SPECIAL_TREATMENT_DOMAINS = ['localhost', 'invalid']\n\n/**\n * Options for configuring how {@link getPublicSuffix} behaves.\n * @public\n */\nexport interface GetPublicSuffixOptions {\n  /**\n   * If set to `true` then the following {@link https://www.rfc-editor.org/rfc/rfc6761.html | Special Use Domains} will\n   * be treated as if they were valid public suffixes ('local', 'example', 'invalid', 'localhost', 'test').\n   *\n   * @remarks\n   * In testing scenarios it's common to configure the cookie store with so that `http://localhost` can be used as a domain:\n   * ```json\n   * {\n   *   allowSpecialUseDomain: true,\n   *   rejectPublicSuffixes: false\n   * }\n   * ```\n   *\n   * @defaultValue false\n   */\n  allowSpecialUseDomain?: boolean | undefined\n  /**\n   * If set to `true` then any errors that occur while executing {@link getPublicSuffix} will be silently ignored.\n   *\n   * @defaultValue false\n   */\n  ignoreError?: boolean | undefined\n}\n\nconst defaultGetPublicSuffixOptions: GetPublicSuffixOptions = {\n  allowSpecialUseDomain: false,\n  ignoreError: false,\n}\n\n/**\n * Returns the public suffix of this hostname. The public suffix is the shortest domain\n * name upon which a cookie can be set.\n *\n * @remarks\n * A \"public suffix\" is a domain that is controlled by a\n * public registry, such as \"com\", \"co.uk\", and \"pvt.k12.wy.us\".\n * This step is essential for preventing attacker.com from\n * disrupting the integrity of example.com by setting a cookie\n * with a Domain attribute of \"com\".  Unfortunately, the set of\n * public suffixes (also known as \"registry controlled domains\")\n * changes over time.  If feasible, user agents SHOULD use an\n * up-to-date public suffix list, such as the one maintained by\n * the Mozilla project at http://publicsuffix.org/.\n * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.3 | RFC6265 - Section 5.3})\n *\n * @example\n * ```\n * getPublicSuffix('www.example.com') === 'example.com'\n * getPublicSuffix('www.subdomain.example.com') === 'example.com'\n * ```\n *\n * @param domain - the domain attribute of a cookie\n * @param options - optional configuration for controlling how the public suffix is determined\n * @public\n */\nexport function getPublicSuffix(\n  domain: string,\n  options: GetPublicSuffixOptions = {},\n): string | undefined {\n  options = { ...defaultGetPublicSuffixOptions, ...options }\n  const domainParts = domain.split('.')\n  const topLevelDomain = domainParts[domainParts.length - 1]\n  const allowSpecialUseDomain = !!options.allowSpecialUseDomain\n  const ignoreError = !!options.ignoreError\n\n  if (\n    allowSpecialUseDomain &&\n    topLevelDomain !== undefined &&\n    SPECIAL_USE_DOMAINS.includes(topLevelDomain)\n  ) {\n    if (domainParts.length > 1) {\n      // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n      const secondLevelDomain = domainParts[domainParts.length - 2]!\n      // In aforementioned example, the eTLD/pubSuf will be apple.localhost\n      return `${secondLevelDomain}.${topLevelDomain}`\n    } else if (SPECIAL_TREATMENT_DOMAINS.includes(topLevelDomain)) {\n      // For a single word special use domain, e.g. 'localhost' or 'invalid', per RFC 6761,\n      // \"Application software MAY recognize {localhost/invalid} names as special, or\n      // MAY pass them to name resolution APIs as they would for other domain names.\"\n      return topLevelDomain\n    }\n  }\n\n  if (\n    !ignoreError &&\n    topLevelDomain !== undefined &&\n    SPECIAL_USE_DOMAINS.includes(topLevelDomain)\n  ) {\n    throw new Error(\n      `Cookie has domain set to the public suffix \"${topLevelDomain}\" which is a special use domain. To allow this, configure your CookieJar with {allowSpecialUseDomain: true, rejectPublicSuffixes: false}.`,\n    )\n  }\n\n  const publicSuffix = getDomain(domain, {\n    allowIcannDomains: true,\n    allowPrivateDomains: true,\n  })\n  if (publicSuffix) return publicSuffix\n}\n", "import { getPublicSuffix } from './getPublicSuffix.js'\n\n/**\n * Generates the permutation of all possible values that {@link domainMatch} the given `domain` parameter. The\n * array is in shortest-to-longest order. Useful when building custom {@link Store} implementations.\n *\n * @example\n * ```\n * permuteDomain('foo.bar.example.com')\n * // ['example.com', 'bar.example.com', 'foo.bar.example.com']\n * ```\n *\n * @public\n * @param domain - the domain to generate permutations for\n * @param allowSpecialUseDomain - flag to control if {@link https://www.rfc-editor.org/rfc/rfc6761.html | Special Use Domains} such as `localhost` should be allowed\n */\nexport function permuteDomain(\n  domain: string,\n  allowSpecialUseDomain?: boolean,\n): string[] | undefined {\n  const pubSuf = getPublicSuffix(domain, {\n    allowSpecialUseDomain: allowSpecialUseDomain,\n  })\n\n  if (!pubSuf) {\n    return undefined\n  }\n  if (pubSuf == domain) {\n    return [domain]\n  }\n\n  // Nuke trailing dot\n  if (domain.slice(-1) == '.') {\n    domain = domain.slice(0, -1)\n  }\n\n  const prefix = domain.slice(0, -(pubSuf.length + 1)) // \".example.com\"\n  const parts = prefix.split('.').reverse()\n  let cur = pubSuf\n  const permutations = [cur]\n  while (parts.length) {\n    // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n    const part = parts.shift()!\n    cur = `${part}.${cur}`\n    permutations.push(cur)\n  }\n  return permutations\n}\n", "// disabling this lint on this whole file because Store should be abstract\n// but we have implementations in the wild that may not implement all features\n/* eslint-disable @typescript-eslint/no-unused-vars */\n\nimport type { <PERSON><PERSON> } from './cookie/index.js'\nimport type { Callback, ErrorCallback, Nullable } from './utils.js'\n\n/**\n * Base class for {@link CookieJar} stores.\n *\n * The storage model for each {@link CookieJar} instance can be replaced with a custom implementation. The default is\n * {@link MemoryCookieStore}.\n *\n * @remarks\n * - Stores should inherit from the base Store class, which is available as a top-level export.\n *\n * - Stores are asynchronous by default, but if {@link Store.synchronous} is set to true, then the `*Sync` methods\n *     of the containing {@link CookieJar} can be used.\n *\n * @public\n */\nexport class Store {\n  /**\n   * Store implementations that support synchronous methods must return `true`.\n   */\n  synchronous: boolean\n\n  constructor() {\n    this.synchronous = false\n  }\n\n  /**\n   * Retrieve a {@link Cookie} with the given `domain`, `path`, and `key` (`name`). The RFC maintains that exactly\n   * one of these cookies should exist in a store. If the store is using versioning, this means that the latest or\n   * newest such cookie should be returned.\n   *\n   * Callback takes an error and the resulting Cookie object. If no cookie is found then null MUST be passed instead (that is, not an error).\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   */\n  findCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n  ): Promise<Cookie | undefined>\n  /**\n   * Retrieve a {@link Cookie} with the given `domain`, `path`, and `key` (`name`). The RFC maintains that exactly\n   * one of these cookies should exist in a store. If the store is using versioning, this means that the latest or\n   * newest such cookie should be returned.\n   *\n   * Callback takes an error and the resulting Cookie object. If no cookie is found then null MUST be passed instead (that is, not an error).\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   * @param callback - A function to call with either the found cookie or an error.\n   */\n  findCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n    callback: Callback<Cookie | undefined>,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  findCookie(\n    _domain: Nullable<string>,\n    _path: Nullable<string>,\n    _key: Nullable<string>,\n    _callback?: Callback<Cookie | undefined>,\n  ): unknown {\n    throw new Error('findCookie is not implemented')\n  }\n\n  /**\n   * Locates all {@link Cookie} values matching the given `domain` and `path`.\n   *\n   * The resulting list is checked for applicability to the current request according to the RFC (`domain-match`, `path-match`,\n   * `http-only-flag`, `secure-flag`, `expiry`, and so on), so it's OK to use an optimistic search algorithm when implementing\n   * this method. However, the search algorithm used SHOULD try to find cookies that {@link domainMatch} the `domain` and\n   * {@link pathMatch} the `path` in order to limit the amount of checking that needs to be done.\n   *\n   * @remarks\n   * - As of version `0.9.12`, the `allPaths` option to cookiejar.getCookies() above causes the path here to be `null`.\n   *\n   * - If the `path` is `null`, `path-matching` MUST NOT be performed (that is, `domain-matching` only).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param allowSpecialUseDomain - If `true` then special-use domain suffixes, will be allowed in matches. Defaults to `false`.\n   */\n  findCookies(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    allowSpecialUseDomain?: boolean,\n  ): Promise<Cookie[]>\n  /**\n   * Locates all {@link Cookie} values matching the given `domain` and `path`.\n   *\n   * The resulting list is checked for applicability to the current request according to the RFC (`domain-match`, `path-match`,\n   * `http-only-flag`, `secure-flag`, `expiry`, and so on), so it's OK to use an optimistic search algorithm when implementing\n   * this method. However, the search algorithm used SHOULD try to find cookies that {@link domainMatch} the `domain` and\n   * {@link pathMatch} the `path` in order to limit the amount of checking that needs to be done.\n   *\n   * @remarks\n   * - As of version `0.9.12`, the `allPaths` option to cookiejar.getCookies() above causes the path here to be `null`.\n   *\n   * - If the `path` is `null`, `path-matching` MUST NOT be performed (that is, `domain-matching` only).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param allowSpecialUseDomain - If `true` then special-use domain suffixes, will be allowed in matches. Defaults to `false`.\n   * @param callback - A function to call with either the found cookies or an error.\n   */\n  findCookies(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    allowSpecialUseDomain?: boolean,\n    callback?: Callback<Cookie[]>,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  findCookies(\n    _domain: Nullable<string>,\n    _path: Nullable<string>,\n    _allowSpecialUseDomain: boolean | Callback<Cookie[]> = false,\n    _callback?: Callback<Cookie[]>,\n  ): unknown {\n    throw new Error('findCookies is not implemented')\n  }\n\n  /**\n   * Adds a new {@link Cookie} to the store. The implementation SHOULD replace any existing cookie with the same `domain`,\n   * `path`, and `key` properties.\n   *\n   * @remarks\n   * - Depending on the nature of the implementation, it's possible that between the call to `fetchCookie` and `putCookie`\n   * that a duplicate `putCookie` can occur.\n   *\n   * - The {@link Cookie} object MUST NOT be modified; as the caller has already updated the `creation` and `lastAccessed` properties.\n   *\n   * @param cookie - The cookie to store.\n   */\n  putCookie(cookie: Cookie): Promise<void>\n  /**\n   * Adds a new {@link Cookie} to the store. The implementation SHOULD replace any existing cookie with the same `domain`,\n   * `path`, and `key` properties.\n   *\n   * @remarks\n   * - Depending on the nature of the implementation, it's possible that between the call to `fetchCookie` and `putCookie`\n   * that a duplicate `putCookie` can occur.\n   *\n   * - The {@link Cookie} object MUST NOT be modified; as the caller has already updated the `creation` and `lastAccessed` properties.\n   *\n   * @param cookie - The cookie to store.\n   * @param callback - A function to call when the cookie has been stored or an error has occurred.\n   */\n  putCookie(cookie: Cookie, callback: ErrorCallback): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  putCookie(_cookie: Cookie, _callback?: ErrorCallback): unknown {\n    throw new Error('putCookie is not implemented')\n  }\n\n  /**\n   * Update an existing {@link Cookie}. The implementation MUST update the `value` for a cookie with the same `domain`,\n   * `path`, and `key`. The implementation SHOULD check that the old value in the store is equivalent to oldCookie -\n   * how the conflict is resolved is up to the store.\n   *\n   * @remarks\n   * - The `lastAccessed` property is always different between the two objects (to the precision possible via JavaScript's clock).\n   *\n   * - Both `creation` and `creationIndex` are guaranteed to be the same.\n   *\n   * - Stores MAY ignore or defer the `lastAccessed` change at the cost of affecting how cookies are selected for automatic deletion.\n   *\n   * - Stores may wish to optimize changing the `value` of the cookie in the store versus storing a new cookie.\n   *\n   * - The `newCookie` and `oldCookie` objects MUST NOT be modified.\n   *\n   * @param oldCookie - the cookie that is already present in the store.\n   * @param newCookie - the cookie to replace the one already present in the store.\n   */\n  updateCookie(oldCookie: Cookie, newCookie: Cookie): Promise<void>\n  /**\n   * Update an existing {@link Cookie}. The implementation MUST update the `value` for a cookie with the same `domain`,\n   * `path`, and `key`. The implementation SHOULD check that the old value in the store is equivalent to oldCookie -\n   * how the conflict is resolved is up to the store.\n   *\n   * @remarks\n   * - The `lastAccessed` property is always different between the two objects (to the precision possible via JavaScript's clock).\n   *\n   * - Both `creation` and `creationIndex` are guaranteed to be the same.\n   *\n   * - Stores MAY ignore or defer the `lastAccessed` change at the cost of affecting how cookies are selected for automatic deletion.\n   *\n   * - Stores may wish to optimize changing the `value` of the cookie in the store versus storing a new cookie.\n   *\n   * - The `newCookie` and `oldCookie` objects MUST NOT be modified.\n   *\n   * @param oldCookie - the cookie that is already present in the store.\n   * @param newCookie - the cookie to replace the one already present in the store.\n   * @param callback - A function to call when the cookie has been updated or an error has occurred.\n   */\n  updateCookie(\n    oldCookie: Cookie,\n    newCookie: Cookie,\n    callback: ErrorCallback,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  updateCookie(\n    _oldCookie: Cookie,\n    _newCookie: Cookie,\n    _callback?: ErrorCallback,\n  ): unknown {\n    // recommended default implementation:\n    // return this.putCookie(newCookie, cb);\n    throw new Error('updateCookie is not implemented')\n  }\n\n  /**\n   * Remove a cookie from the store (see notes on `findCookie` about the uniqueness constraint).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   */\n  removeCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n  ): Promise<void>\n  /**\n   * Remove a cookie from the store (see notes on `findCookie` about the uniqueness constraint).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   * @param callback - A function to call when the cookie has been removed or an error occurs.\n   */\n  removeCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n    callback: ErrorCallback,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  removeCookie(\n    _domain: Nullable<string>,\n    _path: Nullable<string>,\n    _key: Nullable<string>,\n    _callback?: ErrorCallback,\n  ): unknown {\n    throw new Error('removeCookie is not implemented')\n  }\n\n  /**\n   * Removes matching cookies from the store. The `path` parameter is optional and if missing,\n   * means all paths in a domain should be removed.\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   */\n  removeCookies(domain: string, path: Nullable<string>): Promise<void>\n  /**\n   * Removes matching cookies from the store. The `path` parameter is optional and if missing,\n   * means all paths in a domain should be removed.\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param callback - A function to call when the cookies have been removed or an error occurs.\n   */\n  removeCookies(\n    domain: string,\n    path: Nullable<string>,\n    callback: ErrorCallback,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  removeCookies(\n    _domain: string,\n    _path: Nullable<string>,\n    _callback?: ErrorCallback,\n  ): unknown {\n    throw new Error('removeCookies is not implemented')\n  }\n\n  /**\n   * Removes all cookies from the store.\n   */\n  removeAllCookies(): Promise<void>\n  /**\n   * Removes all cookies from the store.\n   *\n   * @param callback - A function to call when all the cookies have been removed or an error occurs.\n   */\n  removeAllCookies(callback: ErrorCallback): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  removeAllCookies(_callback?: ErrorCallback): unknown {\n    throw new Error('removeAllCookies is not implemented')\n  }\n\n  /**\n   * Gets all the cookies in the store.\n   *\n   * @remarks\n   * - Cookies SHOULD be returned in creation order to preserve sorting via {@link cookieCompare}.\n   */\n  getAllCookies(): Promise<Cookie[]>\n  /**\n   * Gets all the cookies in the store.\n   *\n   * @remarks\n   * - Cookies SHOULD be returned in creation order to preserve sorting via {@link cookieCompare}.\n   *\n   * @param callback - A function to call when all the cookies have been retrieved or an error occurs.\n   */\n  getAllCookies(callback: Callback<Cookie[]>): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  getAllCookies(_callback?: Callback<Cookie[]>): unknown {\n    throw new Error(\n      'getAllCookies is not implemented (therefore jar cannot be serialized)',\n    )\n  }\n}\n", "/**\n * A callback function that accepts an error or a result.\n * @public\n */\nexport interface Callback<T> {\n  (error: Error, result?: never): void\n  (error: null, result: T): void\n}\n\n/**\n * A callback function that only accepts an error.\n * @public\n */\nexport interface ErrorCallback {\n  (error: Error | null): void\n}\n\n/**\n * The inverse of NonNullable<T>.\n * @public\n */\nexport type Nullable<T> = T | null | undefined\n\n/** Wrapped `Object.prototype.toString`, so that you don't need to remember to use `.call()`. */\nexport const objectToString = (obj: unknown): string =>\n  Object.prototype.toString.call(obj)\n\n/**\n * Converts an array to string, safely handling symbols, null prototype objects, and recursive arrays.\n */\nconst safeArrayToString = (\n  arr: unknown[],\n  seenArrays: WeakSet<object>,\n): string => {\n  // See https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/toString#description\n  if (typeof arr.join !== 'function') return objectToString(arr)\n  seenArrays.add(arr)\n  const mapped = arr.map((val) =>\n    val === null || val === undefined || seenArrays.has(val)\n      ? ''\n      : safeToStringImpl(val, seenArrays),\n  )\n  return mapped.join()\n}\n\nconst safeToStringImpl = (val: unknown, seenArrays = new WeakSet()): string => {\n  // Using .toString() fails for null/undefined and implicit conversion (val + \"\") fails for symbols\n  // and objects with null prototype\n  if (typeof val !== 'object' || val === null) {\n    return String(val)\n  } else if (typeof val.toString === 'function') {\n    return Array.isArray(val)\n      ? // Arrays have a weird custom toString that we need to replicate\n        safeArrayToString(val, seenArrays)\n      : // eslint-disable-next-line @typescript-eslint/no-base-to-string\n        String(val)\n  } else {\n    // This case should just be objects with null prototype, so we can just use Object#toString\n    return objectToString(val)\n  }\n}\n\n/** Safely converts any value to string, using the value's own `toString` when available. */\nexport const safeToString = (val: unknown): string => safeToStringImpl(val)\n\n/** Utility object for promise/callback interop. */\nexport interface PromiseCallback<T> {\n  promise: Promise<T>\n  callback: Callback<T>\n  resolve: (value: T) => Promise<T>\n  reject: (error: Error) => Promise<T>\n}\n\n/** Converts a callback into a utility object where either a callback or a promise can be used. */\nexport function createPromiseCallback<T>(cb?: Callback<T>): PromiseCallback<T> {\n  let callback: Callback<T>\n  let resolve: (result: T) => void\n  let reject: (error: Error) => void\n\n  const promise = new Promise<T>((_resolve, _reject) => {\n    resolve = _resolve\n    reject = _reject\n  })\n\n  if (typeof cb === 'function') {\n    callback = (err, result): void => {\n      try {\n        if (err) cb(err)\n        // If `err` is null, we know `result` must be `T`\n        // The assertion isn't *strictly* correct, as `T` could be nullish, but, ehh, good enough...\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        else cb(null, result!)\n      } catch (e) {\n        reject(e instanceof Error ? e : new Error())\n      }\n    }\n  } else {\n    callback = (err, result): void => {\n      try {\n        // If `err` is null, we know `result` must be `T`\n        // The assertion isn't *strictly* correct, as `T` could be nullish, but, ehh, good enough...\n        if (err) reject(err)\n        // eslint-disable-next-line @typescript-eslint/no-non-null-assertion\n        else resolve(result!)\n      } catch (e) {\n        reject(e instanceof Error ? e : new Error())\n      }\n    }\n  }\n\n  return {\n    promise,\n    callback,\n    resolve: (value: T): Promise<T> => {\n      callback(null, value)\n      return promise\n    },\n    reject: (error: Error): Promise<T> => {\n      callback(error)\n      return promise\n    },\n  }\n}\n\nexport function inOperator<K extends string, T extends object>(\n  k: K,\n  o: T,\n): o is T & Record<K, unknown> {\n  return k in o\n}\n", "import type { <PERSON><PERSON> } from './cookie/cookie.js'\nimport { pathMatch } from './pathMatch.js'\nimport { permuteDomain } from './permuteDomain.js'\nimport { Store } from './store.js'\nimport {\n  Callback,\n  createPromiseCallback,\n  Error<PERSON>allback,\n  Nullable,\n} from './utils.js'\n\n/**\n * The internal structure used in {@link MemoryCookieStore}.\n * @internal\n */\nexport type MemoryCookieStoreIndex = {\n  [domain: string]: {\n    [path: string]: {\n      [key: string]: Cookie\n    }\n  }\n}\n\n/**\n * An in-memory {@link Store} implementation for {@link CookieJar}. This is the default implementation used by\n * {@link CookieJar} and supports both async and sync operations. Also supports serialization, getAllCookies, and removeAllCookies.\n * @public\n */\nexport class MemoryCookieStore extends Store {\n  /**\n   * This value is `true` since {@link MemoryCookieStore} implements synchronous functionality.\n   */\n  override synchronous: boolean\n\n  /**\n   * @internal\n   */\n  idx: MemoryCookieStoreIndex\n\n  /**\n   * Create a new {@link MemoryCookieStore}.\n   */\n  constructor() {\n    super()\n    this.synchronous = true\n    this.idx = Object.create(null) as MemoryCookieStoreIndex\n  }\n\n  /**\n   * Retrieve a {@link <PERSON>ie} with the given `domain`, `path`, and `key` (`name`). The RFC maintains that exactly\n   * one of these cookies should exist in a store. If the store is using versioning, this means that the latest or\n   * newest such cookie should be returned.\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   */\n  override findCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n  ): Promise<Cookie | undefined>\n  /**\n   * Retrieve a {@link Cookie} with the given `domain`, `path`, and `key` (`name`). The RFC maintains that exactly\n   * one of these cookies should exist in a store. If the store is using versioning, this means that the latest or\n   * newest such cookie should be returned.\n   *\n   * Callback takes an error and the resulting Cookie object. If no cookie is found then null MUST be passed instead (that is, not an error).\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   * @param callback - A function to call with either the found cookie or an error.\n   */\n  override findCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n    callback: Callback<Cookie | undefined>,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override findCookie(\n    domain: Nullable<string>,\n    path: Nullable<string>,\n    key: Nullable<string>,\n    callback?: Callback<Cookie | undefined>,\n  ): unknown {\n    const promiseCallback = createPromiseCallback(callback)\n    if (domain == null || path == null || key == null) {\n      return promiseCallback.resolve(undefined)\n    }\n    const result = this.idx[domain]?.[path]?.[key]\n    return promiseCallback.resolve(result)\n  }\n\n  /**\n   * Locates all {@link Cookie} values matching the given `domain` and `path`.\n   *\n   * The resulting list is checked for applicability to the current request according to the RFC (`domain-match`, `path-match`,\n   * `http-only-flag`, `secure-flag`, `expiry`, and so on), so it's OK to use an optimistic search algorithm when implementing\n   * this method. However, the search algorithm used SHOULD try to find cookies that {@link domainMatch} the `domain` and\n   * {@link pathMatch} the `path` in order to limit the amount of checking that needs to be done.\n   *\n   * @remarks\n   * - As of version `0.9.12`, the `allPaths` option to cookiejar.getCookies() above causes the path here to be `null`.\n   *\n   * - If the `path` is `null`, `path-matching` MUST NOT be performed (that is, `domain-matching` only).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param allowSpecialUseDomain - If `true` then special-use domain suffixes, will be allowed in matches. Defaults to `false`.\n   */\n  override findCookies(\n    domain: string,\n    path: string,\n    allowSpecialUseDomain?: boolean,\n  ): Promise<Cookie[]>\n  /**\n   * Locates all {@link Cookie} values matching the given `domain` and `path`.\n   *\n   * The resulting list is checked for applicability to the current request according to the RFC (`domain-match`, `path-match`,\n   * `http-only-flag`, `secure-flag`, `expiry`, and so on), so it's OK to use an optimistic search algorithm when implementing\n   * this method. However, the search algorithm used SHOULD try to find cookies that {@link domainMatch} the `domain` and\n   * {@link pathMatch} the `path` in order to limit the amount of checking that needs to be done.\n   *\n   * @remarks\n   * - As of version `0.9.12`, the `allPaths` option to cookiejar.getCookies() above causes the path here to be `null`.\n   *\n   * - If the `path` is `null`, `path-matching` MUST NOT be performed (that is, `domain-matching` only).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param allowSpecialUseDomain - If `true` then special-use domain suffixes, will be allowed in matches. Defaults to `false`.\n   * @param callback - A function to call with either the found cookies or an error.\n   */\n  override findCookies(\n    domain: string,\n    path: string,\n    allowSpecialUseDomain?: boolean,\n    callback?: Callback<Cookie[]>,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override findCookies(\n    domain: string,\n    path: string,\n    allowSpecialUseDomain: boolean | Callback<Cookie[]> = false,\n    callback?: Callback<Cookie[]>,\n  ): unknown {\n    if (typeof allowSpecialUseDomain === 'function') {\n      callback = allowSpecialUseDomain\n      // TODO: It's weird that `allowSpecialUseDomain` defaults to false with no callback,\n      // but true with a callback. This is legacy behavior from v4.\n      allowSpecialUseDomain = true\n    }\n\n    const results: Cookie[] = []\n    const promiseCallback = createPromiseCallback<Cookie[]>(callback)\n\n    if (!domain) {\n      return promiseCallback.resolve([])\n    }\n\n    let pathMatcher: (\n      domainIndex: MemoryCookieStoreIndex[string] | undefined,\n    ) => void\n    if (!path) {\n      // null means \"all paths\"\n      pathMatcher = function matchAll(domainIndex): void {\n        for (const curPath in domainIndex) {\n          const pathIndex = domainIndex[curPath]\n          for (const key in pathIndex) {\n            const value = pathIndex[key]\n            if (value) {\n              results.push(value)\n            }\n          }\n        }\n      }\n    } else {\n      pathMatcher = function matchRFC(domainIndex): void {\n        //NOTE: we should use path-match algorithm from S5.1.4 here\n        //(see : https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/canonical_cookie.cc#L299)\n        for (const cookiePath in domainIndex) {\n          if (pathMatch(path, cookiePath)) {\n            const pathIndex = domainIndex[cookiePath]\n            for (const key in pathIndex) {\n              const value = pathIndex[key]\n              if (value) {\n                results.push(value)\n              }\n            }\n          }\n        }\n      }\n    }\n\n    const domains = permuteDomain(domain, allowSpecialUseDomain) || [domain]\n    const idx = this.idx\n    domains.forEach((curDomain) => {\n      const domainIndex = idx[curDomain]\n      if (!domainIndex) {\n        return\n      }\n      pathMatcher(domainIndex)\n    })\n\n    return promiseCallback.resolve(results)\n  }\n\n  /**\n   * Adds a new {@link Cookie} to the store. The implementation SHOULD replace any existing cookie with the same `domain`,\n   * `path`, and `key` properties.\n   *\n   * @remarks\n   * - Depending on the nature of the implementation, it's possible that between the call to `fetchCookie` and `putCookie`\n   * that a duplicate `putCookie` can occur.\n   *\n   * - The {@link Cookie} object MUST NOT be modified; as the caller has already updated the `creation` and `lastAccessed` properties.\n   *\n   * @param cookie - The cookie to store.\n   */\n  override putCookie(cookie: Cookie): Promise<void>\n  /**\n   * Adds a new {@link Cookie} to the store. The implementation SHOULD replace any existing cookie with the same `domain`,\n   * `path`, and `key` properties.\n   *\n   * @remarks\n   * - Depending on the nature of the implementation, it's possible that between the call to `fetchCookie` and `putCookie`\n   * that a duplicate `putCookie` can occur.\n   *\n   * - The {@link Cookie} object MUST NOT be modified; as the caller has already updated the `creation` and `lastAccessed` properties.\n   *\n   * @param cookie - The cookie to store.\n   * @param callback - A function to call when the cookie has been stored or an error has occurred.\n   */\n  override putCookie(cookie: Cookie, callback: ErrorCallback): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override putCookie(cookie: Cookie, callback?: ErrorCallback): unknown {\n    const promiseCallback = createPromiseCallback<undefined>(callback)\n\n    const { domain, path, key } = cookie\n\n    // Guarding against invalid input\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (domain == null || path == null || key == null) {\n      return promiseCallback.resolve(undefined)\n    }\n\n    const domainEntry =\n      this.idx[domain] ??\n      (Object.create(null) as MemoryCookieStoreIndex[string])\n\n    this.idx[domain] = domainEntry\n\n    const pathEntry =\n      domainEntry[path] ??\n      (Object.create(null) as MemoryCookieStoreIndex[string][string])\n\n    domainEntry[path] = pathEntry\n\n    pathEntry[key] = cookie\n\n    return promiseCallback.resolve(undefined)\n  }\n\n  /**\n   * Update an existing {@link Cookie}. The implementation MUST update the `value` for a cookie with the same `domain`,\n   * `path`, and `key`. The implementation SHOULD check that the old value in the store is equivalent to oldCookie -\n   * how the conflict is resolved is up to the store.\n   *\n   * @remarks\n   * - The `lastAccessed` property is always different between the two objects (to the precision possible via JavaScript's clock).\n   *\n   * - Both `creation` and `creationIndex` are guaranteed to be the same.\n   *\n   * - Stores MAY ignore or defer the `lastAccessed` change at the cost of affecting how cookies are selected for automatic deletion.\n   *\n   * - Stores may wish to optimize changing the `value` of the cookie in the store versus storing a new cookie.\n   *\n   * - The `newCookie` and `oldCookie` objects MUST NOT be modified.\n   *\n   * @param oldCookie - the cookie that is already present in the store.\n   * @param newCookie - the cookie to replace the one already present in the store.\n   */\n  override updateCookie(oldCookie: Cookie, newCookie: Cookie): Promise<void>\n  /**\n   * Update an existing {@link Cookie}. The implementation MUST update the `value` for a cookie with the same `domain`,\n   * `path`, and `key`. The implementation SHOULD check that the old value in the store is equivalent to oldCookie -\n   * how the conflict is resolved is up to the store.\n   *\n   * @remarks\n   * - The `lastAccessed` property is always different between the two objects (to the precision possible via JavaScript's clock).\n   *\n   * - Both `creation` and `creationIndex` are guaranteed to be the same.\n   *\n   * - Stores MAY ignore or defer the `lastAccessed` change at the cost of affecting how cookies are selected for automatic deletion.\n   *\n   * - Stores may wish to optimize changing the `value` of the cookie in the store versus storing a new cookie.\n   *\n   * - The `newCookie` and `oldCookie` objects MUST NOT be modified.\n   *\n   * @param oldCookie - the cookie that is already present in the store.\n   * @param newCookie - the cookie to replace the one already present in the store.\n   * @param callback - A function to call when the cookie has been updated or an error has occurred.\n   */\n  override updateCookie(\n    oldCookie: Cookie,\n    newCookie: Cookie,\n    callback: ErrorCallback,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override updateCookie(\n    _oldCookie: Cookie,\n    newCookie: Cookie,\n    callback?: ErrorCallback,\n  ): unknown {\n    // updateCookie() may avoid updating cookies that are identical.  For example,\n    // lastAccessed may not be important to some stores and an equality\n    // comparison could exclude that field.\n    // Don't return a value when using a callback, so that the return type is truly \"void\"\n    if (callback) this.putCookie(newCookie, callback)\n    else return this.putCookie(newCookie)\n  }\n\n  /**\n   * Remove a cookie from the store (see notes on `findCookie` about the uniqueness constraint).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   */\n  override removeCookie(\n    domain: string,\n    path: string,\n    key: string,\n  ): Promise<void>\n  /**\n   * Remove a cookie from the store (see notes on `findCookie` about the uniqueness constraint).\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param key - The cookie name to match against.\n   * @param callback - A function to call when the cookie has been removed or an error occurs.\n   */\n  override removeCookie(\n    domain: string,\n    path: string,\n    key: string,\n    callback: ErrorCallback,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override removeCookie(\n    domain: string,\n    path: string,\n    key: string,\n    callback?: ErrorCallback,\n  ): unknown {\n    const promiseCallback = createPromiseCallback<undefined>(callback)\n    delete this.idx[domain]?.[path]?.[key]\n    return promiseCallback.resolve(undefined)\n  }\n\n  /**\n   * Removes matching cookies from the store. The `path` parameter is optional and if missing,\n   * means all paths in a domain should be removed.\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   */\n  override removeCookies(domain: string, path: string): Promise<void>\n  /**\n   * Removes matching cookies from the store. The `path` parameter is optional and if missing,\n   * means all paths in a domain should be removed.\n   *\n   * @param domain - The cookie domain to match against.\n   * @param path - The cookie path to match against.\n   * @param callback - A function to call when the cookies have been removed or an error occurs.\n   */\n  override removeCookies(\n    domain: string,\n    path: string,\n    callback: ErrorCallback,\n  ): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override removeCookies(\n    domain: string,\n    path: string,\n    callback?: ErrorCallback,\n  ): unknown {\n    const promiseCallback = createPromiseCallback<undefined>(callback)\n\n    const domainEntry = this.idx[domain]\n    if (domainEntry) {\n      if (path) {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete domainEntry[path]\n      } else {\n        // eslint-disable-next-line @typescript-eslint/no-dynamic-delete\n        delete this.idx[domain]\n      }\n    }\n\n    return promiseCallback.resolve(undefined)\n  }\n\n  /**\n   * Removes all cookies from the store.\n   */\n  override removeAllCookies(): Promise<void>\n  /**\n   * Removes all cookies from the store.\n   *\n   * @param callback - A function to call when all the cookies have been removed or an error occurs.\n   */\n  override removeAllCookies(callback: ErrorCallback): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override removeAllCookies(callback?: ErrorCallback): unknown {\n    const promiseCallback = createPromiseCallback<undefined>(callback)\n    this.idx = Object.create(null) as MemoryCookieStoreIndex\n    return promiseCallback.resolve(undefined)\n  }\n\n  /**\n   * Gets all the cookies in the store.\n   *\n   * @remarks\n   * - Cookies SHOULD be returned in creation order to preserve sorting via {@link cookieCompare}.\n   */\n  override getAllCookies(): Promise<Cookie[]>\n  /**\n   * Gets all the cookies in the store.\n   *\n   * @remarks\n   * - Cookies SHOULD be returned in creation order to preserve sorting via {@link cookieCompare}.\n   *\n   * @param callback - A function to call when all the cookies have been retrieved or an error occurs.\n   */\n  override getAllCookies(callback: Callback<Cookie[]>): void\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  override getAllCookies(callback?: Callback<Cookie[]>): unknown {\n    const promiseCallback = createPromiseCallback<Cookie[]>(callback)\n\n    const cookies: Cookie[] = []\n    const idx = this.idx\n\n    const domains = Object.keys(idx)\n    domains.forEach((domain) => {\n      const domainEntry = idx[domain] ?? {}\n      const paths = Object.keys(domainEntry)\n      paths.forEach((path) => {\n        const pathEntry = domainEntry[path] ?? {}\n        const keys = Object.keys(pathEntry)\n        keys.forEach((key) => {\n          const keyEntry = pathEntry[key]\n          if (keyEntry != null) {\n            cookies.push(keyEntry)\n          }\n        })\n      })\n    })\n\n    // Sort by creationIndex so deserializing retains the creation order.\n    // When implementing your own store, this SHOULD retain the order too\n    cookies.sort((a, b) => {\n      return (a.creationIndex || 0) - (b.creationIndex || 0)\n    })\n\n    return promiseCallback.resolve(cookies)\n  }\n}\n", "/* ************************************************************************************\nExtracted from check-types.js\nhttps://gitlab.com/philbooth/check-types.js\n\nMIT License\n\nCopyright (c) 2012, 2013, 2014, 2015, 2016, 2017, 2018, 2019 Phil Booth\n\nPermission is hereby granted, free of charge, to any person obtaining a copy\nof this software and associated documentation files (the \"Software\"), to deal\nin the Software without restriction, including without limitation the rights\nto use, copy, modify, merge, publish, distribute, sublicense, and/or sell\ncopies of the Software, and to permit persons to whom the Software is\nfurnished to do so, subject to the following conditions:\n\nThe above copyright notice and this permission notice shall be included in all\ncopies or substantial portions of the Software.\n\nTHE SOFTWARE IS PROVIDED \"AS IS\", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR\nIMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,\nFITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE\nAUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER\nLIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,\nOUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE\nSOFTWARE.\n\n************************************************************************************ */\n\nimport { Callback, objectToString, safeToString } from './utils.js'\n\n/* Validation functions copied from check-types package - https://www.npmjs.com/package/check-types */\n\n/** Determines whether the argument is a non-empty string. */\nexport function isNonEmptyString(data: unknown): boolean {\n  return isString(data) && data !== ''\n}\n\n/** Determines whether the argument is a *valid* Date. */\nexport function isDate(data: unknown): boolean {\n  return data instanceof Date && isInteger(data.getTime())\n}\n\n/** Determines whether the argument is the empty string. */\nexport function isEmptyString(data: unknown): boolean {\n  return data === '' || (data instanceof String && data.toString() === '')\n}\n\n/** Determines whether the argument is a string. */\nexport function isString(data: unknown): boolean {\n  return typeof data === 'string' || data instanceof String\n}\n\n/** Determines whether the string representation of the argument is \"[object Object]\". */\nexport function isObject(data: unknown): boolean {\n  return objectToString(data) === '[object Object]'\n}\n\n/** Determines whether the argument is an integer. */\nexport function isInteger(data: unknown): boolean {\n  return typeof data === 'number' && data % 1 === 0\n}\n\n/* -- End validation functions -- */\n\n/**\n * When the first argument is false, an error is created with the given message. If a callback is\n * provided, the error is passed to the callback, otherwise the error is thrown.\n */\nexport function validate(\n  bool: boolean,\n  cbOrMessage?: Callback<never> | string,\n  message?: string,\n): void {\n  if (bool) return // Validation passes\n  const cb = typeof cbOrMessage === 'function' ? cbOrMessage : undefined\n  let options = typeof cbOrMessage === 'function' ? message : cbOrMessage\n  // The default message prior to v5 was '[object Object]' due to a bug, and the message is kept\n  // for backwards compatibility.\n  if (!isObject(options)) options = '[object Object]'\n\n  const err = new ParameterError(safeToString(options))\n  if (cb) cb(err)\n  else throw err\n}\n\n/**\n * Represents a validation error.\n * @public\n */\nexport class ParameterError extends Error {}\n", "/**\n * The version of `tough-cookie`\n * @public\n */\nexport const version = '6.0.0'\n", "/**\n * Cookie prefixes are a way to indicate that a given cookie was set with a set of attributes simply by inspecting the\n * first few characters of the cookie's name. These are defined in {@link https://datatracker.ietf.org/doc/html/draft-ietf-httpbis-rfc6265bis-13#section-4.1.3 | RFC6265bis - Section 4.1.3}.\n *\n * The following values can be used to configure how a {@link CookieJar} enforces attribute restrictions for Cookie prefixes:\n *\n * - `silent` - Enable cookie prefix checking but silently ignores the cookie if conditions are not met. This is the default configuration for a {@link CookieJar}.\n *\n * - `strict` - Enables cookie prefix checking and will raise an error if conditions are not met.\n *\n * - `unsafe-disabled` - Disables cookie prefix checking.\n * @public\n */\nexport const PrefixSecurityEnum = {\n  SILENT: 'silent',\n  STRICT: 'strict',\n  DISABLED: 'unsafe-disabled',\n} as const\nObject.freeze(PrefixSecurityEnum)\n\nconst IP_V6_REGEX = `\n\\\\[?(?:\n(?:[a-fA-F\\\\d]{1,4}:){7}(?:[a-fA-F\\\\d]{1,4}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|:[a-fA-F\\\\d]{1,4}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,2}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){4}(?:(?::[a-fA-F\\\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,3}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){3}(?:(?::[a-fA-F\\\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,4}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){2}(?:(?::[a-fA-F\\\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,5}|:)|\n(?:[a-fA-F\\\\d]{1,4}:){1}(?:(?::[a-fA-F\\\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,6}|:)|\n(?::(?:(?::[a-fA-F\\\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)(?:\\\\.(?:25[0-5]|2[0-4]\\\\d|1\\\\d\\\\d|[1-9]\\\\d|\\\\d)){3}|(?::[a-fA-F\\\\d]{1,4}){1,7}|:))\n)(?:%[0-9a-zA-Z]{1,})?\\\\]?\n`\n  .replace(/\\s*\\/\\/.*$/gm, '')\n  .replace(/\\n/g, '')\n  .trim()\nexport const IP_V6_REGEX_OBJECT: RegExp = new RegExp(`^${IP_V6_REGEX}$`)\n\nconst IP_V4_REGEX = `(?:(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])\\\\.){3}(?:25[0-5]|2[0-4][0-9]|1[0-9][0-9]|[1-9][0-9]|[0-9])`\nexport const IP_V4_REGEX_OBJECT: RegExp = new RegExp(`^${IP_V4_REGEX}$`)\n\n/**\n * A JSON representation of a {@link CookieJar}.\n * @public\n */\nexport interface SerializedCookieJar {\n  /**\n   * The version of `tough-cookie` used during serialization.\n   */\n  version: string\n  /**\n   * The name of the store used during serialization.\n   */\n  storeType: string | null\n  /**\n   * The value of {@link CreateCookieJarOptions.rejectPublicSuffixes} configured on the {@link CookieJar}.\n   */\n  rejectPublicSuffixes: boolean\n  /**\n   * Other configuration settings on the {@link CookieJar}.\n   */\n  [key: string]: unknown\n  /**\n   * The list of {@link Cookie} values serialized as JSON objects.\n   */\n  cookies: SerializedCookie[]\n}\n\n/**\n * A JSON object that is created when {@link Cookie.toJSON} is called. This object will contain the properties defined in {@link Cookie.serializableProperties}.\n * @public\n */\nexport type SerializedCookie = {\n  key?: string\n  value?: string\n  [key: string]: unknown\n}\n", "import { IP_V6_REGEX_OBJECT } from './constants.js'\nimport type { Nullable } from '../utils.js'\n\n/**\n * Normalizes a domain to lowercase and punycode-encoded.\n * Runtime-agnostic equivalent to node's `domainToASCII`.\n * @see https://nodejs.org/docs/latest-v22.x/api/url.html#urldomaintoasciidomain\n */\nfunction domainToASCII(domain: string): string {\n  return new URL(`http://${domain}`).hostname\n}\n\n/**\n * Transforms a domain name into a canonical domain name. The canonical domain name is a domain name\n * that has been trimmed, lowercased, stripped of leading dot, and optionally punycode-encoded\n * ({@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.1.2 | Section 5.1.2 of RFC 6265}). For\n * the most part, this function is idempotent (calling the function with the output from a previous call\n * returns the same output).\n *\n * @remarks\n * A canonicalized host name is the string generated by the following\n * algorithm:\n *\n * 1.  Convert the host name to a sequence of individual domain name\n *     labels.\n *\n * 2.  Convert each label that is not a Non-Reserved LDH (NR-LDH) label,\n *     to an A-label (see Section ******* of [RFC5890] for the former\n *     and latter), or to a \"punycode label\" (a label resulting from the\n *     \"ToASCII\" conversion in Section 4 of [RFC3490]), as appropriate\n *     (see Section 6.3 of this specification).\n *\n * 3.  Concatenate the resulting labels, separated by a %x2E (\".\")\n *     character.\n *\n * @example\n * ```\n * canonicalDomain('.EXAMPLE.com') === 'example.com'\n * ```\n *\n * @param domainName - the domain name to generate the canonical domain from\n * @public\n */\nexport function canonicalDomain(\n  domainName: Nullable<string>,\n): string | undefined {\n  if (domainName == null) {\n    return undefined\n  }\n  let str = domainName.trim().replace(/^\\./, '') // S4.1.2.3 & S5.2.3: ignore leading .\n\n  if (IP_V6_REGEX_OBJECT.test(str)) {\n    if (!str.startsWith('[')) {\n      str = '[' + str\n    }\n    if (!str.endsWith(']')) {\n      str = str + ']'\n    }\n    return domainToASCII(str).slice(1, -1) // remove [ and ]\n  }\n\n  // convert to IDN if any non-ASCII characters\n  // eslint-disable-next-line no-control-regex\n  if (/[^\\u0001-\\u007f]/.test(str)) {\n    return domainToASCII(str)\n  }\n\n  // ASCII-only domain - not canonicalized with new URL() because it may be a malformed URL\n  return str.toLowerCase()\n}\n", "/**\n * Format a {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date | Date} into\n * the {@link https://www.rfc-editor.org/rfc/rfc2616#section-3.3.1 | preferred Internet standard format}\n * defined in {@link https://www.rfc-editor.org/rfc/rfc822#section-5 | RFC822} and\n * updated in {@link https://www.rfc-editor.org/rfc/rfc1123#page-55 | RFC1123}.\n *\n * @example\n * ```\n * formatDate(new Date(0)) === 'Thu, 01 Jan 1970 00:00:00 GMT`\n * ```\n *\n * @param date - the date value to format\n * @public\n */\nexport function formatDate(date: Date): string {\n  return date.toUTCString()\n}\n", "// date-time parsing constants (RFC6265 S5.1.1)\n\nimport type { Nullable } from '../utils.js'\n\n// eslint-disable-next-line no-control-regex\nconst DATE_DELIM = /[\\x09\\x20-\\x2F\\x3B-\\x40\\x5B-\\x60\\x7B-\\x7E]/\n\nconst MONTH_TO_NUM = {\n  jan: 0,\n  feb: 1,\n  mar: 2,\n  apr: 3,\n  may: 4,\n  jun: 5,\n  jul: 6,\n  aug: 7,\n  sep: 8,\n  oct: 9,\n  nov: 10,\n  dec: 11,\n}\n\n/*\n * Parses a Natural number (i.e., non-negative integer) with either the\n *    <min>*<max>DIGIT ( non-digit *OCTET )\n * or\n *    <min>*<max>DIGIT\n * grammar (RFC6265 S5.1.1).\n *\n * The \"trailingOK\" boolean controls if the grammar accepts a\n * \"( non-digit *OCTET )\" trailer.\n */\nfunction parseDigits(\n  token: string,\n  minDigits: number,\n  maxDigits: number,\n  trailingOK: boolean,\n): number | undefined {\n  let count = 0\n  while (count < token.length) {\n    const c = token.charCodeAt(count)\n    // \"non-digit = %x00-2F / %x3A-FF\"\n    if (c <= 0x2f || c >= 0x3a) {\n      break\n    }\n    count++\n  }\n\n  // constrain to a minimum and maximum number of digits.\n  if (count < minDigits || count > maxDigits) {\n    return\n  }\n\n  if (!trailingOK && count != token.length) {\n    return\n  }\n\n  return parseInt(token.slice(0, count), 10)\n}\n\nfunction parseTime(token: string): number[] | undefined {\n  const parts = token.split(':')\n  const result = [0, 0, 0]\n\n  /* RF6256 S5.1.1:\n   *      time            = hms-time ( non-digit *OCTET )\n   *      hms-time        = time-field \":\" time-field \":\" time-field\n   *      time-field      = 1*2DIGIT\n   */\n\n  if (parts.length !== 3) {\n    return\n  }\n\n  for (let i = 0; i < 3; i++) {\n    // \"time-field\" must be strictly \"1*2DIGIT\", HOWEVER, \"hms-time\" can be\n    // followed by \"( non-digit *OCTET )\" therefore the last time-field can\n    // have a trailer\n    const trailingOK = i == 2\n    const numPart = parts[i]\n    if (numPart === undefined) {\n      return\n    }\n    const num = parseDigits(numPart, 1, 2, trailingOK)\n    if (num === undefined) {\n      return\n    }\n    result[i] = num\n  }\n\n  return result\n}\n\nfunction parseMonth(token: string): number | undefined {\n  token = String(token).slice(0, 3).toLowerCase()\n  switch (token) {\n    case 'jan':\n      return MONTH_TO_NUM.jan\n    case 'feb':\n      return MONTH_TO_NUM.feb\n    case 'mar':\n      return MONTH_TO_NUM.mar\n    case 'apr':\n      return MONTH_TO_NUM.apr\n    case 'may':\n      return MONTH_TO_NUM.may\n    case 'jun':\n      return MONTH_TO_NUM.jun\n    case 'jul':\n      return MONTH_TO_NUM.jul\n    case 'aug':\n      return MONTH_TO_NUM.aug\n    case 'sep':\n      return MONTH_TO_NUM.sep\n    case 'oct':\n      return MONTH_TO_NUM.oct\n    case 'nov':\n      return MONTH_TO_NUM.nov\n    case 'dec':\n      return MONTH_TO_NUM.dec\n    default:\n      return\n  }\n}\n\n/**\n * Parse a cookie date string into a {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date | Date}. Parses according to\n * {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.1.1 | RFC6265 - Section 5.1.1}, not\n * {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date/parse | Date.parse()}.\n *\n * @remarks\n *\n * ### RFC6265 - 5.1.1. Dates\n *\n * The user agent MUST use an algorithm equivalent to the following\n * algorithm to parse a cookie-date.  Note that the various boolean\n * flags defined as a part of the algorithm (i.e., found-time, found-\n * day-of-month, found-month, found-year) are initially \"not set\".\n *\n * 1.  Using the grammar below, divide the cookie-date into date-tokens.\n *\n * ```\n *     cookie-date     = *delimiter date-token-list *delimiter\n *     date-token-list = date-token *( 1*delimiter date-token )\n *     date-token      = 1*non-delimiter\n *\n *     delimiter       = %x09 / %x20-2F / %x3B-40 / %x5B-60 / %x7B-7E\n *     non-delimiter   = %x00-08 / %x0A-1F / DIGIT / \":\" / ALPHA / %x7F-FF\n *     non-digit       = %x00-2F / %x3A-FF\n *\n *     day-of-month    = 1*2DIGIT ( non-digit *OCTET )\n *     month           = ( \"jan\" / \"feb\" / \"mar\" / \"apr\" /\n *                        \"may\" / \"jun\" / \"jul\" / \"aug\" /\n *                        \"sep\" / \"oct\" / \"nov\" / \"dec\" ) *OCTET\n *     year            = 2*4DIGIT ( non-digit *OCTET )\n *     time            = hms-time ( non-digit *OCTET )\n *     hms-time        = time-field \":\" time-field \":\" time-field\n *     time-field      = 1*2DIGIT\n * ```\n *\n * 2. Process each date-token sequentially in the order the date-tokens\n *     appear in the cookie-date:\n *\n *     1. If the found-time flag is not set and the token matches the\n *         time production, set the found-time flag and set the hour-\n *         value, minute-value, and second-value to the numbers denoted\n *         by the digits in the date-token, respectively.  Skip the\n *         remaining sub-steps and continue to the next date-token.\n *\n *     2. If the found-day-of-month flag is not set and the date-token\n *         matches the day-of-month production, set the found-day-of-\n *         month flag and set the day-of-month-value to the number\n *         denoted by the date-token.  Skip the remaining sub-steps and\n *         continue to the next date-token.\n *\n *     3. If the found-month flag is not set and the date-token matches\n *         the month production, set the found-month flag and set the\n *         month-value to the month denoted by the date-token.  Skip the\n *         remaining sub-steps and continue to the next date-token.\n *\n *     4. If the found-year flag is not set and the date-token matches\n *         the year production, set the found-year flag and set the\n *         year-value to the number denoted by the date-token.  Skip the\n *         remaining sub-steps and continue to the next date-token.\n *\n *  3. If the year-value is greater than or equal to 70 and less than or\n *      equal to 99, increment the year-value by 1900.\n *\n *  4. If the year-value is greater than or equal to 0 and less than or\n *      equal to 69, increment the year-value by 2000.\n *\n *      1. NOTE: Some existing user agents interpret two-digit years differently.\n *\n *  5. Abort these steps and fail to parse the cookie-date if:\n *\n *      - at least one of the found-day-of-month, found-month, found-\n *          year, or found-time flags is not set,\n *\n *      - the day-of-month-value is less than 1 or greater than 31,\n *\n *      - the year-value is less than 1601,\n *\n *      - the hour-value is greater than 23,\n *\n *      - the minute-value is greater than 59, or\n *\n *      - the second-value is greater than 59.\n *\n *      (Note that leap seconds cannot be represented in this syntax.)\n *\n *  6. Let the parsed-cookie-date be the date whose day-of-month, month,\n *      year, hour, minute, and second (in UTC) are the day-of-month-\n *      value, the month-value, the year-value, the hour-value, the\n *      minute-value, and the second-value, respectively.  If no such\n *      date exists, abort these steps and fail to parse the cookie-date.\n *\n *  7. Return the parsed-cookie-date as the result of this algorithm.\n *\n * @example\n * ```\n * parseDate('Wed, 09 Jun 2021 10:18:14 GMT')\n * ```\n *\n * @param cookieDate - the cookie date string\n * @public\n */\nexport function parseDate(cookieDate: Nullable<string>): Date | undefined {\n  if (!cookieDate) {\n    return\n  }\n\n  /* RFC6265 S5.1.1:\n   * 2. Process each date-token sequentially in the order the date-tokens\n   * appear in the cookie-date\n   */\n  const tokens = cookieDate.split(DATE_DELIM)\n\n  let hour: number | undefined\n  let minute: number | undefined\n  let second: number | undefined\n  let dayOfMonth: number | undefined\n  let month: number | undefined\n  let year: number | undefined\n\n  for (let i = 0; i < tokens.length; i++) {\n    const token = (tokens[i] ?? '').trim()\n    if (!token.length) {\n      continue\n    }\n\n    /* 2.1. If the found-time flag is not set and the token matches the time\n     * production, set the found-time flag and set the hour- value,\n     * minute-value, and second-value to the numbers denoted by the digits in\n     * the date-token, respectively.  Skip the remaining sub-steps and continue\n     * to the next date-token.\n     */\n    if (second === undefined) {\n      const result = parseTime(token)\n      if (result) {\n        hour = result[0]\n        minute = result[1]\n        second = result[2]\n        continue\n      }\n    }\n\n    /* 2.2. If the found-day-of-month flag is not set and the date-token matches\n     * the day-of-month production, set the found-day-of- month flag and set\n     * the day-of-month-value to the number denoted by the date-token.  Skip\n     * the remaining sub-steps and continue to the next date-token.\n     */\n    if (dayOfMonth === undefined) {\n      // \"day-of-month = 1*2DIGIT ( non-digit *OCTET )\"\n      const result = parseDigits(token, 1, 2, true)\n      if (result !== undefined) {\n        dayOfMonth = result\n        continue\n      }\n    }\n\n    /* 2.3. If the found-month flag is not set and the date-token matches the\n     * month production, set the found-month flag and set the month-value to\n     * the month denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (month === undefined) {\n      const result = parseMonth(token)\n      if (result !== undefined) {\n        month = result\n        continue\n      }\n    }\n\n    /* 2.4. If the found-year flag is not set and the date-token matches the\n     * year production, set the found-year flag and set the year-value to the\n     * number denoted by the date-token.  Skip the remaining sub-steps and\n     * continue to the next date-token.\n     */\n    if (year === undefined) {\n      // \"year = 2*4DIGIT ( non-digit *OCTET )\"\n      const result = parseDigits(token, 2, 4, true)\n      if (result !== undefined) {\n        year = result\n        /* From S5.1.1:\n         * 3.  If the year-value is greater than or equal to 70 and less\n         * than or equal to 99, increment the year-value by 1900.\n         * 4.  If the year-value is greater than or equal to 0 and less\n         * than or equal to 69, increment the year-value by 2000.\n         */\n        if (year >= 70 && year <= 99) {\n          year += 1900\n        } else if (year >= 0 && year <= 69) {\n          year += 2000\n        }\n      }\n    }\n  }\n\n  /* RFC 6265 S5.1.1\n   * \"5. Abort these steps and fail to parse the cookie-date if:\n   *     *  at least one of the found-day-of-month, found-month, found-\n   *        year, or found-time flags is not set,\n   *     *  the day-of-month-value is less than 1 or greater than 31,\n   *     *  the year-value is less than 1601,\n   *     *  the hour-value is greater than 23,\n   *     *  the minute-value is greater than 59, or\n   *     *  the second-value is greater than 59.\n   *     (Note that leap seconds cannot be represented in this syntax.)\"\n   *\n   * So, in order as above:\n   */\n  if (\n    dayOfMonth === undefined ||\n    month === undefined ||\n    year === undefined ||\n    hour === undefined ||\n    minute === undefined ||\n    second === undefined ||\n    dayOfMonth < 1 ||\n    dayOfMonth > 31 ||\n    year < 1601 ||\n    hour > 23 ||\n    minute > 59 ||\n    second > 59\n  ) {\n    return\n  }\n\n  return new Date(Date.UTC(year, month, dayOfMonth, hour, minute, second))\n}\n", "/*!\n * Copyright (c) 2015-2020, Salesforce.com, Inc.\n * All rights reserved.\n *\n * Redistribution and use in source and binary forms, with or without\n * modification, are permitted provided that the following conditions are met:\n *\n * 1. Redistributions of source code must retain the above copyright notice,\n * this list of conditions and the following disclaimer.\n *\n * 2. Redistributions in binary form must reproduce the above copyright notice,\n * this list of conditions and the following disclaimer in the documentation\n * and/or other materials provided with the distribution.\n *\n * 3. Neither the name of Salesforce.com nor the names of its contributors may\n * be used to endorse or promote products derived from this software without\n * specific prior written permission.\n *\n * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS \"AS IS\"\n * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE\n * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE\n * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT HOLDER OR CONTRIBUTORS BE\n * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR\n * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF\n * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS\n * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN\n * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)\n * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE\n * POSSIBILITY OF SUCH DAMAGE.\n */\nimport { getPublicSuffix } from '../getPublicSuffix.js'\nimport * as validators from '../validators.js'\nimport { inOperator } from '../utils.js'\n\nimport { formatDate } from './formatDate.js'\nimport { parseDate } from './parseDate.js'\nimport { canonicalDomain } from './canonicalDomain.js'\nimport type { SerializedCookie } from './constants.js'\n\n// From RFC6265 S4.1.1\n// note that it excludes \\x3B \";\"\nconst COOKIE_OCTETS = /^[\\x21\\x23-\\x2B\\x2D-\\x3A\\x3C-\\x5B\\x5D-\\x7E]+$/\n\n// RFC6265 S4.1.1 defines path value as 'any CHAR except CTLs or \";\"'\n// Note ';' is \\x3B\nconst PATH_VALUE = /[\\x20-\\x3A\\x3C-\\x7E]+/\n\n// eslint-disable-next-line no-control-regex\nconst CONTROL_CHARS = /[\\x00-\\x1F]/\n\n// From Chromium // '\\r', '\\n' and '\\0' should be treated as a terminator in\n// the \"relaxed\" mode, see:\n// https://github.com/ChromiumWebApps/chromium/blob/b3d3b4da8bb94c1b2e061600df106d590fda3620/net/cookies/parsed_cookie.cc#L60\nconst TERMINATORS = ['\\n', '\\r', '\\0']\n\nfunction trimTerminator(str: string): string {\n  if (validators.isEmptyString(str)) return str\n  for (let t = 0; t < TERMINATORS.length; t++) {\n    const terminator = TERMINATORS[t]\n    const terminatorIdx = terminator ? str.indexOf(terminator) : -1\n    if (terminatorIdx !== -1) {\n      str = str.slice(0, terminatorIdx)\n    }\n  }\n\n  return str\n}\n\nfunction parseCookiePair(\n  cookiePair: string,\n  looseMode: boolean,\n): Cookie | undefined {\n  cookiePair = trimTerminator(cookiePair)\n\n  let firstEq = cookiePair.indexOf('=')\n  if (looseMode) {\n    if (firstEq === 0) {\n      // '=' is immediately at start\n      cookiePair = cookiePair.substring(1)\n      firstEq = cookiePair.indexOf('=') // might still need to split on '='\n    }\n  } else {\n    // non-loose mode\n    if (firstEq <= 0) {\n      // no '=' or is at start\n      return undefined // needs to have non-empty \"cookie-name\"\n    }\n  }\n\n  let cookieName, cookieValue\n  if (firstEq <= 0) {\n    cookieName = ''\n    cookieValue = cookiePair.trim()\n  } else {\n    cookieName = cookiePair.slice(0, firstEq).trim()\n    cookieValue = cookiePair.slice(firstEq + 1).trim()\n  }\n\n  if (CONTROL_CHARS.test(cookieName) || CONTROL_CHARS.test(cookieValue)) {\n    return undefined\n  }\n\n  const c = new Cookie()\n  c.key = cookieName\n  c.value = cookieValue\n  return c\n}\n\n/**\n * Optional configuration to be used when parsing cookies.\n * @public\n */\nexport interface ParseCookieOptions {\n  /**\n   * If `true` then keyless cookies like `=abc` and `=` which are not RFC-compliant will be parsed.\n   */\n  loose?: boolean | undefined\n}\n\nfunction parse(str: string, options?: ParseCookieOptions): Cookie | undefined {\n  if (validators.isEmptyString(str) || !validators.isString(str)) {\n    return undefined\n  }\n\n  str = str.trim()\n\n  // We use a regex to parse the \"name-value-pair\" part of S5.2\n  const firstSemi = str.indexOf(';') // S5.2 step 1\n  const cookiePair = firstSemi === -1 ? str : str.slice(0, firstSemi)\n  const c = parseCookiePair(cookiePair, options?.loose ?? false)\n  if (!c) {\n    return undefined\n  }\n\n  if (firstSemi === -1) {\n    return c\n  }\n\n  // S5.2.3 \"unparsed-attributes consist of the remainder of the set-cookie-string\n  // (including the %x3B (\";\") in question).\" plus later on in the same section\n  // \"discard the first \";\" and trim\".\n  const unparsed = str.slice(firstSemi + 1).trim()\n\n  // \"If the unparsed-attributes string is empty, skip the rest of these\n  // steps.\"\n  if (unparsed.length === 0) {\n    return c\n  }\n\n  /*\n   * S5.2 says that when looping over the items \"[p]rocess the attribute-name\n   * and attribute-value according to the requirements in the following\n   * subsections\" for every item.  Plus, for many of the individual attributes\n   * in S5.3 it says to use the \"attribute-value of the last attribute in the\n   * cookie-attribute-list\".  Therefore, in this implementation, we overwrite\n   * the previous value.\n   */\n  const cookie_avs = unparsed.split(';')\n  while (cookie_avs.length) {\n    const av = (cookie_avs.shift() ?? '').trim()\n    if (av.length === 0) {\n      // happens if \";;\" appears\n      continue\n    }\n    const av_sep = av.indexOf('=')\n    let av_key: string, av_value: string | null\n\n    if (av_sep === -1) {\n      av_key = av\n      av_value = null\n    } else {\n      av_key = av.slice(0, av_sep)\n      av_value = av.slice(av_sep + 1)\n    }\n\n    av_key = av_key.trim().toLowerCase()\n\n    if (av_value) {\n      av_value = av_value.trim()\n    }\n\n    switch (av_key) {\n      case 'expires': // S5.2.1\n        if (av_value) {\n          const exp = parseDate(av_value)\n          // \"If the attribute-value failed to parse as a cookie date, ignore the\n          // cookie-av.\"\n          if (exp) {\n            // over and underflow not realistically a concern: V8's getTime() seems to\n            // store something larger than a 32-bit time_t (even with 32-bit node)\n            c.expires = exp\n          }\n        }\n        break\n\n      case 'max-age': // S5.2.2\n        if (av_value) {\n          // \"If the first character of the attribute-value is not a DIGIT or a \"-\"\n          // character ...[or]... If the remainder of attribute-value contains a\n          // non-DIGIT character, ignore the cookie-av.\"\n          if (/^-?[0-9]+$/.test(av_value)) {\n            const delta = parseInt(av_value, 10)\n            // \"If delta-seconds is less than or equal to zero (0), let expiry-time\n            // be the earliest representable date and time.\"\n            c.setMaxAge(delta)\n          }\n        }\n        break\n\n      case 'domain': // S5.2.3\n        // \"If the attribute-value is empty, the behavior is undefined.  However,\n        // the user agent SHOULD ignore the cookie-av entirely.\"\n        if (av_value) {\n          // S5.2.3 \"Let cookie-domain be the attribute-value without the leading %x2E\n          // (\".\") character.\"\n          const domain = av_value.trim().replace(/^\\./, '')\n          if (domain) {\n            // \"Convert the cookie-domain to lower case.\"\n            c.domain = domain.toLowerCase()\n          }\n        }\n        break\n\n      case 'path': // S5.2.4\n        /*\n         * \"If the attribute-value is empty or if the first character of the\n         * attribute-value is not %x2F (\"/\"):\n         *   Let cookie-path be the default-path.\n         * Otherwise:\n         *   Let cookie-path be the attribute-value.\"\n         *\n         * We'll represent the default-path as null since it depends on the\n         * context of the parsing.\n         */\n        c.path = av_value && av_value[0] === '/' ? av_value : null\n        break\n\n      case 'secure': // S5.2.5\n        /*\n         * \"If the attribute-name case-insensitively matches the string \"Secure\",\n         * the user agent MUST append an attribute to the cookie-attribute-list\n         * with an attribute-name of Secure and an empty attribute-value.\"\n         */\n        c.secure = true\n        break\n\n      case 'httponly': // S5.2.6 -- effectively the same as 'secure'\n        c.httpOnly = true\n        break\n\n      case 'samesite': // RFC6265bis-02 S5.3.7\n        switch (av_value ? av_value.toLowerCase() : '') {\n          case 'strict':\n            c.sameSite = 'strict'\n            break\n          case 'lax':\n            c.sameSite = 'lax'\n            break\n          case 'none':\n            c.sameSite = 'none'\n            break\n          default:\n            c.sameSite = undefined\n            break\n        }\n        break\n\n      default:\n        c.extensions = c.extensions || []\n        c.extensions.push(av)\n        break\n    }\n  }\n\n  return c\n}\n\nfunction fromJSON(str: unknown): Cookie | undefined {\n  if (!str || validators.isEmptyString(str)) {\n    return undefined\n  }\n\n  let obj: unknown\n  if (typeof str === 'string') {\n    try {\n      obj = JSON.parse(str)\n    } catch {\n      return undefined\n    }\n  } else {\n    // assume it's an Object\n    obj = str\n  }\n\n  const c = new Cookie()\n  Cookie.serializableProperties.forEach((prop) => {\n    if (obj && typeof obj === 'object' && inOperator(prop, obj)) {\n      const val = obj[prop]\n      if (val === undefined) {\n        return\n      }\n\n      if (inOperator(prop, cookieDefaults) && val === cookieDefaults[prop]) {\n        return\n      }\n\n      switch (prop) {\n        case 'key':\n        case 'value':\n        case 'sameSite':\n          if (typeof val === 'string') {\n            c[prop] = val\n          }\n          break\n        case 'expires':\n        case 'creation':\n        case 'lastAccessed':\n          if (\n            typeof val === 'number' ||\n            typeof val === 'string' ||\n            val instanceof Date\n          ) {\n            c[prop] = obj[prop] == 'Infinity' ? 'Infinity' : new Date(val)\n          } else if (val === null) {\n            c[prop] = null\n          }\n          break\n        case 'maxAge':\n          if (\n            typeof val === 'number' ||\n            val === 'Infinity' ||\n            val === '-Infinity'\n          ) {\n            c[prop] = val\n          }\n          break\n        case 'domain':\n        case 'path':\n          if (typeof val === 'string' || val === null) {\n            c[prop] = val\n          }\n          break\n        case 'secure':\n        case 'httpOnly':\n          if (typeof val === 'boolean') {\n            c[prop] = val\n          }\n          break\n        case 'extensions':\n          if (\n            Array.isArray(val) &&\n            val.every((item) => typeof item === 'string')\n          ) {\n            c[prop] = val\n          }\n          break\n        case 'hostOnly':\n        case 'pathIsDefault':\n          if (typeof val === 'boolean' || val === null) {\n            c[prop] = val\n          }\n          break\n      }\n    }\n  })\n\n  return c\n}\n\n/**\n * Configurable values that can be set when creating a {@link Cookie}.\n * @public\n */\nexport interface CreateCookieOptions {\n  /** {@inheritDoc Cookie.key} */\n  key?: string\n  /** {@inheritDoc Cookie.value} */\n  value?: string\n  /** {@inheritDoc Cookie.expires} */\n  expires?: Date | 'Infinity' | null\n  /** {@inheritDoc Cookie.maxAge} */\n  maxAge?: number | 'Infinity' | '-Infinity' | null\n  /** {@inheritDoc Cookie.domain} */\n  domain?: string | null\n  /** {@inheritDoc Cookie.path} */\n  path?: string | null\n  /** {@inheritDoc Cookie.secure} */\n  secure?: boolean\n  /** {@inheritDoc Cookie.httpOnly} */\n  httpOnly?: boolean\n  /** {@inheritDoc Cookie.extensions} */\n  extensions?: string[] | null\n  /** {@inheritDoc Cookie.creation} */\n  creation?: Date | 'Infinity' | null\n  /** {@inheritDoc Cookie.hostOnly} */\n  hostOnly?: boolean | null\n  /** {@inheritDoc Cookie.pathIsDefault} */\n  pathIsDefault?: boolean | null\n  /** {@inheritDoc Cookie.lastAccessed} */\n  lastAccessed?: Date | 'Infinity' | null\n  /** {@inheritDoc Cookie.sameSite} */\n  sameSite?: string | undefined\n}\n\nconst cookieDefaults = {\n  // the order in which the RFC has them:\n  key: '',\n  value: '',\n  expires: 'Infinity',\n  maxAge: null,\n  domain: null,\n  path: null,\n  secure: false,\n  httpOnly: false,\n  extensions: null,\n  // set by the CookieJar:\n  hostOnly: null,\n  pathIsDefault: null,\n  creation: null,\n  lastAccessed: null,\n  sameSite: undefined,\n} as const satisfies Required<CreateCookieOptions>\n\n/**\n * An HTTP cookie (web cookie, browser cookie) is a small piece of data that a server sends to a user's web browser.\n * It is defined in {@link https://www.rfc-editor.org/rfc/rfc6265.html | RFC6265}.\n * @public\n */\nexport class Cookie {\n  /**\n   * The name or key of the cookie\n   */\n  key: string\n  /**\n   * The value of the cookie\n   */\n  value: string\n  /**\n   * The 'Expires' attribute of the cookie\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.1 | RFC6265 Section 5.2.1}).\n   */\n  expires: Date | 'Infinity' | null\n  /**\n   * The 'Max-Age' attribute of the cookie\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.2 | RFC6265 Section 5.2.2}).\n   */\n  maxAge: number | 'Infinity' | '-Infinity' | null\n  /**\n   * The 'Domain' attribute of the cookie represents the domain the cookie belongs to\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.3 | RFC6265 Section 5.2.3}).\n   */\n  domain: string | null\n  /**\n   * The 'Path' attribute of the cookie represents the path of the cookie\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.4 | RFC6265 Section 5.2.4}).\n   */\n  path: string | null\n  /**\n   * The 'Secure' flag of the cookie indicates if the scope of the cookie is\n   * limited to secure channels (e.g.; HTTPS) or not\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.5 | RFC6265 Section 5.2.5}).\n   */\n  secure: boolean\n  /**\n   * The 'HttpOnly' flag of the cookie indicates if the cookie is inaccessible to\n   * client scripts or not\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.6 | RFC6265 Section 5.2.6}).\n   */\n  httpOnly: boolean\n  /**\n   * Contains attributes which are not part of the defined spec but match the `extension-av` syntax\n   * defined in Section 4.1.1 of RFC6265\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-4.1.1 | RFC6265 Section 4.1.1}).\n   */\n  extensions: string[] | null\n  /**\n   * Set to the date and time when a Cookie is initially stored or a matching cookie is\n   * received that replaces an existing cookie\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.3 | RFC6265 Section 5.3}).\n   *\n   * Also used to maintain ordering among cookies. Among cookies that have equal-length path fields,\n   * cookies with earlier creation-times are listed before cookies with later creation-times\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.4 | RFC6265 Section 5.4}).\n   */\n  creation: Date | 'Infinity' | null\n  /**\n   * A global counter used to break ordering ties between two cookies that have equal-length path fields\n   * and the same creation-time.\n   */\n  creationIndex: number\n  /**\n   * A boolean flag indicating if a cookie is a host-only cookie (i.e.; when the request's host exactly\n   * matches the domain of the cookie) or not\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.3 | RFC6265 Section 5.3}).\n   */\n  hostOnly: boolean | null\n  /**\n   * A boolean flag indicating if a cookie had no 'Path' attribute and the default path\n   * was used\n   * (See {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.2.4 | RFC6265 Section 5.2.4}).\n   */\n  pathIsDefault: boolean | null\n  /**\n   * Set to the date and time when a cookie was initially stored ({@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.3 | RFC6265 Section 5.3}) and updated whenever\n   * the cookie is retrieved from the {@link CookieJar} ({@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.4 | RFC6265 Section 5.4}).\n   */\n  lastAccessed: Date | 'Infinity' | null\n  /**\n   * The 'SameSite' attribute of a cookie as defined in RFC6265bis\n   * (See {@link https://www.ietf.org/archive/id/draft-ietf-httpbis-rfc6265bis-13.html#section-5.2 | RFC6265bis (v13) Section 5.2 }).\n   */\n  sameSite: string | undefined\n\n  /**\n   * Create a new Cookie instance.\n   * @public\n   * @param options - The attributes to set on the cookie\n   */\n  constructor(options: CreateCookieOptions = {}) {\n    this.key = options.key ?? cookieDefaults.key\n    this.value = options.value ?? cookieDefaults.value\n    this.expires = options.expires ?? cookieDefaults.expires\n    this.maxAge = options.maxAge ?? cookieDefaults.maxAge\n    this.domain = options.domain ?? cookieDefaults.domain\n    this.path = options.path ?? cookieDefaults.path\n    this.secure = options.secure ?? cookieDefaults.secure\n    this.httpOnly = options.httpOnly ?? cookieDefaults.httpOnly\n    this.extensions = options.extensions ?? cookieDefaults.extensions\n    this.creation = options.creation ?? cookieDefaults.creation\n    this.hostOnly = options.hostOnly ?? cookieDefaults.hostOnly\n    this.pathIsDefault = options.pathIsDefault ?? cookieDefaults.pathIsDefault\n    this.lastAccessed = options.lastAccessed ?? cookieDefaults.lastAccessed\n    this.sameSite = options.sameSite ?? cookieDefaults.sameSite\n\n    this.creation = options.creation ?? new Date()\n\n    // used to break creation ties in cookieCompare():\n    Object.defineProperty(this, 'creationIndex', {\n      configurable: false,\n      enumerable: false, // important for assert.deepEqual checks\n      writable: true,\n      value: ++Cookie.cookiesCreated,\n    })\n    // Duplicate operation, but it makes TypeScript happy...\n    this.creationIndex = Cookie.cookiesCreated\n  }\n\n  [Symbol.for('nodejs.util.inspect.custom')](): string {\n    const now = Date.now()\n    const hostOnly = this.hostOnly != null ? this.hostOnly.toString() : '?'\n    const createAge =\n      this.creation && this.creation !== 'Infinity'\n        ? `${String(now - this.creation.getTime())}ms`\n        : '?'\n    const accessAge =\n      this.lastAccessed && this.lastAccessed !== 'Infinity'\n        ? `${String(now - this.lastAccessed.getTime())}ms`\n        : '?'\n    return `Cookie=\"${this.toString()}; hostOnly=${hostOnly}; aAge=${accessAge}; cAge=${createAge}\"`\n  }\n\n  /**\n   * For convenience in using `JSON.stringify(cookie)`. Returns a plain-old Object that can be JSON-serialized.\n   *\n   * @remarks\n   * - Any `Date` properties (such as {@link Cookie.expires}, {@link Cookie.creation}, and {@link Cookie.lastAccessed}) are exported in ISO format (`Date.toISOString()`).\n   *\n   *  - Custom Cookie properties are discarded. In tough-cookie 1.x, since there was no {@link Cookie.toJSON} method explicitly defined, all enumerable properties were captured.\n   *      If you want a property to be serialized, add the property name to {@link Cookie.serializableProperties}.\n   */\n  toJSON(): SerializedCookie {\n    const obj: SerializedCookie = {}\n\n    for (const prop of Cookie.serializableProperties) {\n      const val = this[prop]\n\n      if (val === cookieDefaults[prop]) {\n        continue // leave as prototype default\n      }\n\n      switch (prop) {\n        case 'key':\n        case 'value':\n        case 'sameSite':\n          if (typeof val === 'string') {\n            obj[prop] = val\n          }\n          break\n        case 'expires':\n        case 'creation':\n        case 'lastAccessed':\n          if (\n            typeof val === 'number' ||\n            typeof val === 'string' ||\n            val instanceof Date\n          ) {\n            obj[prop] =\n              val == 'Infinity' ? 'Infinity' : new Date(val).toISOString()\n          } else if (val === null) {\n            obj[prop] = null\n          }\n          break\n        case 'maxAge':\n          if (\n            typeof val === 'number' ||\n            val === 'Infinity' ||\n            val === '-Infinity'\n          ) {\n            obj[prop] = val\n          }\n          break\n        case 'domain':\n        case 'path':\n          if (typeof val === 'string' || val === null) {\n            obj[prop] = val\n          }\n          break\n        case 'secure':\n        case 'httpOnly':\n          if (typeof val === 'boolean') {\n            obj[prop] = val\n          }\n          break\n        case 'extensions':\n          if (Array.isArray(val)) {\n            obj[prop] = val\n          }\n          break\n        case 'hostOnly':\n        case 'pathIsDefault':\n          if (typeof val === 'boolean' || val === null) {\n            obj[prop] = val\n          }\n          break\n      }\n    }\n\n    return obj\n  }\n\n  /**\n   * Does a deep clone of this cookie, implemented exactly as `Cookie.fromJSON(cookie.toJSON())`.\n   * @public\n   */\n  clone(): Cookie | undefined {\n    return fromJSON(this.toJSON())\n  }\n\n  /**\n   * Validates cookie attributes for semantic correctness. Useful for \"lint\" checking any `Set-Cookie` headers you generate.\n   * For now, it returns a boolean, but eventually could return a reason string.\n   *\n   * @remarks\n   * Works for a few things, but is by no means comprehensive.\n   *\n   * @beta\n   */\n  validate(): boolean {\n    if (!this.value || !COOKIE_OCTETS.test(this.value)) {\n      return false\n    }\n    if (\n      this.expires != 'Infinity' &&\n      !(this.expires instanceof Date) &&\n      !parseDate(this.expires)\n    ) {\n      return false\n    }\n    if (\n      this.maxAge != null &&\n      this.maxAge !== 'Infinity' &&\n      (this.maxAge === '-Infinity' || this.maxAge <= 0)\n    ) {\n      return false // \"Max-Age=\" non-zero-digit *DIGIT\n    }\n    if (this.path != null && !PATH_VALUE.test(this.path)) {\n      return false\n    }\n\n    const cdomain = this.cdomain()\n    if (cdomain) {\n      if (cdomain.match(/\\.$/)) {\n        return false // S4.1.2.3 suggests that this is bad. domainMatch() tests confirm this\n      }\n      const suffix = getPublicSuffix(cdomain)\n      if (suffix == null) {\n        // it's a public suffix\n        return false\n      }\n    }\n    return true\n  }\n\n  /**\n   * Sets the 'Expires' attribute on a cookie.\n   *\n   * @remarks\n   * When given a `string` value it will be parsed with {@link parseDate}. If the value can't be parsed as a cookie date\n   * then the 'Expires' attribute will be set to `\"Infinity\"`.\n   *\n   * @param exp - the new value for the 'Expires' attribute of the cookie.\n   */\n  setExpires(exp: string | Date): void {\n    if (exp instanceof Date) {\n      this.expires = exp\n    } else {\n      this.expires = parseDate(exp) || 'Infinity'\n    }\n  }\n\n  /**\n   * Sets the 'Max-Age' attribute (in seconds) on a cookie.\n   *\n   * @remarks\n   * Coerces `-Infinity` to `\"-Infinity\"` and `Infinity` to `\"Infinity\"` so it can be serialized to JSON.\n   *\n   * @param age - the new value for the 'Max-Age' attribute (in seconds).\n   */\n  setMaxAge(age: number): void {\n    if (age === Infinity) {\n      this.maxAge = 'Infinity'\n    } else if (age === -Infinity) {\n      this.maxAge = '-Infinity'\n    } else {\n      this.maxAge = age\n    }\n  }\n\n  /**\n   * Encodes to a `Cookie` header value (specifically, the {@link Cookie.key} and {@link Cookie.value} properties joined with \"=\").\n   * @public\n   */\n  cookieString(): string {\n    const val = this.value || ''\n    if (this.key) {\n      return `${this.key}=${val}`\n    }\n    return val\n  }\n\n  /**\n   * Encodes to a `Set-Cookie header` value.\n   * @public\n   */\n  toString(): string {\n    let str = this.cookieString()\n\n    if (this.expires != 'Infinity') {\n      if (this.expires instanceof Date) {\n        str += `; Expires=${formatDate(this.expires)}`\n      }\n    }\n\n    if (this.maxAge != null && this.maxAge != Infinity) {\n      str += `; Max-Age=${String(this.maxAge)}`\n    }\n\n    if (this.domain && !this.hostOnly) {\n      str += `; Domain=${this.domain}`\n    }\n    if (this.path) {\n      str += `; Path=${this.path}`\n    }\n\n    if (this.secure) {\n      str += '; Secure'\n    }\n    if (this.httpOnly) {\n      str += '; HttpOnly'\n    }\n    if (this.sameSite && this.sameSite !== 'none') {\n      if (\n        this.sameSite.toLowerCase() ===\n        Cookie.sameSiteCanonical.lax.toLowerCase()\n      ) {\n        str += `; SameSite=${Cookie.sameSiteCanonical.lax}`\n      } else if (\n        this.sameSite.toLowerCase() ===\n        Cookie.sameSiteCanonical.strict.toLowerCase()\n      ) {\n        str += `; SameSite=${Cookie.sameSiteCanonical.strict}`\n      } else {\n        str += `; SameSite=${this.sameSite}`\n      }\n    }\n    if (this.extensions) {\n      this.extensions.forEach((ext) => {\n        str += `; ${ext}`\n      })\n    }\n\n    return str\n  }\n\n  /**\n   * Computes the TTL relative to now (milliseconds).\n   *\n   * @remarks\n   * - `Infinity` is returned for cookies without an explicit expiry\n   *\n   * - `0` is returned if the cookie is expired.\n   *\n   * - Otherwise a time-to-live in milliseconds is returned.\n   *\n   * @param now - passing an explicit value is mostly used for testing purposes since this defaults to the `Date.now()`\n   * @public\n   */\n  TTL(now: number = Date.now()): number {\n    // TTL() partially replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie()\n    // elsewhere)\n    // S5.3 says to give the \"latest representable date\" for which we use Infinity\n    // For \"expired\" we use 0\n    // -----\n    // RFC6265 S4.1.2.2 If a cookie has both the Max-Age and the Expires\n    // attribute, the Max-Age attribute has precedence and controls the\n    // expiration date of the cookie.\n    // (Concurs with S5.3 step 3)\n    if (this.maxAge != null && typeof this.maxAge === 'number') {\n      return this.maxAge <= 0 ? 0 : this.maxAge * 1000\n    }\n\n    const expires = this.expires\n    if (expires === 'Infinity') {\n      return Infinity\n    }\n\n    return (expires?.getTime() ?? now) - (now || Date.now())\n  }\n\n  /**\n   * Computes the absolute unix-epoch milliseconds that this cookie expires.\n   *\n   * The \"Max-Age\" attribute takes precedence over \"Expires\" (as per the RFC). The {@link Cookie.lastAccessed} attribute\n   * (or the `now` parameter if given) is used to offset the {@link Cookie.maxAge} attribute.\n   *\n   * If Expires ({@link Cookie.expires}) is set, that's returned.\n   *\n   * @param now - can be used to provide a time offset (instead of {@link Cookie.lastAccessed}) to use when calculating the \"Max-Age\" value\n   */\n  expiryTime(now?: Date): number | undefined {\n    // expiryTime() replaces the \"expiry-time\" parts of S5.3 step 3 (setCookie() elsewhere)\n    if (this.maxAge != null) {\n      const relativeTo = now || this.lastAccessed || new Date()\n      const maxAge = typeof this.maxAge === 'number' ? this.maxAge : -Infinity\n      const age = maxAge <= 0 ? -Infinity : maxAge * 1000\n      if (relativeTo === 'Infinity') {\n        return Infinity\n      }\n      return relativeTo.getTime() + age\n    }\n\n    if (this.expires == 'Infinity') {\n      return Infinity\n    }\n\n    return this.expires ? this.expires.getTime() : undefined\n  }\n\n  /**\n   * Similar to {@link Cookie.expiryTime}, computes the absolute unix-epoch milliseconds that this cookie expires and returns it as a Date.\n   *\n   * The \"Max-Age\" attribute takes precedence over \"Expires\" (as per the RFC). The {@link Cookie.lastAccessed} attribute\n   * (or the `now` parameter if given) is used to offset the {@link Cookie.maxAge} attribute.\n   *\n   * If Expires ({@link Cookie.expires}) is set, that's returned.\n   *\n   * @param now - can be used to provide a time offset (instead of {@link Cookie.lastAccessed}) to use when calculating the \"Max-Age\" value\n   */\n  expiryDate(now?: Date): Date | undefined {\n    const millisec = this.expiryTime(now)\n    if (millisec == Infinity) {\n      // The 31-bit value of 2147483647000 was chosen to be the MAX_TIME representable\n      // in tough-cookie though MDN states that the actual maximum value for a Date is 8.64e15.\n      // I'm guessing this is due to the Y2038 problem that would affect systems that store\n      // unix time as 32-bit integers.\n      // See:\n      // - https://github.com/salesforce/tough-cookie/commit/0616f70bf725e00c63d442544ad230c4f8b23357\n      // - https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Date#the_epoch_timestamps_and_invalid_date\n      // - https://en.wikipedia.org/wiki/Year_2038_problem\n      return new Date(2147483647000)\n    } else if (millisec == -Infinity) {\n      return new Date(0)\n    } else {\n      return millisec == undefined ? undefined : new Date(millisec)\n    }\n  }\n\n  /**\n   * Indicates if the cookie has been persisted to a store or not.\n   * @public\n   */\n  isPersistent(): boolean {\n    // This replaces the \"persistent-flag\" parts of S5.3 step 3\n    return this.maxAge != null || this.expires != 'Infinity'\n  }\n\n  /**\n   * Calls {@link canonicalDomain} with the {@link Cookie.domain} property.\n   * @public\n   */\n  canonicalizedDomain(): string | undefined {\n    // Mostly S5.1.2 and S5.2.3:\n    return canonicalDomain(this.domain)\n  }\n\n  /**\n   * Alias for {@link Cookie.canonicalizedDomain}\n   * @public\n   */\n  cdomain(): string | undefined {\n    return canonicalDomain(this.domain)\n  }\n\n  /**\n   * Parses a string into a Cookie object.\n   *\n   * @remarks\n   * Note: when parsing a `Cookie` header it must be split by ';' before each Cookie string can be parsed.\n   *\n   * @example\n   * ```\n   * // parse a `Set-Cookie` header\n   * const setCookieHeader = 'a=bcd; Expires=Tue, 18 Oct 2011 07:05:03 GMT'\n   * const cookie = Cookie.parse(setCookieHeader)\n   * cookie.key === 'a'\n   * cookie.value === 'bcd'\n   * cookie.expires === new Date(Date.parse('Tue, 18 Oct 2011 07:05:03 GMT'))\n   * ```\n   *\n   * @example\n   * ```\n   * // parse a `Cookie` header\n   * const cookieHeader = 'name=value; name2=value2; name3=value3'\n   * const cookies = cookieHeader.split(';').map(Cookie.parse)\n   * cookies[0].name === 'name'\n   * cookies[0].value === 'value'\n   * cookies[1].name === 'name2'\n   * cookies[1].value === 'value2'\n   * cookies[2].name === 'name3'\n   * cookies[2].value === 'value3'\n   * ```\n   *\n   * @param str - The `Set-Cookie` header or a Cookie string to parse.\n   * @param options - Configures `strict` or `loose` mode for cookie parsing\n   */\n  static parse(str: string, options?: ParseCookieOptions): Cookie | undefined {\n    return parse(str, options)\n  }\n\n  /**\n   * Does the reverse of {@link Cookie.toJSON}.\n   *\n   * @remarks\n   * Any Date properties (such as .expires, .creation, and .lastAccessed) are parsed via Date.parse, not tough-cookie's parseDate, since ISO timestamps are being handled at this layer.\n   *\n   * @example\n   * ```\n   * const json = JSON.stringify({\n   *   key: 'alpha',\n   *   value: 'beta',\n   *   domain: 'example.com',\n   *   path: '/foo',\n   *   expires: '2038-01-19T03:14:07.000Z',\n   * })\n   * const cookie = Cookie.fromJSON(json)\n   * cookie.key === 'alpha'\n   * cookie.value === 'beta'\n   * cookie.domain === 'example.com'\n   * cookie.path === '/foo'\n   * cookie.expires === new Date(Date.parse('2038-01-19T03:14:07.000Z'))\n   * ```\n   *\n   * @param str - An unparsed JSON string or a value that has already been parsed as JSON\n   */\n  static fromJSON(str: unknown): Cookie | undefined {\n    return fromJSON(str)\n  }\n\n  private static cookiesCreated = 0\n\n  /**\n   * @internal\n   */\n  static sameSiteLevel = {\n    strict: 3,\n    lax: 2,\n    none: 1,\n  } as const\n\n  /**\n   * @internal\n   */\n  static sameSiteCanonical = {\n    strict: 'Strict',\n    lax: 'Lax',\n  } as const\n\n  /**\n   * Cookie properties that will be serialized when using {@link Cookie.fromJSON} and {@link Cookie.toJSON}.\n   * @public\n   */\n  static serializableProperties = [\n    'key',\n    'value',\n    'expires',\n    'maxAge',\n    'domain',\n    'path',\n    'secure',\n    'httpOnly',\n    'extensions',\n    'hostOnly',\n    'pathIsDefault',\n    'creation',\n    'lastAccessed',\n    'sameSite',\n  ] as const\n}\n", "import type { <PERSON><PERSON> } from './cookie.js'\n\n/**\n * The maximum timestamp a cookie, in milliseconds. The value is (2^31 - 1) seconds since the Unix\n * epoch, corresponding to 2038-01-19.\n */\nconst MAX_TIME = 2147483647000\n\n/**\n * A comparison function that can be used with {@link https://developer.mozilla.org/en-US/docs/Web/JavaScript/Reference/Global_Objects/Array/sort | Array.sort()},\n * which orders a list of cookies into the recommended order given in Step 2 of {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.4 | RFC6265 - Section 5.4}.\n *\n * The sort algorithm is, in order of precedence:\n *\n * - Longest {@link Cookie.path}\n *\n * - Oldest {@link Cookie.creation} (which has a 1-ms precision, same as Date)\n *\n * - Lowest {@link Cookie.creationIndex} (to get beyond the 1-ms precision)\n *\n * @remarks\n * ### RFC6265 - Section 5.4 - Step 2\n *\n * The user agent SHOULD sort the cookie-list in the following order:\n *\n * - Cookies with longer paths are listed before cookies with shorter paths.\n *\n * - Among cookies that have equal-length path fields, cookies with\n *    earlier creation-times are listed before cookies with later\n *    creation-times.\n *\n * NOTE: Not all user agents sort the cookie-list in this order, but\n * this order reflects common practice when this document was\n * written, and, historically, there have been servers that\n * (erroneously) depended on this order.\n *\n * ### Custom Store Implementors\n *\n * Since the JavaScript Date is limited to a 1-ms precision, cookies within the same millisecond are entirely possible.\n * This is especially true when using the `now` option to `CookieJar.setCookie(...)`. The {@link Cookie.creationIndex}\n * property is a per-process global counter, assigned during construction with `new Cookie()`, which preserves the spirit\n * of the RFC sorting: older cookies go first. This works great for {@link MemoryCookieStore} since `Set-Cookie` headers\n * are parsed in order, but is not so great for distributed systems.\n *\n * Sophisticated Stores may wish to set this to some other\n * logical clock so that if cookies `A` and `B` are created in the same millisecond, but cookie `A` is created before\n * cookie `B`, then `A.creationIndex < B.creationIndex`.\n *\n * @example\n * ```\n * const cookies = [\n *   new Cookie({ key: 'a', value: '' }),\n *   new Cookie({ key: 'b', value: '' }),\n *   new Cookie({ key: 'c', value: '', path: '/path' }),\n *   new Cookie({ key: 'd', value: '', path: '/path' }),\n * ]\n * cookies.sort(cookieCompare)\n * // cookie sort order would be ['c', 'd', 'a', 'b']\n * ```\n *\n * @param a - the first Cookie for comparison\n * @param b - the second Cookie for comparison\n * @public\n */\nexport function cookieCompare(a: Cookie, b: Cookie): number {\n  let cmp: number\n\n  // descending for length: b CMP a\n  const aPathLen = a.path ? a.path.length : 0\n  const bPathLen = b.path ? b.path.length : 0\n  cmp = bPathLen - aPathLen\n  if (cmp !== 0) {\n    return cmp\n  }\n\n  // ascending for time: a CMP b\n  const aTime =\n    a.creation && a.creation instanceof Date ? a.creation.getTime() : MAX_TIME\n  const bTime =\n    b.creation && b.creation instanceof Date ? b.creation.getTime() : MAX_TIME\n  cmp = aTime - bTime\n  if (cmp !== 0) {\n    return cmp\n  }\n\n  // break ties for the same millisecond (precision of JavaScript's clock)\n  cmp = (a.creationIndex || 0) - (b.creationIndex || 0)\n\n  return cmp\n}\n", "import type { Nullable } from '../utils.js'\n\n/**\n * Given a current request/response path, gives the path appropriate for storing\n * in a cookie. This is basically the \"directory\" of a \"file\" in the path, but\n * is specified by {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.1.4 | RFC6265 - Section 5.1.4}.\n *\n * @remarks\n * ### RFC6265 - Section 5.1.4\n *\n * The user agent MUST use an algorithm equivalent to the following algorithm to compute the default-path of a cookie:\n *\n * 1. Let uri-path be the path portion of the request-uri if such a\n *     portion exists (and empty otherwise).  For example, if the\n *     request-uri contains just a path (and optional query string),\n *     then the uri-path is that path (without the %x3F (\"?\") character\n *     or query string), and if the request-uri contains a full\n *     absoluteURI, the uri-path is the path component of that URI.\n *\n * 2. If the uri-path is empty or if the first character of the uri-\n *     path is not a %x2F (\"/\") character, output %x2F (\"/\") and skip\n *     the remaining steps.\n *\n * 3. If the uri-path contains no more than one %x2F (\"/\") character,\n *     output %x2F (\"/\") and skip the remaining step.\n *\n * 4. Output the characters of the uri-path from the first character up\n *     to, but not including, the right-most %x2F (\"/\").\n *\n * @example\n * ```\n * defaultPath('') === '/'\n * defaultPath('/some-path') === '/'\n * defaultPath('/some-parent-path/some-path') === '/some-parent-path'\n * defaultPath('relative-path') === '/'\n * ```\n *\n * @param path - the path portion of the request-uri (excluding the hostname, query, fragment, and so on)\n * @public\n */\nexport function defaultPath(path?: Nullable<string>): string {\n  // \"2. If the uri-path is empty or if the first character of the uri-path is not\n  // a %x2F (\"/\") character, output %x2F (\"/\") and skip the remaining steps.\n  if (!path || path.slice(0, 1) !== '/') {\n    return '/'\n  }\n\n  // \"3. If the uri-path contains no more than one %x2F (\"/\") character, output\n  // %x2F (\"/\") and skip the remaining step.\"\n  if (path === '/') {\n    return path\n  }\n\n  const rightSlash = path.lastIndexOf('/')\n  if (rightSlash === 0) {\n    return '/'\n  }\n\n  // \"4. Output the characters of the uri-path from the first character up to,\n  // but not including, the right-most %x2F (\"/\").\"\n  return path.slice(0, rightSlash)\n}\n", "import type { Nullable } from '../utils.js'\nimport { canonicalDomain } from './canonicalDomain.js'\n\n// Dumped from ip-regex@4.0.0, with the following changes:\n// * all capturing groups converted to non-capturing -- \"(?:)\"\n// * support for IPv6 Scoped Literal (\"%eth1\") removed\n// * lowercase hexadecimal only\nconst IP_REGEX_LOWERCASE =\n  /(?:^(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}$)|(?:^(?:(?:[a-f\\d]{1,4}:){7}(?:[a-f\\d]{1,4}|:)|(?:[a-f\\d]{1,4}:){6}(?:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|:[a-f\\d]{1,4}|:)|(?:[a-f\\d]{1,4}:){5}(?::(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,2}|:)|(?:[a-f\\d]{1,4}:){4}(?:(?::[a-f\\d]{1,4}){0,1}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,3}|:)|(?:[a-f\\d]{1,4}:){3}(?:(?::[a-f\\d]{1,4}){0,2}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,4}|:)|(?:[a-f\\d]{1,4}:){2}(?:(?::[a-f\\d]{1,4}){0,3}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,5}|:)|(?:[a-f\\d]{1,4}:){1}(?:(?::[a-f\\d]{1,4}){0,4}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,6}|:)|(?::(?:(?::[a-f\\d]{1,4}){0,5}:(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)(?:\\.(?:25[0-5]|2[0-4]\\d|1\\d\\d|[1-9]\\d|\\d)){3}|(?::[a-f\\d]{1,4}){1,7}|:)))$)/\n\n/**\n * Answers \"does this real domain match the domain in a cookie?\". The `domain` is the \"current\" domain name and the\n * `cookieDomain` is the \"cookie\" domain name. Matches according to {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.1.3 | RFC6265 - Section 5.1.3},\n * but it helps to think of it as a \"suffix match\".\n *\n * @remarks\n * ### 5.1.3.  Domain Matching\n *\n * A string domain-matches a given domain string if at least one of the\n * following conditions hold:\n *\n * - The domain string and the string are identical.  (Note that both\n *     the domain string and the string will have been canonicalized to\n *     lower case at this point.)\n *\n * - All of the following conditions hold:\n *\n *     - The domain string is a suffix of the string.\n *\n *     - The last character of the string that is not included in the\n *         domain string is a %x2E (\".\") character.\n *\n *     - The string is a host name (i.e., not an IP address).\n *\n * @example\n * ```\n * domainMatch('example.com', 'example.com') === true\n * domainMatch('eXaMpLe.cOm', 'ExAmPlE.CoM') === true\n * domainMatch('no.ca', 'yes.ca') === false\n * ```\n *\n * @param domain - The domain string to test\n * @param cookieDomain - The cookie domain string to match against\n * @param canonicalize - The canonicalize parameter toggles whether the domain parameters get normalized with canonicalDomain or not\n * @public\n */\nexport function domainMatch(\n  domain?: Nullable<string>,\n  cookieDomain?: Nullable<string>,\n  canonicalize?: boolean,\n): boolean | undefined {\n  if (domain == null || cookieDomain == null) {\n    return undefined\n  }\n\n  let _str: Nullable<string>\n  let _domStr: Nullable<string>\n\n  if (canonicalize !== false) {\n    _str = canonicalDomain(domain)\n    _domStr = canonicalDomain(cookieDomain)\n  } else {\n    _str = domain\n    _domStr = cookieDomain\n  }\n\n  if (_str == null || _domStr == null) {\n    return undefined\n  }\n\n  /*\n   * S5.1.3:\n   * \"A string domain-matches a given domain string if at least one of the\n   * following conditions hold:\"\n   *\n   * \" o The domain string and the string are identical. (Note that both the\n   * domain string and the string will have been canonicalized to lower case at\n   * this point)\"\n   */\n  if (_str == _domStr) {\n    return true\n  }\n\n  /* \" o All of the following [three] conditions hold:\" */\n\n  /* \"* The domain string is a suffix of the string\" */\n  const idx = _str.lastIndexOf(_domStr)\n  if (idx <= 0) {\n    return false // it's a non-match (-1) or prefix (0)\n  }\n\n  // next, check it's a proper suffix\n  // e.g., \"a.b.c\".indexOf(\"b.c\") === 2\n  // 5 === 3+2\n  if (_str.length !== _domStr.length + idx) {\n    return false // it's not a suffix\n  }\n\n  /* \"  * The last character of the string that is not included in the\n   * domain string is a %x2E (\".\") character.\" */\n  if (_str.substring(idx - 1, idx) !== '.') {\n    return false // doesn't align on \".\"\n  }\n\n  /* \"  * The string is a host name (i.e., not an IP address).\" */\n  return !IP_REGEX_LOWERCASE.test(_str)\n}\n", "import { IP_V4_REGEX_OBJECT, IP_V6_REGEX_OBJECT } from './constants.js'\n\nfunction isLoopbackV4(address: string): boolean {\n  // *********/8: first octet = 127\n  const octets = address.split('.')\n  return (\n    octets.length === 4 &&\n    octets[0] !== undefined &&\n    parseInt(octets[0], 10) === 127\n  )\n}\n\nfunction isLoopbackV6(address: string): boolean {\n  // new URL(...) follows the WHATWG URL Standard\n  // which compresses IPv6 addresses, therefore the IPv6\n  // loopback address will always be compressed to '[::1]':\n  // https://url.spec.whatwg.org/#concept-ipv6-serializer\n  return address === '::1'\n}\n\nfunction isNormalizedLocalhostTLD(lowerHost: string): boolean {\n  return lowerHost.endsWith('.localhost')\n}\n\nfunction isLocalHostname(host: string): boolean {\n  const lowerHost = host.toLowerCase()\n  return lowerHost === 'localhost' || isNormalizedLocalhostTLD(lowerHost)\n}\n\n// Adapted from https://github.com/chromium/chromium/blob/main/url/gurl.cc#L440-L448\nfunction hostNoBrackets(host: string): string {\n  if (host.length >= 2 && host.startsWith('[') && host.endsWith(']')) {\n    return host.substring(1, host.length - 1)\n  }\n  return host\n}\n\n/**\n * Determines if a URL string represents a potentially trustworthy origin.\n *\n * A URL is considered potentially trustworthy if it:\n * - Uses HTTPS or WSS schemes\n * - If `allowSecureOnLocal` is `true`:\n *   - Points to a loopback address (IPv4 *********/8 or IPv6 ::1)\n *   - Uses localhost or *.localhost hostnames\n *\n * @param inputUrl - The URL string or URL object to check.\n * @param allowSecureOnLocal - Whether to treat localhost and loopback addresses as trustworthy.\n * @returns `true` if the URL is potentially trustworthy, otherwise `false`.\n * @see {@link https://w3c.github.io/webappsec-secure-contexts/#potentially-trustworthy-origin | Potentially Trustworthy Origin algorithm}\n */\nexport function isPotentiallyTrustworthy(\n  inputUrl: string | URL,\n  allowSecureOnLocal: boolean = true,\n): boolean {\n  let url: URL\n\n  // try ... catch doubles as an opaque origin check\n  if (typeof inputUrl === 'string') {\n    try {\n      url = new URL(inputUrl)\n    } catch {\n      return false\n    }\n  } else {\n    url = inputUrl\n  }\n\n  const scheme = url.protocol.replace(':', '').toLowerCase()\n  const hostname = hostNoBrackets(url.hostname).replace(/\\.+$/, '')\n\n  if (\n    scheme === 'https' ||\n    scheme === 'wss' // https://w3c.github.io/webappsec-secure-contexts/#potentially-trustworthy-origin\n  ) {\n    return true\n  }\n\n  if (!allowSecureOnLocal) {\n    return false\n  }\n\n  // If it's already an IP literal, check if it's a loopback address\n  if (IP_V4_REGEX_OBJECT.test(hostname)) {\n    return isLoopbackV4(hostname)\n  }\n\n  if (IP_V6_REGEX_OBJECT.test(hostname)) {\n    return isLoopbackV6(hostname)\n  }\n\n  // RFC 6761 states that localhost names will always resolve\n  // to the respective IP loopback address:\n  // https://datatracker.ietf.org/doc/html/rfc6761#section-6.3\n  return isLocalHostname(hostname)\n}\n", "import { getPublicSuffix } from '../getPublicSuffix.js'\nimport * as validators from '../validators.js'\nimport { ParameterError } from '../validators.js'\nimport { Store } from '../store.js'\nimport { MemoryCookieStore } from '../memstore.js'\nimport { pathMatch } from '../pathMatch.js'\nimport { Cookie } from './cookie.js'\nimport {\n  Callback,\n  createPromiseCallback,\n  ErrorCallback,\n  inOperator,\n  Nullable,\n  safeToString,\n} from '../utils.js'\nimport { canonicalDomain } from './canonicalDomain.js'\nimport {\n  IP_V6_REGEX_OBJECT,\n  PrefixSecurityEnum,\n  SerializedCookieJar,\n} from './constants.js'\nimport { defaultPath } from './defaultPath.js'\nimport { domainMatch } from './domainMatch.js'\nimport { cookieCompare } from './cookieCompare.js'\nimport { version } from '../version.js'\nimport { isPotentiallyTrustworthy } from './secureContext.js'\n\nconst defaultSetCookieOptions: SetCookieOptions = {\n  loose: false,\n  sameSiteContext: undefined,\n  ignoreError: false,\n  http: true,\n}\n\n/**\n * Configuration options used when calling `CookieJar.setCookie(...)`\n * @public\n */\nexport interface SetCookieOptions {\n  /**\n   * Controls if a cookie string should be parsed using `loose` mode or not.\n   * See {@link Cookie.parse} and {@link ParseCookieOptions} for more details.\n   *\n   * Defaults to `false` if not provided.\n   */\n  loose?: boolean | undefined\n  /**\n   * Set this to 'none', 'lax', or 'strict' to enforce SameSite cookies upon storage.\n   *\n   * - `'strict'` - If the request is on the same \"site for cookies\" (see the RFC draft\n   *     for more information), pass this option to add a layer of defense against CSRF.\n   *\n   * - `'lax'` - If the request is from another site, but is directly because of navigation\n   *     by the user, such as, `<link type=prefetch>` or `<a href=\"...\">`, then use `lax`.\n   *\n   * - `'none'` - This indicates a cross-origin request.\n   *\n   * - `undefined` - SameSite is not enforced! This can be a valid use-case for when\n   *     CSRF isn't in the threat model of the system being built.\n   *\n   * Defaults to `undefined` if not provided.\n   *\n   * @remarks\n   * - It is highly recommended that you read {@link https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-02##section-8.8 | RFC6265bis - Section 8.8}\n   *    which discusses security considerations and defence on SameSite cookies in depth.\n   */\n  sameSiteContext?: 'strict' | 'lax' | 'none' | undefined\n  /**\n   * Silently ignore things like parse errors and invalid domains. Store errors aren't ignored by this option.\n   *\n   * Defaults to `false` if not provided.\n   */\n  ignoreError?: boolean | undefined\n  /**\n   * Indicates if this is an HTTP or non-HTTP API. Affects HttpOnly cookies.\n   *\n   * Defaults to `true` if not provided.\n   */\n  http?: boolean | undefined\n  /**\n   * Forces the cookie creation and access time of cookies to this value when stored.\n   *\n   * Defaults to `Date.now()` if not provided.\n   */\n  now?: Date | undefined\n}\n\nconst defaultGetCookieOptions: GetCookiesOptions = {\n  http: true,\n  expire: true,\n  allPaths: false,\n  sameSiteContext: undefined,\n  sort: undefined,\n}\n\n/**\n * Configuration options used when calling `CookieJar.getCookies(...)`.\n * @public\n */\nexport interface GetCookiesOptions {\n  /**\n   * Indicates if this is an HTTP or non-HTTP API. Affects HttpOnly cookies.\n   *\n   * Defaults to `true` if not provided.\n   */\n  http?: boolean | undefined\n  /**\n   * Perform `expiry-time` checking of cookies and asynchronously remove expired\n   * cookies from the store.\n   *\n   * @remarks\n   * - Using `false` returns expired cookies and does not remove them from the\n   *     store, which is potentially useful for replaying `Set-Cookie` headers.\n   *\n   * Defaults to `true` if not provided.\n   */\n  expire?: boolean | undefined\n  /**\n   * If `true`, do not scope cookies by path. If `false`, then RFC-compliant path scoping will be used.\n   *\n   * @remarks\n   * - May not be supported by the underlying store (the default {@link MemoryCookieStore} supports it).\n   *\n   * Defaults to `false` if not provided.\n   */\n  allPaths?: boolean | undefined\n  /**\n   * Set this to 'none', 'lax', or 'strict' to enforce SameSite cookies upon retrieval.\n   *\n   * - `'strict'` - If the request is on the same \"site for cookies\" (see the RFC draft\n   *     for more information), pass this option to add a layer of defense against CSRF.\n   *\n   * - `'lax'` - If the request is from another site, but is directly because of navigation\n   *     by the user, such as, `<link type=prefetch>` or `<a href=\"...\">`, then use `lax`.\n   *\n   * - `'none'` - This indicates a cross-origin request.\n   *\n   * - `undefined` - SameSite is not enforced! This can be a valid use-case for when\n   *     CSRF isn't in the threat model of the system being built.\n   *\n   * Defaults to `undefined` if not provided.\n   *\n   * @remarks\n   * - It is highly recommended that you read {@link https://tools.ietf.org/html/draft-ietf-httpbis-rfc6265bis-02##section-8.8 | RFC6265bis - Section 8.8}\n   *    which discusses security considerations and defence on SameSite cookies in depth.\n   */\n  sameSiteContext?: 'none' | 'lax' | 'strict' | undefined\n  /**\n   * Flag to indicate if the returned cookies should be sorted or not.\n   *\n   * Defaults to `undefined` if not provided.\n   */\n  sort?: boolean | undefined\n}\n\n/**\n * Configuration settings to be used with a {@link CookieJar}.\n * @public\n */\nexport interface CreateCookieJarOptions {\n  /**\n   * Reject cookies that match those defined in the {@link https://publicsuffix.org/ | Public Suffix List} (e.g.; domains like \"com\" and \"co.uk\").\n   *\n   * Defaults to `true` if not specified.\n   */\n  rejectPublicSuffixes?: boolean | undefined\n  /**\n   * Accept malformed cookies like `bar` and `=bar`, which have an implied empty name but are not RFC-compliant.\n   *\n   * Defaults to `false` if not specified.\n   */\n  looseMode?: boolean | undefined\n  /**\n   * Controls how cookie prefixes are handled. See {@link PrefixSecurityEnum}.\n   *\n   * Defaults to `silent` if not specified.\n   */\n  prefixSecurity?: 'strict' | 'silent' | 'unsafe-disabled' | undefined\n  /**\n   * Accepts {@link https://datatracker.ietf.org/doc/html/rfc6761 | special-use domains } such as `local`.\n   * This is not in the standard, but is used sometimes on the web and is accepted by most browsers. It is\n   * also useful for testing purposes.\n   *\n   * Defaults to `true` if not specified.\n   */\n  allowSpecialUseDomain?: boolean | undefined\n  /**\n   * Flag to indicate if localhost and loopback addresses with an unsecure scheme should store and retrieve `Secure` cookies.\n   *\n   * If `true`, localhost, loopback addresses or similarly local addresses are treated as secure contexts\n   * and thus will store and retrieve `Secure` cookies even with an unsecure scheme.\n   *\n   * If `false`, only secure schemes (`https` and `wss`) will store and retrieve `Secure` cookies.\n   *\n   * @remarks\n   * When set to `true`, the {@link https://w3c.github.io/webappsec-secure-contexts/#potentially-trustworthy-origin | potentially trustworthy}\n   *  algorithm is followed to determine if a URL is considered a secure context.\n   */\n  allowSecureOnLocal?: boolean | undefined\n}\n\nconst SAME_SITE_CONTEXT_VAL_ERR =\n  'Invalid sameSiteContext option for getCookies(); expected one of \"strict\", \"lax\", or \"none\"'\n\ntype UrlContext = {\n  hostname: string\n  pathname: string\n  protocol: string\n}\n\nfunction getCookieContext(url: unknown): UrlContext {\n  if (\n    url &&\n    typeof url === 'object' &&\n    'hostname' in url &&\n    typeof url.hostname === 'string' &&\n    'pathname' in url &&\n    typeof url.pathname === 'string' &&\n    'protocol' in url &&\n    typeof url.protocol === 'string'\n  ) {\n    return {\n      hostname: url.hostname,\n      pathname: url.pathname,\n      protocol: url.protocol,\n    }\n  } else if (typeof url === 'string') {\n    try {\n      return new URL(decodeURI(url))\n    } catch {\n      return new URL(url)\n    }\n  } else {\n    throw new ParameterError('`url` argument is not a string or URL.')\n  }\n}\n\ntype SameSiteLevel = keyof (typeof Cookie)['sameSiteLevel']\nfunction checkSameSiteContext(value: string): SameSiteLevel | undefined {\n  const context = String(value).toLowerCase()\n  if (context === 'none' || context === 'lax' || context === 'strict') {\n    return context\n  } else {\n    return undefined\n  }\n}\n\n/**\n *  If the cookie-name begins with a case-sensitive match for the\n *  string \"__Secure-\", abort these steps and ignore the cookie\n *  entirely unless the cookie's secure-only-flag is true.\n * @param cookie\n * @returns boolean\n */\nfunction isSecurePrefixConditionMet(cookie: Cookie): boolean {\n  const startsWithSecurePrefix =\n    typeof cookie.key === 'string' && cookie.key.startsWith('__Secure-')\n  return !startsWithSecurePrefix || cookie.secure\n}\n\n/**\n *  If the cookie-name begins with a case-sensitive match for the\n *  string \"__Host-\", abort these steps and ignore the cookie\n *  entirely unless the cookie meets all the following criteria:\n *    1.  The cookie's secure-only-flag is true.\n *    2.  The cookie's host-only-flag is true.\n *    3.  The cookie-attribute-list contains an attribute with an\n *        attribute-name of \"Path\", and the cookie's path is \"/\".\n * @param cookie\n * @returns boolean\n */\nfunction isHostPrefixConditionMet(cookie: Cookie): boolean {\n  const startsWithHostPrefix =\n    typeof cookie.key === 'string' && cookie.key.startsWith('__Host-')\n  return (\n    !startsWithHostPrefix ||\n    Boolean(\n      cookie.secure &&\n        cookie.hostOnly &&\n        cookie.path != null &&\n        cookie.path === '/',\n    )\n  )\n}\n\ntype PrefixSecurityValue =\n  (typeof PrefixSecurityEnum)[keyof typeof PrefixSecurityEnum]\nfunction getNormalizedPrefixSecurity(\n  prefixSecurity: string,\n): PrefixSecurityValue {\n  const normalizedPrefixSecurity = prefixSecurity.toLowerCase()\n  /* The three supported options */\n  switch (normalizedPrefixSecurity) {\n    case PrefixSecurityEnum.STRICT:\n    case PrefixSecurityEnum.SILENT:\n    case PrefixSecurityEnum.DISABLED:\n      return normalizedPrefixSecurity\n    default:\n      return PrefixSecurityEnum.SILENT\n  }\n}\n\n/**\n * A CookieJar is for storage and retrieval of {@link Cookie} objects as defined in\n * {@link https://www.rfc-editor.org/rfc/rfc6265.html#section-5.3 | RFC6265 - Section 5.3}.\n *\n * It also supports a pluggable persistence layer via {@link Store}.\n * @public\n */\nexport class CookieJar {\n  private readonly rejectPublicSuffixes: boolean\n  private readonly enableLooseMode: boolean\n  private readonly allowSpecialUseDomain: boolean\n  private readonly allowSecureOnLocal: boolean\n\n  /**\n   * The configured {@link Store} for the {@link CookieJar}.\n   */\n  readonly store: Store\n\n  /**\n   * The configured {@link PrefixSecurityEnum} value for the {@link CookieJar}.\n   */\n  readonly prefixSecurity: string\n\n  /**\n   * Creates a new `CookieJar` instance.\n   *\n   * @remarks\n   * - If a custom store is not passed to the constructor, an in-memory store ({@link MemoryCookieStore} will be created and used.\n   * - If a boolean value is passed as the `options` parameter, this is equivalent to passing `{ rejectPublicSuffixes: <value> }`\n   *\n   * @param store - a custom {@link Store} implementation (defaults to {@link MemoryCookieStore})\n   * @param options - configures how cookies are processed by the cookie jar\n   */\n  constructor(\n    store?: Nullable<Store>,\n    options?: CreateCookieJarOptions | boolean,\n  ) {\n    if (typeof options === 'boolean') {\n      options = { rejectPublicSuffixes: options }\n    }\n    this.rejectPublicSuffixes = options?.rejectPublicSuffixes ?? true\n    this.enableLooseMode = options?.looseMode ?? false\n    this.allowSpecialUseDomain = options?.allowSpecialUseDomain ?? true\n    this.allowSecureOnLocal = options?.allowSecureOnLocal ?? true\n    this.prefixSecurity = getNormalizedPrefixSecurity(\n      options?.prefixSecurity ?? 'silent',\n    )\n    this.store = store ?? new MemoryCookieStore()\n  }\n\n  private callSync<T>(\n    fn: (this: CookieJar, callback: Callback<T>) => void,\n  ): T | undefined {\n    if (!this.store.synchronous) {\n      throw new Error(\n        'CookieJar store is not synchronous; use async API instead.',\n      )\n    }\n    let syncErr: Error | null = null\n    let syncResult: T | undefined = undefined\n\n    try {\n      fn.call(this, (error: Error | null, result?: T) => {\n        syncErr = error\n        syncResult = result\n      })\n    } catch (err) {\n      syncErr = err as Error\n    }\n\n    if (syncErr) throw syncErr\n\n    return syncResult\n  }\n\n  /**\n   * Attempt to set the {@link Cookie} in the {@link CookieJar}.\n   *\n   * @remarks\n   * - If successfully persisted, the {@link Cookie} will have updated\n   *     {@link Cookie.creation}, {@link Cookie.lastAccessed} and {@link Cookie.hostOnly}\n   *     properties.\n   *\n   * - As per the RFC, the {@link Cookie.hostOnly} flag is set if there was no `Domain={value}`\n   *     attribute on the cookie string. The {@link Cookie.domain} property is set to the\n   *     fully-qualified hostname of `currentUrl` in this case. Matching this cookie requires an\n   *     exact hostname match (not a {@link domainMatch} as per usual)\n   *\n   * @param cookie - The cookie object or cookie string to store. A string value will be parsed into a cookie using {@link Cookie.parse}.\n   * @param url - The domain to store the cookie with.\n   * @param callback - A function to call after a cookie has been successfully stored.\n   * @public\n   */\n  setCookie(\n    cookie: string | Cookie,\n    url: string | URL,\n    callback: Callback<Cookie | undefined>,\n  ): void\n  /**\n   * Attempt to set the {@link Cookie} in the {@link CookieJar}.\n   *\n   * @remarks\n   * - If successfully persisted, the {@link Cookie} will have updated\n   *     {@link Cookie.creation}, {@link Cookie.lastAccessed} and {@link Cookie.hostOnly}\n   *     properties.\n   *\n   * - As per the RFC, the {@link Cookie.hostOnly} flag is set if there was no `Domain={value}`\n   *     attribute on the cookie string. The {@link Cookie.domain} property is set to the\n   *     fully-qualified hostname of `currentUrl` in this case. Matching this cookie requires an\n   *     exact hostname match (not a {@link domainMatch} as per usual)\n   *\n   * @param cookie - The cookie object or cookie string to store. A string value will be parsed into a cookie using {@link Cookie.parse}.\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when storing the cookie.\n   * @param callback - A function to call after a cookie has been successfully stored.\n   * @public\n   */\n  setCookie(\n    cookie: string | Cookie,\n    url: string | URL,\n    options: SetCookieOptions,\n    callback: Callback<Cookie | undefined>,\n  ): void\n  /**\n   * Attempt to set the {@link Cookie} in the {@link CookieJar}.\n   *\n   * @remarks\n   * - If successfully persisted, the {@link Cookie} will have updated\n   *     {@link Cookie.creation}, {@link Cookie.lastAccessed} and {@link Cookie.hostOnly}\n   *     properties.\n   *\n   * - As per the RFC, the {@link Cookie.hostOnly} flag is set if there was no `Domain={value}`\n   *     attribute on the cookie string. The {@link Cookie.domain} property is set to the\n   *     fully-qualified hostname of `currentUrl` in this case. Matching this cookie requires an\n   *     exact hostname match (not a {@link domainMatch} as per usual)\n   *\n   * @param cookie - The cookie object or cookie string to store. A string value will be parsed into a cookie using {@link Cookie.parse}.\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when storing the cookie.\n   * @public\n   */\n  setCookie(\n    cookie: string | Cookie,\n    url: string | URL,\n    options?: SetCookieOptions,\n  ): Promise<Cookie | undefined>\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  setCookie(\n    cookie: string | Cookie,\n    url: string | URL,\n    options: SetCookieOptions | Callback<Cookie | undefined>,\n    callback?: Callback<Cookie | undefined>,\n  ): unknown\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  setCookie(\n    cookie: string | Cookie,\n    url: string | URL,\n    options?: SetCookieOptions | Callback<Cookie | undefined>,\n    callback?: Callback<Cookie | undefined>,\n  ): unknown {\n    if (typeof options === 'function') {\n      callback = options\n      options = undefined\n    }\n    const promiseCallback = createPromiseCallback(callback)\n    const cb = promiseCallback.callback\n    let context: UrlContext\n\n    try {\n      if (typeof url === 'string') {\n        validators.validate(\n          validators.isNonEmptyString(url),\n          callback,\n          safeToString(options),\n        )\n      }\n\n      context = getCookieContext(url)\n\n      if (typeof url === 'function') {\n        return promiseCallback.reject(new Error('No URL was specified'))\n      }\n\n      if (typeof options === 'function') {\n        options = defaultSetCookieOptions\n      }\n\n      validators.validate(typeof cb === 'function', cb)\n\n      if (\n        !validators.isNonEmptyString(cookie) &&\n        !validators.isObject(cookie) &&\n        cookie instanceof String &&\n        cookie.length == 0\n      ) {\n        return promiseCallback.resolve(undefined)\n      }\n    } catch (err) {\n      return promiseCallback.reject(err as Error)\n    }\n\n    const host = canonicalDomain(context.hostname) ?? null\n    const loose = options?.loose || this.enableLooseMode\n\n    let sameSiteContext = null\n    if (options?.sameSiteContext) {\n      sameSiteContext = checkSameSiteContext(options.sameSiteContext)\n      if (!sameSiteContext) {\n        return promiseCallback.reject(new Error(SAME_SITE_CONTEXT_VAL_ERR))\n      }\n    }\n\n    // S5.3 step 1\n    if (typeof cookie === 'string' || cookie instanceof String) {\n      const parsedCookie = Cookie.parse(cookie.toString(), { loose: loose })\n      if (!parsedCookie) {\n        const err = new Error('Cookie failed to parse')\n        return options?.ignoreError\n          ? promiseCallback.resolve(undefined)\n          : promiseCallback.reject(err)\n      }\n      cookie = parsedCookie\n    } else if (!(cookie instanceof Cookie)) {\n      // If you're seeing this error, and are passing in a Cookie object,\n      // it *might* be a Cookie object from another loaded version of tough-cookie.\n      const err = new Error(\n        'First argument to setCookie must be a Cookie object or string',\n      )\n\n      return options?.ignoreError\n        ? promiseCallback.resolve(undefined)\n        : promiseCallback.reject(err)\n    }\n\n    // S5.3 step 2\n    const now = options?.now || new Date() // will assign later to save effort in the face of errors\n\n    // S5.3 step 3: NOOP; persistent-flag and expiry-time is handled by getCookie()\n\n    // S5.3 step 4: NOOP; domain is null by default\n\n    // S5.3 step 5: public suffixes\n    if (this.rejectPublicSuffixes && cookie.domain) {\n      try {\n        const cdomain = cookie.cdomain()\n        const suffix =\n          typeof cdomain === 'string'\n            ? getPublicSuffix(cdomain, {\n                allowSpecialUseDomain: this.allowSpecialUseDomain,\n                ignoreError: options?.ignoreError,\n              })\n            : null\n        if (suffix == null && !IP_V6_REGEX_OBJECT.test(cookie.domain)) {\n          // e.g. \"com\"\n          const err = new Error('Cookie has domain set to a public suffix')\n\n          return options?.ignoreError\n            ? promiseCallback.resolve(undefined)\n            : promiseCallback.reject(err)\n        }\n        // Using `any` here rather than `unknown` to avoid a type assertion, at the cost of needing\n        // to disable eslint directives. It's easier to have this one spot of technically incorrect\n        // types, rather than having to deal with _all_ callback errors being `unknown`.\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      } catch (err: any) {\n        return options?.ignoreError\n          ? promiseCallback.resolve(undefined)\n          : // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            promiseCallback.reject(err)\n      }\n    }\n\n    // S5.3 step 6:\n    if (cookie.domain) {\n      if (\n        !domainMatch(host ?? undefined, cookie.cdomain() ?? undefined, false)\n      ) {\n        const err = new Error(\n          `Cookie not in this host's domain. Cookie:${\n            cookie.cdomain() ?? 'null'\n          } Request:${host ?? 'null'}`,\n        )\n        return options?.ignoreError\n          ? promiseCallback.resolve(undefined)\n          : promiseCallback.reject(err)\n      }\n\n      if (cookie.hostOnly == null) {\n        // don't reset if already set\n        cookie.hostOnly = false\n      }\n    } else {\n      cookie.hostOnly = true\n      cookie.domain = host\n    }\n\n    //S5.2.4 If the attribute-value is empty or if the first character of the\n    //attribute-value is not %x2F (\"/\"):\n    //Let cookie-path be the default-path.\n    if (!cookie.path || cookie.path[0] !== '/') {\n      cookie.path = defaultPath(context.pathname)\n      cookie.pathIsDefault = true\n    }\n\n    // S5.3 step 8: NOOP; secure attribute\n    // S5.3 step 9: NOOP; httpOnly attribute\n\n    // S5.3 step 10\n    if (options?.http === false && cookie.httpOnly) {\n      const err = new Error(\"Cookie is HttpOnly and this isn't an HTTP API\")\n      return options.ignoreError\n        ? promiseCallback.resolve(undefined)\n        : promiseCallback.reject(err)\n    }\n\n    // 6252bis-02 S5.4 Step 13 & 14:\n    if (\n      cookie.sameSite !== 'none' &&\n      cookie.sameSite !== undefined &&\n      sameSiteContext\n    ) {\n      // \"If the cookie's \"same-site-flag\" is not \"None\", and the cookie\n      //  is being set from a context whose \"site for cookies\" is not an\n      //  exact match for request-uri's host's registered domain, then\n      //  abort these steps and ignore the newly created cookie entirely.\"\n      if (sameSiteContext === 'none') {\n        const err = new Error(\n          'Cookie is SameSite but this is a cross-origin request',\n        )\n        return options?.ignoreError\n          ? promiseCallback.resolve(undefined)\n          : promiseCallback.reject(err)\n      }\n    }\n\n    /* 6265bis-02 S5.4 Steps 15 & 16 */\n    const ignoreErrorForPrefixSecurity =\n      this.prefixSecurity === PrefixSecurityEnum.SILENT\n    const prefixSecurityDisabled =\n      this.prefixSecurity === PrefixSecurityEnum.DISABLED\n    /* If prefix checking is not disabled ...*/\n    if (!prefixSecurityDisabled) {\n      let errorFound = false\n      let errorMsg\n      /* Check secure prefix condition */\n      if (!isSecurePrefixConditionMet(cookie)) {\n        errorFound = true\n        errorMsg = 'Cookie has __Secure prefix but Secure attribute is not set'\n      } else if (!isHostPrefixConditionMet(cookie)) {\n        /* Check host prefix condition */\n        errorFound = true\n        errorMsg =\n          \"Cookie has __Host prefix but either Secure or HostOnly attribute is not set or Path is not '/'\"\n      }\n      if (errorFound) {\n        return options?.ignoreError || ignoreErrorForPrefixSecurity\n          ? promiseCallback.resolve(undefined)\n          : promiseCallback.reject(new Error(errorMsg))\n      }\n    }\n\n    const store = this.store\n\n    // TODO: It feels weird to be manipulating the store as a side effect of a method.\n    // We should either do it in the constructor or not at all.\n    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition\n    if (!store.updateCookie) {\n      store.updateCookie = async function (\n        _oldCookie: Cookie,\n        newCookie: Cookie,\n        cb?: ErrorCallback,\n      ): Promise<void> {\n        return this.putCookie(newCookie).then(\n          () => cb?.(null),\n          (error: unknown) => cb?.(error as Error),\n        )\n      }\n    }\n\n    const withCookie: Callback<Cookie | undefined> = function withCookie(\n      err,\n      oldCookie,\n    ): void {\n      if (err) {\n        cb(err)\n        return\n      }\n\n      const next: ErrorCallback = function (err) {\n        if (err) {\n          cb(err)\n        } else if (typeof cookie === 'string') {\n          cb(null, undefined)\n        } else {\n          cb(null, cookie)\n        }\n      }\n\n      if (oldCookie) {\n        // S5.3 step 11 - \"If the cookie store contains a cookie with the same name,\n        // domain, and path as the newly created cookie:\"\n        if (\n          options &&\n          'http' in options &&\n          options.http === false &&\n          oldCookie.httpOnly\n        ) {\n          // step 11.2\n          err = new Error(\"old Cookie is HttpOnly and this isn't an HTTP API\")\n          if (options.ignoreError) cb(null, undefined)\n          else cb(err)\n          return\n        }\n        if (cookie instanceof Cookie) {\n          cookie.creation = oldCookie.creation\n          // step 11.3\n          cookie.creationIndex = oldCookie.creationIndex\n          // preserve tie-breaker\n          cookie.lastAccessed = now\n          // Step 11.4 (delete cookie) is implied by just setting the new one:\n          store.updateCookie(oldCookie, cookie, next) // step 12\n        }\n      } else {\n        if (cookie instanceof Cookie) {\n          cookie.creation = cookie.lastAccessed = now\n          store.putCookie(cookie, next) // step 12\n        }\n      }\n    }\n\n    // TODO: Refactor to avoid using a callback\n    store.findCookie(cookie.domain, cookie.path, cookie.key, withCookie)\n    return promiseCallback.promise\n  }\n\n  /**\n   * Synchronously attempt to set the {@link Cookie} in the {@link CookieJar}.\n   *\n   * <strong>Note:</strong> Only works if the configured {@link Store} is also synchronous.\n   *\n   * @remarks\n   * - If successfully persisted, the {@link Cookie} will have updated\n   *     {@link Cookie.creation}, {@link Cookie.lastAccessed} and {@link Cookie.hostOnly}\n   *     properties.\n   *\n   * - As per the RFC, the {@link Cookie.hostOnly} flag is set if there was no `Domain={value}`\n   *     attribute on the cookie string. The {@link Cookie.domain} property is set to the\n   *     fully-qualified hostname of `currentUrl` in this case. Matching this cookie requires an\n   *     exact hostname match (not a {@link domainMatch} as per usual)\n   *\n   * @param cookie - The cookie object or cookie string to store. A string value will be parsed into a cookie using {@link Cookie.parse}.\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when storing the cookie.\n   * @public\n   */\n  setCookieSync(\n    cookie: string | Cookie,\n    url: string,\n    options?: SetCookieOptions,\n  ): Cookie | undefined {\n    const setCookieFn = options\n      ? this.setCookie.bind(this, cookie, url, options)\n      : this.setCookie.bind(this, cookie, url)\n    return this.callSync(setCookieFn)\n  }\n\n  /**\n   * Retrieve the list of cookies that can be sent in a Cookie header for the\n   * current URL.\n   *\n   * @remarks\n   * - The array of cookies returned will be sorted according to {@link cookieCompare}.\n   *\n   * - The {@link Cookie.lastAccessed} property will be updated on all returned cookies.\n   *\n   * @param url - The domain to store the cookie with.\n   */\n  getCookies(url: string): Promise<Cookie[]>\n  /**\n   * Retrieve the list of cookies that can be sent in a Cookie header for the\n   * current URL.\n   *\n   * @remarks\n   * - The array of cookies returned will be sorted according to {@link cookieCompare}.\n   *\n   * - The {@link Cookie.lastAccessed} property will be updated on all returned cookies.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param callback - A function to call after a cookie has been successfully retrieved.\n   */\n  getCookies(url: string, callback: Callback<Cookie[]>): void\n  /**\n   * Retrieve the list of cookies that can be sent in a Cookie header for the\n   * current URL.\n   *\n   * @remarks\n   * - The array of cookies returned will be sorted according to {@link cookieCompare}.\n   *\n   * - The {@link Cookie.lastAccessed} property will be updated on all returned cookies.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   * @param callback - A function to call after a cookie has been successfully retrieved.\n   */\n  getCookies(\n    url: string | URL,\n    options: GetCookiesOptions | undefined,\n    callback: Callback<Cookie[]>,\n  ): void\n  /**\n   * Retrieve the list of cookies that can be sent in a Cookie header for the\n   * current URL.\n   *\n   * @remarks\n   * - The array of cookies returned will be sorted according to {@link cookieCompare}.\n   *\n   * - The {@link Cookie.lastAccessed} property will be updated on all returned cookies.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   */\n  getCookies(url: string | URL, options?: GetCookiesOptions): Promise<Cookie[]>\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  getCookies(\n    url: string | URL,\n    options: GetCookiesOptions | undefined | Callback<Cookie[]>,\n    callback?: Callback<Cookie[]>,\n  ): unknown\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  getCookies(\n    url: string | URL,\n    options?: GetCookiesOptions | Callback<Cookie[]>,\n    callback?: Callback<Cookie[]>,\n  ): unknown {\n    // RFC6365 S5.4\n    if (typeof options === 'function') {\n      callback = options\n      options = defaultGetCookieOptions\n    } else if (options === undefined) {\n      options = defaultGetCookieOptions\n    }\n    const promiseCallback = createPromiseCallback(callback)\n    const cb = promiseCallback.callback\n    let context: UrlContext\n\n    try {\n      if (typeof url === 'string') {\n        validators.validate(validators.isNonEmptyString(url), cb, url)\n      }\n\n      context = getCookieContext(url)\n\n      validators.validate(\n        validators.isObject(options),\n        cb,\n        safeToString(options),\n      )\n\n      validators.validate(typeof cb === 'function', cb)\n    } catch (parameterError) {\n      return promiseCallback.reject(parameterError as Error)\n    }\n\n    const host = canonicalDomain(context.hostname)\n    const path = context.pathname || '/'\n\n    // https://datatracker.ietf.org/doc/html/draft-ietf-httpbis-rfc6265bis-19#section-5.8.3-*******.2\n    // deliberately expects the user agent to determine the notion of a \"secure\" connection,\n    // and in practice this converges to a \"potentially trustworthy origin\" as defined in:\n    // https://www.w3.org/TR/secure-contexts/#is-origin-trustworthy\n    const potentiallyTrustworthy = isPotentiallyTrustworthy(\n      url,\n      this.allowSecureOnLocal,\n    )\n\n    let sameSiteLevel = 0\n    if (options.sameSiteContext) {\n      const sameSiteContext = checkSameSiteContext(options.sameSiteContext)\n      if (sameSiteContext == null) {\n        return promiseCallback.reject(new Error(SAME_SITE_CONTEXT_VAL_ERR))\n      }\n      sameSiteLevel = Cookie.sameSiteLevel[sameSiteContext]\n      if (!sameSiteLevel) {\n        return promiseCallback.reject(new Error(SAME_SITE_CONTEXT_VAL_ERR))\n      }\n    }\n\n    const http = options.http ?? true\n\n    const now = Date.now()\n    const expireCheck = options.expire ?? true\n    const allPaths = options.allPaths ?? false\n    const store = this.store\n\n    function matchingCookie(c: Cookie): boolean {\n      // \"Either:\n      //   The cookie's host-only-flag is true and the canonicalized\n      //   request-host is identical to the cookie's domain.\n      // Or:\n      //   The cookie's host-only-flag is false and the canonicalized\n      //   request-host domain-matches the cookie's domain.\"\n      if (c.hostOnly) {\n        if (c.domain != host) {\n          return false\n        }\n      } else {\n        if (!domainMatch(host ?? undefined, c.domain ?? undefined, false)) {\n          return false\n        }\n      }\n\n      // \"The request-uri's path path-matches the cookie's path.\"\n      if (!allPaths && typeof c.path === 'string' && !pathMatch(path, c.path)) {\n        return false\n      }\n\n      // \"If the cookie's secure-only-flag is true, then the request-uri's\n      // scheme must denote a \"secure\" protocol\"\n      if (c.secure && !potentiallyTrustworthy) {\n        return false\n      }\n\n      // \"If the cookie's http-only-flag is true, then exclude the cookie if the\n      // cookie-string is being generated for a \"non-HTTP\" API\"\n      if (c.httpOnly && !http) {\n        return false\n      }\n\n      // RFC6265bis-02 S5.3.7\n      if (sameSiteLevel) {\n        let cookieLevel: number\n        if (c.sameSite === 'lax') {\n          cookieLevel = Cookie.sameSiteLevel.lax\n        } else if (c.sameSite === 'strict') {\n          cookieLevel = Cookie.sameSiteLevel.strict\n        } else {\n          cookieLevel = Cookie.sameSiteLevel.none\n        }\n        if (cookieLevel > sameSiteLevel) {\n          // only allow cookies at or below the request level\n          return false\n        }\n      }\n\n      // deferred from S5.3\n      // non-RFC: allow retention of expired cookies by choice\n      const expiryTime = c.expiryTime()\n      if (expireCheck && expiryTime != undefined && expiryTime <= now) {\n        store.removeCookie(c.domain, c.path, c.key, () => {}) // result ignored\n        return false\n      }\n\n      return true\n    }\n\n    store.findCookies(\n      host,\n      allPaths ? null : path,\n      this.allowSpecialUseDomain,\n      (err, cookies): void => {\n        if (err) {\n          cb(err)\n          return\n        }\n\n        if (cookies == null) {\n          cb(null, [])\n          return\n        }\n\n        cookies = cookies.filter(matchingCookie)\n\n        // sorting of S5.4 part 2\n        if ('sort' in options && options.sort !== false) {\n          cookies = cookies.sort(cookieCompare)\n        }\n\n        // S5.4 part 3\n        const now = new Date()\n        for (const cookie of cookies) {\n          cookie.lastAccessed = now\n        }\n        // TODO persist lastAccessed\n\n        cb(null, cookies)\n      },\n    )\n\n    return promiseCallback.promise\n  }\n\n  /**\n   * Synchronously retrieve the list of cookies that can be sent in a Cookie header for the\n   * current URL.\n   *\n   * <strong>Note</strong>: Only works if the configured Store is also synchronous.\n   *\n   * @remarks\n   * - The array of cookies returned will be sorted according to {@link cookieCompare}.\n   *\n   * - The {@link Cookie.lastAccessed} property will be updated on all returned cookies.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   */\n  getCookiesSync(url: string, options?: GetCookiesOptions): Cookie[] {\n    return this.callSync(this.getCookies.bind(this, url, options)) ?? []\n  }\n\n  /**\n   * Accepts the same options as `.getCookies()` but returns a string suitable for a\n   * `Cookie` header rather than an Array.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   * @param callback - A function to call after the `Cookie` header string has been created.\n   */\n  getCookieString(\n    url: string,\n    options: GetCookiesOptions,\n    callback: Callback<string | undefined>,\n  ): void\n  /**\n   * Accepts the same options as `.getCookies()` but returns a string suitable for a\n   * `Cookie` header rather than an Array.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param callback - A function to call after the `Cookie` header string has been created.\n   */\n  getCookieString(url: string, callback: Callback<string | undefined>): void\n  /**\n   * Accepts the same options as `.getCookies()` but returns a string suitable for a\n   * `Cookie` header rather than an Array.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   */\n  getCookieString(url: string, options?: GetCookiesOptions): Promise<string>\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  getCookieString(\n    url: string,\n    options: GetCookiesOptions | Callback<string | undefined>,\n    callback?: Callback<string | undefined>,\n  ): unknown\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  getCookieString(\n    url: string,\n    options?: GetCookiesOptions | Callback<string | undefined>,\n    callback?: Callback<string | undefined>,\n  ): unknown {\n    if (typeof options === 'function') {\n      callback = options\n      options = undefined\n    }\n    const promiseCallback = createPromiseCallback(callback)\n    const next: Callback<Cookie[]> = function (err, cookies) {\n      if (err) {\n        promiseCallback.callback(err)\n      } else {\n        promiseCallback.callback(\n          null,\n          cookies\n            ?.sort(cookieCompare)\n            .map((c) => c.cookieString())\n            .join('; '),\n        )\n      }\n    }\n\n    this.getCookies(url, options, next)\n    return promiseCallback.promise\n  }\n\n  /**\n   * Synchronous version of `.getCookieString()`. Accepts the same options as `.getCookies()` but returns a string suitable for a\n   * `Cookie` header rather than an Array.\n   *\n   * <strong>Note</strong>: Only works if the configured Store is also synchronous.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   */\n  getCookieStringSync(url: string, options?: GetCookiesOptions): string {\n    return (\n      this.callSync(\n        options\n          ? this.getCookieString.bind(this, url, options)\n          : this.getCookieString.bind(this, url),\n      ) ?? ''\n    )\n  }\n\n  /**\n   * Returns an array of strings suitable for `Set-Cookie` headers. Accepts the same options\n   * as `.getCookies()`.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param callback - A function to call after the `Set-Cookie` header strings have been created.\n   */\n  getSetCookieStrings(\n    url: string,\n    callback: Callback<string[] | undefined>,\n  ): void\n  /**\n   * Returns an array of strings suitable for `Set-Cookie` headers. Accepts the same options\n   * as `.getCookies()`.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   * @param callback - A function to call after the `Set-Cookie` header strings have been created.\n   */\n  getSetCookieStrings(\n    url: string,\n    options: GetCookiesOptions,\n    callback: Callback<string[] | undefined>,\n  ): void\n  /**\n   * Returns an array of strings suitable for `Set-Cookie` headers. Accepts the same options\n   * as `.getCookies()`.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   */\n  getSetCookieStrings(\n    url: string,\n    options?: GetCookiesOptions,\n  ): Promise<string[] | undefined>\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  getSetCookieStrings(\n    url: string,\n    options: GetCookiesOptions,\n    callback?: Callback<string[] | undefined>,\n  ): unknown\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  getSetCookieStrings(\n    url: string,\n    options?: GetCookiesOptions | Callback<string[] | undefined>,\n    callback?: Callback<string[] | undefined>,\n  ): unknown {\n    if (typeof options === 'function') {\n      callback = options\n      options = undefined\n    }\n    const promiseCallback = createPromiseCallback<string[] | undefined>(\n      callback,\n    )\n\n    const next: Callback<Cookie[] | undefined> = function (err, cookies) {\n      if (err) {\n        promiseCallback.callback(err)\n      } else {\n        promiseCallback.callback(\n          null,\n          cookies?.map((c) => {\n            return c.toString()\n          }),\n        )\n      }\n    }\n\n    this.getCookies(url, options, next)\n    return promiseCallback.promise\n  }\n\n  /**\n   * Synchronous version of `.getSetCookieStrings()`. Returns an array of strings suitable for `Set-Cookie` headers.\n   * Accepts the same options as `.getCookies()`.\n   *\n   * <strong>Note</strong>: Only works if the configured Store is also synchronous.\n   *\n   * @param url - The domain to store the cookie with.\n   * @param options - Configuration settings to use when retrieving the cookies.\n   */\n  getSetCookieStringsSync(\n    url: string,\n    options: GetCookiesOptions = {},\n  ): string[] {\n    return (\n      this.callSync(this.getSetCookieStrings.bind(this, url, options)) ?? []\n    )\n  }\n\n  /**\n   * Serialize the CookieJar if the underlying store supports `.getAllCookies`.\n   * @param callback - A function to call after the CookieJar has been serialized\n   */\n  serialize(callback: Callback<SerializedCookieJar>): void\n  /**\n   * Serialize the CookieJar if the underlying store supports `.getAllCookies`.\n   */\n  serialize(): Promise<SerializedCookieJar>\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  serialize(callback?: Callback<SerializedCookieJar>): unknown {\n    const promiseCallback = createPromiseCallback<SerializedCookieJar>(callback)\n\n    let type: string | null = this.store.constructor.name\n    if (validators.isObject(type)) {\n      type = null\n    }\n\n    // update README.md \"Serialization Format\" if you change this, please!\n    const serialized: SerializedCookieJar = {\n      // The version of tough-cookie that serialized this jar. Generally a good\n      // practice since future versions can make data import decisions based on\n      // known past behavior. When/if this matters, use `semver`.\n      version: `tough-cookie@${version}`,\n\n      // add the store type, to make humans happy:\n      storeType: type,\n\n      // CookieJar configuration:\n      rejectPublicSuffixes: this.rejectPublicSuffixes,\n      enableLooseMode: this.enableLooseMode,\n      allowSpecialUseDomain: this.allowSpecialUseDomain,\n      prefixSecurity: getNormalizedPrefixSecurity(this.prefixSecurity),\n\n      // this gets filled from getAllCookies:\n      cookies: [],\n    }\n\n    if (typeof this.store.getAllCookies !== 'function') {\n      return promiseCallback.reject(\n        new Error(\n          'store does not support getAllCookies and cannot be serialized',\n        ),\n      )\n    }\n\n    this.store.getAllCookies((err, cookies) => {\n      if (err) {\n        promiseCallback.callback(err)\n        return\n      }\n\n      if (cookies == null) {\n        promiseCallback.callback(null, serialized)\n        return\n      }\n\n      serialized.cookies = cookies.map((cookie) => {\n        // convert to serialized 'raw' cookies\n        const serializedCookie = cookie.toJSON()\n\n        // Remove the index so new ones get assigned during deserialization\n        delete serializedCookie.creationIndex\n\n        return serializedCookie\n      })\n\n      promiseCallback.callback(null, serialized)\n    })\n\n    return promiseCallback.promise\n  }\n\n  /**\n   * Serialize the CookieJar if the underlying store supports `.getAllCookies`.\n   *\n   * <strong>Note</strong>: Only works if the configured Store is also synchronous.\n   */\n  serializeSync(): SerializedCookieJar | undefined {\n    return this.callSync((callback) => {\n      this.serialize(callback)\n    })\n  }\n\n  /**\n   * Alias of {@link CookieJar.serializeSync}. Allows the cookie to be serialized\n   * with `JSON.stringify(cookieJar)`.\n   */\n  toJSON(): SerializedCookieJar | undefined {\n    return this.serializeSync()\n  }\n\n  /**\n   * Use the class method CookieJar.deserialize instead of calling this directly\n   * @internal\n   */\n  _importCookies(serialized: unknown, callback: Callback<CookieJar>): void {\n    let cookies: unknown[] | undefined = undefined\n\n    if (\n      serialized &&\n      typeof serialized === 'object' &&\n      inOperator('cookies', serialized) &&\n      Array.isArray(serialized.cookies)\n    ) {\n      cookies = serialized.cookies\n    }\n\n    if (!cookies) {\n      callback(new Error('serialized jar has no cookies array'), undefined)\n      return\n    }\n\n    cookies = cookies.slice() // do not modify the original\n\n    const putNext: ErrorCallback = (err) => {\n      if (err) {\n        callback(err, undefined)\n        return\n      }\n\n      if (Array.isArray(cookies)) {\n        if (!cookies.length) {\n          callback(err, this)\n          return\n        }\n\n        let cookie\n        try {\n          cookie = Cookie.fromJSON(cookies.shift())\n        } catch (e) {\n          callback(e instanceof Error ? e : new Error(), undefined)\n          return\n        }\n\n        if (cookie === undefined) {\n          putNext(null) // skip this cookie\n          return\n        }\n\n        this.store.putCookie(cookie, putNext)\n      }\n    }\n\n    putNext(null)\n  }\n\n  /**\n   * @internal\n   */\n  _importCookiesSync(serialized: unknown): void {\n    this.callSync(this._importCookies.bind(this, serialized))\n  }\n\n  /**\n   * Produces a deep clone of this CookieJar. Modifications to the original do\n   * not affect the clone, and vice versa.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - Transferring between store types is supported so long as the source\n   *     implements `.getAllCookies()` and the destination implements `.putCookie()`.\n   *\n   * @param callback - A function to call when the CookieJar is cloned.\n   */\n  clone(callback: Callback<CookieJar>): void\n  /**\n   * Produces a deep clone of this CookieJar. Modifications to the original do\n   * not affect the clone, and vice versa.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - Transferring between store types is supported so long as the source\n   *     implements `.getAllCookies()` and the destination implements `.putCookie()`.\n   *\n   * @param newStore - The target {@link Store} to clone cookies into.\n   * @param callback - A function to call when the CookieJar is cloned.\n   */\n  clone(newStore: Store, callback: Callback<CookieJar>): void\n  /**\n   * Produces a deep clone of this CookieJar. Modifications to the original do\n   * not affect the clone, and vice versa.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - Transferring between store types is supported so long as the source\n   *     implements `.getAllCookies()` and the destination implements `.putCookie()`.\n   *\n   * @param newStore - The target {@link Store} to clone cookies into.\n   */\n  clone(newStore?: Store): Promise<CookieJar>\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  clone(\n    newStore?: Store | Callback<CookieJar>,\n    callback?: Callback<CookieJar>,\n  ): unknown {\n    if (typeof newStore === 'function') {\n      callback = newStore\n      newStore = undefined\n    }\n\n    const promiseCallback = createPromiseCallback<CookieJar>(callback)\n    const cb = promiseCallback.callback\n\n    this.serialize((err, serialized) => {\n      if (err) {\n        return promiseCallback.reject(err)\n      }\n      return CookieJar.deserialize(serialized ?? '', newStore, cb)\n    })\n\n    return promiseCallback.promise\n  }\n\n  /**\n   * @internal\n   */\n  _cloneSync(newStore?: Store): CookieJar | undefined {\n    const cloneFn =\n      newStore && typeof newStore !== 'function'\n        ? this.clone.bind(this, newStore)\n        : this.clone.bind(this)\n    return this.callSync((callback) => {\n      cloneFn(callback)\n    })\n  }\n\n  /**\n   * Produces a deep clone of this CookieJar. Modifications to the original do\n   * not affect the clone, and vice versa.\n   *\n   * <strong>Note</strong>: Only works if both the configured Store and destination\n   * Store are synchronous.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - Transferring between store types is supported so long as the source\n   *     implements `.getAllCookies()` and the destination implements `.putCookie()`.\n   *\n   * @param newStore - The target {@link Store} to clone cookies into.\n   */\n  cloneSync(newStore?: Store): CookieJar | undefined {\n    if (!newStore) {\n      return this._cloneSync()\n    }\n    if (!newStore.synchronous) {\n      throw new Error(\n        'CookieJar clone destination store is not synchronous; use async API instead.',\n      )\n    }\n    return this._cloneSync(newStore)\n  }\n\n  /**\n   * Removes all cookies from the CookieJar.\n   *\n   * @remarks\n   * - This is a new backwards-compatible feature of tough-cookie version 2.5,\n   *     so not all Stores will implement it efficiently. For Stores that do not\n   *     implement `removeAllCookies`, the fallback is to call `removeCookie` after\n   *     `getAllCookies`.\n   *\n   * - If `getAllCookies` fails or isn't implemented in the Store, an error is returned.\n   *\n   * - If one or more of the `removeCookie` calls fail, only the first error is returned.\n   *\n   * @param callback - A function to call when all the cookies have been removed.\n   */\n  removeAllCookies(callback: ErrorCallback): void\n  /**\n   * Removes all cookies from the CookieJar.\n   *\n   * @remarks\n   * - This is a new backwards-compatible feature of tough-cookie version 2.5,\n   *     so not all Stores will implement it efficiently. For Stores that do not\n   *     implement `removeAllCookies`, the fallback is to call `removeCookie` after\n   *     `getAllCookies`.\n   *\n   * - If `getAllCookies` fails or isn't implemented in the Store, an error is returned.\n   *\n   * - If one or more of the `removeCookie` calls fail, only the first error is returned.\n   */\n  removeAllCookies(): Promise<void>\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  removeAllCookies(callback?: ErrorCallback): unknown {\n    const promiseCallback = createPromiseCallback<undefined>(callback)\n    const cb = promiseCallback.callback\n\n    const store = this.store\n\n    // Check that the store implements its own removeAllCookies(). The default\n    // implementation in Store will immediately call the callback with a \"not\n    // implemented\" Error.\n    if (\n      typeof store.removeAllCookies === 'function' &&\n      store.removeAllCookies !== Store.prototype.removeAllCookies\n    ) {\n      // `Callback<undefined>` and `ErrorCallback` are *technically* incompatible, but for the\n      // standard implementation `cb = (err, result) => {}`, they're essentially the same.\n      store.removeAllCookies(cb as ErrorCallback)\n      return promiseCallback.promise\n    }\n\n    store.getAllCookies((err, cookies): void => {\n      if (err) {\n        cb(err)\n        return\n      }\n\n      if (!cookies) {\n        cookies = []\n      }\n\n      if (cookies.length === 0) {\n        cb(null, undefined)\n        return\n      }\n\n      let completedCount = 0\n      const removeErrors: Error[] = []\n\n      // TODO: Refactor to avoid using callback\n      const removeCookieCb: ErrorCallback = function removeCookieCb(removeErr) {\n        if (removeErr) {\n          removeErrors.push(removeErr)\n        }\n\n        completedCount++\n\n        if (completedCount === cookies.length) {\n          if (removeErrors[0]) cb(removeErrors[0])\n          else cb(null, undefined)\n          return\n        }\n      }\n\n      cookies.forEach((cookie) => {\n        store.removeCookie(\n          cookie.domain,\n          cookie.path,\n          cookie.key,\n          removeCookieCb,\n        )\n      })\n    })\n\n    return promiseCallback.promise\n  }\n\n  /**\n   * Removes all cookies from the CookieJar.\n   *\n   * <strong>Note</strong>: Only works if the configured Store is also synchronous.\n   *\n   * @remarks\n   * - This is a new backwards-compatible feature of tough-cookie version 2.5,\n   *     so not all Stores will implement it efficiently. For Stores that do not\n   *     implement `removeAllCookies`, the fallback is to call `removeCookie` after\n   *     `getAllCookies`.\n   *\n   * - If `getAllCookies` fails or isn't implemented in the Store, an error is returned.\n   *\n   * - If one or more of the `removeCookie` calls fail, only the first error is returned.\n   */\n  removeAllCookiesSync(): void {\n    this.callSync<undefined>((callback) => {\n      // `Callback<undefined>` and `ErrorCallback` are *technically* incompatible, but for the\n      // standard implementation `cb = (err, result) => {}`, they're essentially the same.\n      this.removeAllCookies(callback as ErrorCallback)\n    })\n  }\n\n  /**\n   * A new CookieJar is created and the serialized {@link Cookie} values are added to\n   * the underlying store. Each {@link Cookie} is added via `store.putCookie(...)` in\n   * the order in which they appear in the serialization.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - As a convenience, if `strOrObj` is a string, it is passed through `JSON.parse` first.\n   *\n   * @param strOrObj - A JSON string or object representing the deserialized cookies.\n   * @param callback - A function to call after the {@link CookieJar} has been deserialized.\n   */\n  static deserialize(\n    strOrObj: string | object,\n    callback: Callback<CookieJar>,\n  ): void\n  /**\n   * A new CookieJar is created and the serialized {@link Cookie} values are added to\n   * the underlying store. Each {@link Cookie} is added via `store.putCookie(...)` in\n   * the order in which they appear in the serialization.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - As a convenience, if `strOrObj` is a string, it is passed through `JSON.parse` first.\n   *\n   * @param strOrObj - A JSON string or object representing the deserialized cookies.\n   * @param store - The underlying store to persist the deserialized cookies into.\n   * @param callback - A function to call after the {@link CookieJar} has been deserialized.\n   */\n  static deserialize(\n    strOrObj: string | object,\n    store: Store,\n    callback: Callback<CookieJar>,\n  ): void\n  /**\n   * A new CookieJar is created and the serialized {@link Cookie} values are added to\n   * the underlying store. Each {@link Cookie} is added via `store.putCookie(...)` in\n   * the order in which they appear in the serialization.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - As a convenience, if `strOrObj` is a string, it is passed through `JSON.parse` first.\n   *\n   * @param strOrObj - A JSON string or object representing the deserialized cookies.\n   * @param store - The underlying store to persist the deserialized cookies into.\n   */\n  static deserialize(\n    strOrObj: string | object,\n    store?: Store,\n  ): Promise<CookieJar>\n  /**\n   * @internal No doc because this is an overload that supports the implementation\n   */\n  static deserialize(\n    strOrObj: string | object,\n    store?: Store | Callback<CookieJar>,\n    callback?: Callback<CookieJar>,\n  ): unknown\n  /**\n   * @internal No doc because this is the overload implementation\n   */\n  static deserialize(\n    strOrObj: string | object,\n    store?: Store | Callback<CookieJar>,\n    callback?: Callback<CookieJar>,\n  ): unknown {\n    if (typeof store === 'function') {\n      callback = store\n      store = undefined\n    }\n\n    const promiseCallback = createPromiseCallback<CookieJar>(callback)\n\n    let serialized: unknown\n    if (typeof strOrObj === 'string') {\n      try {\n        serialized = JSON.parse(strOrObj)\n      } catch (e) {\n        return promiseCallback.reject(e instanceof Error ? e : new Error())\n      }\n    } else {\n      serialized = strOrObj\n    }\n\n    const readSerializedProperty = (property: string): unknown => {\n      return serialized &&\n        typeof serialized === 'object' &&\n        inOperator(property, serialized)\n        ? serialized[property]\n        : undefined\n    }\n\n    const readSerializedBoolean = (property: string): boolean | undefined => {\n      const value = readSerializedProperty(property)\n      return typeof value === 'boolean' ? value : undefined\n    }\n\n    const readSerializedString = (property: string): string | undefined => {\n      const value = readSerializedProperty(property)\n      return typeof value === 'string' ? value : undefined\n    }\n\n    const jar = new CookieJar(store, {\n      rejectPublicSuffixes: readSerializedBoolean('rejectPublicSuffixes'),\n      looseMode: readSerializedBoolean('enableLooseMode'),\n      allowSpecialUseDomain: readSerializedBoolean('allowSpecialUseDomain'),\n      prefixSecurity: getNormalizedPrefixSecurity(\n        readSerializedString('prefixSecurity') ?? 'silent',\n      ),\n    })\n\n    jar._importCookies(serialized, (err) => {\n      if (err) {\n        promiseCallback.callback(err)\n        return\n      }\n      promiseCallback.callback(null, jar)\n    })\n\n    return promiseCallback.promise\n  }\n\n  /**\n   * A new CookieJar is created and the serialized {@link Cookie} values are added to\n   * the underlying store. Each {@link Cookie} is added via `store.putCookie(...)` in\n   * the order in which they appear in the serialization.\n   *\n   * <strong>Note</strong>: Only works if the configured Store is also synchronous.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - As a convenience, if `strOrObj` is a string, it is passed through `JSON.parse` first.\n   *\n   * @param strOrObj - A JSON string or object representing the deserialized cookies.\n   * @param store - The underlying store to persist the deserialized cookies into.\n   */\n  static deserializeSync(\n    strOrObj: string | SerializedCookieJar,\n    store?: Store,\n  ): CookieJar {\n    const serialized: unknown =\n      typeof strOrObj === 'string' ? JSON.parse(strOrObj) : strOrObj\n\n    const readSerializedProperty = (property: string): unknown => {\n      return serialized &&\n        typeof serialized === 'object' &&\n        inOperator(property, serialized)\n        ? serialized[property]\n        : undefined\n    }\n\n    const readSerializedBoolean = (property: string): boolean | undefined => {\n      const value = readSerializedProperty(property)\n      return typeof value === 'boolean' ? value : undefined\n    }\n\n    const readSerializedString = (property: string): string | undefined => {\n      const value = readSerializedProperty(property)\n      return typeof value === 'string' ? value : undefined\n    }\n\n    const jar = new CookieJar(store, {\n      rejectPublicSuffixes: readSerializedBoolean('rejectPublicSuffixes'),\n      looseMode: readSerializedBoolean('enableLooseMode'),\n      allowSpecialUseDomain: readSerializedBoolean('allowSpecialUseDomain'),\n      prefixSecurity: getNormalizedPrefixSecurity(\n        readSerializedString('prefixSecurity') ?? 'silent',\n      ),\n    })\n\n    // catch this mistake early:\n    if (!jar.store.synchronous) {\n      throw new Error(\n        'CookieJar store is not synchronous; use async API instead.',\n      )\n    }\n\n    jar._importCookiesSync(serialized)\n    return jar\n  }\n\n  /**\n   * Alias of {@link CookieJar.deserializeSync}.\n   *\n   * @remarks\n   * - When no {@link Store} is provided, a new {@link MemoryCookieStore} will be used.\n   *\n   * - As a convenience, if `strOrObj` is a string, it is passed through `JSON.parse` first.\n   *\n   * @param jsonString - A JSON string or object representing the deserialized cookies.\n   * @param store - The underlying store to persist the deserialized cookies into.\n   */\n  static fromJSON(\n    jsonString: string | SerializedCookieJar,\n    store?: Store,\n  ): CookieJar {\n    return CookieJar.deserializeSync(jsonString, store)\n  }\n}\n", "/**\n * Generates the permutation of all possible values that {@link pathMatch} the `path` parameter.\n * The array is in longest-to-shortest order.  Useful when building custom {@link Store} implementations.\n *\n * @example\n * ```\n * permutePath('/foo/bar/')\n * // ['/foo/bar/', '/foo/bar', '/foo', '/']\n * ```\n *\n * @param path - the path to generate permutations for\n * @public\n */\nexport function permutePath(path: string): string[] {\n  if (path === '/') {\n    return ['/']\n  }\n  const permutations = [path]\n  while (path.length > 1) {\n    const lindex = path.lastIndexOf('/')\n    if (lindex === 0) {\n      break\n    }\n    path = path.slice(0, lindex)\n    permutations.push(path)\n  }\n  permutations.push('/')\n  return permutations\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAAAA;AAAA,EAAA;AAAA,eAAAC;AAAA,EAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACgBO,SAAS,UAAU,SAAiB,YAA6B;AAEtE,MAAI,eAAe,SAAS;AAC1B,WAAO;AAAA,EACT;AAEA,QAAM,MAAM,QAAQ,QAAQ,UAAU;AACtC,MAAI,QAAQ,GAAG;AAGb,QAAI,WAAW,WAAW,SAAS,CAAC,MAAM,KAAK;AAC7C,aAAO;AAAA,IACT;AAKA,QAAI,QAAQ,WAAW,UAAU,KAAK,QAAQ,WAAW,MAAM,MAAM,KAAK;AACxE,aAAO;AAAA,IACT;AAAA,EACF;AAEA,SAAO;AACT;;;ACvCA,mBAA0B;AAG1B,IAAM,sBAAsB,CAAC,SAAS,WAAW,WAAW,aAAa,MAAM;AAE/E,IAAM,4BAA4B,CAAC,aAAa,SAAS;AA+BzD,IAAM,gCAAwD;AAAA,EAC5D,uBAAuB;AAAA,EACvB,aAAa;AACf;AA4BO,SAAS,gBACd,QACA,UAAkC,CAAC,GACf;AACpB,YAAU,EAAE,GAAG,+BAA+B,GAAG,QAAQ;AACzD,QAAM,cAAc,OAAO,MAAM,GAAG;AACpC,QAAM,iBAAiB,YAAY,YAAY,SAAS,CAAC;AACzD,QAAM,wBAAwB,CAAC,CAAC,QAAQ;AACxC,QAAM,cAAc,CAAC,CAAC,QAAQ;AAE9B,MACE,yBACA,mBAAmB,UACnB,oBAAoB,SAAS,cAAc,GAC3C;AACA,QAAI,YAAY,SAAS,GAAG;AAE1B,YAAM,oBAAoB,YAAY,YAAY,SAAS,CAAC;AAE5D,aAAO,GAAG,iBAAiB,IAAI,cAAc;AAAA,IAC/C,WAAW,0BAA0B,SAAS,cAAc,GAAG;AAI7D,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MACE,CAAC,eACD,mBAAmB,UACnB,oBAAoB,SAAS,cAAc,GAC3C;AACA,UAAM,IAAI;AAAA,MACR,+CAA+C,cAAc;AAAA,IAC/D;AAAA,EACF;AAEA,QAAM,mBAAe,wBAAU,QAAQ;AAAA,IACrC,mBAAmB;AAAA,IACnB,qBAAqB;AAAA,EACvB,CAAC;AACD,MAAI,aAAc,QAAO;AAC3B;;;AC9FO,SAAS,cACd,QACA,uBACsB;AACtB,QAAM,SAAS,gBAAgB,QAAQ;AAAA,IACrC;AAAA,EACF,CAAC;AAED,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,UAAU,QAAQ;AACpB,WAAO,CAAC,MAAM;AAAA,EAChB;AAGA,MAAI,OAAO,MAAM,EAAE,KAAK,KAAK;AAC3B,aAAS,OAAO,MAAM,GAAG,EAAE;AAAA,EAC7B;AAEA,QAAM,SAAS,OAAO,MAAM,GAAG,EAAE,OAAO,SAAS,EAAE;AACnD,QAAM,QAAQ,OAAO,MAAM,GAAG,EAAE,QAAQ;AACxC,MAAI,MAAM;AACV,QAAM,eAAe,CAAC,GAAG;AACzB,SAAO,MAAM,QAAQ;AAEnB,UAAM,OAAO,MAAM,MAAM;AACzB,UAAM,GAAG,IAAI,IAAI,GAAG;AACpB,iBAAa,KAAK,GAAG;AAAA,EACvB;AACA,SAAO;AACT;;;AC1BO,IAAM,QAAN,MAAY;AAAA,EAMjB,cAAc;AACZ,SAAK,cAAc;AAAA,EACrB;AAAA;AAAA;AAAA;AAAA,EAqCA,WACE,SACA,OACA,MACA,WACS;AACT,UAAM,IAAI,MAAM,+BAA+B;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA,EAmDA,YACE,SACA,OACA,yBAAuD,OACvD,WACS;AACT,UAAM,IAAI,MAAM,gCAAgC;AAAA,EAClD;AAAA;AAAA;AAAA;AAAA,EAgCA,UAAU,SAAiB,WAAoC;AAC7D,UAAM,IAAI,MAAM,8BAA8B;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA,EAkDA,aACE,YACA,YACA,WACS;AAGT,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EA+BA,aACE,SACA,OACA,MACA,WACS;AACT,UAAM,IAAI,MAAM,iCAAiC;AAAA,EACnD;AAAA;AAAA;AAAA;AAAA,EA0BA,cACE,SACA,OACA,WACS;AACT,UAAM,IAAI,MAAM,kCAAkC;AAAA,EACpD;AAAA;AAAA;AAAA;AAAA,EAeA,iBAAiB,WAAoC;AACnD,UAAM,IAAI,MAAM,qCAAqC;AAAA,EACvD;AAAA;AAAA;AAAA;AAAA,EAqBA,cAAc,WAAyC;AACrD,UAAM,IAAI;AAAA,MACR;AAAA,IACF;AAAA,EACF;AACF;;;ACxTO,IAAM,iBAAiB,CAAC,QAC7B,OAAO,UAAU,SAAS,KAAK,GAAG;AAKpC,IAAM,oBAAoB,CACxB,KACA,eACW;AAEX,MAAI,OAAO,IAAI,SAAS,WAAY,QAAO,eAAe,GAAG;AAC7D,aAAW,IAAI,GAAG;AAClB,QAAM,SAAS,IAAI;AAAA,IAAI,CAAC,QACtB,QAAQ,QAAQ,QAAQ,UAAa,WAAW,IAAI,GAAG,IACnD,KACA,iBAAiB,KAAK,UAAU;AAAA,EACtC;AACA,SAAO,OAAO,KAAK;AACrB;AAEA,IAAM,mBAAmB,CAAC,KAAc,aAAa,oBAAI,QAAQ,MAAc;AAG7E,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,WAAO,OAAO,GAAG;AAAA,EACnB,WAAW,OAAO,IAAI,aAAa,YAAY;AAC7C,WAAO,MAAM,QAAQ,GAAG;AAAA;AAAA,MAEpB,kBAAkB,KAAK,UAAU;AAAA;AAAA;AAAA,MAEjC,OAAO,GAAG;AAAA;AAAA,EAChB,OAAO;AAEL,WAAO,eAAe,GAAG;AAAA,EAC3B;AACF;AAGO,IAAM,eAAe,CAAC,QAAyB,iBAAiB,GAAG;AAWnE,SAAS,sBAAyB,IAAsC;AAC7E,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,QAAM,UAAU,IAAI,QAAW,CAAC,UAAU,YAAY;AACpD,cAAU;AACV,aAAS;AAAA,EACX,CAAC;AAED,MAAI,OAAO,OAAO,YAAY;AAC5B,eAAW,CAAC,KAAK,WAAiB;AAChC,UAAI;AACF,YAAI,IAAK,IAAG,GAAG;AAAA,YAIV,IAAG,MAAM,MAAO;AAAA,MACvB,SAAS,GAAG;AACV,eAAO,aAAa,QAAQ,IAAI,IAAI,MAAM,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF,OAAO;AACL,eAAW,CAAC,KAAK,WAAiB;AAChC,UAAI;AAGF,YAAI,IAAK,QAAO,GAAG;AAAA,YAEd,SAAQ,MAAO;AAAA,MACtB,SAAS,GAAG;AACV,eAAO,aAAa,QAAQ,IAAI,IAAI,MAAM,CAAC;AAAA,MAC7C;AAAA,IACF;AAAA,EACF;AAEA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,SAAS,CAAC,UAAyB;AACjC,eAAS,MAAM,KAAK;AACpB,aAAO;AAAA,IACT;AAAA,IACA,QAAQ,CAAC,UAA6B;AACpC,eAAS,KAAK;AACd,aAAO;AAAA,IACT;AAAA,EACF;AACF;AAEO,SAAS,WACd,GACA,GAC6B;AAC7B,SAAO,KAAK;AACd;;;ACrGO,IAAM,oBAAN,cAAgC,MAAM;AAAA;AAAA;AAAA;AAAA,EAc3C,cAAc;AACZ,UAAM;AACN,SAAK,cAAc;AACnB,SAAK,MAAM,uBAAO,OAAO,IAAI;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA,EAoCS,WACP,QACA,MACA,KACA,UACS;AACT,UAAM,kBAAkB,sBAAsB,QAAQ;AACtD,QAAI,UAAU,QAAQ,QAAQ,QAAQ,OAAO,MAAM;AACjD,aAAO,gBAAgB,QAAQ,MAAS;AAAA,IAC1C;AACA,UAAM,SAAS,KAAK,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG;AAC7C,WAAO,gBAAgB,QAAQ,MAAM;AAAA,EACvC;AAAA;AAAA;AAAA;AAAA,EAmDS,YACP,QACA,MACA,wBAAsD,OACtD,UACS;AACT,QAAI,OAAO,0BAA0B,YAAY;AAC/C,iBAAW;AAGX,8BAAwB;AAAA,IAC1B;AAEA,UAAM,UAAoB,CAAC;AAC3B,UAAM,kBAAkB,sBAAgC,QAAQ;AAEhE,QAAI,CAAC,QAAQ;AACX,aAAO,gBAAgB,QAAQ,CAAC,CAAC;AAAA,IACnC;AAEA,QAAI;AAGJ,QAAI,CAAC,MAAM;AAET,oBAAc,SAAS,SAAS,aAAmB;AACjD,mBAAW,WAAW,aAAa;AACjC,gBAAM,YAAY,YAAY,OAAO;AACrC,qBAAW,OAAO,WAAW;AAC3B,kBAAM,QAAQ,UAAU,GAAG;AAC3B,gBAAI,OAAO;AACT,sBAAQ,KAAK,KAAK;AAAA,YACpB;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,oBAAc,SAAS,SAAS,aAAmB;AAGjD,mBAAW,cAAc,aAAa;AACpC,cAAI,UAAU,MAAM,UAAU,GAAG;AAC/B,kBAAM,YAAY,YAAY,UAAU;AACxC,uBAAW,OAAO,WAAW;AAC3B,oBAAM,QAAQ,UAAU,GAAG;AAC3B,kBAAI,OAAO;AACT,wBAAQ,KAAK,KAAK;AAAA,cACpB;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,UAAM,UAAU,cAAc,QAAQ,qBAAqB,KAAK,CAAC,MAAM;AACvE,UAAM,MAAM,KAAK;AACjB,YAAQ,QAAQ,CAAC,cAAc;AAC7B,YAAM,cAAc,IAAI,SAAS;AACjC,UAAI,CAAC,aAAa;AAChB;AAAA,MACF;AACA,kBAAY,WAAW;AAAA,IACzB,CAAC;AAED,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EACxC;AAAA;AAAA;AAAA;AAAA,EAgCS,UAAU,QAAgB,UAAmC;AACpE,UAAM,kBAAkB,sBAAiC,QAAQ;AAEjE,UAAM,EAAE,QAAQ,MAAM,IAAI,IAAI;AAI9B,QAAI,UAAU,QAAQ,QAAQ,QAAQ,OAAO,MAAM;AACjD,aAAO,gBAAgB,QAAQ,MAAS;AAAA,IAC1C;AAEA,UAAM,cACJ,KAAK,IAAI,MAAM,KACd,uBAAO,OAAO,IAAI;AAErB,SAAK,IAAI,MAAM,IAAI;AAEnB,UAAM,YACJ,YAAY,IAAI,KACf,uBAAO,OAAO,IAAI;AAErB,gBAAY,IAAI,IAAI;AAEpB,cAAU,GAAG,IAAI;AAEjB,WAAO,gBAAgB,QAAQ,MAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAkDS,aACP,YACA,WACA,UACS;AAKT,QAAI,SAAU,MAAK,UAAU,WAAW,QAAQ;AAAA,QAC3C,QAAO,KAAK,UAAU,SAAS;AAAA,EACtC;AAAA;AAAA;AAAA;AAAA,EA+BS,aACP,QACA,MACA,KACA,UACS;AACT,UAAM,kBAAkB,sBAAiC,QAAQ;AACjE,WAAO,KAAK,IAAI,MAAM,IAAI,IAAI,IAAI,GAAG;AACrC,WAAO,gBAAgB,QAAQ,MAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EA0BS,cACP,QACA,MACA,UACS;AACT,UAAM,kBAAkB,sBAAiC,QAAQ;AAEjE,UAAM,cAAc,KAAK,IAAI,MAAM;AACnC,QAAI,aAAa;AACf,UAAI,MAAM;AAER,eAAO,YAAY,IAAI;AAAA,MACzB,OAAO;AAEL,eAAO,KAAK,IAAI,MAAM;AAAA,MACxB;AAAA,IACF;AAEA,WAAO,gBAAgB,QAAQ,MAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAeS,iBAAiB,UAAmC;AAC3D,UAAM,kBAAkB,sBAAiC,QAAQ;AACjE,SAAK,MAAM,uBAAO,OAAO,IAAI;AAC7B,WAAO,gBAAgB,QAAQ,MAAS;AAAA,EAC1C;AAAA;AAAA;AAAA;AAAA,EAqBS,cAAc,UAAwC;AAC7D,UAAM,kBAAkB,sBAAgC,QAAQ;AAEhE,UAAM,UAAoB,CAAC;AAC3B,UAAM,MAAM,KAAK;AAEjB,UAAM,UAAU,OAAO,KAAK,GAAG;AAC/B,YAAQ,QAAQ,CAAC,WAAW;AAC1B,YAAM,cAAc,IAAI,MAAM,KAAK,CAAC;AACpC,YAAM,QAAQ,OAAO,KAAK,WAAW;AACrC,YAAM,QAAQ,CAAC,SAAS;AACtB,cAAM,YAAY,YAAY,IAAI,KAAK,CAAC;AACxC,cAAM,OAAO,OAAO,KAAK,SAAS;AAClC,aAAK,QAAQ,CAAC,QAAQ;AACpB,gBAAM,WAAW,UAAU,GAAG;AAC9B,cAAI,YAAY,MAAM;AACpB,oBAAQ,KAAK,QAAQ;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH,CAAC;AAAA,IACH,CAAC;AAID,YAAQ,KAAK,CAAC,GAAG,MAAM;AACrB,cAAQ,EAAE,iBAAiB,MAAM,EAAE,iBAAiB;AAAA,IACtD,CAAC;AAED,WAAO,gBAAgB,QAAQ,OAAO;AAAA,EACxC;AACF;;;ACncO,SAAS,iBAAiB,MAAwB;AACvD,SAAO,SAAS,IAAI,KAAK,SAAS;AACpC;AAQO,SAAS,cAAc,MAAwB;AACpD,SAAO,SAAS,MAAO,gBAAgB,UAAU,KAAK,SAAS,MAAM;AACvE;AAGO,SAAS,SAAS,MAAwB;AAC/C,SAAO,OAAO,SAAS,YAAY,gBAAgB;AACrD;AAGO,SAAS,SAAS,MAAwB;AAC/C,SAAO,eAAe,IAAI,MAAM;AAClC;AAaO,SAAS,SACd,MACA,aACA,SACM;AACN,MAAI,KAAM;AACV,QAAM,KAAK,OAAO,gBAAgB,aAAa,cAAc;AAC7D,MAAI,UAAU,OAAO,gBAAgB,aAAa,UAAU;AAG5D,MAAI,CAAC,SAAS,OAAO,EAAG,WAAU;AAElC,QAAM,MAAM,IAAI,eAAe,aAAa,OAAO,CAAC;AACpD,MAAI,GAAI,IAAG,GAAG;AAAA,MACT,OAAM;AACb;AAMO,IAAM,iBAAN,cAA6B,MAAM;AAAC;;;ACrFpC,IAAM,UAAU;;;ACShB,IAAM,qBAAqB;AAAA,EAChC,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,UAAU;AACZ;AACA,OAAO,OAAO,kBAAkB;AAEhC,IAAM,cAAc;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYjB,QAAQ,gBAAgB,EAAE,EAC1B,QAAQ,OAAO,EAAE,EACjB,KAAK;AACD,IAAM,qBAA6B,IAAI,OAAO,IAAI,WAAW,GAAG;AAEvE,IAAM,cAAc;AACb,IAAM,qBAA6B,IAAI,OAAO,IAAI,WAAW,GAAG;;;AC9BvE,SAAS,cAAc,QAAwB;AAC7C,SAAO,IAAI,IAAI,UAAU,MAAM,EAAE,EAAE;AACrC;AAiCO,SAAS,gBACd,YACoB;AACpB,MAAI,cAAc,MAAM;AACtB,WAAO;AAAA,EACT;AACA,MAAI,MAAM,WAAW,KAAK,EAAE,QAAQ,OAAO,EAAE;AAE7C,MAAI,mBAAmB,KAAK,GAAG,GAAG;AAChC,QAAI,CAAC,IAAI,WAAW,GAAG,GAAG;AACxB,YAAM,MAAM;AAAA,IACd;AACA,QAAI,CAAC,IAAI,SAAS,GAAG,GAAG;AACtB,YAAM,MAAM;AAAA,IACd;AACA,WAAO,cAAc,GAAG,EAAE,MAAM,GAAG,EAAE;AAAA,EACvC;AAIA,MAAI,mBAAmB,KAAK,GAAG,GAAG;AAChC,WAAO,cAAc,GAAG;AAAA,EAC1B;AAGA,SAAO,IAAI,YAAY;AACzB;;;ACvDO,SAAS,WAAW,MAAoB;AAC7C,SAAO,KAAK,YAAY;AAC1B;;;ACXA,IAAM,aAAa;AAEnB,IAAM,eAAe;AAAA,EACnB,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AAAA,EACL,KAAK;AACP;AAYA,SAAS,YACP,OACA,WACA,WACA,YACoB;AACpB,MAAI,QAAQ;AACZ,SAAO,QAAQ,MAAM,QAAQ;AAC3B,UAAM,IAAI,MAAM,WAAW,KAAK;AAEhC,QAAI,KAAK,MAAQ,KAAK,IAAM;AAC1B;AAAA,IACF;AACA;AAAA,EACF;AAGA,MAAI,QAAQ,aAAa,QAAQ,WAAW;AAC1C;AAAA,EACF;AAEA,MAAI,CAAC,cAAc,SAAS,MAAM,QAAQ;AACxC;AAAA,EACF;AAEA,SAAO,SAAS,MAAM,MAAM,GAAG,KAAK,GAAG,EAAE;AAC3C;AAEA,SAAS,UAAU,OAAqC;AACtD,QAAM,QAAQ,MAAM,MAAM,GAAG;AAC7B,QAAM,SAAS,CAAC,GAAG,GAAG,CAAC;AAQvB,MAAI,MAAM,WAAW,GAAG;AACtB;AAAA,EACF;AAEA,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAI1B,UAAM,aAAa,KAAK;AACxB,UAAM,UAAU,MAAM,CAAC;AACvB,QAAI,YAAY,QAAW;AACzB;AAAA,IACF;AACA,UAAM,MAAM,YAAY,SAAS,GAAG,GAAG,UAAU;AACjD,QAAI,QAAQ,QAAW;AACrB;AAAA,IACF;AACA,WAAO,CAAC,IAAI;AAAA,EACd;AAEA,SAAO;AACT;AAEA,SAAS,WAAW,OAAmC;AACrD,UAAQ,OAAO,KAAK,EAAE,MAAM,GAAG,CAAC,EAAE,YAAY;AAC9C,UAAQ,OAAO;AAAA,IACb,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB,KAAK;AACH,aAAO,aAAa;AAAA,IACtB;AACE;AAAA,EACJ;AACF;AAuGO,SAAS,UAAU,YAAgD;AACxE,MAAI,CAAC,YAAY;AACf;AAAA,EACF;AAMA,QAAM,SAAS,WAAW,MAAM,UAAU;AAE1C,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,MAAI;AAEJ,WAAS,IAAI,GAAG,IAAI,OAAO,QAAQ,KAAK;AACtC,UAAM,SAAS,OAAO,CAAC,KAAK,IAAI,KAAK;AACrC,QAAI,CAAC,MAAM,QAAQ;AACjB;AAAA,IACF;AAQA,QAAI,WAAW,QAAW;AACxB,YAAM,SAAS,UAAU,KAAK;AAC9B,UAAI,QAAQ;AACV,eAAO,OAAO,CAAC;AACf,iBAAS,OAAO,CAAC;AACjB,iBAAS,OAAO,CAAC;AACjB;AAAA,MACF;AAAA,IACF;AAOA,QAAI,eAAe,QAAW;AAE5B,YAAM,SAAS,YAAY,OAAO,GAAG,GAAG,IAAI;AAC5C,UAAI,WAAW,QAAW;AACxB,qBAAa;AACb;AAAA,MACF;AAAA,IACF;AAOA,QAAI,UAAU,QAAW;AACvB,YAAM,SAAS,WAAW,KAAK;AAC/B,UAAI,WAAW,QAAW;AACxB,gBAAQ;AACR;AAAA,MACF;AAAA,IACF;AAOA,QAAI,SAAS,QAAW;AAEtB,YAAM,SAAS,YAAY,OAAO,GAAG,GAAG,IAAI;AAC5C,UAAI,WAAW,QAAW;AACxB,eAAO;AAOP,YAAI,QAAQ,MAAM,QAAQ,IAAI;AAC5B,kBAAQ;AAAA,QACV,WAAW,QAAQ,KAAK,QAAQ,IAAI;AAClC,kBAAQ;AAAA,QACV;AAAA,MACF;AAAA,IACF;AAAA,EACF;AAeA,MACE,eAAe,UACf,UAAU,UACV,SAAS,UACT,SAAS,UACT,WAAW,UACX,WAAW,UACX,aAAa,KACb,aAAa,MACb,OAAO,QACP,OAAO,MACP,SAAS,MACT,SAAS,IACT;AACA;AAAA,EACF;AAEA,SAAO,IAAI,KAAK,KAAK,IAAI,MAAM,OAAO,YAAY,MAAM,QAAQ,MAAM,CAAC;AACzE;;;ACpTA,IAAM,gBAAgB;AAItB,IAAM,aAAa;AAGnB,IAAM,gBAAgB;AAKtB,IAAM,cAAc,CAAC,MAAM,MAAM,IAAI;AAErC,SAAS,eAAe,KAAqB;AAC3C,MAAe,cAAc,GAAG,EAAG,QAAO;AAC1C,WAAS,IAAI,GAAG,IAAI,YAAY,QAAQ,KAAK;AAC3C,UAAM,aAAa,YAAY,CAAC;AAChC,UAAM,gBAAgB,aAAa,IAAI,QAAQ,UAAU,IAAI;AAC7D,QAAI,kBAAkB,IAAI;AACxB,YAAM,IAAI,MAAM,GAAG,aAAa;AAAA,IAClC;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,gBACP,YACA,WACoB;AACpB,eAAa,eAAe,UAAU;AAEtC,MAAI,UAAU,WAAW,QAAQ,GAAG;AACpC,MAAI,WAAW;AACb,QAAI,YAAY,GAAG;AAEjB,mBAAa,WAAW,UAAU,CAAC;AACnC,gBAAU,WAAW,QAAQ,GAAG;AAAA,IAClC;AAAA,EACF,OAAO;AAEL,QAAI,WAAW,GAAG;AAEhB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,YAAY;AAChB,MAAI,WAAW,GAAG;AAChB,iBAAa;AACb,kBAAc,WAAW,KAAK;AAAA,EAChC,OAAO;AACL,iBAAa,WAAW,MAAM,GAAG,OAAO,EAAE,KAAK;AAC/C,kBAAc,WAAW,MAAM,UAAU,CAAC,EAAE,KAAK;AAAA,EACnD;AAEA,MAAI,cAAc,KAAK,UAAU,KAAK,cAAc,KAAK,WAAW,GAAG;AACrE,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,IAAI,OAAO;AACrB,IAAE,MAAM;AACR,IAAE,QAAQ;AACV,SAAO;AACT;AAaA,SAAS,MAAM,KAAa,SAAkD;AAC5E,MAAe,cAAc,GAAG,KAAK,CAAY,SAAS,GAAG,GAAG;AAC9D,WAAO;AAAA,EACT;AAEA,QAAM,IAAI,KAAK;AAGf,QAAM,YAAY,IAAI,QAAQ,GAAG;AACjC,QAAM,aAAa,cAAc,KAAK,MAAM,IAAI,MAAM,GAAG,SAAS;AAClE,QAAM,IAAI,gBAAgB,YAAY,SAAS,SAAS,KAAK;AAC7D,MAAI,CAAC,GAAG;AACN,WAAO;AAAA,EACT;AAEA,MAAI,cAAc,IAAI;AACpB,WAAO;AAAA,EACT;AAKA,QAAM,WAAW,IAAI,MAAM,YAAY,CAAC,EAAE,KAAK;AAI/C,MAAI,SAAS,WAAW,GAAG;AACzB,WAAO;AAAA,EACT;AAUA,QAAM,aAAa,SAAS,MAAM,GAAG;AACrC,SAAO,WAAW,QAAQ;AACxB,UAAM,MAAM,WAAW,MAAM,KAAK,IAAI,KAAK;AAC3C,QAAI,GAAG,WAAW,GAAG;AAEnB;AAAA,IACF;AACA,UAAM,SAAS,GAAG,QAAQ,GAAG;AAC7B,QAAI,QAAgB;AAEpB,QAAI,WAAW,IAAI;AACjB,eAAS;AACT,iBAAW;AAAA,IACb,OAAO;AACL,eAAS,GAAG,MAAM,GAAG,MAAM;AAC3B,iBAAW,GAAG,MAAM,SAAS,CAAC;AAAA,IAChC;AAEA,aAAS,OAAO,KAAK,EAAE,YAAY;AAEnC,QAAI,UAAU;AACZ,iBAAW,SAAS,KAAK;AAAA,IAC3B;AAEA,YAAQ,QAAQ;AAAA,MACd,KAAK;AACH,YAAI,UAAU;AACZ,gBAAM,MAAM,UAAU,QAAQ;AAG9B,cAAI,KAAK;AAGP,cAAE,UAAU;AAAA,UACd;AAAA,QACF;AACA;AAAA,MAEF,KAAK;AACH,YAAI,UAAU;AAIZ,cAAI,aAAa,KAAK,QAAQ,GAAG;AAC/B,kBAAM,QAAQ,SAAS,UAAU,EAAE;AAGnC,cAAE,UAAU,KAAK;AAAA,UACnB;AAAA,QACF;AACA;AAAA,MAEF,KAAK;AAGH,YAAI,UAAU;AAGZ,gBAAM,SAAS,SAAS,KAAK,EAAE,QAAQ,OAAO,EAAE;AAChD,cAAI,QAAQ;AAEV,cAAE,SAAS,OAAO,YAAY;AAAA,UAChC;AAAA,QACF;AACA;AAAA,MAEF,KAAK;AAWH,UAAE,OAAO,YAAY,SAAS,CAAC,MAAM,MAAM,WAAW;AACtD;AAAA,MAEF,KAAK;AAMH,UAAE,SAAS;AACX;AAAA,MAEF,KAAK;AACH,UAAE,WAAW;AACb;AAAA,MAEF,KAAK;AACH,gBAAQ,WAAW,SAAS,YAAY,IAAI,IAAI;AAAA,UAC9C,KAAK;AACH,cAAE,WAAW;AACb;AAAA,UACF,KAAK;AACH,cAAE,WAAW;AACb;AAAA,UACF,KAAK;AACH,cAAE,WAAW;AACb;AAAA,UACF;AACE,cAAE,WAAW;AACb;AAAA,QACJ;AACA;AAAA,MAEF;AACE,UAAE,aAAa,EAAE,cAAc,CAAC;AAChC,UAAE,WAAW,KAAK,EAAE;AACpB;AAAA,IACJ;AAAA,EACF;AAEA,SAAO;AACT;AAEA,SAAS,SAAS,KAAkC;AAClD,MAAI,CAAC,OAAkB,cAAc,GAAG,GAAG;AACzC,WAAO;AAAA,EACT;AAEA,MAAI;AACJ,MAAI,OAAO,QAAQ,UAAU;AAC3B,QAAI;AACF,YAAM,KAAK,MAAM,GAAG;AAAA,IACtB,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AAEL,UAAM;AAAA,EACR;AAEA,QAAM,IAAI,IAAI,OAAO;AACrB,SAAO,uBAAuB,QAAQ,CAAC,SAAS;AAC9C,QAAI,OAAO,OAAO,QAAQ,YAAY,WAAW,MAAM,GAAG,GAAG;AAC3D,YAAM,MAAM,IAAI,IAAI;AACpB,UAAI,QAAQ,QAAW;AACrB;AAAA,MACF;AAEA,UAAI,WAAW,MAAM,cAAc,KAAK,QAAQ,eAAe,IAAI,GAAG;AACpE;AAAA,MACF;AAEA,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,UAAU;AAC3B,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,cACE,OAAO,QAAQ,YACf,OAAO,QAAQ,YACf,eAAe,MACf;AACA,cAAE,IAAI,IAAI,IAAI,IAAI,KAAK,aAAa,aAAa,IAAI,KAAK,GAAG;AAAA,UAC/D,WAAW,QAAQ,MAAM;AACvB,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AACH,cACE,OAAO,QAAQ,YACf,QAAQ,cACR,QAAQ,aACR;AACA,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,WAAW;AAC5B,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AACH,cACE,MAAM,QAAQ,GAAG,KACjB,IAAI,MAAM,CAAC,SAAS,OAAO,SAAS,QAAQ,GAC5C;AACA,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,aAAa,QAAQ,MAAM;AAC5C,cAAE,IAAI,IAAI;AAAA,UACZ;AACA;AAAA,MACJ;AAAA,IACF;AAAA,EACF,CAAC;AAED,SAAO;AACT;AAqCA,IAAM,iBAAiB;AAAA;AAAA,EAErB,KAAK;AAAA,EACL,OAAO;AAAA,EACP,SAAS;AAAA,EACT,QAAQ;AAAA,EACR,QAAQ;AAAA,EACR,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,YAAY;AAAA;AAAA,EAEZ,UAAU;AAAA,EACV,eAAe;AAAA,EACf,UAAU;AAAA,EACV,cAAc;AAAA,EACd,UAAU;AACZ;AAOO,IAAM,UAAN,MAAM,QAAO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0FlB,YAAY,UAA+B,CAAC,GAAG;AAC7C,SAAK,MAAM,QAAQ,OAAO,eAAe;AACzC,SAAK,QAAQ,QAAQ,SAAS,eAAe;AAC7C,SAAK,UAAU,QAAQ,WAAW,eAAe;AACjD,SAAK,SAAS,QAAQ,UAAU,eAAe;AAC/C,SAAK,SAAS,QAAQ,UAAU,eAAe;AAC/C,SAAK,OAAO,QAAQ,QAAQ,eAAe;AAC3C,SAAK,SAAS,QAAQ,UAAU,eAAe;AAC/C,SAAK,WAAW,QAAQ,YAAY,eAAe;AACnD,SAAK,aAAa,QAAQ,cAAc,eAAe;AACvD,SAAK,WAAW,QAAQ,YAAY,eAAe;AACnD,SAAK,WAAW,QAAQ,YAAY,eAAe;AACnD,SAAK,gBAAgB,QAAQ,iBAAiB,eAAe;AAC7D,SAAK,eAAe,QAAQ,gBAAgB,eAAe;AAC3D,SAAK,WAAW,QAAQ,YAAY,eAAe;AAEnD,SAAK,WAAW,QAAQ,YAAY,oBAAI,KAAK;AAG7C,WAAO,eAAe,MAAM,iBAAiB;AAAA,MAC3C,cAAc;AAAA,MACd,YAAY;AAAA;AAAA,MACZ,UAAU;AAAA,MACV,OAAO,EAAE,QAAO;AAAA,IAClB,CAAC;AAED,SAAK,gBAAgB,QAAO;AAAA,EAC9B;AAAA,EAEA,CAAC,OAAO,IAAI,4BAA4B,CAAC,IAAY;AACnD,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,WAAW,KAAK,YAAY,OAAO,KAAK,SAAS,SAAS,IAAI;AACpE,UAAM,YACJ,KAAK,YAAY,KAAK,aAAa,aAC/B,GAAG,OAAO,MAAM,KAAK,SAAS,QAAQ,CAAC,CAAC,OACxC;AACN,UAAM,YACJ,KAAK,gBAAgB,KAAK,iBAAiB,aACvC,GAAG,OAAO,MAAM,KAAK,aAAa,QAAQ,CAAC,CAAC,OAC5C;AACN,WAAO,WAAW,KAAK,SAAS,CAAC,cAAc,QAAQ,UAAU,SAAS,UAAU,SAAS;AAAA,EAC/F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,SAA2B;AACzB,UAAM,MAAwB,CAAC;AAE/B,eAAW,QAAQ,QAAO,wBAAwB;AAChD,YAAM,MAAM,KAAK,IAAI;AAErB,UAAI,QAAQ,eAAe,IAAI,GAAG;AAChC;AAAA,MACF;AAEA,cAAQ,MAAM;AAAA,QACZ,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,UAAU;AAC3B,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AACH,cACE,OAAO,QAAQ,YACf,OAAO,QAAQ,YACf,eAAe,MACf;AACA,gBAAI,IAAI,IACN,OAAO,aAAa,aAAa,IAAI,KAAK,GAAG,EAAE,YAAY;AAAA,UAC/D,WAAW,QAAQ,MAAM;AACvB,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QACF,KAAK;AACH,cACE,OAAO,QAAQ,YACf,QAAQ,cACR,QAAQ,aACR;AACA,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,WAAW;AAC5B,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QACF,KAAK;AACH,cAAI,MAAM,QAAQ,GAAG,GAAG;AACtB,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,QACF,KAAK;AAAA,QACL,KAAK;AACH,cAAI,OAAO,QAAQ,aAAa,QAAQ,MAAM;AAC5C,gBAAI,IAAI,IAAI;AAAA,UACd;AACA;AAAA,MACJ;AAAA,IACF;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAA4B;AAC1B,WAAO,SAAS,KAAK,OAAO,CAAC;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAoB;AAClB,QAAI,CAAC,KAAK,SAAS,CAAC,cAAc,KAAK,KAAK,KAAK,GAAG;AAClD,aAAO;AAAA,IACT;AACA,QACE,KAAK,WAAW,cAChB,EAAE,KAAK,mBAAmB,SAC1B,CAAC,UAAU,KAAK,OAAO,GACvB;AACA,aAAO;AAAA,IACT;AACA,QACE,KAAK,UAAU,QACf,KAAK,WAAW,eACf,KAAK,WAAW,eAAe,KAAK,UAAU,IAC/C;AACA,aAAO;AAAA,IACT;AACA,QAAI,KAAK,QAAQ,QAAQ,CAAC,WAAW,KAAK,KAAK,IAAI,GAAG;AACpD,aAAO;AAAA,IACT;AAEA,UAAM,UAAU,KAAK,QAAQ;AAC7B,QAAI,SAAS;AACX,UAAI,QAAQ,MAAM,KAAK,GAAG;AACxB,eAAO;AAAA,MACT;AACA,YAAM,SAAS,gBAAgB,OAAO;AACtC,UAAI,UAAU,MAAM;AAElB,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,WAAW,KAA0B;AACnC,QAAI,eAAe,MAAM;AACvB,WAAK,UAAU;AAAA,IACjB,OAAO;AACL,WAAK,UAAU,UAAU,GAAG,KAAK;AAAA,IACnC;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,UAAU,KAAmB;AAC3B,QAAI,QAAQ,UAAU;AACpB,WAAK,SAAS;AAAA,IAChB,WAAW,QAAQ,WAAW;AAC5B,WAAK,SAAS;AAAA,IAChB,OAAO;AACL,WAAK,SAAS;AAAA,IAChB;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAuB;AACrB,UAAM,MAAM,KAAK,SAAS;AAC1B,QAAI,KAAK,KAAK;AACZ,aAAO,GAAG,KAAK,GAAG,IAAI,GAAG;AAAA,IAC3B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAmB;AACjB,QAAI,MAAM,KAAK,aAAa;AAE5B,QAAI,KAAK,WAAW,YAAY;AAC9B,UAAI,KAAK,mBAAmB,MAAM;AAChC,eAAO,aAAa,WAAW,KAAK,OAAO,CAAC;AAAA,MAC9C;AAAA,IACF;AAEA,QAAI,KAAK,UAAU,QAAQ,KAAK,UAAU,UAAU;AAClD,aAAO,aAAa,OAAO,KAAK,MAAM,CAAC;AAAA,IACzC;AAEA,QAAI,KAAK,UAAU,CAAC,KAAK,UAAU;AACjC,aAAO,YAAY,KAAK,MAAM;AAAA,IAChC;AACA,QAAI,KAAK,MAAM;AACb,aAAO,UAAU,KAAK,IAAI;AAAA,IAC5B;AAEA,QAAI,KAAK,QAAQ;AACf,aAAO;AAAA,IACT;AACA,QAAI,KAAK,UAAU;AACjB,aAAO;AAAA,IACT;AACA,QAAI,KAAK,YAAY,KAAK,aAAa,QAAQ;AAC7C,UACE,KAAK,SAAS,YAAY,MAC1B,QAAO,kBAAkB,IAAI,YAAY,GACzC;AACA,eAAO,cAAc,QAAO,kBAAkB,GAAG;AAAA,MACnD,WACE,KAAK,SAAS,YAAY,MAC1B,QAAO,kBAAkB,OAAO,YAAY,GAC5C;AACA,eAAO,cAAc,QAAO,kBAAkB,MAAM;AAAA,MACtD,OAAO;AACL,eAAO,cAAc,KAAK,QAAQ;AAAA,MACpC;AAAA,IACF;AACA,QAAI,KAAK,YAAY;AACnB,WAAK,WAAW,QAAQ,CAAC,QAAQ;AAC/B,eAAO,KAAK,GAAG;AAAA,MACjB,CAAC;AAAA,IACH;AAEA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAeA,IAAI,MAAc,KAAK,IAAI,GAAW;AAUpC,QAAI,KAAK,UAAU,QAAQ,OAAO,KAAK,WAAW,UAAU;AAC1D,aAAO,KAAK,UAAU,IAAI,IAAI,KAAK,SAAS;AAAA,IAC9C;AAEA,UAAM,UAAU,KAAK;AACrB,QAAI,YAAY,YAAY;AAC1B,aAAO;AAAA,IACT;AAEA,YAAQ,SAAS,QAAQ,KAAK,QAAQ,OAAO,KAAK,IAAI;AAAA,EACxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,KAAgC;AAEzC,QAAI,KAAK,UAAU,MAAM;AACvB,YAAM,aAAa,OAAO,KAAK,gBAAgB,oBAAI,KAAK;AACxD,YAAM,SAAS,OAAO,KAAK,WAAW,WAAW,KAAK,SAAS;AAC/D,YAAM,MAAM,UAAU,IAAI,YAAY,SAAS;AAC/C,UAAI,eAAe,YAAY;AAC7B,eAAO;AAAA,MACT;AACA,aAAO,WAAW,QAAQ,IAAI;AAAA,IAChC;AAEA,QAAI,KAAK,WAAW,YAAY;AAC9B,aAAO;AAAA,IACT;AAEA,WAAO,KAAK,UAAU,KAAK,QAAQ,QAAQ,IAAI;AAAA,EACjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,WAAW,KAA8B;AACvC,UAAM,WAAW,KAAK,WAAW,GAAG;AACpC,QAAI,YAAY,UAAU;AASxB,aAAO,oBAAI,KAAK,YAAa;AAAA,IAC/B,WAAW,YAAY,WAAW;AAChC,aAAO,oBAAI,KAAK,CAAC;AAAA,IACnB,OAAO;AACL,aAAO,YAAY,SAAY,SAAY,IAAI,KAAK,QAAQ;AAAA,IAC9D;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAwB;AAEtB,WAAO,KAAK,UAAU,QAAQ,KAAK,WAAW;AAAA,EAChD;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,sBAA0C;AAExC,WAAO,gBAAgB,KAAK,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,UAA8B;AAC5B,WAAO,gBAAgB,KAAK,MAAM;AAAA,EACpC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAkCA,OAAO,MAAM,KAAa,SAAkD;AAC1E,WAAO,MAAM,KAAK,OAAO;AAAA,EAC3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA2BA,OAAO,SAAS,KAAkC;AAChD,WAAO,SAAS,GAAG;AAAA,EACrB;AAyCF;AA7kBa,QAsiBI,iBAAiB;AAAA;AAAA;AAAA;AAtiBrB,QA2iBJ,gBAAgB;AAAA,EACrB,QAAQ;AAAA,EACR,KAAK;AAAA,EACL,MAAM;AACR;AAAA;AAAA;AAAA;AA/iBW,QAojBJ,oBAAoB;AAAA,EACzB,QAAQ;AAAA,EACR,KAAK;AACP;AAAA;AAAA;AAAA;AAAA;AAvjBW,QA6jBJ,yBAAyB;AAAA,EAC9B;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AACF;AA5kBK,IAAM,SAAN;;;ACtaP,IAAM,WAAW;AA0DV,SAAS,cAAc,GAAW,GAAmB;AAC1D,MAAI;AAGJ,QAAM,WAAW,EAAE,OAAO,EAAE,KAAK,SAAS;AAC1C,QAAM,WAAW,EAAE,OAAO,EAAE,KAAK,SAAS;AAC1C,QAAM,WAAW;AACjB,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AAGA,QAAM,QACJ,EAAE,YAAY,EAAE,oBAAoB,OAAO,EAAE,SAAS,QAAQ,IAAI;AACpE,QAAM,QACJ,EAAE,YAAY,EAAE,oBAAoB,OAAO,EAAE,SAAS,QAAQ,IAAI;AACpE,QAAM,QAAQ;AACd,MAAI,QAAQ,GAAG;AACb,WAAO;AAAA,EACT;AAGA,SAAO,EAAE,iBAAiB,MAAM,EAAE,iBAAiB;AAEnD,SAAO;AACT;;;ACjDO,SAAS,YAAY,MAAiC;AAG3D,MAAI,CAAC,QAAQ,KAAK,MAAM,GAAG,CAAC,MAAM,KAAK;AACrC,WAAO;AAAA,EACT;AAIA,MAAI,SAAS,KAAK;AAChB,WAAO;AAAA,EACT;AAEA,QAAM,aAAa,KAAK,YAAY,GAAG;AACvC,MAAI,eAAe,GAAG;AACpB,WAAO;AAAA,EACT;AAIA,SAAO,KAAK,MAAM,GAAG,UAAU;AACjC;;;ACtDA,IAAM,qBACJ;AAsCK,SAAS,YACd,QACA,cACA,cACqB;AACrB,MAAI,UAAU,QAAQ,gBAAgB,MAAM;AAC1C,WAAO;AAAA,EACT;AAEA,MAAI;AACJ,MAAI;AAEJ,MAAI,iBAAiB,OAAO;AAC1B,WAAO,gBAAgB,MAAM;AAC7B,cAAU,gBAAgB,YAAY;AAAA,EACxC,OAAO;AACL,WAAO;AACP,cAAU;AAAA,EACZ;AAEA,MAAI,QAAQ,QAAQ,WAAW,MAAM;AACnC,WAAO;AAAA,EACT;AAWA,MAAI,QAAQ,SAAS;AACnB,WAAO;AAAA,EACT;AAKA,QAAM,MAAM,KAAK,YAAY,OAAO;AACpC,MAAI,OAAO,GAAG;AACZ,WAAO;AAAA,EACT;AAKA,MAAI,KAAK,WAAW,QAAQ,SAAS,KAAK;AACxC,WAAO;AAAA,EACT;AAIA,MAAI,KAAK,UAAU,MAAM,GAAG,GAAG,MAAM,KAAK;AACxC,WAAO;AAAA,EACT;AAGA,SAAO,CAAC,mBAAmB,KAAK,IAAI;AACtC;;;ACxGA,SAAS,aAAa,SAA0B;AAE9C,QAAM,SAAS,QAAQ,MAAM,GAAG;AAChC,SACE,OAAO,WAAW,KAClB,OAAO,CAAC,MAAM,UACd,SAAS,OAAO,CAAC,GAAG,EAAE,MAAM;AAEhC;AAEA,SAAS,aAAa,SAA0B;AAK9C,SAAO,YAAY;AACrB;AAEA,SAAS,yBAAyB,WAA4B;AAC5D,SAAO,UAAU,SAAS,YAAY;AACxC;AAEA,SAAS,gBAAgB,MAAuB;AAC9C,QAAM,YAAY,KAAK,YAAY;AACnC,SAAO,cAAc,eAAe,yBAAyB,SAAS;AACxE;AAGA,SAAS,eAAe,MAAsB;AAC5C,MAAI,KAAK,UAAU,KAAK,KAAK,WAAW,GAAG,KAAK,KAAK,SAAS,GAAG,GAAG;AAClE,WAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;AAAA,EAC1C;AACA,SAAO;AACT;AAgBO,SAAS,yBACd,UACA,qBAA8B,MACrB;AACT,MAAI;AAGJ,MAAI,OAAO,aAAa,UAAU;AAChC,QAAI;AACF,YAAM,IAAI,IAAI,QAAQ;AAAA,IACxB,QAAQ;AACN,aAAO;AAAA,IACT;AAAA,EACF,OAAO;AACL,UAAM;AAAA,EACR;AAEA,QAAM,SAAS,IAAI,SAAS,QAAQ,KAAK,EAAE,EAAE,YAAY;AACzD,QAAM,WAAW,eAAe,IAAI,QAAQ,EAAE,QAAQ,QAAQ,EAAE;AAEhE,MACE,WAAW,WACX,WAAW,OACX;AACA,WAAO;AAAA,EACT;AAEA,MAAI,CAAC,oBAAoB;AACvB,WAAO;AAAA,EACT;AAGA,MAAI,mBAAmB,KAAK,QAAQ,GAAG;AACrC,WAAO,aAAa,QAAQ;AAAA,EAC9B;AAEA,MAAI,mBAAmB,KAAK,QAAQ,GAAG;AACrC,WAAO,aAAa,QAAQ;AAAA,EAC9B;AAKA,SAAO,gBAAgB,QAAQ;AACjC;;;ACpEA,IAAM,0BAA4C;AAAA,EAChD,OAAO;AAAA,EACP,iBAAiB;AAAA,EACjB,aAAa;AAAA,EACb,MAAM;AACR;AAuDA,IAAM,0BAA6C;AAAA,EACjD,MAAM;AAAA,EACN,QAAQ;AAAA,EACR,UAAU;AAAA,EACV,iBAAiB;AAAA,EACjB,MAAM;AACR;AA4GA,IAAM,4BACJ;AAQF,SAAS,iBAAiB,KAA0B;AAClD,MACE,OACA,OAAO,QAAQ,YACf,cAAc,OACd,OAAO,IAAI,aAAa,YACxB,cAAc,OACd,OAAO,IAAI,aAAa,YACxB,cAAc,OACd,OAAO,IAAI,aAAa,UACxB;AACA,WAAO;AAAA,MACL,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,MACd,UAAU,IAAI;AAAA,IAChB;AAAA,EACF,WAAW,OAAO,QAAQ,UAAU;AAClC,QAAI;AACF,aAAO,IAAI,IAAI,UAAU,GAAG,CAAC;AAAA,IAC/B,QAAQ;AACN,aAAO,IAAI,IAAI,GAAG;AAAA,IACpB;AAAA,EACF,OAAO;AACL,UAAM,IAAI,eAAe,wCAAwC;AAAA,EACnE;AACF;AAGA,SAAS,qBAAqB,OAA0C;AACtE,QAAM,UAAU,OAAO,KAAK,EAAE,YAAY;AAC1C,MAAI,YAAY,UAAU,YAAY,SAAS,YAAY,UAAU;AACnE,WAAO;AAAA,EACT,OAAO;AACL,WAAO;AAAA,EACT;AACF;AASA,SAAS,2BAA2B,QAAyB;AAC3D,QAAM,yBACJ,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,WAAW,WAAW;AACrE,SAAO,CAAC,0BAA0B,OAAO;AAC3C;AAaA,SAAS,yBAAyB,QAAyB;AACzD,QAAM,uBACJ,OAAO,OAAO,QAAQ,YAAY,OAAO,IAAI,WAAW,SAAS;AACnE,SACE,CAAC,wBACD;AAAA,IACE,OAAO,UACL,OAAO,YACP,OAAO,QAAQ,QACf,OAAO,SAAS;AAAA,EACpB;AAEJ;AAIA,SAAS,4BACP,gBACqB;AACrB,QAAM,2BAA2B,eAAe,YAAY;AAE5D,UAAQ,0BAA0B;AAAA,IAChC,KAAK,mBAAmB;AAAA,IACxB,KAAK,mBAAmB;AAAA,IACxB,KAAK,mBAAmB;AACtB,aAAO;AAAA,IACT;AACE,aAAO,mBAAmB;AAAA,EAC9B;AACF;AASO,IAAM,YAAN,MAAM,WAAU;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EA0BrB,YACE,OACA,SACA;AACA,QAAI,OAAO,YAAY,WAAW;AAChC,gBAAU,EAAE,sBAAsB,QAAQ;AAAA,IAC5C;AACA,SAAK,uBAAuB,SAAS,wBAAwB;AAC7D,SAAK,kBAAkB,SAAS,aAAa;AAC7C,SAAK,wBAAwB,SAAS,yBAAyB;AAC/D,SAAK,qBAAqB,SAAS,sBAAsB;AACzD,SAAK,iBAAiB;AAAA,MACpB,SAAS,kBAAkB;AAAA,IAC7B;AACA,SAAK,QAAQ,SAAS,IAAI,kBAAkB;AAAA,EAC9C;AAAA,EAEQ,SACN,IACe;AACf,QAAI,CAAC,KAAK,MAAM,aAAa;AAC3B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,QAAI,UAAwB;AAC5B,QAAI,aAA4B;AAEhC,QAAI;AACF,SAAG,KAAK,MAAM,CAAC,OAAqB,WAAe;AACjD,kBAAU;AACV,qBAAa;AAAA,MACf,CAAC;AAAA,IACH,SAAS,KAAK;AACZ,gBAAU;AAAA,IACZ;AAEA,QAAI,QAAS,OAAM;AAEnB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA,EAqFA,UACE,QACA,KACA,SACA,UACS;AACT,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU;AAAA,IACZ;AACA,UAAM,kBAAkB,sBAAsB,QAAQ;AACtD,UAAM,KAAK,gBAAgB;AAC3B,QAAI;AAEJ,QAAI;AACF,UAAI,OAAO,QAAQ,UAAU;AAC3B,QAAW;AAAA,UACE,iBAAiB,GAAG;AAAA,UAC/B;AAAA,UACA,aAAa,OAAO;AAAA,QACtB;AAAA,MACF;AAEA,gBAAU,iBAAiB,GAAG;AAE9B,UAAI,OAAO,QAAQ,YAAY;AAC7B,eAAO,gBAAgB,OAAO,IAAI,MAAM,sBAAsB,CAAC;AAAA,MACjE;AAEA,UAAI,OAAO,YAAY,YAAY;AACjC,kBAAU;AAAA,MACZ;AAEA,MAAW,SAAS,OAAO,OAAO,YAAY,EAAE;AAEhD,UACE,CAAY,iBAAiB,MAAM,KACnC,CAAY,SAAS,MAAM,KAC3B,kBAAkB,UAClB,OAAO,UAAU,GACjB;AACA,eAAO,gBAAgB,QAAQ,MAAS;AAAA,MAC1C;AAAA,IACF,SAAS,KAAK;AACZ,aAAO,gBAAgB,OAAO,GAAY;AAAA,IAC5C;AAEA,UAAM,OAAO,gBAAgB,QAAQ,QAAQ,KAAK;AAClD,UAAM,QAAQ,SAAS,SAAS,KAAK;AAErC,QAAI,kBAAkB;AACtB,QAAI,SAAS,iBAAiB;AAC5B,wBAAkB,qBAAqB,QAAQ,eAAe;AAC9D,UAAI,CAAC,iBAAiB;AACpB,eAAO,gBAAgB,OAAO,IAAI,MAAM,yBAAyB,CAAC;AAAA,MACpE;AAAA,IACF;AAGA,QAAI,OAAO,WAAW,YAAY,kBAAkB,QAAQ;AAC1D,YAAM,eAAe,OAAO,MAAM,OAAO,SAAS,GAAG,EAAE,MAAa,CAAC;AACrE,UAAI,CAAC,cAAc;AACjB,cAAM,MAAM,IAAI,MAAM,wBAAwB;AAC9C,eAAO,SAAS,cACZ,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,GAAG;AAAA,MAChC;AACA,eAAS;AAAA,IACX,WAAW,EAAE,kBAAkB,SAAS;AAGtC,YAAM,MAAM,IAAI;AAAA,QACd;AAAA,MACF;AAEA,aAAO,SAAS,cACZ,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,GAAG;AAAA,IAChC;AAGA,UAAM,MAAM,SAAS,OAAO,oBAAI,KAAK;AAOrC,QAAI,KAAK,wBAAwB,OAAO,QAAQ;AAC9C,UAAI;AACF,cAAM,UAAU,OAAO,QAAQ;AAC/B,cAAM,SACJ,OAAO,YAAY,WACf,gBAAgB,SAAS;AAAA,UACvB,uBAAuB,KAAK;AAAA,UAC5B,aAAa,SAAS;AAAA,QACxB,CAAC,IACD;AACN,YAAI,UAAU,QAAQ,CAAC,mBAAmB,KAAK,OAAO,MAAM,GAAG;AAE7D,gBAAM,MAAM,IAAI,MAAM,0CAA0C;AAEhE,iBAAO,SAAS,cACZ,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,GAAG;AAAA,QAChC;AAAA,MAKF,SAAS,KAAU;AACjB,eAAO,SAAS,cACZ,gBAAgB,QAAQ,MAAS;AAAA;AAAA,UAEjC,gBAAgB,OAAO,GAAG;AAAA;AAAA,MAChC;AAAA,IACF;AAGA,QAAI,OAAO,QAAQ;AACjB,UACE,CAAC,YAAY,QAAQ,QAAW,OAAO,QAAQ,KAAK,QAAW,KAAK,GACpE;AACA,cAAM,MAAM,IAAI;AAAA,UACd,4CACE,OAAO,QAAQ,KAAK,MACtB,YAAY,QAAQ,MAAM;AAAA,QAC5B;AACA,eAAO,SAAS,cACZ,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,GAAG;AAAA,MAChC;AAEA,UAAI,OAAO,YAAY,MAAM;AAE3B,eAAO,WAAW;AAAA,MACpB;AAAA,IACF,OAAO;AACL,aAAO,WAAW;AAClB,aAAO,SAAS;AAAA,IAClB;AAKA,QAAI,CAAC,OAAO,QAAQ,OAAO,KAAK,CAAC,MAAM,KAAK;AAC1C,aAAO,OAAO,YAAY,QAAQ,QAAQ;AAC1C,aAAO,gBAAgB;AAAA,IACzB;AAMA,QAAI,SAAS,SAAS,SAAS,OAAO,UAAU;AAC9C,YAAM,MAAM,IAAI,MAAM,+CAA+C;AACrE,aAAO,QAAQ,cACX,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,GAAG;AAAA,IAChC;AAGA,QACE,OAAO,aAAa,UACpB,OAAO,aAAa,UACpB,iBACA;AAKA,UAAI,oBAAoB,QAAQ;AAC9B,cAAM,MAAM,IAAI;AAAA,UACd;AAAA,QACF;AACA,eAAO,SAAS,cACZ,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,GAAG;AAAA,MAChC;AAAA,IACF;AAGA,UAAM,+BACJ,KAAK,mBAAmB,mBAAmB;AAC7C,UAAM,yBACJ,KAAK,mBAAmB,mBAAmB;AAE7C,QAAI,CAAC,wBAAwB;AAC3B,UAAI,aAAa;AACjB,UAAI;AAEJ,UAAI,CAAC,2BAA2B,MAAM,GAAG;AACvC,qBAAa;AACb,mBAAW;AAAA,MACb,WAAW,CAAC,yBAAyB,MAAM,GAAG;AAE5C,qBAAa;AACb,mBACE;AAAA,MACJ;AACA,UAAI,YAAY;AACd,eAAO,SAAS,eAAe,+BAC3B,gBAAgB,QAAQ,MAAS,IACjC,gBAAgB,OAAO,IAAI,MAAM,QAAQ,CAAC;AAAA,MAChD;AAAA,IACF;AAEA,UAAM,QAAQ,KAAK;AAKnB,QAAI,CAAC,MAAM,cAAc;AACvB,YAAM,eAAe,eACnB,YACA,WACAC,KACe;AACf,eAAO,KAAK,UAAU,SAAS,EAAE;AAAA,UAC/B,MAAMA,MAAK,IAAI;AAAA,UACf,CAAC,UAAmBA,MAAK,KAAc;AAAA,QACzC;AAAA,MACF;AAAA,IACF;AAEA,UAAM,aAA2C,SAASC,YACxD,KACA,WACM;AACN,UAAI,KAAK;AACP,WAAG,GAAG;AACN;AAAA,MACF;AAEA,YAAM,OAAsB,SAAUC,MAAK;AACzC,YAAIA,MAAK;AACP,aAAGA,IAAG;AAAA,QACR,WAAW,OAAO,WAAW,UAAU;AACrC,aAAG,MAAM,MAAS;AAAA,QACpB,OAAO;AACL,aAAG,MAAM,MAAM;AAAA,QACjB;AAAA,MACF;AAEA,UAAI,WAAW;AAGb,YACE,WACA,UAAU,WACV,QAAQ,SAAS,SACjB,UAAU,UACV;AAEA,gBAAM,IAAI,MAAM,mDAAmD;AACnE,cAAI,QAAQ,YAAa,IAAG,MAAM,MAAS;AAAA,cACtC,IAAG,GAAG;AACX;AAAA,QACF;AACA,YAAI,kBAAkB,QAAQ;AAC5B,iBAAO,WAAW,UAAU;AAE5B,iBAAO,gBAAgB,UAAU;AAEjC,iBAAO,eAAe;AAEtB,gBAAM,aAAa,WAAW,QAAQ,IAAI;AAAA,QAC5C;AAAA,MACF,OAAO;AACL,YAAI,kBAAkB,QAAQ;AAC5B,iBAAO,WAAW,OAAO,eAAe;AACxC,gBAAM,UAAU,QAAQ,IAAI;AAAA,QAC9B;AAAA,MACF;AAAA,IACF;AAGA,UAAM,WAAW,OAAO,QAAQ,OAAO,MAAM,OAAO,KAAK,UAAU;AACnE,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAsBA,cACE,QACA,KACA,SACoB;AACpB,UAAM,cAAc,UAChB,KAAK,UAAU,KAAK,MAAM,QAAQ,KAAK,OAAO,IAC9C,KAAK,UAAU,KAAK,MAAM,QAAQ,GAAG;AACzC,WAAO,KAAK,SAAS,WAAW;AAAA,EAClC;AAAA;AAAA;AAAA;AAAA,EAqEA,WACE,KACA,SACA,UACS;AAET,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU;AAAA,IACZ,WAAW,YAAY,QAAW;AAChC,gBAAU;AAAA,IACZ;AACA,UAAM,kBAAkB,sBAAsB,QAAQ;AACtD,UAAM,KAAK,gBAAgB;AAC3B,QAAI;AAEJ,QAAI;AACF,UAAI,OAAO,QAAQ,UAAU;AAC3B,QAAW,SAAoB,iBAAiB,GAAG,GAAG,IAAI,GAAG;AAAA,MAC/D;AAEA,gBAAU,iBAAiB,GAAG;AAE9B,MAAW;AAAA,QACE,SAAS,OAAO;AAAA,QAC3B;AAAA,QACA,aAAa,OAAO;AAAA,MACtB;AAEA,MAAW,SAAS,OAAO,OAAO,YAAY,EAAE;AAAA,IAClD,SAAS,gBAAgB;AACvB,aAAO,gBAAgB,OAAO,cAAuB;AAAA,IACvD;AAEA,UAAM,OAAO,gBAAgB,QAAQ,QAAQ;AAC7C,UAAM,OAAO,QAAQ,YAAY;AAMjC,UAAM,yBAAyB;AAAA,MAC7B;AAAA,MACA,KAAK;AAAA,IACP;AAEA,QAAI,gBAAgB;AACpB,QAAI,QAAQ,iBAAiB;AAC3B,YAAM,kBAAkB,qBAAqB,QAAQ,eAAe;AACpE,UAAI,mBAAmB,MAAM;AAC3B,eAAO,gBAAgB,OAAO,IAAI,MAAM,yBAAyB,CAAC;AAAA,MACpE;AACA,sBAAgB,OAAO,cAAc,eAAe;AACpD,UAAI,CAAC,eAAe;AAClB,eAAO,gBAAgB,OAAO,IAAI,MAAM,yBAAyB,CAAC;AAAA,MACpE;AAAA,IACF;AAEA,UAAM,OAAO,QAAQ,QAAQ;AAE7B,UAAM,MAAM,KAAK,IAAI;AACrB,UAAM,cAAc,QAAQ,UAAU;AACtC,UAAM,WAAW,QAAQ,YAAY;AACrC,UAAM,QAAQ,KAAK;AAEnB,aAAS,eAAe,GAAoB;AAO1C,UAAI,EAAE,UAAU;AACd,YAAI,EAAE,UAAU,MAAM;AACpB,iBAAO;AAAA,QACT;AAAA,MACF,OAAO;AACL,YAAI,CAAC,YAAY,QAAQ,QAAW,EAAE,UAAU,QAAW,KAAK,GAAG;AACjE,iBAAO;AAAA,QACT;AAAA,MACF;AAGA,UAAI,CAAC,YAAY,OAAO,EAAE,SAAS,YAAY,CAAC,UAAU,MAAM,EAAE,IAAI,GAAG;AACvE,eAAO;AAAA,MACT;AAIA,UAAI,EAAE,UAAU,CAAC,wBAAwB;AACvC,eAAO;AAAA,MACT;AAIA,UAAI,EAAE,YAAY,CAAC,MAAM;AACvB,eAAO;AAAA,MACT;AAGA,UAAI,eAAe;AACjB,YAAI;AACJ,YAAI,EAAE,aAAa,OAAO;AACxB,wBAAc,OAAO,cAAc;AAAA,QACrC,WAAW,EAAE,aAAa,UAAU;AAClC,wBAAc,OAAO,cAAc;AAAA,QACrC,OAAO;AACL,wBAAc,OAAO,cAAc;AAAA,QACrC;AACA,YAAI,cAAc,eAAe;AAE/B,iBAAO;AAAA,QACT;AAAA,MACF;AAIA,YAAM,aAAa,EAAE,WAAW;AAChC,UAAI,eAAe,cAAc,UAAa,cAAc,KAAK;AAC/D,cAAM,aAAa,EAAE,QAAQ,EAAE,MAAM,EAAE,KAAK,MAAM;AAAA,QAAC,CAAC;AACpD,eAAO;AAAA,MACT;AAEA,aAAO;AAAA,IACT;AAEA,UAAM;AAAA,MACJ;AAAA,MACA,WAAW,OAAO;AAAA,MAClB,KAAK;AAAA,MACL,CAAC,KAAK,YAAkB;AACtB,YAAI,KAAK;AACP,aAAG,GAAG;AACN;AAAA,QACF;AAEA,YAAI,WAAW,MAAM;AACnB,aAAG,MAAM,CAAC,CAAC;AACX;AAAA,QACF;AAEA,kBAAU,QAAQ,OAAO,cAAc;AAGvC,YAAI,UAAU,WAAW,QAAQ,SAAS,OAAO;AAC/C,oBAAU,QAAQ,KAAK,aAAa;AAAA,QACtC;AAGA,cAAMC,OAAM,oBAAI,KAAK;AACrB,mBAAW,UAAU,SAAS;AAC5B,iBAAO,eAAeA;AAAA,QACxB;AAGA,WAAG,MAAM,OAAO;AAAA,MAClB;AAAA,IACF;AAEA,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,eAAe,KAAa,SAAuC;AACjE,WAAO,KAAK,SAAS,KAAK,WAAW,KAAK,MAAM,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,EACrE;AAAA;AAAA;AAAA;AAAA,EA0CA,gBACE,KACA,SACA,UACS;AACT,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU;AAAA,IACZ;AACA,UAAM,kBAAkB,sBAAsB,QAAQ;AACtD,UAAM,OAA2B,SAAU,KAAK,SAAS;AACvD,UAAI,KAAK;AACP,wBAAgB,SAAS,GAAG;AAAA,MAC9B,OAAO;AACL,wBAAgB;AAAA,UACd;AAAA,UACA,SACI,KAAK,aAAa,EACnB,IAAI,CAAC,MAAM,EAAE,aAAa,CAAC,EAC3B,KAAK,IAAI;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAEA,SAAK,WAAW,KAAK,SAAS,IAAI;AAClC,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,oBAAoB,KAAa,SAAqC;AACpE,WACE,KAAK;AAAA,MACH,UACI,KAAK,gBAAgB,KAAK,MAAM,KAAK,OAAO,IAC5C,KAAK,gBAAgB,KAAK,MAAM,GAAG;AAAA,IACzC,KAAK;AAAA,EAET;AAAA;AAAA;AAAA;AAAA,EAgDA,oBACE,KACA,SACA,UACS;AACT,QAAI,OAAO,YAAY,YAAY;AACjC,iBAAW;AACX,gBAAU;AAAA,IACZ;AACA,UAAM,kBAAkB;AAAA,MACtB;AAAA,IACF;AAEA,UAAM,OAAuC,SAAU,KAAK,SAAS;AACnE,UAAI,KAAK;AACP,wBAAgB,SAAS,GAAG;AAAA,MAC9B,OAAO;AACL,wBAAgB;AAAA,UACd;AAAA,UACA,SAAS,IAAI,CAAC,MAAM;AAClB,mBAAO,EAAE,SAAS;AAAA,UACpB,CAAC;AAAA,QACH;AAAA,MACF;AAAA,IACF;AAEA,SAAK,WAAW,KAAK,SAAS,IAAI;AAClC,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,wBACE,KACA,UAA6B,CAAC,GACpB;AACV,WACE,KAAK,SAAS,KAAK,oBAAoB,KAAK,MAAM,KAAK,OAAO,CAAC,KAAK,CAAC;AAAA,EAEzE;AAAA;AAAA;AAAA;AAAA,EAcA,UAAU,UAAmD;AAC3D,UAAM,kBAAkB,sBAA2C,QAAQ;AAE3E,QAAI,OAAsB,KAAK,MAAM,YAAY;AACjD,QAAe,SAAS,IAAI,GAAG;AAC7B,aAAO;AAAA,IACT;AAGA,UAAM,aAAkC;AAAA;AAAA;AAAA;AAAA,MAItC,SAAS,gBAAgB,OAAO;AAAA;AAAA,MAGhC,WAAW;AAAA;AAAA,MAGX,sBAAsB,KAAK;AAAA,MAC3B,iBAAiB,KAAK;AAAA,MACtB,uBAAuB,KAAK;AAAA,MAC5B,gBAAgB,4BAA4B,KAAK,cAAc;AAAA;AAAA,MAG/D,SAAS,CAAC;AAAA,IACZ;AAEA,QAAI,OAAO,KAAK,MAAM,kBAAkB,YAAY;AAClD,aAAO,gBAAgB;AAAA,QACrB,IAAI;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AAEA,SAAK,MAAM,cAAc,CAAC,KAAK,YAAY;AACzC,UAAI,KAAK;AACP,wBAAgB,SAAS,GAAG;AAC5B;AAAA,MACF;AAEA,UAAI,WAAW,MAAM;AACnB,wBAAgB,SAAS,MAAM,UAAU;AACzC;AAAA,MACF;AAEA,iBAAW,UAAU,QAAQ,IAAI,CAAC,WAAW;AAE3C,cAAM,mBAAmB,OAAO,OAAO;AAGvC,eAAO,iBAAiB;AAExB,eAAO;AAAA,MACT,CAAC;AAED,sBAAgB,SAAS,MAAM,UAAU;AAAA,IAC3C,CAAC;AAED,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,gBAAiD;AAC/C,WAAO,KAAK,SAAS,CAAC,aAAa;AACjC,WAAK,UAAU,QAAQ;AAAA,IACzB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,SAA0C;AACxC,WAAO,KAAK,cAAc;AAAA,EAC5B;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,eAAe,YAAqB,UAAqC;AACvE,QAAI,UAAiC;AAErC,QACE,cACA,OAAO,eAAe,YACtB,WAAW,WAAW,UAAU,KAChC,MAAM,QAAQ,WAAW,OAAO,GAChC;AACA,gBAAU,WAAW;AAAA,IACvB;AAEA,QAAI,CAAC,SAAS;AACZ,eAAS,IAAI,MAAM,qCAAqC,GAAG,MAAS;AACpE;AAAA,IACF;AAEA,cAAU,QAAQ,MAAM;AAExB,UAAM,UAAyB,CAAC,QAAQ;AACtC,UAAI,KAAK;AACP,iBAAS,KAAK,MAAS;AACvB;AAAA,MACF;AAEA,UAAI,MAAM,QAAQ,OAAO,GAAG;AAC1B,YAAI,CAAC,QAAQ,QAAQ;AACnB,mBAAS,KAAK,IAAI;AAClB;AAAA,QACF;AAEA,YAAI;AACJ,YAAI;AACF,mBAAS,OAAO,SAAS,QAAQ,MAAM,CAAC;AAAA,QAC1C,SAAS,GAAG;AACV,mBAAS,aAAa,QAAQ,IAAI,IAAI,MAAM,GAAG,MAAS;AACxD;AAAA,QACF;AAEA,YAAI,WAAW,QAAW;AACxB,kBAAQ,IAAI;AACZ;AAAA,QACF;AAEA,aAAK,MAAM,UAAU,QAAQ,OAAO;AAAA,MACtC;AAAA,IACF;AAEA,YAAQ,IAAI;AAAA,EACd;AAAA;AAAA;AAAA;AAAA,EAKA,mBAAmB,YAA2B;AAC5C,SAAK,SAAS,KAAK,eAAe,KAAK,MAAM,UAAU,CAAC;AAAA,EAC1D;AAAA;AAAA;AAAA;AAAA,EA6CA,MACE,UACA,UACS;AACT,QAAI,OAAO,aAAa,YAAY;AAClC,iBAAW;AACX,iBAAW;AAAA,IACb;AAEA,UAAM,kBAAkB,sBAAiC,QAAQ;AACjE,UAAM,KAAK,gBAAgB;AAE3B,SAAK,UAAU,CAAC,KAAK,eAAe;AAClC,UAAI,KAAK;AACP,eAAO,gBAAgB,OAAO,GAAG;AAAA,MACnC;AACA,aAAO,WAAU,YAAY,cAAc,IAAI,UAAU,EAAE;AAAA,IAC7D,CAAC;AAED,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA,EAKA,WAAW,UAAyC;AAClD,UAAM,UACJ,YAAY,OAAO,aAAa,aAC5B,KAAK,MAAM,KAAK,MAAM,QAAQ,IAC9B,KAAK,MAAM,KAAK,IAAI;AAC1B,WAAO,KAAK,SAAS,CAAC,aAAa;AACjC,cAAQ,QAAQ;AAAA,IAClB,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,UAAU,UAAyC;AACjD,QAAI,CAAC,UAAU;AACb,aAAO,KAAK,WAAW;AAAA,IACzB;AACA,QAAI,CAAC,SAAS,aAAa;AACzB,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AACA,WAAO,KAAK,WAAW,QAAQ;AAAA,EACjC;AAAA;AAAA;AAAA;AAAA,EAmCA,iBAAiB,UAAmC;AAClD,UAAM,kBAAkB,sBAAiC,QAAQ;AACjE,UAAM,KAAK,gBAAgB;AAE3B,UAAM,QAAQ,KAAK;AAKnB,QACE,OAAO,MAAM,qBAAqB,cAClC,MAAM,qBAAqB,MAAM,UAAU,kBAC3C;AAGA,YAAM,iBAAiB,EAAmB;AAC1C,aAAO,gBAAgB;AAAA,IACzB;AAEA,UAAM,cAAc,CAAC,KAAK,YAAkB;AAC1C,UAAI,KAAK;AACP,WAAG,GAAG;AACN;AAAA,MACF;AAEA,UAAI,CAAC,SAAS;AACZ,kBAAU,CAAC;AAAA,MACb;AAEA,UAAI,QAAQ,WAAW,GAAG;AACxB,WAAG,MAAM,MAAS;AAClB;AAAA,MACF;AAEA,UAAI,iBAAiB;AACrB,YAAM,eAAwB,CAAC;AAG/B,YAAM,iBAAgC,SAASC,gBAAe,WAAW;AACvE,YAAI,WAAW;AACb,uBAAa,KAAK,SAAS;AAAA,QAC7B;AAEA;AAEA,YAAI,mBAAmB,QAAQ,QAAQ;AACrC,cAAI,aAAa,CAAC,EAAG,IAAG,aAAa,CAAC,CAAC;AAAA,cAClC,IAAG,MAAM,MAAS;AACvB;AAAA,QACF;AAAA,MACF;AAEA,cAAQ,QAAQ,CAAC,WAAW;AAC1B,cAAM;AAAA,UACJ,OAAO;AAAA,UACP,OAAO;AAAA,UACP,OAAO;AAAA,UACP;AAAA,QACF;AAAA,MACF,CAAC;AAAA,IACH,CAAC;AAED,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,uBAA6B;AAC3B,SAAK,SAAoB,CAAC,aAAa;AAGrC,WAAK,iBAAiB,QAAyB;AAAA,IACjD,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA,EAkEA,OAAO,YACL,UACA,OACA,UACS;AACT,QAAI,OAAO,UAAU,YAAY;AAC/B,iBAAW;AACX,cAAQ;AAAA,IACV;AAEA,UAAM,kBAAkB,sBAAiC,QAAQ;AAEjE,QAAI;AACJ,QAAI,OAAO,aAAa,UAAU;AAChC,UAAI;AACF,qBAAa,KAAK,MAAM,QAAQ;AAAA,MAClC,SAAS,GAAG;AACV,eAAO,gBAAgB,OAAO,aAAa,QAAQ,IAAI,IAAI,MAAM,CAAC;AAAA,MACpE;AAAA,IACF,OAAO;AACL,mBAAa;AAAA,IACf;AAEA,UAAM,yBAAyB,CAAC,aAA8B;AAC5D,aAAO,cACL,OAAO,eAAe,YACtB,WAAW,UAAU,UAAU,IAC7B,WAAW,QAAQ,IACnB;AAAA,IACN;AAEA,UAAM,wBAAwB,CAAC,aAA0C;AACvE,YAAM,QAAQ,uBAAuB,QAAQ;AAC7C,aAAO,OAAO,UAAU,YAAY,QAAQ;AAAA,IAC9C;AAEA,UAAM,uBAAuB,CAAC,aAAyC;AACrE,YAAM,QAAQ,uBAAuB,QAAQ;AAC7C,aAAO,OAAO,UAAU,WAAW,QAAQ;AAAA,IAC7C;AAEA,UAAM,MAAM,IAAI,WAAU,OAAO;AAAA,MAC/B,sBAAsB,sBAAsB,sBAAsB;AAAA,MAClE,WAAW,sBAAsB,iBAAiB;AAAA,MAClD,uBAAuB,sBAAsB,uBAAuB;AAAA,MACpE,gBAAgB;AAAA,QACd,qBAAqB,gBAAgB,KAAK;AAAA,MAC5C;AAAA,IACF,CAAC;AAED,QAAI,eAAe,YAAY,CAAC,QAAQ;AACtC,UAAI,KAAK;AACP,wBAAgB,SAAS,GAAG;AAC5B;AAAA,MACF;AACA,sBAAgB,SAAS,MAAM,GAAG;AAAA,IACpC,CAAC;AAED,WAAO,gBAAgB;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAiBA,OAAO,gBACL,UACA,OACW;AACX,UAAM,aACJ,OAAO,aAAa,WAAW,KAAK,MAAM,QAAQ,IAAI;AAExD,UAAM,yBAAyB,CAAC,aAA8B;AAC5D,aAAO,cACL,OAAO,eAAe,YACtB,WAAW,UAAU,UAAU,IAC7B,WAAW,QAAQ,IACnB;AAAA,IACN;AAEA,UAAM,wBAAwB,CAAC,aAA0C;AACvE,YAAM,QAAQ,uBAAuB,QAAQ;AAC7C,aAAO,OAAO,UAAU,YAAY,QAAQ;AAAA,IAC9C;AAEA,UAAM,uBAAuB,CAAC,aAAyC;AACrE,YAAM,QAAQ,uBAAuB,QAAQ;AAC7C,aAAO,OAAO,UAAU,WAAW,QAAQ;AAAA,IAC7C;AAEA,UAAM,MAAM,IAAI,WAAU,OAAO;AAAA,MAC/B,sBAAsB,sBAAsB,sBAAsB;AAAA,MAClE,WAAW,sBAAsB,iBAAiB;AAAA,MAClD,uBAAuB,sBAAsB,uBAAuB;AAAA,MACpE,gBAAgB;AAAA,QACd,qBAAqB,gBAAgB,KAAK;AAAA,MAC5C;AAAA,IACF,CAAC;AAGD,QAAI,CAAC,IAAI,MAAM,aAAa;AAC1B,YAAM,IAAI;AAAA,QACR;AAAA,MACF;AAAA,IACF;AAEA,QAAI,mBAAmB,UAAU;AACjC,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,OAAO,SACL,YACA,OACW;AACX,WAAO,WAAU,gBAAgB,YAAY,KAAK;AAAA,EACpD;AACF;;;AC3uDO,SAAS,YAAY,MAAwB;AAClD,MAAI,SAAS,KAAK;AAChB,WAAO,CAAC,GAAG;AAAA,EACb;AACA,QAAM,eAAe,CAAC,IAAI;AAC1B,SAAO,KAAK,SAAS,GAAG;AACtB,UAAM,SAAS,KAAK,YAAY,GAAG;AACnC,QAAI,WAAW,GAAG;AAChB;AAAA,IACF;AACA,WAAO,KAAK,MAAM,GAAG,MAAM;AAC3B,iBAAa,KAAK,IAAI;AAAA,EACxB;AACA,eAAa,KAAK,GAAG;AACrB,SAAO;AACT;;;AnBaO,SAASC,OACd,KACA,SACoB;AACpB,SAAO,OAAO,MAAM,KAAK,OAAO;AAClC;AAMO,SAASC,UAAS,KAAkC;AACzD,SAAO,OAAO,SAAS,GAAG;AAC5B;", "names": ["fromJSON", "parse", "cb", "<PERSON><PERSON><PERSON><PERSON>", "err", "now", "removeCookieCb", "parse", "fromJSON"]}
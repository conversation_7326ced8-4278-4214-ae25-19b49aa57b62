{"author": {"name": "<PERSON>", "email": "<EMAIL>", "website": "https://github.com/stash"}, "contributors": [{"name": "<PERSON>", "website": "https://github.com/inikulin"}, {"name": "<PERSON><PERSON>", "website": "https://github.com/<PERSON><PERSON>"}, {"name": "<PERSON>", "website": "https://github.com/ruoho"}, {"name": "<PERSON>", "website": "https://github.com/ianlivingstone"}, {"name": "<PERSON>", "website": "https://github.com/awaterma"}, {"name": "<PERSON> ", "website": "https://github.com/medelibero-sfdc"}, {"name": "<PERSON>", "website": "https://github.com/jstewmon"}, {"name": "<PERSON>", "website": "https://github.com/miggs125"}, {"name": "<PERSON>", "website": "https://github.com/Sebmaster"}, {"name": "<PERSON>", "website": "https://github.com/apsavin"}, {"name": "<PERSON><PERSON>", "website": "https://github.com/lalitkapoor"}, {"name": "<PERSON>", "website": "https://github.com/sambthompson"}, {"name": "<PERSON>", "website": "https://github.com/colincasey"}, {"name": "<PERSON>", "website": "https://github.com/wjhsf"}], "license": "BSD-3-<PERSON><PERSON>", "name": "tough-cookie", "description": "RFC6265 <PERSON><PERSON> and <PERSON><PERSON> for node.js", "keywords": ["HTTP", "cookie", "cookies", "set-cookie", "cookiejar", "jar", "RFC6265", "RFC2965"], "version": "6.0.0", "homepage": "https://github.com/salesforce/tough-cookie", "repository": {"type": "git", "url": "git://github.com/salesforce/tough-cookie.git"}, "bugs": {"url": "https://github.com/salesforce/tough-cookie/issues"}, "type": "module", "main": "./dist/index.cjs", "module": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {"import": {"types": "./dist/index.d.ts", "import": "./dist/index.js"}, "require": {"types": "./dist/index.d.cts", "require": "./dist/index.cjs"}}, "files": ["dist"], "scripts": {"build": "tsup lib/cookie/index.ts --format cjs,esm --dts --clean --sourcemap", "lint": "npm run _lint:check", "prepack": "npm run build", "prepare-pr": "npm run build && npm test -- run && npm run _api:update && npm run _docs:generate && npm run _format:fix && npm run _lint:fix && npm run _lint:types", "test": "vitest", "version": "npm run _version:generate && npm run prepare-pr && git add --renormalize .", "_api:check": "api-extractor run --verbose", "_api:update": "api-extractor run --verbose --local", "_docs:generate": "api-documenter markdown --input-folder ./tmp --output-folder ./api/docs", "_docs:fix": "prettier ./api/docs --write", "_format:check": "prettier . --check", "_format:fix": "prettier . --write", "_lint:check": "eslint .", "_lint:fix": "eslint . --fix", "_lint:types": "attw --pack .", "_version:generate": "genversion --template version-template.ejs --force lib/version.ts"}, "//": "We only support node LTS versions, but v16 still works. We won't block v16 until it becomes a burden.", "engines": {"node": ">=16"}, "devDependencies": {"@arethetypeswrong/cli": "^0.18.2", "@eslint/js": "^9.24.0", "@microsoft/api-documenter": "^7.26.20", "@microsoft/api-extractor": "^7.52.3", "@types/node": "^20.19.6", "@vitest/eslint-plugin": "^1.1.40", "eslint": "^9.24.0", "eslint-config-prettier": "^10.1.2", "eslint-import-resolver-typescript": "^4.3.2", "eslint-plugin-import": "^2.31.0", "eslint-plugin-prettier": "^5.2.6", "genversion": "^3.2.0", "globals": "^16.0.0", "prettier": "^3.5.3", "tsup": "^8.5.0", "typescript": "5.5.3", "typescript-eslint": "^8.29.1", "vitest": "^3.1.1"}, "dependencies": {"tldts": "^7.0.5"}}
{"version": 3, "file": "is-valid.js", "sourceRoot": "", "sources": ["../../../src/is-valid.ts"], "names": [], "mappings": ";AAAA;;;;;;;GAOG;;AAcH,4BAyDC;AArED,SAAS,YAAY,CAAC,IAAY;IAChC,OAAO,CACL,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,GAAG,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,IAAI,IAAI,IAAI,EAAE,CAAC,IAAI,IAAI,GAAG,GAAG,CACxE,CAAC;AACJ,CAAC;AAED;;;;;GAKG;AACH,mBAAyB,QAAgB;IACvC,IAAI,QAAQ,CAAC,MAAM,GAAG,GAAG,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;QAC1B,OAAO,KAAK,CAAC;IACf,CAAC;IAED;IACE,eAAe,CAAC,CAAC,YAAY,CAAC,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACrD,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,IAAI,YAAY;QAC7C,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,KAAK,EAAE,CAAC,mBAAmB;MACjD,CAAC;QACD,OAAO,KAAK,CAAC;IACf,CAAC;IAED,qCAAqC;IACrC,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;IACtB,IAAI,YAAY,GAAG,CAAC,CAAC,CAAC;IACtB,MAAM,GAAG,GAAG,QAAQ,CAAC,MAAM,CAAC;IAE5B,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,IAAI,CAAC,EAAE,CAAC;QAChC,MAAM,IAAI,GAAG,QAAQ,CAAC,UAAU,CAAC,CAAC,CAAC,CAAC;QACpC,IAAI,IAAI,KAAK,EAAE,CAAC,SAAS,EAAE,CAAC;YAC1B;YACE,+DAA+D;YAC/D,CAAC,GAAG,YAAY,GAAG,EAAE;gBACrB,sDAAsD;gBACtD,YAAY,KAAK,EAAE;gBACnB,+DAA+D;gBAC/D,YAAY,KAAK,EAAE;gBACnB,qEAAqE;gBACrE,YAAY,KAAK,EAAE,EACnB,CAAC;gBACD,OAAO,KAAK,CAAC;YACf,CAAC;YAED,YAAY,GAAG,CAAC,CAAC;QACnB,CAAC;aAAM,IACL,CAAC,EAAC,eAAgB,CAAC,YAAY,CAAC,IAAI,CAAC,IAAI,IAAI,KAAK,EAAE,IAAI,IAAI,KAAK,EAAE,CAAC,CAAC,EACrE,CAAC;YACD,uDAAuD;YACvD,OAAO,KAAK,CAAC;QACf,CAAC;QAED,YAAY,GAAG,IAAI,CAAC;IACtB,CAAC;IAED,OAAO;IACL,iDAAiD;IACjD,GAAG,GAAG,YAAY,GAAG,CAAC,IAAI,EAAE;QAC5B,wEAAwE;QACxE,wEAAwE;QACxE,sDAAsD;QACtD,YAAY,KAAK,EAAE,CACpB,CAAC;AACJ,CAAC"}
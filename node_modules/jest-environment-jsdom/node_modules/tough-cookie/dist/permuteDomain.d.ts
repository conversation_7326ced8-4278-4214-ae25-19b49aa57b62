/**
 * Generates the permutation of all possible values that {@link domainMatch} the given `domain` parameter. The
 * array is in shortest-to-longest order. Useful when building custom {@link Store} implementations.
 *
 * @example
 * ```
 * permuteDomain('foo.bar.example.com')
 * // ['example.com', 'bar.example.com', 'foo.bar.example.com']
 * ```
 *
 * @public
 * @param domain - the domain to generate permutations for
 * @param allowSpecialUseDomain - flag to control if {@link https://www.rfc-editor.org/rfc/rfc6761.html | Special Use Domains} such as `localhost` should be allowed
 */
export declare function permuteDomain(domain: string, allowSpecialUseDomain?: boolean): string[] | undefined;

{"version": 3, "file": "types.d.ts", "sourceRoot": "", "sources": ["../../src.ts/contract/types.ts"], "names": [], "mappings": "AAAA,OAAO,KAAK,EACR,aAAa,EAAE,gBAAgB,EAAE,MAAM,EAAE,KAAK,EACjD,MAAM,iBAAiB,CAAC;AACzB,OAAO,KAAK,EACR,kBAAkB,EAAE,0BAA0B,EAAE,WAAW,EAC9D,MAAM,uBAAuB,CAAC;AAE/B,OAAO,KAAK,EAAE,2BAA2B,EAAE,MAAM,eAAe,CAAC;AAGjE;;;;;;;;;;;;;;;;;;;GAmBG;AACH,MAAM,MAAM,iBAAiB,GAAG,MAAM,GAAG,aAAa,GAAG,WAAW,GAAG,mBAAmB,CAAC;AAE3F;;GAEG;AACH,MAAM,WAAW,iBAAiB;IAC9B,CAAE,IAAI,EAAE,MAAM,GAAI,kBAAkB,CAAC;CACxC;AAED;;GAEG;AACH,MAAM,WAAW,mBAAmB;IAChC,cAAc,IAAI,OAAO,CAAC,WAAW,CAAC,CAAC;IACvC,QAAQ,EAAE,aAAa,CAAC;CAC3B;AAED;;GAEG;AACH,MAAM,WAAW,mBAAoB,SAAQ,0BAA0B;IACnE;;OAEG;IACH,EAAE,EAAE,MAAM,CAAC;IAEX;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;OAEG;IACH,IAAI,CAAC,EAAE,MAAM,CAAC;CACjB;AAED;;GAEG;AACH,MAAM,WAAW,yBAA0B,SAAQ,IAAI,CAAC,mBAAmB,EAAE,IAAI,CAAC;CAAI;AAEtF;;GAEG;AACH,MAAM,WAAW,SAAU,SAAQ,IAAI,CAAC,kBAAkB,EAAE,IAAI,GAAG,MAAM,CAAC;CAAI;AAG9E;;;;;GAKG;AACH,MAAM,MAAM,gBAAgB,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAE,GAAG,CAAC,EAAE,SAAS,CAAE,CAAC;AAE7E;;;;;;GAMG;AACH,MAAM,MAAM,kBAAkB,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,IAAI,gBAAgB,CAAC;KAAI,CAAC,IAAI,MAAM,CAAC,CAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK;CAAE,CAAC,CAAC;AAQ9G;;GAEG;AACH,MAAM,WAAW,kBAAkB,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,GAAG,EAAE,CAAC,SAAS,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,2BAA2B;IACvJ,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAE7C;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;OAGG;IACH,QAAQ,EAAE,gBAAgB,CAAC;IAE3B;;;OAGG;IACH,WAAW,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,gBAAgB,CAAC;IAE9D;;;OAGG;IACH,mBAAmB,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAElF;;;;;OAKG;IACH,UAAU,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,CAAC,CAAC,CAAC;IAEvD;;OAEG;IACH,IAAI,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;IAE3E;;OAEG;IACH,WAAW,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAE7D;;;OAGG;IACH,gBAAgB,CAAC,GAAG,IAAI,EAAE,kBAAkB,CAAC,CAAC,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CACrE;AAED;;GAEG;AACH,MAAM,WAAW,cAAc,CAC3B,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC,EACjC,CAAC,GAAG,GAAG,EACP,CAAC,SAAS,CAAC,GAAG,2BAA2B,GAAG,CAAC,GAAG,2BAA2B,CAC7E,SAAQ,kBAAkB,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAAI;AAEzC;;GAEG;AACH,MAAM,WAAW,sBAAsB,CACnC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,EACpB,CAAC,GAAG,GAAG,CACT,SAAQ,cAAc,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC;CAAI;AAGrC;;;;GAIG;AACH,MAAM,MAAM,iBAAiB,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,IAAI;KAAI,CAAC,IAAI,MAAM,CAAC,CAAE,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,GAAG,KAAK,GAAG,IAAI;CAAE,CAAC;AAEjG,MAAM,WAAW,aAAa,CAAC,CAAC,SAAS,KAAK,CAAC,GAAG,CAAC,GAAG,KAAK,CAAC,GAAG,CAAC;IAC5D,CAAC,GAAG,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,GAAG,mBAAmB,CAAC;IAErD;;OAEG;IACH,IAAI,EAAE,MAAM,CAAC;IAEb;;;OAGG;IACH,QAAQ,EAAE,aAAa,CAAC;IAExB;;;OAGG;IACH,WAAW,CAAC,GAAG,IAAI,EAAE,iBAAiB,CAAC,CAAC,CAAC,GAAG,aAAa,CAAC;CAC7D;AAED;;GAEG;AACH,MAAM,WAAW,eAAe;IAC5B,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;IAEnF;;;;;OAKG;IACH,mBAAmB,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,mBAAmB,CAAC,CAAC;IAE9F;;;;OAIG;IACH,UAAU,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;IAExE;;;;OAIG;IACH,IAAI,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,2BAA2B,CAAC,CAAC;IAEvF;;;;OAIG;IACH,WAAW,CAAC,SAAS,CAAC,EAAE,IAAI,CAAC,kBAAkB,EAAE,IAAI,CAAC,GAAG,OAAO,CAAC,MAAM,CAAC,CAAC;CAC5E"}
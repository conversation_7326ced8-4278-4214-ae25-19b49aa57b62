{"version": 3, "file": "hmac.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/hmac.ts"], "names": [], "mappings": "AAAA;;;;;;;GAOG;AACH,OAAO,EAAE,UAAU,EAAE,MAAM,aAAa,CAAC;AACzC,OAAO,EAAE,QAAQ,EAAE,OAAO,EAAE,MAAM,mBAAmB,CAAC;AAKtD,IAAI,MAAM,GAAG,KAAK,CAAC;AAEnB,MAAM,YAAY,GAAG,UAAS,SAA8B,EAAE,GAAe,EAAE,IAAgB;IAC3F,OAAO,UAAU,CAAC,SAAS,EAAE,GAAG,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,MAAM,EAAE,CAAC;AAC5D,CAAC,CAAA;AAED,IAAI,aAAa,GAAG,YAAY,CAAC;AAEjC;;;;;;;;;;;;;;;;GAgBG;AACH,MAAM,UAAU,WAAW,CAAC,SAA8B,EAAE,IAAe,EAAE,KAAgB;IACzF,MAAM,GAAG,GAAG,QAAQ,CAAC,IAAI,EAAE,KAAK,CAAC,CAAC;IAClC,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,OAAO,CAAC,aAAa,CAAC,SAAS,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;AACxD,CAAC;AACD,WAAW,CAAC,CAAC,GAAG,YAAY,CAAC;AAC7B,WAAW,CAAC,IAAI,GAAI,cAAa,MAAM,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;AACjD,WAAW,CAAC,QAAQ,GAAG,UAAS,IAAsF;IAClH,IAAI,MAAM,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;KAAE;IACzD,aAAa,GAAG,IAAI,CAAC;AACzB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC"}
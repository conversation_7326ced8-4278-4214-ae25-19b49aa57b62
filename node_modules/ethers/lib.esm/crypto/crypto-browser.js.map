{"version": 3, "file": "crypto-browser.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/crypto-browser.ts"], "names": [], "mappings": "AAAA,0BAA0B;AAE1B,OAAO,EAAE,IAAI,EAAE,MAAM,oBAAoB,CAAC;AAC1C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAC9C,OAAO,EAAE,MAAM,EAAE,MAAM,sBAAsB,CAAC;AAE9C,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,MAAM,mBAAmB,CAAC;AAW3D,SAAS,SAAS;IAChB,IAAI,OAAO,IAAI,KAAK,WAAW,EAAE;QAAE,OAAO,IAAI,CAAC;KAAE;IACjD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IACrD,IAAI,OAAO,MAAM,KAAK,WAAW,EAAE;QAAE,OAAO,MAAM,CAAC;KAAE;IACrD,MAAM,IAAI,KAAK,CAAC,gCAAgC,CAAC,CAAC;AACpD,CAAC;AAAA,CAAC;AAEF,MAAM,SAAS,GAAG,SAAS,EAAE,CAAC;AAC9B,MAAM,MAAM,GAAQ,SAAS,CAAC,MAAM,IAAI,SAAS,CAAC,QAAQ,CAAC;AAQ3D,MAAM,UAAU,UAAU,CAAC,IAAY;IACnC,QAAQ,IAAI,EAAE;QACV,KAAK,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;QACtC,KAAK,QAAQ,CAAC,CAAC,OAAO,MAAM,CAAC,MAAM,EAAE,CAAC;KACzC;IACD,cAAc,CAAC,KAAK,EAAE,gCAAgC,EAAE,WAAW,EAAE,IAAI,CAAC,CAAC;AAC/E,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,KAAa,EAAE,GAAe;IACrD,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACzC,cAAc,CAAC,IAAI,IAAI,IAAI,EAAE,wBAAwB,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC3E,OAAO,IAAI,CAAC,MAAM,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;AAClC,CAAC;AAED,MAAM,UAAU,UAAU,CAAC,QAAoB,EAAE,IAAgB,EAAE,UAAkB,EAAE,MAAc,EAAE,KAA0B;IAC7H,MAAM,IAAI,GAAG,CAAC,EAAE,MAAM,EAAE,MAAM,EAAE,CAAC,KAAK,CAAC,CAAC,CAAC;IACzC,cAAc,CAAC,IAAI,IAAI,IAAI,EAAE,0BAA0B,EAAE,WAAW,EAAE,KAAK,CAAC,CAAC;IAC7E,OAAO,MAAM,CAAC,IAAI,EAAE,QAAQ,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,UAAU,EAAE,KAAK,EAAE,MAAM,EAAE,CAAC,CAAC;AAC1E,CAAC;AAED,MAAM,UAAU,WAAW,CAAC,MAAc;IACtC,MAAM,CAAC,MAAM,IAAI,IAAI,EAAE,iDAAiD,EAAE,uBAAuB,EAAE;QAC/F,SAAS,EAAE,aAAa;KAAE,CAAC,CAAC;IAEhC,cAAc,CAAC,MAAM,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,MAAM,GAAG,CAAC,IAAI,MAAM,IAAI,IAAI,EAAE,gBAAgB,EAAE,QAAQ,EAAE,MAAM,CAAC,CAAC;IAE7G,MAAM,MAAM,GAAG,IAAI,UAAU,CAAC,MAAM,CAAC,CAAC;IACtC,MAAM,CAAC,eAAe,CAAC,MAAM,CAAC,CAAC;IAC/B,OAAO,MAAM,CAAC;AAClB,CAAC"}
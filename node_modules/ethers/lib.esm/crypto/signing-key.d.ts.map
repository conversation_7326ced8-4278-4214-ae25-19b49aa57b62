{"version": 3, "file": "signing-key.d.ts", "sourceRoot": "", "sources": ["../../src.ts/crypto/signing-key.ts"], "names": [], "mappings": "AAAA;;;;GAIG;AASH,OAAO,EAAE,SAAS,EAAE,MAAM,gBAAgB,CAAC;AAE3C,OAAO,KAAK,EAAE,SAAS,EAAE,MAAM,mBAAmB,CAAC;AAEnD,OAAO,KAAK,EAAE,aAAa,EAAE,MAAM,YAAY,CAAC;AAGhD;;;GAGG;AACH,qBAAa,UAAU;;IAGnB;;OAEG;gBACS,UAAU,EAAE,SAAS;IAKjC;;OAEG;IACH,IAAI,UAAU,IAAI,MAAM,CAA6B;IAErD;;;;;OAKG;IACH,IAAI,SAAS,IAAI,MAAM,CAA0D;IAEjF;;;;;;OAMG;IACH,IAAI,mBAAmB,IAAI,MAAM,CAAgE;IAEjG;;OAEG;IACH,IAAI,CAAC,MAAM,EAAE,SAAS,GAAG,SAAS;IAclC;;;;;;;;;;;;;;;;;;;;;OAqBG;IACH,mBAAmB,CAAC,KAAK,EAAE,SAAS,GAAG,MAAM;IAK7C;;;;;;;;;;;;;;;;;;;;;;;;OAwBG;IACH,MAAM,CAAC,gBAAgB,CAAC,GAAG,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM;IAqBrE;;;;;;;;;;;;;;;;;OAiBG;IACH,MAAM,CAAC,gBAAgB,CAAC,MAAM,EAAE,SAAS,EAAE,SAAS,EAAE,aAAa,GAAG,MAAM;IAc5E;;;;;;;;;OASG;IACH,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,SAAS,EAAE,EAAE,EAAE,SAAS,EAAE,UAAU,CAAC,EAAE,OAAO,GAAG,MAAM;CAK/E"}
{"version": 3, "file": "scrypt.js", "sourceRoot": "", "sources": ["../../src.ts/crypto/scrypt.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,MAAM,IAAI,UAAU,EAAE,WAAW,IAAI,WAAW,EAAE,MAAM,sBAAsB,CAAC;AAExF,OAAO,EAAE,QAAQ,EAAE,OAAO,IAAI,CAAC,EAAE,MAAM,mBAAmB,CAAC;AAe3D,IAAI,UAAU,GAAG,KAAK,EAAE,WAAW,GAAG,KAAK,CAAC;AAE5C,MAAM,YAAY,GAAG,KAAK,WAAU,MAAkB,EAAE,IAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,UAA6B;IACnJ,OAAO,MAAM,WAAW,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;AAC3E,CAAC,CAAA;AACD,MAAM,WAAW,GAAG,UAAS,MAAkB,EAAE,IAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa;IAC7G,OAAO,UAAU,CAAC,MAAM,EAAE,IAAI,EAAE,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,CAAC,CAAC;AACxD,CAAC,CAAA;AAED,IAAI,aAAa,GAAgJ,YAAY,CAAC;AAC9K,IAAI,YAAY,GAAwG,WAAW,CAAA;AAGnI;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;GAoCG;AACH,MAAM,CAAC,KAAK,UAAU,MAAM,CAAC,OAAkB,EAAE,KAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa,EAAE,QAA2B;IAC1I,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,CAAC,CAAC,MAAM,aAAa,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,EAAE,QAAQ,CAAC,CAAC,CAAC;AAC1E,CAAC;AACD,MAAM,CAAC,CAAC,GAAG,YAAY,CAAC;AACxB,MAAM,CAAC,IAAI,GAAG,cAAmB,WAAW,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;AACtD,MAAM,CAAC,QAAQ,GAAG,UAAS,IAA+I;IACtK,IAAI,WAAW,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;KAAE;IACzD,aAAa,GAAG,IAAI,CAAC;AACzB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;AAEtB;;;;;;;;;;;;;;;;;;;;;GAqBG;AACH,MAAM,UAAU,UAAU,CAAC,OAAkB,EAAE,KAAgB,EAAE,CAAS,EAAE,CAAS,EAAE,CAAS,EAAE,KAAa;IAC3G,MAAM,MAAM,GAAG,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;IAC3C,MAAM,IAAI,GAAG,QAAQ,CAAC,KAAK,EAAE,MAAM,CAAC,CAAC;IACrC,OAAO,CAAC,CAAC,YAAY,CAAC,MAAM,EAAE,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,CAAC,EAAE,KAAK,CAAC,CAAC,CAAC;AACzD,CAAC;AACD,UAAU,CAAC,CAAC,GAAG,WAAW,CAAC;AAC3B,UAAU,CAAC,IAAI,GAAG,cAAmB,UAAU,GAAG,IAAI,CAAC,CAAC,CAAC,CAAA;AACzD,UAAU,CAAC,QAAQ,GAAG,UAAS,IAAyG;IACpI,IAAI,UAAU,EAAE;QAAE,MAAM,IAAI,KAAK,CAAC,sBAAsB,CAAC,CAAC;KAAE;IAC5D,YAAY,GAAG,IAAI,CAAC;AACxB,CAAC,CAAA;AACD,MAAM,CAAC,MAAM,CAAC,UAAU,CAAC,CAAC"}
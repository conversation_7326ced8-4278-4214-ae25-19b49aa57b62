{"version": 3, "file": "authorization.js", "sourceRoot": "", "sources": ["../../src.ts/hash/authorization.ts"], "names": [], "mappings": "AAAA,OAAO,EAAE,UAAU,EAAE,MAAM,qBAAqB,CAAC;AACjD,OAAO,EAAE,SAAS,EAAE,MAAM,oBAAoB,CAAC;AAC/C,OAAO,EAAE,cAAc,EAAE,MAAM,yBAAyB,CAAC;AACzD,OAAO,EACH,cAAc,EAAE,MAAM,EAAE,SAAS,EAAE,SAAS,EAC/C,MAAM,mBAAmB,CAAC;AAY3B;;GAEG;AACH,MAAM,UAAU,iBAAiB,CAAC,IAA0B;IACxD,cAAc,CAAC,OAAM,CAAC,IAAI,CAAC,OAAO,CAAC,KAAK,QAAQ,EAAE,uCAAuC,EAAE,cAAc,EAAE,IAAI,CAAC,CAAC;IACjH,OAAO,SAAS,CAAC,MAAM,CAAC;QACpB,MAAM,EAAE,SAAS,CAAC;YACd,CAAC,IAAI,CAAC,OAAO,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,OAAO,CAAC,CAAA,CAAC,CAAC,IAAI;YACtD,UAAU,CAAC,IAAI,CAAC,OAAO,CAAC;YACxB,CAAC,IAAI,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC,IAAI,CAAC,KAAK,CAAC,CAAA,CAAC,CAAC,IAAI;SACrD,CAAC;KACL,CAAC,CAAC,CAAC;AACR,CAAC;AAED;;;GAGG;AACH,MAAM,UAAU,mBAAmB,CAAC,IAA0B,EAAE,GAAkB;IAC9E,OAAO,cAAc,CAAC,iBAAiB,CAAC,IAAI,CAAC,EAAE,GAAG,CAAC,CAAC;AACxD,CAAC"}
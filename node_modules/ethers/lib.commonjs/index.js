"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.ethers = void 0;
const tslib_1 = require("tslib");
/**
 *  The Application Programming Interface (API) is the collection of
 *  functions, classes and types offered by the Ethers library.
 *
 *  @_section: api:Application Programming Interface  [about-api]
 *  @_navTitle: API
 */
const ethers = tslib_1.__importStar(require("./ethers.js"));
exports.ethers = ethers;
tslib_1.__exportStar(require("./ethers.js"), exports);
//# sourceMappingURL=index.js.map
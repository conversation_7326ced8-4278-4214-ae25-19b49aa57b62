{"version": 3, "file": "bit-reader.js", "sourceRoot": "", "sources": ["../../src.ts/wordlists/bit-reader.ts"], "names": [], "mappings": ";;;AAAA,MAAM,MAAM,GAAG,kEAAkE,CAAC;AAElF;;GAEG;AACH,SAAgB,UAAU,CAAC,KAAa,EAAE,IAAY;IAClD,MAAM,QAAQ,GAAG,CAAC,CAAC,IAAI,KAAK,CAAC,GAAG,CAAC,CAAC;IAClC,MAAM,MAAM,GAAkB,EAAG,CAAC;IAClC,IAAI,KAAK,GAAG,CAAC,EAAE,IAAI,GAAG,CAAC,EAAE,KAAK,GAAG,CAAC,CAAC;IACnC,KAAK,IAAI,CAAC,GAAG,CAAC,EAAE,CAAC,GAAG,IAAI,CAAC,MAAM,EAAE,CAAC,EAAE,EAAE;QAElC,4BAA4B;QAC5B,KAAK,GAAG,CAAC,CAAC,KAAK,IAAI,CAAC,CAAC,GAAG,MAAM,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QACjD,IAAI,IAAI,CAAC,CAAC;QAEV,qCAAqC;QACrC,OAAO,IAAI,IAAI,KAAK,EAAE;YAClB,mBAAmB;YACnB,MAAM,KAAK,GAAG,CAAC,KAAK,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,CAAC;YACxC,KAAK,IAAI,CAAC,CAAC,IAAI,CAAC,IAAI,GAAG,KAAK,CAAC,CAAC,GAAG,CAAC,CAAC;YACnC,IAAI,IAAI,KAAK,CAAC;YAEd,kDAAkD;YAClD,kCAAkC;YAClC,IAAI,KAAK,KAAK,CAAC,EAAE;gBACb,KAAK,IAAI,QAAQ,CAAC;aACrB;iBAAM;gBACH,MAAM,CAAC,IAAI,CAAC,KAAK,GAAG,KAAK,CAAC,CAAC;gBAC3B,KAAK,GAAG,CAAC,CAAC;aACb;SACJ;KACJ;IAED,OAAO,MAAM,CAAC;AAClB,CAAC;AA7BD,gCA6BC"}
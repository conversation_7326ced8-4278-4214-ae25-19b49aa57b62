var csstree=(()=>{var Na=Object.create;var Lt=Object.defineProperty;var Oa=Object.getOwnPropertyDescriptor;var Fa=Object.getOwnPropertyNames;var Ra=Object.getPrototypeOf,Ma=Object.prototype.hasOwnProperty;var Be=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),x=(e,t)=>{for(var r in t)Lt(e,r,{get:t[r],enumerable:!0})},so=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of Fa(t))!Ma.call(e,i)&&i!==r&&Lt(e,i,{get:()=>t[i],enumerable:!(n=Oa(t,i))||n.enumerable});return e};var Ba=(e,t,r)=>(r=e!=null?Na(Ra(e)):{},so(t||!e||!e.__esModule?Lt(r,"default",{value:e,enumerable:!0}):r,e)),_a=e=>so(Lt({},"__esModule",{value:!0}),e);var vo=Be(br=>{var wo="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/".split("");br.encode=function(e){if(0<=e&&e<wo.length)return wo[e];throw new TypeError("Must be between 0 and 63: "+e)};br.decode=function(e){var t=65,r=90,n=97,i=122,o=48,a=57,u=43,l=47,s=26,c=52;return t<=e&&e<=r?e-t:n<=e&&e<=i?e-n+s:o<=e&&e<=a?e-o+c:e==u?62:e==l?63:-1}});var Lo=Be(yr=>{var So=vo(),xr=5,Co=1<<xr,To=Co-1,Ao=Co;function Xa(e){return e<0?(-e<<1)+1:(e<<1)+0}function $a(e){var t=(e&1)===1,r=e>>1;return t?-r:r}yr.encode=function(t){var r="",n,i=Xa(t);do n=i&To,i>>>=xr,i>0&&(n|=Ao),r+=So.encode(n);while(i>0);return r};yr.decode=function(t,r,n){var i=t.length,o=0,a=0,u,l;do{if(r>=i)throw new Error("Expected more digits in base 64 VLQ value.");if(l=So.decode(t.charCodeAt(r++)),l===-1)throw new Error("Invalid base64 digit: "+t.charAt(r-1));u=!!(l&Ao),l&=To,o=o+(l<<a),a+=xr}while(u);n.value=$a(o),n.rest=r}});var Rt=Be(V=>{function Za(e,t,r){if(t in e)return e[t];if(arguments.length===3)return r;throw new Error('"'+t+'" is a required argument.')}V.getArg=Za;var Eo=/^(?:([\w+\-.]+):)?\/\/(?:(\w+:\w+)@)?([\w.-]*)(?::(\d+))?(.*)$/,Ja=/^data:.+\,.+$/;function at(e){var t=e.match(Eo);return t?{scheme:t[1],auth:t[2],host:t[3],port:t[4],path:t[5]}:null}V.urlParse=at;function Ve(e){var t="";return e.scheme&&(t+=e.scheme+":"),t+="//",e.auth&&(t+=e.auth+"@"),e.host&&(t+=e.host),e.port&&(t+=":"+e.port),e.path&&(t+=e.path),t}V.urlGenerate=Ve;var el=32;function tl(e){var t=[];return function(r){for(var n=0;n<t.length;n++)if(t[n].input===r){var i=t[0];return t[0]=t[n],t[n]=i,t[0].result}var o=e(r);return t.unshift({input:r,result:o}),t.length>el&&t.pop(),o}}var kr=tl(function(t){var r=t,n=at(t);if(n){if(!n.path)return t;r=n.path}for(var i=V.isAbsolute(r),o=[],a=0,u=0;;)if(a=u,u=r.indexOf("/",a),u===-1){o.push(r.slice(a));break}else for(o.push(r.slice(a,u));u<r.length&&r[u]==="/";)u++;for(var l,s=0,u=o.length-1;u>=0;u--)l=o[u],l==="."?o.splice(u,1):l===".."?s++:s>0&&(l===""?(o.splice(u+1,s),s=0):(o.splice(u,2),s--));return r=o.join("/"),r===""&&(r=i?"/":"."),n?(n.path=r,Ve(n)):r});V.normalize=kr;function zo(e,t){e===""&&(e="."),t===""&&(t=".");var r=at(t),n=at(e);if(n&&(e=n.path||"/"),r&&!r.scheme)return n&&(r.scheme=n.scheme),Ve(r);if(r||t.match(Ja))return t;if(n&&!n.host&&!n.path)return n.host=t,Ve(n);var i=t.charAt(0)==="/"?t:kr(e.replace(/\/+$/,"")+"/"+t);return n?(n.path=i,Ve(n)):i}V.join=zo;V.isAbsolute=function(e){return e.charAt(0)==="/"||Eo.test(e)};function rl(e,t){e===""&&(e="."),e=e.replace(/\/$/,"");for(var r=0;t.indexOf(e+"/")!==0;){var n=e.lastIndexOf("/");if(n<0||(e=e.slice(0,n),e.match(/^([^\/]+:\/)?\/*$/)))return t;++r}return Array(r+1).join("../")+t.substr(e.length+1)}V.relative=rl;var Po=function(){var e=Object.create(null);return!("__proto__"in e)}();function Io(e){return e}function nl(e){return Do(e)?"$"+e:e}V.toSetString=Po?Io:nl;function il(e){return Do(e)?e.slice(1):e}V.fromSetString=Po?Io:il;function Do(e){if(!e)return!1;var t=e.length;if(t<9||e.charCodeAt(t-1)!==95||e.charCodeAt(t-2)!==95||e.charCodeAt(t-3)!==111||e.charCodeAt(t-4)!==116||e.charCodeAt(t-5)!==111||e.charCodeAt(t-6)!==114||e.charCodeAt(t-7)!==112||e.charCodeAt(t-8)!==95||e.charCodeAt(t-9)!==95)return!1;for(var r=t-10;r>=0;r--)if(e.charCodeAt(r)!==36)return!1;return!0}function ol(e,t,r){var n=ye(e.source,t.source);return n!==0||(n=e.originalLine-t.originalLine,n!==0)||(n=e.originalColumn-t.originalColumn,n!==0||r)||(n=e.generatedColumn-t.generatedColumn,n!==0)||(n=e.generatedLine-t.generatedLine,n!==0)?n:ye(e.name,t.name)}V.compareByOriginalPositions=ol;function sl(e,t,r){var n;return n=e.originalLine-t.originalLine,n!==0||(n=e.originalColumn-t.originalColumn,n!==0||r)||(n=e.generatedColumn-t.generatedColumn,n!==0)||(n=e.generatedLine-t.generatedLine,n!==0)?n:ye(e.name,t.name)}V.compareByOriginalPositionsNoSource=sl;function al(e,t,r){var n=e.generatedLine-t.generatedLine;return n!==0||(n=e.generatedColumn-t.generatedColumn,n!==0||r)||(n=ye(e.source,t.source),n!==0)||(n=e.originalLine-t.originalLine,n!==0)||(n=e.originalColumn-t.originalColumn,n!==0)?n:ye(e.name,t.name)}V.compareByGeneratedPositionsDeflated=al;function ll(e,t,r){var n=e.generatedColumn-t.generatedColumn;return n!==0||r||(n=ye(e.source,t.source),n!==0)||(n=e.originalLine-t.originalLine,n!==0)||(n=e.originalColumn-t.originalColumn,n!==0)?n:ye(e.name,t.name)}V.compareByGeneratedPositionsDeflatedNoLine=ll;function ye(e,t){return e===t?0:e===null?1:t===null?-1:e>t?1:-1}function cl(e,t){var r=e.generatedLine-t.generatedLine;return r!==0||(r=e.generatedColumn-t.generatedColumn,r!==0)||(r=ye(e.source,t.source),r!==0)||(r=e.originalLine-t.originalLine,r!==0)||(r=e.originalColumn-t.originalColumn,r!==0)?r:ye(e.name,t.name)}V.compareByGeneratedPositionsInflated=cl;function ul(e){return JSON.parse(e.replace(/^\)]}'[^\n]*\n/,""))}V.parseSourceMapInput=ul;function pl(e,t,r){if(t=t||"",e&&(e[e.length-1]!=="/"&&t[0]!=="/"&&(e+="/"),t=e+t),r){var n=at(r);if(!n)throw new Error("sourceMapURL could not be parsed");if(n.path){var i=n.path.lastIndexOf("/");i>=0&&(n.path=n.path.substring(0,i+1))}t=zo(Ve(n),t)}return kr(t)}V.computeSourceURL=pl});var Oo=Be(No=>{var wr=Rt(),vr=Object.prototype.hasOwnProperty,Ne=typeof Map<"u";function ke(){this._array=[],this._set=Ne?new Map:Object.create(null)}ke.fromArray=function(t,r){for(var n=new ke,i=0,o=t.length;i<o;i++)n.add(t[i],r);return n};ke.prototype.size=function(){return Ne?this._set.size:Object.getOwnPropertyNames(this._set).length};ke.prototype.add=function(t,r){var n=Ne?t:wr.toSetString(t),i=Ne?this.has(t):vr.call(this._set,n),o=this._array.length;(!i||r)&&this._array.push(t),i||(Ne?this._set.set(t,o):this._set[n]=o)};ke.prototype.has=function(t){if(Ne)return this._set.has(t);var r=wr.toSetString(t);return vr.call(this._set,r)};ke.prototype.indexOf=function(t){if(Ne){var r=this._set.get(t);if(r>=0)return r}else{var n=wr.toSetString(t);if(vr.call(this._set,n))return this._set[n]}throw new Error('"'+t+'" is not in the set.')};ke.prototype.at=function(t){if(t>=0&&t<this._array.length)return this._array[t];throw new Error("No element indexed by "+t)};ke.prototype.toArray=function(){return this._array.slice()};No.ArraySet=ke});var Mo=Be(Ro=>{var Fo=Rt();function hl(e,t){var r=e.generatedLine,n=t.generatedLine,i=e.generatedColumn,o=t.generatedColumn;return n>r||n==r&&o>=i||Fo.compareByGeneratedPositionsInflated(e,t)<=0}function Mt(){this._array=[],this._sorted=!0,this._last={generatedLine:-1,generatedColumn:0}}Mt.prototype.unsortedForEach=function(t,r){this._array.forEach(t,r)};Mt.prototype.add=function(t){hl(this._last,t)?(this._last=t,this._array.push(t)):(this._sorted=!1,this._array.push(t))};Mt.prototype.toArray=function(){return this._sorted||(this._array.sort(Fo.compareByGeneratedPositionsInflated),this._sorted=!0),this._array};Ro.MappingList=Mt});var _o=Be(Bo=>{var lt=Lo(),U=Rt(),Bt=Oo().ArraySet,ml=Mo().MappingList;function ie(e){e||(e={}),this._file=U.getArg(e,"file",null),this._sourceRoot=U.getArg(e,"sourceRoot",null),this._skipValidation=U.getArg(e,"skipValidation",!1),this._sources=new Bt,this._names=new Bt,this._mappings=new ml,this._sourcesContents=null}ie.prototype._version=3;ie.fromSourceMap=function(t){var r=t.sourceRoot,n=new ie({file:t.file,sourceRoot:r});return t.eachMapping(function(i){var o={generated:{line:i.generatedLine,column:i.generatedColumn}};i.source!=null&&(o.source=i.source,r!=null&&(o.source=U.relative(r,o.source)),o.original={line:i.originalLine,column:i.originalColumn},i.name!=null&&(o.name=i.name)),n.addMapping(o)}),t.sources.forEach(function(i){var o=i;r!==null&&(o=U.relative(r,i)),n._sources.has(o)||n._sources.add(o);var a=t.sourceContentFor(i);a!=null&&n.setSourceContent(i,a)}),n};ie.prototype.addMapping=function(t){var r=U.getArg(t,"generated"),n=U.getArg(t,"original",null),i=U.getArg(t,"source",null),o=U.getArg(t,"name",null);this._skipValidation||this._validateMapping(r,n,i,o),i!=null&&(i=String(i),this._sources.has(i)||this._sources.add(i)),o!=null&&(o=String(o),this._names.has(o)||this._names.add(o)),this._mappings.add({generatedLine:r.line,generatedColumn:r.column,originalLine:n!=null&&n.line,originalColumn:n!=null&&n.column,source:i,name:o})};ie.prototype.setSourceContent=function(t,r){var n=t;this._sourceRoot!=null&&(n=U.relative(this._sourceRoot,n)),r!=null?(this._sourcesContents||(this._sourcesContents=Object.create(null)),this._sourcesContents[U.toSetString(n)]=r):this._sourcesContents&&(delete this._sourcesContents[U.toSetString(n)],Object.keys(this._sourcesContents).length===0&&(this._sourcesContents=null))};ie.prototype.applySourceMap=function(t,r,n){var i=r;if(r==null){if(t.file==null)throw new Error(`SourceMapGenerator.prototype.applySourceMap requires either an explicit source file, or the source map's "file" property. Both were omitted.`);i=t.file}var o=this._sourceRoot;o!=null&&(i=U.relative(o,i));var a=new Bt,u=new Bt;this._mappings.unsortedForEach(function(l){if(l.source===i&&l.originalLine!=null){var s=t.originalPositionFor({line:l.originalLine,column:l.originalColumn});s.source!=null&&(l.source=s.source,n!=null&&(l.source=U.join(n,l.source)),o!=null&&(l.source=U.relative(o,l.source)),l.originalLine=s.line,l.originalColumn=s.column,s.name!=null&&(l.name=s.name))}var c=l.source;c!=null&&!a.has(c)&&a.add(c);var h=l.name;h!=null&&!u.has(h)&&u.add(h)},this),this._sources=a,this._names=u,t.sources.forEach(function(l){var s=t.sourceContentFor(l);s!=null&&(n!=null&&(l=U.join(n,l)),o!=null&&(l=U.relative(o,l)),this.setSourceContent(l,s))},this)};ie.prototype._validateMapping=function(t,r,n,i){if(r&&typeof r.line!="number"&&typeof r.column!="number")throw new Error("original.line and original.column are not numbers -- you probably meant to omit the original mapping entirely and only map the generated position. If so, pass null for the original mapping instead of an object with empty or null values.");if(!(t&&"line"in t&&"column"in t&&t.line>0&&t.column>=0&&!r&&!n&&!i)){if(t&&"line"in t&&"column"in t&&r&&"line"in r&&"column"in r&&t.line>0&&t.column>=0&&r.line>0&&r.column>=0&&n)return;throw new Error("Invalid mapping: "+JSON.stringify({generated:t,source:n,original:r,name:i}))}};ie.prototype._serializeMappings=function(){for(var t=0,r=1,n=0,i=0,o=0,a=0,u="",l,s,c,h,m=this._mappings.toArray(),f=0,w=m.length;f<w;f++){if(s=m[f],l="",s.generatedLine!==r)for(t=0;s.generatedLine!==r;)l+=";",r++;else if(f>0){if(!U.compareByGeneratedPositionsInflated(s,m[f-1]))continue;l+=","}l+=lt.encode(s.generatedColumn-t),t=s.generatedColumn,s.source!=null&&(h=this._sources.indexOf(s.source),l+=lt.encode(h-a),a=h,l+=lt.encode(s.originalLine-1-i),i=s.originalLine-1,l+=lt.encode(s.originalColumn-n),n=s.originalColumn,s.name!=null&&(c=this._names.indexOf(s.name),l+=lt.encode(c-o),o=c)),u+=l}return u};ie.prototype._generateSourcesContent=function(t,r){return t.map(function(n){if(!this._sourcesContents)return null;r!=null&&(n=U.relative(r,n));var i=U.toSetString(n);return Object.prototype.hasOwnProperty.call(this._sourcesContents,i)?this._sourcesContents[i]:null},this)};ie.prototype.toJSON=function(){var t={version:this._version,sources:this._sources.toArray(),names:this._names.toArray(),mappings:this._serializeMappings()};return this._file!=null&&(t.file=this._file),this._sourceRoot!=null&&(t.sourceRoot=this._sourceRoot),this._sourcesContents&&(t.sourcesContent=this._generateSourcesContent(t.sources,t.sourceRoot)),t};ie.prototype.toString=function(){return JSON.stringify(this.toJSON())};Bo.SourceMapGenerator=ie});var hf={};x(hf,{Lexer:()=>Fe,List:()=>X,OffsetToLocation:()=>Ge,TokenStream:()=>Ye,clone:()=>lr,createLexer:()=>nf,createSyntax:()=>er,definitionSyntax:()=>qr,find:()=>sf,findAll:()=>lf,findLast:()=>af,fork:()=>pf,fromPlainObject:()=>uf,generate:()=>tf,ident:()=>oo,isCustomProperty:()=>ht,keyword:()=>pt,lexer:()=>rf,parse:()=>ef,property:()=>Ht,string:()=>ir,toPlainObject:()=>cf,tokenNames:()=>Pe,tokenTypes:()=>_e,tokenize:()=>Jm,url:()=>sr,vendorPrefix:()=>El,version:()=>Xm,walk:()=>of});var _e={};x(_e,{AtKeyword:()=>P,BadString:()=>Ee,BadUrl:()=>Y,CDC:()=>q,CDO:()=>ue,Colon:()=>D,Comma:()=>R,Comment:()=>E,Delim:()=>y,Dimension:()=>k,EOF:()=>ne,Function:()=>g,Hash:()=>T,Ident:()=>p,LeftCurlyBracket:()=>I,LeftParenthesis:()=>v,LeftSquareBracket:()=>W,Number:()=>b,Percentage:()=>L,RightCurlyBracket:()=>H,RightParenthesis:()=>d,RightSquareBracket:()=>K,Semicolon:()=>F,String:()=>_,Url:()=>B,WhiteSpace:()=>S});var ne=0,p=1,g=2,P=3,T=4,_=5,Ee=6,B=7,Y=8,y=9,b=10,L=11,k=12,S=13,ue=14,q=15,D=16,F=17,R=18,W=19,K=20,v=21,d=22,I=23,H=24,E=25;function j(e){return e>=48&&e<=57}function te(e){return j(e)||e>=65&&e<=70||e>=97&&e<=102}function zt(e){return e>=65&&e<=90}function Wa(e){return e>=97&&e<=122}function ja(e){return zt(e)||Wa(e)}function qa(e){return e>=128}function Et(e){return ja(e)||qa(e)||e===95}function We(e){return Et(e)||j(e)||e===45}function Ua(e){return e>=0&&e<=8||e===11||e>=14&&e<=31||e===127}function nt(e){return e===10||e===13||e===12}function pe(e){return nt(e)||e===32||e===9}function Z(e,t){return!(e!==92||nt(t)||t===0)}function je(e,t,r){return e===45?Et(t)||t===45||Z(t,r):Et(e)?!0:e===92?Z(e,t):!1}function Pt(e,t,r){return e===43||e===45?j(t)?2:t===46&&j(r)?3:0:e===46?j(t)?2:0:j(e)?1:0}function It(e){return e===65279||e===65534?1:0}var cr=new Array(128),Ha=128,it=130,ur=131,Dt=132,pr=133;for(let e=0;e<cr.length;e++)cr[e]=pe(e)&&it||j(e)&&ur||Et(e)&&Dt||Ua(e)&&pr||e||Ha;function Nt(e){return e<128?cr[e]:Dt}function qe(e,t){return t<e.length?e.charCodeAt(t):0}function Ot(e,t,r){return r===13&&qe(e,t+1)===10?2:1}function be(e,t,r){let n=e.charCodeAt(t);return zt(n)&&(n=n|32),n===r}function xe(e,t,r,n){if(r-t!==n.length||t<0||r>e.length)return!1;for(let i=t;i<r;i++){let o=n.charCodeAt(i-t),a=e.charCodeAt(i);if(zt(a)&&(a=a|32),a!==o)return!1}return!0}function ao(e,t){for(;t>=0&&pe(e.charCodeAt(t));t--);return t+1}function ot(e,t){for(;t<e.length&&pe(e.charCodeAt(t));t++);return t}function hr(e,t){for(;t<e.length&&j(e.charCodeAt(t));t++);return t}function ae(e,t){if(t+=2,te(qe(e,t-1))){for(let n=Math.min(e.length,t+5);t<n&&te(qe(e,t));t++);let r=qe(e,t);pe(r)&&(t+=Ot(e,t,r))}return t}function st(e,t){for(;t<e.length;t++){let r=e.charCodeAt(t);if(!We(r)){if(Z(r,qe(e,t+1))){t=ae(e,t)-1;continue}break}}return t}function ze(e,t){let r=e.charCodeAt(t);if((r===43||r===45)&&(r=e.charCodeAt(t+=1)),j(r)&&(t=hr(e,t+1),r=e.charCodeAt(t)),r===46&&j(e.charCodeAt(t+1))&&(t+=2,t=hr(e,t)),be(e,t,101)){let n=0;r=e.charCodeAt(t+1),(r===45||r===43)&&(n=1,r=e.charCodeAt(t+2)),j(r)&&(t=hr(e,t+1+n+1))}return t}function Ft(e,t){for(;t<e.length;t++){let r=e.charCodeAt(t);if(r===41){t++;break}Z(r,qe(e,t+1))&&(t=ae(e,t))}return t}function Ue(e){if(e.length===1&&!te(e.charCodeAt(0)))return e[0];let t=parseInt(e,16);return(t===0||t>=55296&&t<=57343||t>1114111)&&(t=65533),String.fromCodePoint(t)}var Pe=["EOF-token","ident-token","function-token","at-keyword-token","hash-token","string-token","bad-string-token","url-token","bad-url-token","delim-token","number-token","percentage-token","dimension-token","whitespace-token","CDO-token","CDC-token","colon-token","semicolon-token","comma-token","[-token","]-token","(-token",")-token","{-token","}-token","comment-token"];function He(e=null,t){return e===null||e.length<t?new Uint32Array(Math.max(t+1024,16384)):e}var lo=10,Ga=12,co=13;function uo(e){let t=e.source,r=t.length,n=t.length>0?It(t.charCodeAt(0)):0,i=He(e.lines,r),o=He(e.columns,r),a=e.startLine,u=e.startColumn;for(let l=n;l<r;l++){let s=t.charCodeAt(l);i[l]=a,o[l]=u++,(s===lo||s===co||s===Ga)&&(s===co&&l+1<r&&t.charCodeAt(l+1)===lo&&(l++,i[l]=a,o[l]=u),a++,u=1)}i[r]=a,o[r]=u,e.lines=i,e.columns=o,e.computed=!0}var Ge=class{constructor(t,r,n,i){this.setSource(t,r,n,i),this.lines=null,this.columns=null}setSource(t="",r=0,n=1,i=1){this.source=t,this.startOffset=r,this.startLine=n,this.startColumn=i,this.computed=!1}getLocation(t,r){return this.computed||uo(this),{source:r,offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]}}getLocationRange(t,r,n){return this.computed||uo(this),{source:n,start:{offset:this.startOffset+t,line:this.lines[t],column:this.columns[t]},end:{offset:this.startOffset+r,line:this.lines[r],column:this.columns[r]}}}};var he=16777215,me=24,Ie=new Uint8Array(32);Ie[2]=22;Ie[21]=22;Ie[19]=20;Ie[23]=24;function po(e){return Ie[e]!==0}var Ye=class{constructor(t,r){this.setSource(t,r)}reset(){this.eof=!1,this.tokenIndex=-1,this.tokenType=0,this.tokenStart=this.firstCharOffset,this.tokenEnd=this.firstCharOffset}setSource(t="",r=()=>{}){t=String(t||"");let n=t.length,i=He(this.offsetAndType,t.length+1),o=He(this.balance,t.length+1),a=0,u=-1,l=0,s=t.length;this.offsetAndType=null,this.balance=null,o.fill(0),r(t,(c,h,m)=>{let f=a++;if(i[f]=c<<me|m,u===-1&&(u=h),o[f]=s,c===l){let w=o[s];o[s]=f,s=w,l=Ie[i[w]>>me]}else po(c)&&(s=f,l=Ie[c])}),i[a]=0<<me|n,o[a]=a;for(let c=0;c<a;c++){let h=o[c];if(h<=c){let m=o[h];m!==c&&(o[c]=m)}else h>a&&(o[c]=a)}this.source=t,this.firstCharOffset=u===-1?0:u,this.tokenCount=a,this.offsetAndType=i,this.balance=o,this.reset(),this.next()}lookupType(t){return t+=this.tokenIndex,t<this.tokenCount?this.offsetAndType[t]>>me:0}lookupTypeNonSC(t){for(let r=this.tokenIndex;r<this.tokenCount;r++){let n=this.offsetAndType[r]>>me;if(n!==13&&n!==25&&t--===0)return n}return 0}lookupOffset(t){return t+=this.tokenIndex,t<this.tokenCount?this.offsetAndType[t-1]&he:this.source.length}lookupOffsetNonSC(t){for(let r=this.tokenIndex;r<this.tokenCount;r++){let n=this.offsetAndType[r]>>me;if(n!==13&&n!==25&&t--===0)return r-this.tokenIndex}return 0}lookupValue(t,r){return t+=this.tokenIndex,t<this.tokenCount?xe(this.source,this.offsetAndType[t-1]&he,this.offsetAndType[t]&he,r):!1}getTokenStart(t){return t===this.tokenIndex?this.tokenStart:t>0?t<this.tokenCount?this.offsetAndType[t-1]&he:this.offsetAndType[this.tokenCount]&he:this.firstCharOffset}substrToCursor(t){return this.source.substring(t,this.tokenStart)}isBalanceEdge(t){return this.balance[this.tokenIndex]<t}isDelim(t,r){return r?this.lookupType(r)===9&&this.source.charCodeAt(this.lookupOffset(r))===t:this.tokenType===9&&this.source.charCodeAt(this.tokenStart)===t}skip(t){let r=this.tokenIndex+t;r<this.tokenCount?(this.tokenIndex=r,this.tokenStart=this.offsetAndType[r-1]&he,r=this.offsetAndType[r],this.tokenType=r>>me,this.tokenEnd=r&he):(this.tokenIndex=this.tokenCount,this.next())}next(){let t=this.tokenIndex+1;t<this.tokenCount?(this.tokenIndex=t,this.tokenStart=this.tokenEnd,t=this.offsetAndType[t],this.tokenType=t>>me,this.tokenEnd=t&he):(this.eof=!0,this.tokenIndex=this.tokenCount,this.tokenType=0,this.tokenStart=this.tokenEnd=this.source.length)}skipSC(){for(;this.tokenType===13||this.tokenType===25;)this.next()}skipUntilBalanced(t,r){let n=t,i=0,o=0;e:for(;n<this.tokenCount;n++){if(i=this.balance[n],i<t)break e;switch(o=n>0?this.offsetAndType[n-1]&he:this.firstCharOffset,r(this.source.charCodeAt(o))){case 1:break e;case 2:n++;break e;default:po(this.offsetAndType[n]>>me)&&(n=i)}}this.skip(n-this.tokenIndex)}forEachToken(t){for(let r=0,n=this.firstCharOffset;r<this.tokenCount;r++){let i=n,o=this.offsetAndType[r],a=o&he,u=o>>me;n=a,t(u,i,a,r)}}dump(){let t=new Array(this.tokenCount);return this.forEachToken((r,n,i,o)=>{t[o]={idx:o,type:Pe[r],chunk:this.source.substring(n,i),balance:this.balance[o]}}),t}};function Se(e,t){function r(h){return h<u?e.charCodeAt(h):0}function n(){if(s=ze(e,s),je(r(s),r(s+1),r(s+2))){c=12,s=st(e,s);return}if(r(s)===37){c=11,s++;return}c=10}function i(){let h=s;if(s=st(e,s),xe(e,h,s,"url")&&r(s)===40){if(s=ot(e,s+1),r(s)===34||r(s)===39){c=2,s=h+4;return}a();return}if(r(s)===40){c=2,s++;return}c=1}function o(h){for(h||(h=r(s++)),c=5;s<e.length;s++){let m=e.charCodeAt(s);switch(Nt(m)){case h:s++;return;case it:if(nt(m)){s+=Ot(e,s,m),c=6;return}break;case 92:if(s===e.length-1)break;let f=r(s+1);nt(f)?s+=Ot(e,s+1,f):Z(m,f)&&(s=ae(e,s)-1);break}}}function a(){for(c=7,s=ot(e,s);s<e.length;s++){let h=e.charCodeAt(s);switch(Nt(h)){case 41:s++;return;case it:if(s=ot(e,s),r(s)===41||s>=e.length){s<e.length&&s++;return}s=Ft(e,s),c=8;return;case 34:case 39:case 40:case pr:s=Ft(e,s),c=8;return;case 92:if(Z(h,r(s+1))){s=ae(e,s)-1;break}s=Ft(e,s),c=8;return}}}e=String(e||"");let u=e.length,l=It(r(0)),s=l,c;for(;s<u;){let h=e.charCodeAt(s);switch(Nt(h)){case it:c=13,s=ot(e,s+1);break;case 34:o();break;case 35:We(r(s+1))||Z(r(s+1),r(s+2))?(c=4,s=st(e,s+1)):(c=9,s++);break;case 39:o();break;case 40:c=21,s++;break;case 41:c=22,s++;break;case 43:Pt(h,r(s+1),r(s+2))?n():(c=9,s++);break;case 44:c=18,s++;break;case 45:Pt(h,r(s+1),r(s+2))?n():r(s+1)===45&&r(s+2)===62?(c=15,s=s+3):je(h,r(s+1),r(s+2))?i():(c=9,s++);break;case 46:Pt(h,r(s+1),r(s+2))?n():(c=9,s++);break;case 47:r(s+1)===42?(c=25,s=e.indexOf("*/",s+2),s=s===-1?e.length:s+2):(c=9,s++);break;case 58:c=16,s++;break;case 59:c=17,s++;break;case 60:r(s+1)===33&&r(s+2)===45&&r(s+3)===45?(c=14,s=s+4):(c=9,s++);break;case 64:je(r(s+1),r(s+2),r(s+3))?(c=3,s=st(e,s+1)):(c=9,s++);break;case 91:c=19,s++;break;case 92:Z(h,r(s+1))?i():(c=9,s++);break;case 93:c=20,s++;break;case 123:c=23,s++;break;case 125:c=24,s++;break;case ur:n();break;case Dt:i();break;default:c=9,s++}t(c,l,l=s)}}var Ke=null,X=class e{static createItem(t){return{prev:null,next:null,data:t}}constructor(){this.head=null,this.tail=null,this.cursor=null}createItem(t){return e.createItem(t)}allocateCursor(t,r){let n;return Ke!==null?(n=Ke,Ke=Ke.cursor,n.prev=t,n.next=r,n.cursor=this.cursor):n={prev:t,next:r,cursor:this.cursor},this.cursor=n,n}releaseCursor(){let{cursor:t}=this;this.cursor=t.cursor,t.prev=null,t.next=null,t.cursor=Ke,Ke=t}updateCursors(t,r,n,i){let{cursor:o}=this;for(;o!==null;)o.prev===t&&(o.prev=r),o.next===n&&(o.next=i),o=o.cursor}*[Symbol.iterator](){for(let t=this.head;t!==null;t=t.next)yield t.data}get size(){let t=0;for(let r=this.head;r!==null;r=r.next)t++;return t}get isEmpty(){return this.head===null}get first(){return this.head&&this.head.data}get last(){return this.tail&&this.tail.data}fromArray(t){let r=null;this.head=null;for(let n of t){let i=e.createItem(n);r!==null?r.next=i:this.head=i,i.prev=r,r=i}return this.tail=r,this}toArray(){return[...this]}toJSON(){return[...this]}forEach(t,r=this){let n=this.allocateCursor(null,this.head);for(;n.next!==null;){let i=n.next;n.next=i.next,t.call(r,i.data,i,this)}this.releaseCursor()}forEachRight(t,r=this){let n=this.allocateCursor(this.tail,null);for(;n.prev!==null;){let i=n.prev;n.prev=i.prev,t.call(r,i.data,i,this)}this.releaseCursor()}reduce(t,r,n=this){let i=this.allocateCursor(null,this.head),o=r,a;for(;i.next!==null;)a=i.next,i.next=a.next,o=t.call(n,o,a.data,a,this);return this.releaseCursor(),o}reduceRight(t,r,n=this){let i=this.allocateCursor(this.tail,null),o=r,a;for(;i.prev!==null;)a=i.prev,i.prev=a.prev,o=t.call(n,o,a.data,a,this);return this.releaseCursor(),o}some(t,r=this){for(let n=this.head;n!==null;n=n.next)if(t.call(r,n.data,n,this))return!0;return!1}map(t,r=this){let n=new e;for(let i=this.head;i!==null;i=i.next)n.appendData(t.call(r,i.data,i,this));return n}filter(t,r=this){let n=new e;for(let i=this.head;i!==null;i=i.next)t.call(r,i.data,i,this)&&n.appendData(i.data);return n}nextUntil(t,r,n=this){if(t===null)return;let i=this.allocateCursor(null,t);for(;i.next!==null;){let o=i.next;if(i.next=o.next,r.call(n,o.data,o,this))break}this.releaseCursor()}prevUntil(t,r,n=this){if(t===null)return;let i=this.allocateCursor(t,null);for(;i.prev!==null;){let o=i.prev;if(i.prev=o.prev,r.call(n,o.data,o,this))break}this.releaseCursor()}clear(){this.head=null,this.tail=null}copy(){let t=new e;for(let r of this)t.appendData(r);return t}prepend(t){return this.updateCursors(null,t,this.head,t),this.head!==null?(this.head.prev=t,t.next=this.head):this.tail=t,this.head=t,this}prependData(t){return this.prepend(e.createItem(t))}append(t){return this.insert(t)}appendData(t){return this.insert(e.createItem(t))}insert(t,r=null){if(r!==null)if(this.updateCursors(r.prev,t,r,t),r.prev===null){if(this.head!==r)throw new Error("before doesn't belong to list");this.head=t,r.prev=t,t.next=r,this.updateCursors(null,t)}else r.prev.next=t,t.prev=r.prev,r.prev=t,t.next=r;else this.updateCursors(this.tail,t,null,t),this.tail!==null?(this.tail.next=t,t.prev=this.tail):this.head=t,this.tail=t;return this}insertData(t,r){return this.insert(e.createItem(t),r)}remove(t){if(this.updateCursors(t,t.prev,t,t.next),t.prev!==null)t.prev.next=t.next;else{if(this.head!==t)throw new Error("item doesn't belong to list");this.head=t.next}if(t.next!==null)t.next.prev=t.prev;else{if(this.tail!==t)throw new Error("item doesn't belong to list");this.tail=t.prev}return t.prev=null,t.next=null,t}push(t){this.insert(e.createItem(t))}pop(){return this.tail!==null?this.remove(this.tail):null}unshift(t){this.prepend(e.createItem(t))}shift(){return this.head!==null?this.remove(this.head):null}prependList(t){return this.insertList(t,this.head)}appendList(t){return this.insertList(t)}insertList(t,r){return t.head===null?this:(r!=null?(this.updateCursors(r.prev,t.tail,r,t.head),r.prev!==null?(r.prev.next=t.head,t.head.prev=r.prev):this.head=t.head,r.prev=t.tail,t.tail.next=r):(this.updateCursors(this.tail,t.tail,null,t.head),this.tail!==null?(this.tail.next=t.head,t.head.prev=this.tail):this.head=t.head,this.tail=t.tail),t.head=null,t.tail=null,this)}replace(t,r){"head"in r?this.insertList(r,t):this.insert(r,t),this.remove(t)}};function De(e,t){let r=Object.create(SyntaxError.prototype),n=new Error;return Object.assign(r,{name:e,message:t,get stack(){return(n.stack||"").replace(/^(.+\n){1,3}/,`${e}: ${t}
`)}})}var mr=100,ho=60,mo="    ";function fo({source:e,line:t,column:r,baseLine:n,baseColumn:i},o){function a(w,ee){return s.slice(w,ee).map((G,C)=>String(w+C+1).padStart(m)+" |"+G).join(`
`)}let u=`
`.repeat(Math.max(n-1,0)),l=" ".repeat(Math.max(i-1,0)),s=(u+l+e).split(/\r\n?|\n|\f/),c=Math.max(1,t-o)-1,h=Math.min(t+o,s.length+1),m=Math.max(4,String(h).length)+1,f=0;r+=(mo.length-1)*(s[t-1].substr(0,r-1).match(/\t/g)||[]).length,r>mr&&(f=r-ho+3,r=ho-2);for(let w=c;w<=h;w++)w>=0&&w<s.length&&(s[w]=s[w].replace(/\t/g,mo),s[w]=(f>0&&s[w].length>f?"\u2026":"")+s[w].substr(f,mr-2)+(s[w].length>f+mr-1?"\u2026":""));return[a(c,t),new Array(r+m+2).join("-")+"^",a(t,h)].filter(Boolean).join(`
`).replace(/^(\s+\d+\s+\|\n)+/,"").replace(/\n(\s+\d+\s+\|)+$/,"")}function fr(e,t,r,n,i,o=1,a=1){return Object.assign(De("SyntaxError",e),{source:t,offset:r,line:n,column:i,sourceFragment(l){return fo({source:t,line:n,column:i,baseLine:o,baseColumn:a},isNaN(l)?0:l)},get formattedMessage(){return`Parse error: ${e}
`+fo({source:t,line:n,column:i,baseLine:o,baseColumn:a},2)}})}function go(e){let t=this.createList(),r=!1,n={recognizer:e};for(;!this.eof;){switch(this.tokenType){case 25:this.next();continue;case 13:r=!0,this.next();continue}let i=e.getNode.call(this,n);if(i===void 0)break;r&&(e.onWhiteSpace&&e.onWhiteSpace.call(this,i,t,n),r=!1),t.push(i)}return r&&e.onWhiteSpace&&e.onWhiteSpace.call(this,null,t,n),t}var bo=()=>{},Ya=33,Ka=35,dr=59,xo=123,yo=0;function Va(e){return function(){return this[e]()}}function gr(e){let t=Object.create(null);for(let r of Object.keys(e)){let n=e[r],i=n.parse||n;i&&(t[r]=i)}return t}function Qa(e){let t={context:Object.create(null),features:Object.assign(Object.create(null),e.features),scope:Object.assign(Object.create(null),e.scope),atrule:gr(e.atrule),pseudo:gr(e.pseudo),node:gr(e.node)};for(let[r,n]of Object.entries(e.parseContext))switch(typeof n){case"function":t.context[r]=n;break;case"string":t.context[r]=Va(n);break}return{config:t,...t,...t.node}}function ko(e){let t="",r="<unknown>",n=!1,i=bo,o=!1,a=new Ge,u=Object.assign(new Ye,Qa(e||{}),{parseAtrulePrelude:!0,parseRulePrelude:!0,parseValue:!0,parseCustomProperty:!1,readSequence:go,consumeUntilBalanceEnd:()=>0,consumeUntilLeftCurlyBracket(s){return s===xo?1:0},consumeUntilLeftCurlyBracketOrSemicolon(s){return s===xo||s===dr?1:0},consumeUntilExclamationMarkOrSemicolon(s){return s===Ya||s===dr?1:0},consumeUntilSemicolonIncluded(s){return s===dr?2:0},createList(){return new X},createSingleNodeList(s){return new X().appendData(s)},getFirstListNode(s){return s&&s.first},getLastListNode(s){return s&&s.last},parseWithFallback(s,c){let h=this.tokenIndex;try{return s.call(this)}catch(m){if(o)throw m;this.skip(h-this.tokenIndex);let f=c.call(this);return o=!0,i(m,f),o=!1,f}},lookupNonWSType(s){let c;do if(c=this.lookupType(s++),c!==13&&c!==25)return c;while(c!==yo);return yo},charCodeAt(s){return s>=0&&s<t.length?t.charCodeAt(s):0},substring(s,c){return t.substring(s,c)},substrToCursor(s){return this.source.substring(s,this.tokenStart)},cmpChar(s,c){return be(t,s,c)},cmpStr(s,c,h){return xe(t,s,c,h)},consume(s){let c=this.tokenStart;return this.eat(s),this.substrToCursor(c)},consumeFunctionName(){let s=t.substring(this.tokenStart,this.tokenEnd-1);return this.eat(2),s},consumeNumber(s){let c=t.substring(this.tokenStart,ze(t,this.tokenStart));return this.eat(s),c},eat(s){if(this.tokenType!==s){let c=Pe[s].slice(0,-6).replace(/-/g," ").replace(/^./,f=>f.toUpperCase()),h=`${/[[\](){}]/.test(c)?`"${c}"`:c} is expected`,m=this.tokenStart;switch(s){case 1:this.tokenType===2||this.tokenType===7?(m=this.tokenEnd-1,h="Identifier is expected but function found"):h="Identifier is expected";break;case 4:this.isDelim(Ka)&&(this.next(),m++,h="Name is expected");break;case 11:this.tokenType===10&&(m=this.tokenEnd,h="Percent sign is expected");break}this.error(h,m)}this.next()},eatIdent(s){(this.tokenType!==1||this.lookupValue(0,s)===!1)&&this.error(`Identifier "${s}" is expected`),this.next()},eatDelim(s){this.isDelim(s)||this.error(`Delim "${String.fromCharCode(s)}" is expected`),this.next()},getLocation(s,c){return n?a.getLocationRange(s,c,r):null},getLocationFromList(s){if(n){let c=this.getFirstListNode(s),h=this.getLastListNode(s);return a.getLocationRange(c!==null?c.loc.start.offset-a.startOffset:this.tokenStart,h!==null?h.loc.end.offset-a.startOffset:this.tokenStart,r)}return null},error(s,c){let h=typeof c<"u"&&c<t.length?a.getLocation(c):this.eof?a.getLocation(ao(t,t.length-1)):a.getLocation(this.tokenStart);throw new fr(s||"Unexpected input",t,h.offset,h.line,h.column,a.startLine,a.startColumn)}});return Object.assign(function(s,c){t=s,c=c||{},u.setSource(t,Se),a.setSource(t,c.offset,c.line,c.column),r=c.filename||"<unknown>",n=!!c.positions,i=typeof c.onParseError=="function"?c.onParseError:bo,o=!1,u.parseAtrulePrelude="parseAtrulePrelude"in c?!!c.parseAtrulePrelude:!0,u.parseRulePrelude="parseRulePrelude"in c?!!c.parseRulePrelude:!0,u.parseValue="parseValue"in c?!!c.parseValue:!0,u.parseCustomProperty="parseCustomProperty"in c?!!c.parseCustomProperty:!1;let{context:h="default",onComment:m}=c;if(!(h in u.context))throw new Error("Unknown context `"+h+"`");typeof m=="function"&&u.forEachToken((w,ee,G)=>{if(w===25){let C=u.getLocation(ee,G),M=xe(t,G-2,G,"*/")?t.slice(ee+2,G-2):t.slice(ee+2,G);m(M,C)}});let f=u.context[h].call(u,c);return u.eof||u.error(),f},{SyntaxError:fr,config:u.config})}var jo=Ba(_o(),1),Wo=new Set(["Atrule","Selector","Declaration"]);function qo(e){let t=new jo.SourceMapGenerator,r={line:1,column:0},n={line:0,column:0},i={line:1,column:0},o={generated:i},a=1,u=0,l=!1,s=e.node;e.node=function(m){if(m.loc&&m.loc.start&&Wo.has(m.type)){let f=m.loc.start.line,w=m.loc.start.column-1;(n.line!==f||n.column!==w)&&(n.line=f,n.column=w,r.line=a,r.column=u,l&&(l=!1,(r.line!==i.line||r.column!==i.column)&&t.addMapping(o)),l=!0,t.addMapping({source:m.loc.source,original:n,generated:r}))}s.call(this,m),l&&Wo.has(m.type)&&(i.line=a,i.column=u)};let c=e.emit;e.emit=function(m,f,w){for(let ee=0;ee<m.length;ee++)m.charCodeAt(ee)===10?(a++,u=0):u++;c(m,f,w)};let h=e.result;return e.result=function(){return l&&t.addMapping(o),{css:h(),map:t}},e}var _t={};x(_t,{safe:()=>Cr,spec:()=>bl});var fl=43,dl=45,Sr=(e,t)=>{if(e===9&&(e=t),typeof e=="string"){let r=e.charCodeAt(0);return r>127?32768:r<<8}return e},Uo=[[1,1],[1,2],[1,7],[1,8],[1,"-"],[1,10],[1,11],[1,12],[1,15],[1,21],[3,1],[3,2],[3,7],[3,8],[3,"-"],[3,10],[3,11],[3,12],[3,15],[4,1],[4,2],[4,7],[4,8],[4,"-"],[4,10],[4,11],[4,12],[4,15],[12,1],[12,2],[12,7],[12,8],[12,"-"],[12,10],[12,11],[12,12],[12,15],["#",1],["#",2],["#",7],["#",8],["#","-"],["#",10],["#",11],["#",12],["#",15],["-",1],["-",2],["-",7],["-",8],["-","-"],["-",10],["-",11],["-",12],["-",15],[10,1],[10,2],[10,7],[10,8],[10,10],[10,11],[10,12],[10,"%"],[10,15],["@",1],["@",2],["@",7],["@",8],["@","-"],["@",15],[".",10],[".",11],[".",12],["+",10],["+",11],["+",12],["/","*"]],gl=Uo.concat([[1,4],[12,4],[4,4],[3,21],[3,5],[3,16],[11,11],[11,12],[11,2],[11,"-"],[22,1],[22,2],[22,11],[22,12],[22,4],[22,"-"]]);function Ho(e){let t=new Set(e.map(([r,n])=>Sr(r)<<16|Sr(n)));return function(r,n,i){let o=Sr(n,i),a=i.charCodeAt(0);return(a===dl&&n!==1&&n!==2&&n!==15||a===fl?t.has(r<<16|a<<8):t.has(r<<16|o))&&this.emit(" ",13,!0),o}}var bl=Ho(Uo),Cr=Ho(gl);var xl=92;function yl(e,t){if(typeof t=="function"){let r=null;e.children.forEach(n=>{r!==null&&t.call(this,r),this.node(n),r=n});return}e.children.forEach(this.node,this)}function kl(e){Se(e,(t,r,n)=>{this.token(t,e.slice(r,n))})}function Go(e){let t=new Map;for(let[r,n]of Object.entries(e.node))typeof(n.generate||n)=="function"&&t.set(r,n.generate||n);return function(r,n){let i="",o=0,a={node(l){if(t.has(l.type))t.get(l.type).call(u,l);else throw new Error("Unknown node type: "+l.type)},tokenBefore:Cr,token(l,s){o=this.tokenBefore(o,l,s),this.emit(s,l,!1),l===9&&s.charCodeAt(0)===xl&&this.emit(`
`,13,!0)},emit(l){i+=l},result(){return i}};n&&(typeof n.decorator=="function"&&(a=n.decorator(a)),n.sourceMap&&(a=qo(a)),n.mode in _t&&(a.tokenBefore=_t[n.mode]));let u={node:l=>a.node(l),children:yl,token:(l,s)=>a.token(l,s),tokenize:kl};return a.node(r),a.result()}}function Yo(e){return{fromPlainObject(t){return e(t,{enter(r){r.children&&!(r.children instanceof X)&&(r.children=new X().fromArray(r.children))}}),t},toPlainObject(t){return e(t,{leave(r){r.children&&r.children instanceof X&&(r.children=r.children.toArray())}}),t}}}var{hasOwnProperty:Tr}=Object.prototype,ct=function(){};function Ko(e){return typeof e=="function"?e:ct}function Vo(e,t){return function(r,n,i){r.type===t&&e.call(this,r,n,i)}}function wl(e,t){let r=t.structure,n=[];for(let i in r){if(Tr.call(r,i)===!1)continue;let o=r[i],a={name:i,type:!1,nullable:!1};Array.isArray(o)||(o=[o]);for(let u of o)u===null?a.nullable=!0:typeof u=="string"?a.type="node":Array.isArray(u)&&(a.type="list");a.type&&n.push(a)}return n.length?{context:t.walkContext,fields:n}:null}function vl(e){let t={};for(let r in e.node)if(Tr.call(e.node,r)){let n=e.node[r];if(!n.structure)throw new Error("Missed `structure` field in `"+r+"` node type definition");t[r]=wl(r,n)}return t}function Qo(e,t){let r=e.fields.slice(),n=e.context,i=typeof n=="string";return t&&r.reverse(),function(o,a,u,l){let s;i&&(s=a[n],a[n]=o);for(let c of r){let h=o[c.name];if(!c.nullable||h){if(c.type==="list"){if(t?h.reduceRight(l,!1):h.reduce(l,!1))return!0}else if(u(h))return!0}}i&&(a[n]=s)}}function Xo({StyleSheet:e,Atrule:t,Rule:r,Block:n,DeclarationList:i}){return{Atrule:{StyleSheet:e,Atrule:t,Rule:r,Block:n},Rule:{StyleSheet:e,Atrule:t,Rule:r,Block:n},Declaration:{StyleSheet:e,Atrule:t,Rule:r,Block:n,DeclarationList:i}}}function $o(e){let t=vl(e),r={},n={},i=Symbol("break-walk"),o=Symbol("skip-node");for(let s in t)Tr.call(t,s)&&t[s]!==null&&(r[s]=Qo(t[s],!1),n[s]=Qo(t[s],!0));let a=Xo(r),u=Xo(n),l=function(s,c){function h(C,M,ve){let O=m.call(G,C,M,ve);return O===i?!0:O===o?!1:!!(w.hasOwnProperty(C.type)&&w[C.type](C,G,h,ee)||f.call(G,C,M,ve)===i)}let m=ct,f=ct,w=r,ee=(C,M,ve,O)=>C||h(M,ve,O),G={break:i,skip:o,root:s,stylesheet:null,atrule:null,atrulePrelude:null,rule:null,selector:null,block:null,declaration:null,function:null};if(typeof c=="function")m=c;else if(c&&(m=Ko(c.enter),f=Ko(c.leave),c.reverse&&(w=n),c.visit)){if(a.hasOwnProperty(c.visit))w=c.reverse?u[c.visit]:a[c.visit];else if(!t.hasOwnProperty(c.visit))throw new Error("Bad value `"+c.visit+"` for `visit` option (should be: "+Object.keys(t).sort().join(", ")+")");m=Vo(m,c.visit),f=Vo(f,c.visit)}if(m===ct&&f===ct)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");h(s)};return l.break=i,l.skip=o,l.find=function(s,c){let h=null;return l(s,function(m,f,w){if(c.call(this,m,f,w))return h=m,i}),h},l.findLast=function(s,c){let h=null;return l(s,{reverse:!0,enter(m,f,w){if(c.call(this,m,f,w))return h=m,i}}),h},l.findAll=function(s,c){let h=[];return l(s,function(m,f,w){c.call(this,m,f,w)&&h.push(m)}),h},l}function Sl(e){return e}function Cl(e){let{min:t,max:r,comma:n}=e;return t===0&&r===0?n?"#?":"*":t===0&&r===1?"?":t===1&&r===0?n?"#":"+":t===1&&r===1?"":(n?"#":"")+(t===r?"{"+t+"}":"{"+t+","+(r!==0?r:"")+"}")}function Tl(e){switch(e.type){case"Range":return" ["+(e.min===null?"-\u221E":e.min)+","+(e.max===null?"\u221E":e.max)+"]";default:throw new Error("Unknown node type `"+e.type+"`")}}function Al(e,t,r,n){let i=e.combinator===" "||n?e.combinator:" "+e.combinator+" ",o=e.terms.map(a=>Wt(a,t,r,n)).join(i);return e.explicit||r?(n||o[0]===","?"[":"[ ")+o+(n?"]":" ]"):o}function Wt(e,t,r,n){let i;switch(e.type){case"Group":i=Al(e,t,r,n)+(e.disallowEmpty?"!":"");break;case"Multiplier":return Wt(e.term,t,r,n)+t(Cl(e),e);case"Boolean":i="<boolean-expr["+Wt(e.term,t,r,n)+"]>";break;case"Type":i="<"+e.name+(e.opts?t(Tl(e.opts),e.opts):"")+">";break;case"Property":i="<'"+e.name+"'>";break;case"Keyword":i=e.name;break;case"AtKeyword":i="@"+e.name;break;case"Function":i=e.name+"(";break;case"String":case"Token":i=e.value;break;case"Comma":i=",";break;default:throw new Error("Unknown node type `"+e.type+"`")}return t(i,e)}function Oe(e,t){let r=Sl,n=!1,i=!1;return typeof t=="function"?r=t:t&&(n=!!t.forceBraces,i=!!t.compact,typeof t.decorate=="function"&&(r=t.decorate)),Wt(e,r,n,i)}var Zo={offset:0,line:1,column:1};function Ll(e,t){let r=e.tokens,n=e.longestMatch,i=n<r.length&&r[n].node||null,o=i!==t?i:null,a=0,u=0,l=0,s="",c,h;for(let m=0;m<r.length;m++){let f=r[m].value;m===n&&(u=f.length,a=s.length),o!==null&&r[m].node===o&&(m<=n?l++:l=0),s+=f}return n===r.length||l>1?(c=jt(o||t,"end")||ut(Zo,s),h=ut(c)):(c=jt(o,"start")||ut(jt(t,"start")||Zo,s.slice(0,a)),h=jt(o,"end")||ut(c,s.substr(a,u))),{css:s,mismatchOffset:a,mismatchLength:u,start:c,end:h}}function jt(e,t){let r=e&&e.loc&&e.loc[t];return r?"line"in r?ut(r):r:null}function ut({offset:e,line:t,column:r},n){let i={offset:e,line:t,column:r};if(n){let o=n.split(/\n|\r\n?|\f/);i.offset+=n.length,i.line+=o.length-1,i.column=o.length===1?i.column+n.length:o.pop().length+1}return i}var Qe=function(e,t){let r=De("SyntaxReferenceError",e+(t?" `"+t+"`":""));return r.reference=t,r},Jo=function(e,t,r,n){let i=De("SyntaxMatchError",e),{css:o,mismatchOffset:a,mismatchLength:u,start:l,end:s}=Ll(n,r);return i.rawMessage=e,i.syntax=t?Oe(t):"<generic>",i.css=o,i.mismatchOffset=a,i.mismatchLength=u,i.message=e+`
  syntax: `+i.syntax+`
   value: `+(o||"<empty string>")+`
  --------`+new Array(i.mismatchOffset+1).join("-")+"^",Object.assign(i,l),i.loc={source:r&&r.loc&&r.loc.source||"<unknown>",start:l,end:s},i};var qt=new Map,Xe=new Map,Ut=45,pt=zl,Ht=Pl,El=Ar;function ht(e,t){return t=t||0,e.length-t>=2&&e.charCodeAt(t)===Ut&&e.charCodeAt(t+1)===Ut}function Ar(e,t){if(t=t||0,e.length-t>=3&&e.charCodeAt(t)===Ut&&e.charCodeAt(t+1)!==Ut){let r=e.indexOf("-",t+2);if(r!==-1)return e.substring(t,r+1)}return""}function zl(e){if(qt.has(e))return qt.get(e);let t=e.toLowerCase(),r=qt.get(t);if(r===void 0){let n=ht(t,0),i=n?"":Ar(t,0);r=Object.freeze({basename:t.substr(i.length),name:t,prefix:i,vendor:i,custom:n})}return qt.set(e,r),r}function Pl(e){if(Xe.has(e))return Xe.get(e);let t=e,r=e[0];r==="/"?r=e[1]==="/"?"//":"/":r!=="_"&&r!=="*"&&r!=="$"&&r!=="#"&&r!=="+"&&r!=="&"&&(r="");let n=ht(t,r.length);if(!n&&(t=t.toLowerCase(),Xe.has(t))){let u=Xe.get(t);return Xe.set(e,u),u}let i=n?"":Ar(t,r.length),o=t.substr(0,r.length+i.length),a=Object.freeze({basename:t.substr(o.length),name:t.substr(r.length),hack:r,vendor:i,prefix:o,custom:n});return Xe.set(e,a),a}var $e=["initial","inherit","unset","revert","revert-layer"];var ft=43,fe=45,Lr=110,Ze=!0,Dl=!1;function zr(e,t){return e!==null&&e.type===9&&e.value.charCodeAt(0)===t}function mt(e,t,r){for(;e!==null&&(e.type===13||e.type===25);)e=r(++t);return t}function Ce(e,t,r,n){if(!e)return 0;let i=e.value.charCodeAt(t);if(i===ft||i===fe){if(r)return 0;t++}for(;t<e.value.length;t++)if(!j(e.value.charCodeAt(t)))return 0;return n+1}function Er(e,t,r){let n=!1,i=mt(e,t,r);if(e=r(i),e===null)return t;if(e.type!==10)if(zr(e,ft)||zr(e,fe)){if(n=!0,i=mt(r(++i),i,r),e=r(i),e===null||e.type!==10)return 0}else return t;if(!n){let o=e.value.charCodeAt(0);if(o!==ft&&o!==fe)return 0}return Ce(e,n?0:1,n,i)}function Pr(e,t){let r=0;if(!e)return 0;if(e.type===10)return Ce(e,0,Dl,r);if(e.type===1&&e.value.charCodeAt(0)===fe){if(!be(e.value,1,Lr))return 0;switch(e.value.length){case 2:return Er(t(++r),r,t);case 3:return e.value.charCodeAt(2)!==fe?0:(r=mt(t(++r),r,t),e=t(r),Ce(e,0,Ze,r));default:return e.value.charCodeAt(2)!==fe?0:Ce(e,3,Ze,r)}}else if(e.type===1||zr(e,ft)&&t(r+1).type===1){if(e.type!==1&&(e=t(++r)),e===null||!be(e.value,0,Lr))return 0;switch(e.value.length){case 1:return Er(t(++r),r,t);case 2:return e.value.charCodeAt(1)!==fe?0:(r=mt(t(++r),r,t),e=t(r),Ce(e,0,Ze,r));default:return e.value.charCodeAt(1)!==fe?0:Ce(e,2,Ze,r)}}else if(e.type===12){let n=e.value.charCodeAt(0),i=n===ft||n===fe?1:0,o=i;for(;o<e.value.length&&j(e.value.charCodeAt(o));o++);return o===i||!be(e.value,o,Lr)?0:o+1===e.value.length?Er(t(++r),r,t):e.value.charCodeAt(o+1)!==fe?0:o+2===e.value.length?(r=mt(t(++r),r,t),e=t(r),Ce(e,0,Ze,r)):Ce(e,o+2,Ze,r)}return 0}var Nl=43,es=45,ts=63,Ol=117;function Ir(e,t){return e!==null&&e.type===9&&e.value.charCodeAt(0)===t}function Fl(e,t){return e.value.charCodeAt(0)===t}function dt(e,t,r){let n=0;for(let i=t;i<e.value.length;i++){let o=e.value.charCodeAt(i);if(o===es&&r&&n!==0)return dt(e,t+n+1,!1),6;if(!te(o)||++n>6)return 0}return n}function Gt(e,t,r){if(!e)return 0;for(;Ir(r(t),ts);){if(++e>6)return 0;t++}return t}function Dr(e,t){let r=0;if(e===null||e.type!==1||!be(e.value,0,Ol)||(e=t(++r),e===null))return 0;if(Ir(e,Nl))return e=t(++r),e===null?0:e.type===1?Gt(dt(e,0,!0),++r,t):Ir(e,ts)?Gt(1,++r,t):0;if(e.type===10){let n=dt(e,1,!0);return n===0?0:(e=t(++r),e===null?r:e.type===12||e.type===10?!Fl(e,es)||!dt(e,1,!1)?0:r+1:Gt(n,r,t))}return e.type===12?Gt(dt(e,1,!0),++r,t):0}var Rl=["calc(","-moz-calc(","-webkit-calc("],Nr=new Map([[2,22],[21,22],[19,20],[23,24]]);function le(e,t){return t<e.length?e.charCodeAt(t):0}function rs(e,t){return xe(e,0,e.length,t)}function ns(e,t){for(let r=0;r<t.length;r++)if(rs(e,t[r]))return!0;return!1}function is(e,t){return t!==e.length-2?!1:le(e,t)===92&&j(le(e,t+1))}function Yt(e,t,r){if(e&&e.type==="Range"){let n=Number(r!==void 0&&r!==t.length?t.substr(0,r):t);if(isNaN(n)||e.min!==null&&n<e.min&&typeof e.min!="string"||e.max!==null&&n>e.max&&typeof e.max!="string")return!0}return!1}function Ml(e,t){let r=0,n=[],i=0;e:do{switch(e.type){case 24:case 22:case 20:if(e.type!==r)break e;if(r=n.pop(),n.length===0){i++;break e}break;case 2:case 21:case 19:case 23:n.push(r),r=Nr.get(e.type);break}i++}while(e=t(i));return i}function oe(e){return function(t,r,n){return t===null?0:t.type===2&&ns(t.value,Rl)?Ml(t,r):e(t,r,n)}}function N(e){return function(t){return t===null||t.type!==e?0:1}}function Bl(e){if(e===null||e.type!==1)return 0;let t=e.value.toLowerCase();return ns(t,$e)||rs(t,"default")?0:1}function os(e){return e===null||e.type!==1||le(e.value,0)!==45||le(e.value,1)!==45?0:1}function _l(e){return!os(e)||e.value==="--"?0:1}function Wl(e){if(e===null||e.type!==4)return 0;let t=e.value.length;if(t!==4&&t!==5&&t!==7&&t!==9)return 0;for(let r=1;r<t;r++)if(!te(le(e.value,r)))return 0;return 1}function jl(e){return e===null||e.type!==4||!je(le(e.value,1),le(e.value,2),le(e.value,3))?0:1}function ql(e,t){if(!e)return 0;let r=0,n=[],i=0;e:do{switch(e.type){case 6:case 8:break e;case 24:case 22:case 20:if(e.type!==r)break e;r=n.pop();break;case 17:if(r===0)break e;break;case 9:if(r===0&&e.value==="!")break e;break;case 2:case 21:case 19:case 23:n.push(r),r=Nr.get(e.type);break}i++}while(e=t(i));return i}function Ul(e,t){if(!e)return 0;let r=0,n=[],i=0;e:do{switch(e.type){case 6:case 8:break e;case 24:case 22:case 20:if(e.type!==r)break e;r=n.pop();break;case 2:case 21:case 19:case 23:n.push(r),r=Nr.get(e.type);break}i++}while(e=t(i));return i}function we(e){return e&&(e=new Set(e)),function(t,r,n){if(t===null||t.type!==12)return 0;let i=ze(t.value,0);if(e!==null){let o=t.value.indexOf("\\",i),a=o===-1||!is(t.value,o)?t.value.substr(i):t.value.substring(i,o);if(e.has(a.toLowerCase())===!1)return 0}return Yt(n,t.value,i)?0:1}}function Hl(e,t,r){return e===null||e.type!==11||Yt(r,e.value,e.value.length-1)?0:1}function ss(e){return typeof e!="function"&&(e=function(){return 0}),function(t,r,n){return t!==null&&t.type===10&&Number(t.value)===0?1:e(t,r,n)}}function Gl(e,t,r){if(e===null)return 0;let n=ze(e.value,0);return!(n===e.value.length)&&!is(e.value,n)||Yt(r,e.value,n)?0:1}function Yl(e,t,r){if(e===null||e.type!==10)return 0;let n=le(e.value,0)===43||le(e.value,0)===45?1:0;for(;n<e.value.length;n++)if(!j(le(e.value,n)))return 0;return Yt(r,e.value,n)?0:1}var Kl={"ident-token":N(1),"function-token":N(2),"at-keyword-token":N(3),"hash-token":N(4),"string-token":N(5),"bad-string-token":N(6),"url-token":N(7),"bad-url-token":N(8),"delim-token":N(9),"number-token":N(10),"percentage-token":N(11),"dimension-token":N(12),"whitespace-token":N(13),"CDO-token":N(14),"CDC-token":N(15),"colon-token":N(16),"semicolon-token":N(17),"comma-token":N(18),"[-token":N(19),"]-token":N(20),"(-token":N(21),")-token":N(22),"{-token":N(23),"}-token":N(24)},Vl={string:N(5),ident:N(1),percentage:oe(Hl),zero:ss(),number:oe(Gl),integer:oe(Yl),"custom-ident":Bl,"dashed-ident":os,"custom-property-name":_l,"hex-color":Wl,"id-selector":jl,"an-plus-b":Pr,urange:Dr,"declaration-value":ql,"any-value":Ul};function Ql(e){let{angle:t,decibel:r,frequency:n,flex:i,length:o,resolution:a,semitones:u,time:l}=e||{};return{dimension:oe(we(null)),angle:oe(we(t)),decibel:oe(we(r)),frequency:oe(we(n)),flex:oe(we(i)),length:oe(ss(we(o))),resolution:oe(we(a)),semitones:oe(we(u)),time:oe(we(l))}}function as(e){return{...Kl,...Vl,...Ql(e)}}var Kt={};x(Kt,{angle:()=>$l,decibel:()=>rc,flex:()=>tc,frequency:()=>Jl,length:()=>Xl,resolution:()=>ec,semitones:()=>nc,time:()=>Zl});var Xl=["cm","mm","q","in","pt","pc","px","em","rem","ex","rex","cap","rcap","ch","rch","ic","ric","lh","rlh","vw","svw","lvw","dvw","vh","svh","lvh","dvh","vi","svi","lvi","dvi","vb","svb","lvb","dvb","vmin","svmin","lvmin","dvmin","vmax","svmax","lvmax","dvmax","cqw","cqh","cqi","cqb","cqmin","cqmax"],$l=["deg","grad","rad","turn"],Zl=["s","ms"],Jl=["hz","khz"],ec=["dpi","dpcm","dppx","x"],tc=["fr"],rc=["db"],nc=["st"];var qr={};x(qr,{SyntaxError:()=>Vt,generate:()=>Oe,parse:()=>Je,walk:()=>Zt});function Vt(e,t,r){return Object.assign(De("SyntaxError",e),{input:t,offset:r,rawMessage:e,message:e+`
  `+t+`
--`+new Array((r||t.length)+1).join("-")+"^"})}var ic=9,oc=10,sc=12,ac=13,lc=32,ls=new Uint8Array(128).map((e,t)=>/[a-zA-Z0-9\-]/.test(String.fromCharCode(t))?1:0),Qt=class{constructor(t){this.str=t,this.pos=0}charCodeAt(t){return t<this.str.length?this.str.charCodeAt(t):0}charCode(){return this.charCodeAt(this.pos)}isNameCharCode(t=this.charCode()){return t<128&&ls[t]===1}nextCharCode(){return this.charCodeAt(this.pos+1)}nextNonWsCode(t){return this.charCodeAt(this.findWsEnd(t))}skipWs(){this.pos=this.findWsEnd(this.pos)}findWsEnd(t){for(;t<this.str.length;t++){let r=this.str.charCodeAt(t);if(r!==ac&&r!==oc&&r!==sc&&r!==lc&&r!==ic)break}return t}substringToPos(t){return this.str.substring(this.pos,this.pos=t)}eat(t){this.charCode()!==t&&this.error("Expect `"+String.fromCharCode(t)+"`"),this.pos++}peek(){return this.pos<this.str.length?this.str.charAt(this.pos++):""}error(t){throw new Vt(t,this.str,this.pos)}scanSpaces(){return this.substringToPos(this.findWsEnd(this.pos))}scanWord(){let t=this.pos;for(;t<this.str.length;t++){let r=this.str.charCodeAt(t);if(r>=128||ls[r]===0)break}return this.pos===t&&this.error("Expect a keyword"),this.substringToPos(t)}scanNumber(){let t=this.pos;for(;t<this.str.length;t++){let r=this.str.charCodeAt(t);if(r<48||r>57)break}return this.pos===t&&this.error("Expect a number"),this.substringToPos(t)}scanString(){let t=this.str.indexOf("'",this.pos+1);return t===-1&&(this.pos=this.str.length,this.error("Expect an apostrophe")),this.substringToPos(t+1)}};var cc=9,uc=10,pc=12,hc=13,mc=32,gs=33,Mr=35,cs=38,Xt=39,bs=40,fc=41,xs=42,Br=43,_r=44,us=45,Wr=60,Fr=62,Rr=63,dc=64,gt=91,bt=93,$t=123,ps=124,hs=125,ms=8734,fs={" ":1,"&&":2,"||":3,"|":4};function ds(e){let t=null,r=null;return e.eat($t),e.skipWs(),t=e.scanNumber(e),e.skipWs(),e.charCode()===_r?(e.pos++,e.skipWs(),e.charCode()!==hs&&(r=e.scanNumber(e),e.skipWs())):r=t,e.eat(hs),{min:Number(t),max:r?Number(r):0}}function gc(e){let t=null,r=!1;switch(e.charCode()){case xs:e.pos++,t={min:0,max:0};break;case Br:e.pos++,t={min:1,max:0};break;case Rr:e.pos++,t={min:0,max:1};break;case Mr:e.pos++,r=!0,e.charCode()===$t?t=ds(e):e.charCode()===Rr?(e.pos++,t={min:0,max:0}):t={min:1,max:0};break;case $t:t=ds(e);break;default:return null}return{type:"Multiplier",comma:r,min:t.min,max:t.max,term:null}}function Te(e,t){let r=gc(e);return r!==null?(r.term=t,e.charCode()===Mr&&e.charCodeAt(e.pos-1)===Br?Te(e,r):r):t}function Or(e){let t=e.peek();return t===""?null:Te(e,{type:"Token",value:t})}function bc(e){let t;return e.eat(Wr),e.eat(Xt),t=e.scanWord(),e.eat(Xt),e.eat(Fr),Te(e,{type:"Property",name:t})}function xc(e){let t=null,r=null,n=1;return e.eat(gt),e.charCode()===us&&(e.peek(),n=-1),n==-1&&e.charCode()===ms?e.peek():(t=n*Number(e.scanNumber(e)),e.isNameCharCode()&&(t+=e.scanWord())),e.skipWs(),e.eat(_r),e.skipWs(),e.charCode()===ms?e.peek():(n=1,e.charCode()===us&&(e.peek(),n=-1),r=n*Number(e.scanNumber(e)),e.isNameCharCode()&&(r+=e.scanWord())),e.eat(bt),{type:"Range",min:t,max:r}}function yc(e){let t,r=null;if(e.eat(Wr),t=e.scanWord(),t==="boolean-expr"){e.eat(gt);let n=jr(e,bt);return e.eat(bt),e.eat(Fr),Te(e,{type:"Boolean",term:n.terms.length===1?n.terms[0]:n})}return e.charCode()===bs&&e.nextCharCode()===fc&&(e.pos+=2,t+="()"),e.charCodeAt(e.findWsEnd(e.pos))===gt&&(e.skipWs(),r=xc(e)),e.eat(Fr),Te(e,{type:"Type",name:t,opts:r})}function kc(e){let t=e.scanWord();return e.charCode()===bs?(e.pos++,{type:"Function",name:t}):Te(e,{type:"Keyword",name:t})}function wc(e,t){function r(i,o){return{type:"Group",terms:i,combinator:o,disallowEmpty:!1,explicit:!1}}let n;for(t=Object.keys(t).sort((i,o)=>fs[i]-fs[o]);t.length>0;){n=t.shift();let i=0,o=0;for(;i<e.length;i++){let a=e[i];a.type==="Combinator"&&(a.value===n?(o===-1&&(o=i-1),e.splice(i,1),i--):(o!==-1&&i-o>1&&(e.splice(o,i-o,r(e.slice(o,i),n)),i=o+1),o=-1))}o!==-1&&t.length&&e.splice(o,i-o,r(e.slice(o,i),n))}return n}function jr(e,t){let r=Object.create(null),n=[],i,o=null,a=e.pos;for(;e.charCode()!==t&&(i=Sc(e,t));)i.type!=="Spaces"&&(i.type==="Combinator"?((o===null||o.type==="Combinator")&&(e.pos=a,e.error("Unexpected combinator")),r[i.value]=!0):o!==null&&o.type!=="Combinator"&&(r[" "]=!0,n.push({type:"Combinator",value:" "})),n.push(i),o=i,a=e.pos);return o!==null&&o.type==="Combinator"&&(e.pos-=a,e.error("Unexpected combinator")),{type:"Group",terms:n,combinator:wc(n,r)||" ",disallowEmpty:!1,explicit:!1}}function vc(e,t){let r;return e.eat(gt),r=jr(e,t),e.eat(bt),r.explicit=!0,e.charCode()===gs&&(e.pos++,r.disallowEmpty=!0),r}function Sc(e,t){let r=e.charCode();switch(r){case bt:break;case gt:return Te(e,vc(e,t));case Wr:return e.nextCharCode()===Xt?bc(e):yc(e);case ps:return{type:"Combinator",value:e.substringToPos(e.pos+(e.nextCharCode()===ps?2:1))};case cs:return e.pos++,e.eat(cs),{type:"Combinator",value:"&&"};case _r:return e.pos++,{type:"Comma"};case Xt:return Te(e,{type:"String",value:e.scanString()});case mc:case cc:case uc:case hc:case pc:return{type:"Spaces",value:e.scanSpaces()};case dc:return r=e.nextCharCode(),e.isNameCharCode(r)?(e.pos++,{type:"AtKeyword",name:e.scanWord()}):Or(e);case xs:case Br:case Rr:case Mr:case gs:break;case $t:if(r=e.nextCharCode(),r<48||r>57)return Or(e);break;default:return e.isNameCharCode(r)?kc(e):Or(e)}}function Je(e){let t=new Qt(e),r=jr(t);return t.pos!==e.length&&t.error("Unexpected input"),r.terms.length===1&&r.terms[0].type==="Group"?r.terms[0]:r}var xt=function(){};function ys(e){return typeof e=="function"?e:xt}function Zt(e,t,r){function n(a){switch(i.call(r,a),a.type){case"Group":a.terms.forEach(n);break;case"Multiplier":case"Boolean":n(a.term);break;case"Type":case"Property":case"Keyword":case"AtKeyword":case"Function":case"String":case"Token":case"Comma":break;default:throw new Error("Unknown type: "+a.type)}o.call(r,a)}let i=xt,o=xt;if(typeof t=="function"?i=t:t&&(i=ys(t.enter),o=ys(t.leave)),i===xt&&o===xt)throw new Error("Neither `enter` nor `leave` walker handler is set or both aren't a function");n(e,r)}var Cc={decorator(e){let t=[],r=null;return{...e,node(n){let i=r;r=n,e.node.call(this,n),r=i},emit(n,i,o){t.push({type:i,value:n,node:o?null:r})},result(){return t}}}};function Tc(e){let t=[];return Se(e,(r,n,i)=>t.push({type:r,value:e.slice(n,i),node:null})),t}function ks(e,t){return typeof e=="string"?Tc(e):t.generate(e,Cc)}var A={type:"Match"},z={type:"Mismatch"},Jt={type:"DisallowEmpty"},Ac=40,Lc=41;function J(e,t,r){return t===A&&r===z||e===A&&t===A&&r===A?e:(e.type==="If"&&e.else===z&&t===A&&(t=e.then,e=e.match),{type:"If",match:e,then:t,else:r})}function vs(e){return e.length>2&&e.charCodeAt(e.length-2)===Ac&&e.charCodeAt(e.length-1)===Lc}function ws(e){return e.type==="Keyword"||e.type==="AtKeyword"||e.type==="Function"||e.type==="Type"&&vs(e.name)}function Ae(e,t=" ",r=!1){return{type:"Group",terms:e,combinator:t,disallowEmpty:!1,explicit:r}}function yt(e,t,r=new Set){if(!r.has(e))switch(r.add(e),e.type){case"If":e.match=yt(e.match,t,r),e.then=yt(e.then,t,r),e.else=yt(e.else,t,r);break;case"Type":return t[e.name]||e}return e}function Ur(e,t,r){switch(e){case" ":{let n=A;for(let i=t.length-1;i>=0;i--){let o=t[i];n=J(o,n,z)}return n}case"|":{let n=z,i=null;for(let o=t.length-1;o>=0;o--){let a=t[o];if(ws(a)&&(i===null&&o>0&&ws(t[o-1])&&(i=Object.create(null),n=J({type:"Enum",map:i},A,n)),i!==null)){let u=(vs(a.name)?a.name.slice(0,-1):a.name).toLowerCase();if(!(u in i)){i[u]=a;continue}}i=null,n=J(a,A,n)}return n}case"&&":{if(t.length>5)return{type:"MatchOnce",terms:t,all:!0};let n=z;for(let i=t.length-1;i>=0;i--){let o=t[i],a;t.length>1?a=Ur(e,t.filter(function(u){return u!==o}),!1):a=A,n=J(o,a,n)}return n}case"||":{if(t.length>5)return{type:"MatchOnce",terms:t,all:!1};let n=r?A:z;for(let i=t.length-1;i>=0;i--){let o=t[i],a;t.length>1?a=Ur(e,t.filter(function(u){return u!==o}),!0):a=A,n=J(o,a,n)}return n}}}function Ec(e){let t=A,r=et(e.term);if(e.max===0)r=J(r,Jt,z),t=J(r,null,z),t.then=J(A,A,t),e.comma&&(t.then.else=J({type:"Comma",syntax:e},t,z));else for(let n=e.min||1;n<=e.max;n++)e.comma&&t!==A&&(t=J({type:"Comma",syntax:e},t,z)),t=J(r,J(A,A,t),z);if(e.min===0)t=J(A,A,t);else for(let n=0;n<e.min-1;n++)e.comma&&t!==A&&(t=J({type:"Comma",syntax:e},t,z)),t=J(r,t,z);return t}function et(e){if(typeof e=="function")return{type:"Generic",fn:e};switch(e.type){case"Group":{let t=Ur(e.combinator,e.terms.map(et),!1);return e.disallowEmpty&&(t=J(t,Jt,z)),t}case"Multiplier":return Ec(e);case"Boolean":{let t=et(e.term),r=et(Ae([Ae([{type:"Keyword",name:"not"},{type:"Type",name:"!boolean-group"}]),Ae([{type:"Type",name:"!boolean-group"},Ae([{type:"Multiplier",comma:!1,min:0,max:0,term:Ae([{type:"Keyword",name:"and"},{type:"Type",name:"!boolean-group"}])},{type:"Multiplier",comma:!1,min:0,max:0,term:Ae([{type:"Keyword",name:"or"},{type:"Type",name:"!boolean-group"}])}],"|")])],"|")),n=et(Ae([{type:"Type",name:"!term"},Ae([{type:"Token",value:"("},{type:"Type",name:"!self"},{type:"Token",value:")"}]),{type:"Type",name:"general-enclosed"}],"|"));return yt(n,{"!term":t,"!self":r}),yt(r,{"!boolean-group":n}),r}case"Type":case"Property":return{type:e.type,name:e.name,syntax:e};case"Keyword":return{type:e.type,name:e.name.toLowerCase(),syntax:e};case"AtKeyword":return{type:e.type,name:"@"+e.name.toLowerCase(),syntax:e};case"Function":return{type:e.type,name:e.name.toLowerCase()+"(",syntax:e};case"String":return e.value.length===3?{type:"Token",value:e.value.charAt(1),syntax:e}:{type:e.type,value:e.value.substr(1,e.value.length-2).replace(/\\'/g,"'"),syntax:e};case"Token":return{type:e.type,value:e.value,syntax:e};case"Comma":return{type:e.type,syntax:e};default:throw new Error("Unknown node type:",e.type)}}function kt(e,t){return typeof e=="string"&&(e=Je(e)),{type:"MatchGraph",match:et(e),syntax:t||null,source:e}}var{hasOwnProperty:Ss}=Object.prototype,zc=0,Pc=1,Gr=2,Es=3,Cs="Match",Ic="Mismatch",Dc="Maximum iteration number exceeded (please fill an issue on https://github.com/csstree/csstree/issues)",Ts=15e3,Nc=0;function Oc(e){let t=null,r=null,n=e;for(;n!==null;)r=n.prev,n.prev=t,t=n,n=r;return t}function Hr(e,t){if(e.length!==t.length)return!1;for(let r=0;r<e.length;r++){let n=t.charCodeAt(r),i=e.charCodeAt(r);if(i>=65&&i<=90&&(i=i|32),i!==n)return!1}return!0}function Fc(e){return e.type!==9?!1:e.value!=="?"}function As(e){return e===null?!0:e.type===18||e.type===2||e.type===21||e.type===19||e.type===23||Fc(e)}function Ls(e){return e===null?!0:e.type===22||e.type===20||e.type===24||e.type===9&&e.value==="/"}function Rc(e,t,r){function n(){do M++,C=M<e.length?e[M]:null;while(C!==null&&(C.type===13||C.type===25))}function i(se){let ge=M+se;return ge<e.length?e[ge]:null}function o(se,ge){return{nextState:se,matchStack:O,syntaxStack:h,thenStack:m,tokenIndex:M,prev:ge}}function a(se){m={nextState:se,matchStack:O,syntaxStack:h,prev:m}}function u(se){f=o(se,f)}function l(){O={type:Pc,syntax:t.syntax,token:C,prev:O},n(),w=null,M>ve&&(ve=M)}function s(){h={syntax:t.syntax,opts:t.syntax.opts||h!==null&&h.opts||null,prev:h},O={type:Gr,syntax:t.syntax,token:O.token,prev:O}}function c(){O.type===Gr?O=O.prev:O={type:Es,syntax:h.syntax,token:O.token,prev:O},h=h.prev}let h=null,m=null,f=null,w=null,ee=0,G=null,C=null,M=-1,ve=0,O={type:zc,syntax:null,token:null,prev:null};for(n();G===null&&++ee<Ts;)switch(t.type){case"Match":if(m===null){if(C!==null&&(M!==e.length-1||C.value!=="\\0"&&C.value!=="\\9")){t=z;break}G=Cs;break}if(t=m.nextState,t===Jt)if(m.matchStack===O){t=z;break}else t=A;for(;m.syntaxStack!==h;)c();m=m.prev;break;case"Mismatch":if(w!==null&&w!==!1)(f===null||M>f.tokenIndex)&&(f=w,w=!1);else if(f===null){G=Ic;break}t=f.nextState,m=f.thenStack,h=f.syntaxStack,O=f.matchStack,M=f.tokenIndex,C=M<e.length?e[M]:null,f=f.prev;break;case"MatchGraph":t=t.match;break;case"If":t.else!==z&&u(t.else),t.then!==A&&a(t.then),t=t.match;break;case"MatchOnce":t={type:"MatchOnceBuffer",syntax:t,index:0,mask:0};break;case"MatchOnceBuffer":{let Q=t.syntax.terms;if(t.index===Q.length){if(t.mask===0||t.syntax.all){t=z;break}t=A;break}if(t.mask===(1<<Q.length)-1){t=A;break}for(;t.index<Q.length;t.index++){let $=1<<t.index;if(!(t.mask&$)){u(t),a({type:"AddMatchOnce",syntax:t.syntax,mask:t.mask|$}),t=Q[t.index++];break}}break}case"AddMatchOnce":t={type:"MatchOnceBuffer",syntax:t.syntax,index:0,mask:t.mask};break;case"Enum":if(C!==null){let Q=C.value.toLowerCase();if(Q.indexOf("\\")!==-1&&(Q=Q.replace(/\\[09].*$/,"")),Ss.call(t.map,Q)){t=t.map[Q];break}}t=z;break;case"Generic":{let Q=h!==null?h.opts:null,$=M+Math.floor(t.fn(C,i,Q));if(!isNaN($)&&$>M){for(;M<$;)l();t=A}else t=z;break}case"Type":case"Property":{let Q=t.type==="Type"?"types":"properties",$=Ss.call(r,Q)?r[Q][t.name]:null;if(!$||!$.match)throw new Error("Bad syntax reference: "+(t.type==="Type"?"<"+t.name+">":"<'"+t.name+"'>"));if(w!==!1&&C!==null&&t.type==="Type"&&(t.name==="custom-ident"&&C.type===1||t.name==="length"&&C.value==="0")){w===null&&(w=o(t,f)),t=z;break}s(),t=$.matchRef||$.match;break}case"Keyword":{let Q=t.name;if(C!==null){let $=C.value;if($.indexOf("\\")!==-1&&($=$.replace(/\\[09].*$/,"")),Hr($,Q)){l(),t=A;break}}t=z;break}case"AtKeyword":case"Function":if(C!==null&&Hr(C.value,t.name)){l(),t=A;break}t=z;break;case"Token":if(C!==null&&C.value===t.value){l(),t=A;break}t=z;break;case"Comma":C!==null&&C.type===18?As(O.token)?t=z:(l(),t=Ls(C)?z:A):t=As(O.token)||Ls(C)?A:z;break;case"String":let se="",ge=M;for(;ge<e.length&&se.length<t.value.length;ge++)se+=e[ge].value;if(Hr(se,t.value)){for(;M<ge;)l();t=A}else t=z;break;default:throw new Error("Unknown node type: "+t.type)}switch(Nc+=ee,G){case null:console.warn("[csstree-match] BREAK after "+Ts+" iterations"),G=Dc,O=null;break;case Cs:for(;h!==null;)c();break;default:O=null}return{tokens:e,reason:G,iterations:ee,match:O,longestMatch:ve}}function Yr(e,t,r){let n=Rc(e,t,r||{});if(n.match===null)return n;let i=n.match,o=n.match={syntax:t.syntax||null,match:[]},a=[o];for(i=Oc(i).prev;i!==null;){switch(i.type){case Gr:o.match.push(o={syntax:i.syntax,match:[]}),a.push(o);break;case Es:a.pop(),o=a[a.length-1];break;default:o.match.push({syntax:i.syntax||null,token:i.token.value,node:i.token.node})}i=i.prev}return n}var Vr={};x(Vr,{getTrace:()=>zs,isKeyword:()=>_c,isProperty:()=>Bc,isType:()=>Mc});function zs(e){function t(i){return i===null?!1:i.type==="Type"||i.type==="Property"||i.type==="Keyword"}function r(i){if(Array.isArray(i.match)){for(let o=0;o<i.match.length;o++)if(r(i.match[o]))return t(i.syntax)&&n.unshift(i.syntax),!0}else if(i.node===e)return n=t(i.syntax)?[i.syntax]:[],!0;return!1}let n=null;return this.matched!==null&&r(this.matched),n}function Mc(e,t){return Kr(this,e,r=>r.type==="Type"&&r.name===t)}function Bc(e,t){return Kr(this,e,r=>r.type==="Property"&&r.name===t)}function _c(e){return Kr(this,e,t=>t.type==="Keyword")}function Kr(e,t,r){let n=zs.call(e,t);return n===null?!1:n.some(r)}function Ps(e){return"node"in e?e.node:Ps(e.match[0])}function Is(e){return"node"in e?e.node:Is(e.match[e.match.length-1])}function Qr(e,t,r,n,i){function o(u){if(u.syntax!==null&&u.syntax.type===n&&u.syntax.name===i){let l=Ps(u),s=Is(u);e.syntax.walk(t,function(c,h,m){if(c===l){let f=new X;do{if(f.appendData(h.data),h.data===s)break;h=h.next}while(h!==null);a.push({parent:m,nodes:f})}})}Array.isArray(u.match)&&u.match.forEach(o)}let a=[];return r.matched!==null&&o(r.matched),a}var{hasOwnProperty:wt}=Object.prototype;function Xr(e){return typeof e=="number"&&isFinite(e)&&Math.floor(e)===e&&e>=0}function Ds(e){return!!e&&Xr(e.offset)&&Xr(e.line)&&Xr(e.column)}function Wc(e,t){return function(n,i){if(!n||n.constructor!==Object)return i(n,"Type of node should be an Object");for(let o in n){let a=!0;if(wt.call(n,o)!==!1){if(o==="type")n.type!==e&&i(n,"Wrong node type `"+n.type+"`, expected `"+e+"`");else if(o==="loc"){if(n.loc===null)continue;if(n.loc&&n.loc.constructor===Object)if(typeof n.loc.source!="string")o+=".source";else if(!Ds(n.loc.start))o+=".start";else if(!Ds(n.loc.end))o+=".end";else continue;a=!1}else if(t.hasOwnProperty(o)){a=!1;for(let u=0;!a&&u<t[o].length;u++){let l=t[o][u];switch(l){case String:a=typeof n[o]=="string";break;case Boolean:a=typeof n[o]=="boolean";break;case null:a=n[o]===null;break;default:typeof l=="string"?a=n[o]&&n[o].type===l:Array.isArray(l)&&(a=n[o]instanceof X)}}}else i(n,"Unknown field `"+o+"` for "+e+" node type");a||i(n,"Bad value for `"+e+"."+o+"`")}}for(let o in t)wt.call(t,o)&&wt.call(n,o)===!1&&i(n,"Field `"+e+"."+o+"` is missed")}}function Ns(e,t){let r=[];for(let n=0;n<e.length;n++){let i=e[n];if(i===String||i===Boolean)r.push(i.name.toLowerCase());else if(i===null)r.push("null");else if(typeof i=="string")r.push(i);else if(Array.isArray(i))r.push("List<"+(Ns(i,t)||"any")+">");else throw new Error("Wrong value `"+i+"` in `"+t+"` structure definition")}return r.join(" | ")}function jc(e,t){let r=t.structure,n={type:String,loc:!0},i={type:'"'+e+'"'};for(let o in r){if(wt.call(r,o)===!1)continue;let a=n[o]=Array.isArray(r[o])?r[o].slice():[r[o]];i[o]=Ns(a,e+"."+o)}return{docs:i,check:Wc(e,n)}}function Os(e){let t={};if(e.node){for(let r in e.node)if(wt.call(e.node,r)){let n=e.node[r];if(n.structure)t[r]=jc(r,n);else throw new Error("Missed `structure` field in `"+r+"` node type definition")}}return t}function $r(e,t,r){let n={};for(let i in e)e[i].syntax&&(n[i]=r?e[i].syntax:Oe(e[i].syntax,{compact:t}));return n}function qc(e,t,r){let n={};for(let[i,o]of Object.entries(e))n[i]={prelude:o.prelude&&(r?o.prelude.syntax:Oe(o.prelude.syntax,{compact:t})),descriptors:o.descriptors&&$r(o.descriptors,t,r)};return n}function Uc(e){for(let t=0;t<e.length;t++)if(e[t].value.toLowerCase()==="var(")return!0;return!1}function Hc(e){let t=e.terms[0];return e.explicit===!1&&e.terms.length===1&&t.type==="Multiplier"&&t.comma===!0}function ce(e,t,r){return{matched:e,iterations:r,error:t,...Vr}}function tt(e,t,r,n){let i=ks(r,e.syntax),o;return Uc(i)?ce(null,new Error("Matching for a tree with var() is not supported")):(n&&(o=Yr(i,e.cssWideKeywordsSyntax,e)),(!n||!o.match)&&(o=Yr(i,t.match,e),!o.match)?ce(null,new Jo(o.reason,t.syntax,r,o),o.iterations):ce(o.match,null,o.iterations))}var Fe=class{constructor(t,r,n){if(this.cssWideKeywords=$e,this.syntax=r,this.generic=!1,this.units={...Kt},this.atrules=Object.create(null),this.properties=Object.create(null),this.types=Object.create(null),this.structure=n||Os(t),t){if(t.cssWideKeywords&&(this.cssWideKeywords=t.cssWideKeywords),t.units)for(let i of Object.keys(Kt))Array.isArray(t.units[i])&&(this.units[i]=t.units[i]);if(t.types)for(let[i,o]of Object.entries(t.types))this.addType_(i,o);if(t.generic){this.generic=!0;for(let[i,o]of Object.entries(as(this.units)))this.addType_(i,o)}if(t.atrules)for(let[i,o]of Object.entries(t.atrules))this.addAtrule_(i,o);if(t.properties)for(let[i,o]of Object.entries(t.properties))this.addProperty_(i,o)}this.cssWideKeywordsSyntax=kt(this.cssWideKeywords.join(" |  "))}checkStructure(t){function r(o,a){i.push({node:o,message:a})}let n=this.structure,i=[];return this.syntax.walk(t,function(o){n.hasOwnProperty(o.type)?n[o.type].check(o,r):r(o,"Unknown node type `"+o.type+"`")}),i.length?i:!1}createDescriptor(t,r,n,i=null){let o={type:r,name:n},a={type:r,name:n,parent:i,serializable:typeof t=="string"||t&&typeof t.type=="string",syntax:null,match:null,matchRef:null};return typeof t=="function"?a.match=kt(t,o):(typeof t=="string"?Object.defineProperty(a,"syntax",{get(){return Object.defineProperty(a,"syntax",{value:Je(t)}),a.syntax}}):a.syntax=t,Object.defineProperty(a,"match",{get(){return Object.defineProperty(a,"match",{value:kt(a.syntax,o)}),a.match}}),r==="Property"&&Object.defineProperty(a,"matchRef",{get(){let u=a.syntax,l=Hc(u)?kt({...u,terms:[u.terms[0].term]},o):null;return Object.defineProperty(a,"matchRef",{value:l}),l}})),a}addAtrule_(t,r){r&&(this.atrules[t]={type:"Atrule",name:t,prelude:r.prelude?this.createDescriptor(r.prelude,"AtrulePrelude",t):null,descriptors:r.descriptors?Object.keys(r.descriptors).reduce((n,i)=>(n[i]=this.createDescriptor(r.descriptors[i],"AtruleDescriptor",i,t),n),Object.create(null)):null})}addProperty_(t,r){r&&(this.properties[t]=this.createDescriptor(r,"Property",t))}addType_(t,r){r&&(this.types[t]=this.createDescriptor(r,"Type",t))}checkAtruleName(t){if(!this.getAtrule(t))return new Qe("Unknown at-rule","@"+t)}checkAtrulePrelude(t,r){let n=this.checkAtruleName(t);if(n)return n;let i=this.getAtrule(t);if(!i.prelude&&r)return new SyntaxError("At-rule `@"+t+"` should not contain a prelude");if(i.prelude&&!r&&!tt(this,i.prelude,"",!1).matched)return new SyntaxError("At-rule `@"+t+"` should contain a prelude")}checkAtruleDescriptorName(t,r){let n=this.checkAtruleName(t);if(n)return n;let i=this.getAtrule(t),o=pt(r);if(!i.descriptors)return new SyntaxError("At-rule `@"+t+"` has no known descriptors");if(!i.descriptors[o.name]&&!i.descriptors[o.basename])return new Qe("Unknown at-rule descriptor",r)}checkPropertyName(t){if(!this.getProperty(t))return new Qe("Unknown property",t)}matchAtrulePrelude(t,r){let n=this.checkAtrulePrelude(t,r);if(n)return ce(null,n);let i=this.getAtrule(t);return i.prelude?tt(this,i.prelude,r||"",!1):ce(null,null)}matchAtruleDescriptor(t,r,n){let i=this.checkAtruleDescriptorName(t,r);if(i)return ce(null,i);let o=this.getAtrule(t),a=pt(r);return tt(this,o.descriptors[a.name]||o.descriptors[a.basename],n,!1)}matchDeclaration(t){return t.type!=="Declaration"?ce(null,new Error("Not a Declaration node")):this.matchProperty(t.property,t.value)}matchProperty(t,r){if(Ht(t).custom)return ce(null,new Error("Lexer matching doesn't applicable for custom properties"));let n=this.checkPropertyName(t);return n?ce(null,n):tt(this,this.getProperty(t),r,!0)}matchType(t,r){let n=this.getType(t);return n?tt(this,n,r,!1):ce(null,new Qe("Unknown type",t))}match(t,r){return typeof t!="string"&&(!t||!t.type)?ce(null,new Qe("Bad syntax")):((typeof t=="string"||!t.match)&&(t=this.createDescriptor(t,"Type","anonymous")),tt(this,t,r,!1))}findValueFragments(t,r,n,i){return Qr(this,r,this.matchProperty(t,r),n,i)}findDeclarationValueFragments(t,r,n){return Qr(this,t.value,this.matchDeclaration(t),r,n)}findAllFragments(t,r,n){let i=[];return this.syntax.walk(t,{visit:"Declaration",enter:o=>{i.push.apply(i,this.findDeclarationValueFragments(o,r,n))}}),i}getAtrule(t,r=!0){let n=pt(t);return(n.vendor&&r?this.atrules[n.name]||this.atrules[n.basename]:this.atrules[n.name])||null}getAtrulePrelude(t,r=!0){let n=this.getAtrule(t,r);return n&&n.prelude||null}getAtruleDescriptor(t,r){return this.atrules.hasOwnProperty(t)&&this.atrules.declarators&&this.atrules[t].declarators[r]||null}getProperty(t,r=!0){let n=Ht(t);return(n.vendor&&r?this.properties[n.name]||this.properties[n.basename]:this.properties[n.name])||null}getType(t){return hasOwnProperty.call(this.types,t)?this.types[t]:null}validate(){function t(l,s){return s?`<${l}>`:`<'${l}'>`}function r(l,s,c,h){if(c.has(s))return c.get(s);c.set(s,!1),h.syntax!==null&&Zt(h.syntax,function(m){if(m.type!=="Type"&&m.type!=="Property")return;let f=m.type==="Type"?l.types:l.properties,w=m.type==="Type"?i:o;hasOwnProperty.call(f,m.name)?r(l,m.name,w,f[m.name])&&(n.push(`${t(s,c===i)} used broken syntax definition ${t(m.name,m.type==="Type")}`),c.set(s,!0)):(n.push(`${t(s,c===i)} used missed syntax definition ${t(m.name,m.type==="Type")}`),c.set(s,!0))},this)}let n=[],i=new Map,o=new Map;for(let l in this.types)r(this,l,i,this.types[l]);for(let l in this.properties)r(this,l,o,this.properties[l]);let a=[...i.keys()].filter(l=>i.get(l)),u=[...o.keys()].filter(l=>o.get(l));return a.length||u.length?{errors:n,types:a,properties:u}:null}dump(t,r){return{generic:this.generic,cssWideKeywords:this.cssWideKeywords,units:this.units,types:$r(this.types,!r,t),properties:$r(this.properties,!r,t),atrules:qc(this.atrules,!r,t)}}toString(){return JSON.stringify(this.dump())}};function Zr(e,t){return typeof t=="string"&&/^\s*\|/.test(t)?typeof e=="string"?e+t:t.replace(/^\s*\|\s*/,""):t||null}function Fs(e,t){let r=Object.create(null);for(let[n,i]of Object.entries(e))if(i){r[n]={};for(let o of Object.keys(i))t.includes(o)&&(r[n][o]=i[o])}return r}function vt(e,t){let r={...e};for(let[n,i]of Object.entries(t))switch(n){case"generic":r[n]=!!i;break;case"cssWideKeywords":r[n]=e[n]?[...e[n],...i]:i||[];break;case"units":r[n]={...e[n]};for(let[o,a]of Object.entries(i))r[n][o]=Array.isArray(a)?a:[];break;case"atrules":r[n]={...e[n]};for(let[o,a]of Object.entries(i)){let u=r[n][o]||{},l=r[n][o]={prelude:u.prelude||null,descriptors:{...u.descriptors}};if(a){l.prelude=a.prelude?Zr(l.prelude,a.prelude):l.prelude||null;for(let[s,c]of Object.entries(a.descriptors||{}))l.descriptors[s]=c?Zr(l.descriptors[s],c):null;Object.keys(l.descriptors).length||(l.descriptors=null)}}break;case"types":case"properties":r[n]={...e[n]};for(let[o,a]of Object.entries(i))r[n][o]=Zr(r[n][o],a);break;case"scope":case"features":r[n]={...e[n]};for(let[o,a]of Object.entries(i))r[n][o]={...r[n][o],...a};break;case"parseContext":r[n]={...e[n],...i};break;case"atrule":case"pseudo":r[n]={...e[n],...Fs(i,["parse"])};break;case"node":r[n]={...e[n],...Fs(i,["name","structure","parse","generate","walkContext"])};break}return r}function Rs(e){let t=ko(e),r=$o(e),n=Go(e),{fromPlainObject:i,toPlainObject:o}=Yo(r),a={lexer:null,createLexer:u=>new Fe(u,a,a.lexer.structure),tokenize:Se,parse:t,generate:n,walk:r,find:r.find,findLast:r.findLast,findAll:r.findAll,fromPlainObject:i,toPlainObject:o,fork(u){let l=vt({},e);return Rs(typeof u=="function"?u(l):vt(l,u))}};return a.lexer=new Fe({generic:e.generic,cssWideKeywords:e.cssWideKeywords,units:e.units,types:e.types,atrules:e.atrules,properties:e.properties,node:e.node},a),a}var er=e=>Rs(vt({},e));var Ms={generic:!0,cssWideKeywords:["initial","inherit","unset","revert","revert-layer"],units:{angle:["deg","grad","rad","turn"],decibel:["db"],flex:["fr"],frequency:["hz","khz"],length:["cm","mm","q","in","pt","pc","px","em","rem","ex","rex","cap","rcap","ch","rch","ic","ric","lh","rlh","vw","svw","lvw","dvw","vh","svh","lvh","dvh","vi","svi","lvi","dvi","vb","svb","lvb","dvb","vmin","svmin","lvmin","dvmin","vmax","svmax","lvmax","dvmax","cqw","cqh","cqi","cqb","cqmin","cqmax"],resolution:["dpi","dpcm","dppx","x"],semitones:["st"],time:["s","ms"]},types:{"abs()":"abs( <calc-sum> )","absolute-size":"xx-small|x-small|small|medium|large|x-large|xx-large|xxx-large","acos()":"acos( <calc-sum> )","alpha-value":"<number>|<percentage>","angle-percentage":"<angle>|<percentage>","angular-color-hint":"<angle-percentage>","angular-color-stop":"<color>&&<color-stop-angle>?","angular-color-stop-list":"[<angular-color-stop> [, <angular-color-hint>]?]# , <angular-color-stop>","animateable-feature":"scroll-position|contents|<custom-ident>","asin()":"asin( <calc-sum> )","atan()":"atan( <calc-sum> )","atan2()":"atan2( <calc-sum> , <calc-sum> )",attachment:"scroll|fixed|local","attr()":"attr( <attr-name> <type-or-unit>? [, <attr-fallback>]? )","attr-matcher":"['~'|'|'|'^'|'$'|'*']? '='","attr-modifier":"i|s","attribute-selector":"'[' <wq-name> ']'|'[' <wq-name> <attr-matcher> [<string-token>|<ident-token>] <attr-modifier>? ']'","auto-repeat":"repeat( [auto-fill|auto-fit] , [<line-names>? <fixed-size>]+ <line-names>? )","auto-track-list":"[<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>? <auto-repeat> [<line-names>? [<fixed-size>|<fixed-repeat>]]* <line-names>?",axis:"block|inline|x|y","baseline-position":"[first|last]? baseline","basic-shape":"<inset()>|<xywh()>|<rect()>|<circle()>|<ellipse()>|<polygon()>|<path()>","bg-image":"none|<image>","bg-layer":"<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","bg-position":"[[left|center|right|top|bottom|<length-percentage>]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]|[center|[left|right] <length-percentage>?]&&[center|[top|bottom] <length-percentage>?]]","bg-size":"[<length-percentage>|auto]{1,2}|cover|contain","blur()":"blur( <length> )","blend-mode":"normal|multiply|screen|overlay|darken|lighten|color-dodge|color-burn|hard-light|soft-light|difference|exclusion|hue|saturation|color|luminosity",box:"border-box|padding-box|content-box","brightness()":"brightness( <number-percentage> )","calc()":"calc( <calc-sum> )","calc-sum":"<calc-product> [['+'|'-'] <calc-product>]*","calc-product":"<calc-value> ['*' <calc-value>|'/' <number>]*","calc-value":"<number>|<dimension>|<percentage>|<calc-constant>|( <calc-sum> )","calc-constant":"e|pi|infinity|-infinity|NaN","cf-final-image":"<image>|<color>","cf-mixing-image":"<percentage>?&&<image>","circle()":"circle( [<shape-radius>]? [at <position>]? )","clamp()":"clamp( <calc-sum>#{3} )","class-selector":"'.' <ident-token>","clip-source":"<url>",color:"<color-base>|currentColor|<system-color>|<device-cmyk()>|<light-dark()>|<-non-standard-color>","color-stop":"<color-stop-length>|<color-stop-angle>","color-stop-angle":"<angle-percentage>{1,2}","color-stop-length":"<length-percentage>{1,2}","color-stop-list":"[<linear-color-stop> [, <linear-color-hint>]?]# , <linear-color-stop>","color-interpolation-method":"in [<rectangular-color-space>|<polar-color-space> <hue-interpolation-method>?|<custom-color-space>]",combinator:"'>'|'+'|'~'|['|' '|']","common-lig-values":"[common-ligatures|no-common-ligatures]","compat-auto":"searchfield|textarea|push-button|slider-horizontal|checkbox|radio|square-button|menulist|listbox|meter|progress-bar|button","composite-style":"clear|copy|source-over|source-in|source-out|source-atop|destination-over|destination-in|destination-out|destination-atop|xor","compositing-operator":"add|subtract|intersect|exclude","compound-selector":"[<type-selector>? <subclass-selector>*]!","compound-selector-list":"<compound-selector>#","complex-selector":"<complex-selector-unit> [<combinator>? <complex-selector-unit>]*","complex-selector-list":"<complex-selector>#","conic-gradient()":"conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","contextual-alt-values":"[contextual|no-contextual]","content-distribution":"space-between|space-around|space-evenly|stretch","content-list":"[<string>|contents|<image>|<counter>|<quote>|<target>|<leader()>|<attr()>]+","content-position":"center|start|end|flex-start|flex-end","content-replacement":"<image>","contrast()":"contrast( [<number-percentage>] )","cos()":"cos( <calc-sum> )",counter:"<counter()>|<counters()>","counter()":"counter( <counter-name> , <counter-style>? )","counter-name":"<custom-ident>","counter-style":"<counter-style-name>|symbols( )","counter-style-name":"<custom-ident>","counters()":"counters( <counter-name> , <string> , <counter-style>? )","cross-fade()":"cross-fade( <cf-mixing-image> , <cf-final-image>? )","cubic-bezier-timing-function":"ease|ease-in|ease-out|ease-in-out|cubic-bezier( <number [0,1]> , <number> , <number [0,1]> , <number> )","deprecated-system-color":"ActiveBorder|ActiveCaption|AppWorkspace|Background|ButtonFace|ButtonHighlight|ButtonShadow|ButtonText|CaptionText|GrayText|Highlight|HighlightText|InactiveBorder|InactiveCaption|InactiveCaptionText|InfoBackground|InfoText|Menu|MenuText|Scrollbar|ThreeDDarkShadow|ThreeDFace|ThreeDHighlight|ThreeDLightShadow|ThreeDShadow|Window|WindowFrame|WindowText","discretionary-lig-values":"[discretionary-ligatures|no-discretionary-ligatures]","display-box":"contents|none","display-inside":"flow|flow-root|table|flex|grid|ruby","display-internal":"table-row-group|table-header-group|table-footer-group|table-row|table-cell|table-column-group|table-column|table-caption|ruby-base|ruby-text|ruby-base-container|ruby-text-container","display-legacy":"inline-block|inline-list-item|inline-table|inline-flex|inline-grid","display-listitem":"<display-outside>?&&[flow|flow-root]?&&list-item","display-outside":"block|inline|run-in","drop-shadow()":"drop-shadow( <length>{2,3} <color>? )","east-asian-variant-values":"[jis78|jis83|jis90|jis04|simplified|traditional]","east-asian-width-values":"[full-width|proportional-width]","element()":"element( <custom-ident> , [first|start|last|first-except]? )|element( <id-selector> )","ellipse()":"ellipse( [<shape-radius>{2}]? [at <position>]? )","ending-shape":"circle|ellipse","env()":"env( <custom-ident> , <declaration-value>? )","exp()":"exp( <calc-sum> )","explicit-track-list":"[<line-names>? <track-size>]+ <line-names>?","family-name":"<string>|<custom-ident>+","feature-tag-value":"<string> [<integer>|on|off]?","feature-type":"@stylistic|@historical-forms|@styleset|@character-variant|@swash|@ornaments|@annotation","feature-value-block":"<feature-type> '{' <feature-value-declaration-list> '}'","feature-value-block-list":"<feature-value-block>+","feature-value-declaration":"<custom-ident> : <integer>+ ;","feature-value-declaration-list":"<feature-value-declaration>","feature-value-name":"<custom-ident>","fill-rule":"nonzero|evenodd","filter-function":"<blur()>|<brightness()>|<contrast()>|<drop-shadow()>|<grayscale()>|<hue-rotate()>|<invert()>|<opacity()>|<saturate()>|<sepia()>","filter-function-list":"[<filter-function>|<url>]+","final-bg-layer":"<'background-color'>||<bg-image>||<bg-position> [/ <bg-size>]?||<repeat-style>||<attachment>||<box>||<box>","fixed-breadth":"<length-percentage>","fixed-repeat":"repeat( [<integer [1,\u221E]>] , [<line-names>? <fixed-size>]+ <line-names>? )","fixed-size":"<fixed-breadth>|minmax( <fixed-breadth> , <track-breadth> )|minmax( <inflexible-breadth> , <fixed-breadth> )","font-stretch-absolute":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded|<percentage>","font-variant-css21":"[normal|small-caps]","font-weight-absolute":"normal|bold|<number [1,1000]>","frequency-percentage":"<frequency>|<percentage>","general-enclosed":"[<function-token> <any-value>? )]|[( <any-value>? )]","generic-family":"<generic-script-specific>|<generic-complete>|<generic-incomplete>|<-non-standard-generic-family>","generic-name":"serif|sans-serif|cursive|fantasy|monospace","geometry-box":"<shape-box>|fill-box|stroke-box|view-box",gradient:"<linear-gradient()>|<repeating-linear-gradient()>|<radial-gradient()>|<repeating-radial-gradient()>|<conic-gradient()>|<repeating-conic-gradient()>|<-legacy-gradient>","grayscale()":"grayscale( <number-percentage> )","grid-line":"auto|<custom-ident>|[<integer>&&<custom-ident>?]|[span&&[<integer>||<custom-ident>]]","historical-lig-values":"[historical-ligatures|no-historical-ligatures]","hsl()":"hsl( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )","hsla()":"hsla( <hue> <percentage> <percentage> [/ <alpha-value>]? )|hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )",hue:"<number>|<angle>","hue-rotate()":"hue-rotate( <angle> )","hue-interpolation-method":"[shorter|longer|increasing|decreasing] hue","hwb()":"hwb( [<hue>|none] [<percentage>|none] [<percentage>|none] [/ [<alpha-value>|none]]? )","hypot()":"hypot( <calc-sum># )",image:"<url>|<image()>|<image-set()>|<element()>|<paint()>|<cross-fade()>|<gradient>","image()":"image( <image-tags>? [<image-src>? , <color>?]! )","image-set()":"image-set( <image-set-option># )","image-set-option":"[<image>|<string>] [<resolution>||type( <string> )]","image-src":"<url>|<string>","image-tags":"ltr|rtl","inflexible-breadth":"<length-percentage>|min-content|max-content|auto","inset()":"inset( <length-percentage>{1,4} [round <'border-radius'>]? )","invert()":"invert( <number-percentage> )","keyframes-name":"<custom-ident>|<string>","keyframe-block":"<keyframe-selector># { <declaration-list> }","keyframe-block-list":"<keyframe-block>+","keyframe-selector":"from|to|<percentage>|<timeline-range-name> <percentage>","lab()":"lab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )","layer()":"layer( <layer-name> )","layer-name":"<ident> ['.' <ident>]*","lch()":"lch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )","leader()":"leader( <leader-type> )","leader-type":"dotted|solid|space|<string>","length-percentage":"<length>|<percentage>","light-dark()":"light-dark( <color> , <color> )","line-names":"'[' <custom-ident>* ']'","line-name-list":"[<line-names>|<name-repeat>]+","line-style":"none|hidden|dotted|dashed|solid|double|groove|ridge|inset|outset","line-width":"<length>|thin|medium|thick","linear-color-hint":"<length-percentage>","linear-color-stop":"<color> <color-stop-length>?","linear-gradient()":"linear-gradient( [[<angle>|to <side-or-corner>]||<color-interpolation-method>]? , <color-stop-list> )","log()":"log( <calc-sum> , <calc-sum>? )","mask-layer":"<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||<geometry-box>||[<geometry-box>|no-clip]||<compositing-operator>||<masking-mode>","mask-position":"[<length-percentage>|left|center|right] [<length-percentage>|top|center|bottom]?","mask-reference":"none|<image>|<mask-source>","mask-source":"<url>","masking-mode":"alpha|luminance|match-source","matrix()":"matrix( <number>#{6} )","matrix3d()":"matrix3d( <number>#{16} )","max()":"max( <calc-sum># )","media-and":"<media-in-parens> [and <media-in-parens>]+","media-condition":"<media-not>|<media-and>|<media-or>|<media-in-parens>","media-condition-without-or":"<media-not>|<media-and>|<media-in-parens>","media-feature":"( [<mf-plain>|<mf-boolean>|<mf-range>] )","media-in-parens":"( <media-condition> )|<media-feature>|<general-enclosed>","media-not":"not <media-in-parens>","media-or":"<media-in-parens> [or <media-in-parens>]+","media-query":"<media-condition>|[not|only]? <media-type> [and <media-condition-without-or>]?","media-query-list":"<media-query>#","media-type":"<ident>","mf-boolean":"<mf-name>","mf-name":"<ident>","mf-plain":"<mf-name> : <mf-value>","mf-range":"<mf-name> ['<'|'>']? '='? <mf-value>|<mf-value> ['<'|'>']? '='? <mf-name>|<mf-value> '<' '='? <mf-name> '<' '='? <mf-value>|<mf-value> '>' '='? <mf-name> '>' '='? <mf-value>","mf-value":"<number>|<dimension>|<ident>|<ratio>","min()":"min( <calc-sum># )","minmax()":"minmax( [<length-percentage>|min-content|max-content|auto] , [<length-percentage>|<flex>|min-content|max-content|auto] )","mod()":"mod( <calc-sum> , <calc-sum> )","name-repeat":"repeat( [<integer [1,\u221E]>|auto-fill] , <line-names>+ )","named-color":"transparent|aliceblue|antiquewhite|aqua|aquamarine|azure|beige|bisque|black|blanchedalmond|blue|blueviolet|brown|burlywood|cadetblue|chartreuse|chocolate|coral|cornflowerblue|cornsilk|crimson|cyan|darkblue|darkcyan|darkgoldenrod|darkgray|darkgreen|darkgrey|darkkhaki|darkmagenta|darkolivegreen|darkorange|darkorchid|darkred|darksalmon|darkseagreen|darkslateblue|darkslategray|darkslategrey|darkturquoise|darkviolet|deeppink|deepskyblue|dimgray|dimgrey|dodgerblue|firebrick|floralwhite|forestgreen|fuchsia|gainsboro|ghostwhite|gold|goldenrod|gray|green|greenyellow|grey|honeydew|hotpink|indianred|indigo|ivory|khaki|lavender|lavenderblush|lawngreen|lemonchiffon|lightblue|lightcoral|lightcyan|lightgoldenrodyellow|lightgray|lightgreen|lightgrey|lightpink|lightsalmon|lightseagreen|lightskyblue|lightslategray|lightslategrey|lightsteelblue|lightyellow|lime|limegreen|linen|magenta|maroon|mediumaquamarine|mediumblue|mediumorchid|mediumpurple|mediumseagreen|mediumslateblue|mediumspringgreen|mediumturquoise|mediumvioletred|midnightblue|mintcream|mistyrose|moccasin|navajowhite|navy|oldlace|olive|olivedrab|orange|orangered|orchid|palegoldenrod|palegreen|paleturquoise|palevioletred|papayawhip|peachpuff|peru|pink|plum|powderblue|purple|rebeccapurple|red|rosybrown|royalblue|saddlebrown|salmon|sandybrown|seagreen|seashell|sienna|silver|skyblue|slateblue|slategray|slategrey|snow|springgreen|steelblue|tan|teal|thistle|tomato|turquoise|violet|wheat|white|whitesmoke|yellow|yellowgreen","namespace-prefix":"<ident>","ns-prefix":"[<ident-token>|'*']? '|'","number-percentage":"<number>|<percentage>","numeric-figure-values":"[lining-nums|oldstyle-nums]","numeric-fraction-values":"[diagonal-fractions|stacked-fractions]","numeric-spacing-values":"[proportional-nums|tabular-nums]",nth:"<an-plus-b>|even|odd","opacity()":"opacity( [<number-percentage>] )","overflow-position":"unsafe|safe","outline-radius":"<length>|<percentage>","page-body":"<declaration>? [; <page-body>]?|<page-margin-box> <page-body>","page-margin-box":"<page-margin-box-type> '{' <declaration-list> '}'","page-margin-box-type":"@top-left-corner|@top-left|@top-center|@top-right|@top-right-corner|@bottom-left-corner|@bottom-left|@bottom-center|@bottom-right|@bottom-right-corner|@left-top|@left-middle|@left-bottom|@right-top|@right-middle|@right-bottom","page-selector-list":"[<page-selector>#]?","page-selector":"<pseudo-page>+|<ident> <pseudo-page>*","page-size":"A5|A4|A3|B5|B4|JIS-B5|JIS-B4|letter|legal|ledger","path()":"path( [<fill-rule> ,]? <string> )","paint()":"paint( <ident> , <declaration-value>? )","perspective()":"perspective( [<length [0,\u221E]>|none] )","polygon()":"polygon( <fill-rule>? , [<length-percentage> <length-percentage>]# )","polar-color-space":"hsl|hwb|lch|oklch",position:"[[left|center|right]||[top|center|bottom]|[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]?|[[left|right] <length-percentage>]&&[[top|bottom] <length-percentage>]]","pow()":"pow( <calc-sum> , <calc-sum> )","pseudo-class-selector":"':' <ident-token>|':' <function-token> <any-value> ')'","pseudo-element-selector":"':' <pseudo-class-selector>|<legacy-pseudo-element-selector>","pseudo-page":": [left|right|first|blank]",quote:"open-quote|close-quote|no-open-quote|no-close-quote","radial-gradient()":"radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )",ratio:"<number [0,\u221E]> [/ <number [0,\u221E]>]?","ray()":"ray( <angle>&&<ray-size>?&&contain?&&[at <position>]? )","ray-size":"closest-side|closest-corner|farthest-side|farthest-corner|sides","rectangular-color-space":"srgb|srgb-linear|display-p3|a98-rgb|prophoto-rgb|rec2020|lab|oklab|xyz|xyz-d50|xyz-d65","relative-selector":"<combinator>? <complex-selector>","relative-selector-list":"<relative-selector>#","relative-size":"larger|smaller","rem()":"rem( <calc-sum> , <calc-sum> )","repeat-style":"repeat-x|repeat-y|[repeat|space|round|no-repeat]{1,2}","repeating-conic-gradient()":"repeating-conic-gradient( [from <angle>]? [at <position>]? , <angular-color-stop-list> )","repeating-linear-gradient()":"repeating-linear-gradient( [<angle>|to <side-or-corner>]? , <color-stop-list> )","repeating-radial-gradient()":"repeating-radial-gradient( [<ending-shape>||<size>]? [at <position>]? , <color-stop-list> )","reversed-counter-name":"reversed( <counter-name> )","rgb()":"rgb( <percentage>{3} [/ <alpha-value>]? )|rgb( <number>{3} [/ <alpha-value>]? )|rgb( <percentage>#{3} , <alpha-value>? )|rgb( <number>#{3} , <alpha-value>? )","rgba()":"rgba( <percentage>{3} [/ <alpha-value>]? )|rgba( <number>{3} [/ <alpha-value>]? )|rgba( <percentage>#{3} , <alpha-value>? )|rgba( <number>#{3} , <alpha-value>? )","rotate()":"rotate( [<angle>|<zero>] )","rotate3d()":"rotate3d( <number> , <number> , <number> , [<angle>|<zero>] )","rotateX()":"rotateX( [<angle>|<zero>] )","rotateY()":"rotateY( [<angle>|<zero>] )","rotateZ()":"rotateZ( [<angle>|<zero>] )","round()":"round( <rounding-strategy>? , <calc-sum> , <calc-sum> )","rounding-strategy":"nearest|up|down|to-zero","saturate()":"saturate( <number-percentage> )","scale()":"scale( [<number>|<percentage>]#{1,2} )","scale3d()":"scale3d( [<number>|<percentage>]#{3} )","scaleX()":"scaleX( [<number>|<percentage>] )","scaleY()":"scaleY( [<number>|<percentage>] )","scaleZ()":"scaleZ( [<number>|<percentage>] )","scroll()":"scroll( [<axis>||<scroller>]? )",scroller:"root|nearest|self","self-position":"center|start|end|self-start|self-end|flex-start|flex-end","shape-radius":"<length-percentage>|closest-side|farthest-side","sign()":"sign( <calc-sum> )","skew()":"skew( [<angle>|<zero>] , [<angle>|<zero>]? )","skewX()":"skewX( [<angle>|<zero>] )","skewY()":"skewY( [<angle>|<zero>] )","sepia()":"sepia( <number-percentage> )",shadow:"inset?&&<length>{2,4}&&<color>?","shadow-t":"[<length>{2,3}&&<color>?]",shape:"rect( <top> , <right> , <bottom> , <left> )|rect( <top> <right> <bottom> <left> )","shape-box":"<box>|margin-box","side-or-corner":"[left|right]||[top|bottom]","sin()":"sin( <calc-sum> )","single-animation":"<'animation-duration'>||<easing-function>||<'animation-delay'>||<single-animation-iteration-count>||<single-animation-direction>||<single-animation-fill-mode>||<single-animation-play-state>||[none|<keyframes-name>]||<single-animation-timeline>","single-animation-direction":"normal|reverse|alternate|alternate-reverse","single-animation-fill-mode":"none|forwards|backwards|both","single-animation-iteration-count":"infinite|<number>","single-animation-play-state":"running|paused","single-animation-timeline":"auto|none|<dashed-ident>|<scroll()>|<view()>","single-transition":"[none|<single-transition-property>]||<time>||<easing-function>||<time>||<transition-behavior-value>","single-transition-property":"all|<custom-ident>",size:"closest-side|farthest-side|closest-corner|farthest-corner|<length>|<length-percentage>{2}","sqrt()":"sqrt( <calc-sum> )","step-position":"jump-start|jump-end|jump-none|jump-both|start|end","step-timing-function":"step-start|step-end|steps( <integer> [, <step-position>]? )","subclass-selector":"<id-selector>|<class-selector>|<attribute-selector>|<pseudo-class-selector>","supports-condition":"not <supports-in-parens>|<supports-in-parens> [and <supports-in-parens>]*|<supports-in-parens> [or <supports-in-parens>]*","supports-in-parens":"( <supports-condition> )|<supports-feature>|<general-enclosed>","supports-feature":"<supports-decl>|<supports-selector-fn>","supports-decl":"( <declaration> )","supports-selector-fn":"selector( <complex-selector> )",symbol:"<string>|<image>|<custom-ident>","system-color":"AccentColor|AccentColorText|ActiveText|ButtonBorder|ButtonFace|ButtonText|Canvas|CanvasText|Field|FieldText|GrayText|Highlight|HighlightText|LinkText|Mark|MarkText|SelectedItem|SelectedItemText|VisitedText","tan()":"tan( <calc-sum> )",target:"<target-counter()>|<target-counters()>|<target-text()>","target-counter()":"target-counter( [<string>|<url>] , <custom-ident> , <counter-style>? )","target-counters()":"target-counters( [<string>|<url>] , <custom-ident> , <string> , <counter-style>? )","target-text()":"target-text( [<string>|<url>] , [content|before|after|first-letter]? )","time-percentage":"<time>|<percentage>","timeline-range-name":"cover|contain|entry|exit|entry-crossing|exit-crossing","easing-function":"linear|<cubic-bezier-timing-function>|<step-timing-function>","track-breadth":"<length-percentage>|<flex>|min-content|max-content|auto","track-list":"[<line-names>? [<track-size>|<track-repeat>]]+ <line-names>?","track-repeat":"repeat( [<integer [1,\u221E]>] , [<line-names>? <track-size>]+ <line-names>? )","track-size":"<track-breadth>|minmax( <inflexible-breadth> , <track-breadth> )|fit-content( <length-percentage> )","transform-function":"<matrix()>|<translate()>|<translateX()>|<translateY()>|<scale()>|<scaleX()>|<scaleY()>|<rotate()>|<skew()>|<skewX()>|<skewY()>|<matrix3d()>|<translate3d()>|<translateZ()>|<scale3d()>|<scaleZ()>|<rotate3d()>|<rotateX()>|<rotateY()>|<rotateZ()>|<perspective()>","transform-list":"<transform-function>+","transition-behavior-value":"normal|allow-discrete","translate()":"translate( <length-percentage> , <length-percentage>? )","translate3d()":"translate3d( <length-percentage> , <length-percentage> , <length> )","translateX()":"translateX( <length-percentage> )","translateY()":"translateY( <length-percentage> )","translateZ()":"translateZ( <length> )","type-or-unit":"string|color|url|integer|number|length|angle|time|frequency|cap|ch|em|ex|ic|lh|rlh|rem|vb|vi|vw|vh|vmin|vmax|mm|Q|cm|in|pt|pc|px|deg|grad|rad|turn|ms|s|Hz|kHz|%","type-selector":"<wq-name>|<ns-prefix>? '*'","var()":"var( <custom-property-name> , <declaration-value>? )","view()":"view( [<axis>||<'view-timeline-inset'>]? )","viewport-length":"auto|<length-percentage>","visual-box":"content-box|padding-box|border-box","wq-name":"<ns-prefix>? <ident-token>","-legacy-gradient":"<-webkit-gradient()>|<-legacy-linear-gradient>|<-legacy-repeating-linear-gradient>|<-legacy-radial-gradient>|<-legacy-repeating-radial-gradient>","-legacy-linear-gradient":"-moz-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-repeating-linear-gradient":"-moz-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-webkit-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )|-o-repeating-linear-gradient( <-legacy-linear-gradient-arguments> )","-legacy-linear-gradient-arguments":"[<angle>|<side-or-corner>]? , <color-stop-list>","-legacy-radial-gradient":"-moz-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-repeating-radial-gradient":"-moz-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-webkit-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )|-o-repeating-radial-gradient( <-legacy-radial-gradient-arguments> )","-legacy-radial-gradient-arguments":"[<position> ,]? [[[<-legacy-radial-gradient-shape>||<-legacy-radial-gradient-size>]|[<length>|<percentage>]{2}] ,]? <color-stop-list>","-legacy-radial-gradient-size":"closest-side|closest-corner|farthest-side|farthest-corner|contain|cover","-legacy-radial-gradient-shape":"circle|ellipse","-non-standard-font":"-apple-system-body|-apple-system-headline|-apple-system-subheadline|-apple-system-caption1|-apple-system-caption2|-apple-system-footnote|-apple-system-short-body|-apple-system-short-headline|-apple-system-short-subheadline|-apple-system-short-caption1|-apple-system-short-footnote|-apple-system-tall-body","-non-standard-color":"-moz-ButtonDefault|-moz-ButtonHoverFace|-moz-ButtonHoverText|-moz-CellHighlight|-moz-CellHighlightText|-moz-Combobox|-moz-ComboboxText|-moz-Dialog|-moz-DialogText|-moz-dragtargetzone|-moz-EvenTreeRow|-moz-Field|-moz-FieldText|-moz-html-CellHighlight|-moz-html-CellHighlightText|-moz-mac-accentdarkestshadow|-moz-mac-accentdarkshadow|-moz-mac-accentface|-moz-mac-accentlightesthighlight|-moz-mac-accentlightshadow|-moz-mac-accentregularhighlight|-moz-mac-accentregularshadow|-moz-mac-chrome-active|-moz-mac-chrome-inactive|-moz-mac-focusring|-moz-mac-menuselect|-moz-mac-menushadow|-moz-mac-menutextselect|-moz-MenuHover|-moz-MenuHoverText|-moz-MenuBarText|-moz-MenuBarHoverText|-moz-nativehyperlinktext|-moz-OddTreeRow|-moz-win-communicationstext|-moz-win-mediatext|-moz-activehyperlinktext|-moz-default-background-color|-moz-default-color|-moz-hyperlinktext|-moz-visitedhyperlinktext|-webkit-activelink|-webkit-focus-ring-color|-webkit-link|-webkit-text","-non-standard-image-rendering":"optimize-contrast|-moz-crisp-edges|-o-crisp-edges|-webkit-optimize-contrast","-non-standard-overflow":"overlay|-moz-scrollbars-none|-moz-scrollbars-horizontal|-moz-scrollbars-vertical|-moz-hidden-unscrollable","-non-standard-size":"intrinsic|min-intrinsic|-webkit-fill-available|-webkit-fit-content|-webkit-min-content|-webkit-max-content|-moz-available|-moz-fit-content|-moz-min-content|-moz-max-content","-webkit-gradient()":"-webkit-gradient( <-webkit-gradient-type> , <-webkit-gradient-point> [, <-webkit-gradient-point>|, <-webkit-gradient-radius> , <-webkit-gradient-point>] [, <-webkit-gradient-radius>]? [, <-webkit-gradient-color-stop>]* )","-webkit-gradient-color-stop":"from( <color> )|color-stop( [<number-zero-one>|<percentage>] , <color> )|to( <color> )","-webkit-gradient-point":"[left|center|right|<length-percentage>] [top|center|bottom|<length-percentage>]","-webkit-gradient-radius":"<length>|<percentage>","-webkit-gradient-type":"linear|radial","-webkit-mask-box-repeat":"repeat|stretch|round","-ms-filter-function-list":"<-ms-filter-function>+","-ms-filter-function":"<-ms-filter-function-progid>|<-ms-filter-function-legacy>","-ms-filter-function-progid":"'progid:' [<ident-token> '.']* [<ident-token>|<function-token> <any-value>? )]","-ms-filter-function-legacy":"<ident-token>|<function-token> <any-value>? )","absolute-color-base":"<hex-color>|<absolute-color-function>|<named-color>|transparent","absolute-color-function":"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<oklab()>|<oklch()>|<color()>",age:"child|young|old","anchor-name":"<dashed-ident>","attr-name":"<wq-name>","attr-fallback":"<any-value>","bg-clip":"<box>|border|text",bottom:"<length>|auto","container-name":"<custom-ident>","container-condition":"not <query-in-parens>|<query-in-parens> [[and <query-in-parens>]*|[or <query-in-parens>]*]","coord-box":"content-box|padding-box|border-box|fill-box|stroke-box|view-box","generic-voice":"[<age>? <gender> <integer>?]",gender:"male|female|neutral","generic-script-specific":"generic( kai )|generic( fangsong )|generic( nastaliq )","generic-complete":"serif|sans-serif|system-ui|cursive|fantasy|math|monospace","generic-incomplete":"ui-serif|ui-sans-serif|ui-monospace|ui-rounded","-non-standard-generic-family":"-apple-system|BlinkMacSystemFont",left:"<length>|auto","color-base":"<hex-color>|<color-function>|<named-color>|<color-mix()>|transparent","color-function":"<rgb()>|<rgba()>|<hsl()>|<hsla()>|<hwb()>|<lab()>|<lch()>|<oklab()>|<oklch()>|<color()>","device-cmyk()":"<legacy-device-cmyk-syntax>|<modern-device-cmyk-syntax>","legacy-device-cmyk-syntax":"device-cmyk( <number>#{4} )","modern-device-cmyk-syntax":"device-cmyk( <cmyk-component>{4} [/ [<alpha-value>|none]]? )","cmyk-component":"<number>|<percentage>|none","color-mix()":"color-mix( <color-interpolation-method> , [<color>&&<percentage [0,100]>?]#{2} )","color-space":"<rectangular-color-space>|<polar-color-space>|<custom-color-space>","custom-color-space":"<dashed-ident>",paint:"none|<color>|<url> [none|<color>]?|context-fill|context-stroke","palette-identifier":"<dashed-ident>",right:"<length>|auto","scope-start":"<forgiving-selector-list>","scope-end":"<forgiving-selector-list>","forgiving-selector-list":"<complex-real-selector-list>","forgiving-relative-selector-list":"<relative-real-selector-list>","selector-list":"<complex-selector-list>","complex-real-selector-list":"<complex-real-selector>#","simple-selector-list":"<simple-selector>#","relative-real-selector-list":"<relative-real-selector>#","complex-selector-unit":"[<compound-selector>? <pseudo-compound-selector>*]!","complex-real-selector":"<compound-selector> [<combinator>? <compound-selector>]*","relative-real-selector":"<combinator>? <complex-real-selector>","pseudo-compound-selector":"<pseudo-element-selector> <pseudo-class-selector>*","simple-selector":"<type-selector>|<subclass-selector>","legacy-pseudo-element-selector":"':' [before|after|first-line|first-letter]","single-animation-composition":"replace|add|accumulate","svg-length":"<percentage>|<length>|<number>","svg-writing-mode":"lr-tb|rl-tb|tb-rl|lr|rl|tb",top:"<length>|auto",x:"<number>",y:"<number>",declaration:"<ident-token> : <declaration-value>? ['!' important]?","declaration-list":"[<declaration>? ';']* <declaration>?",url:"url( <string> <url-modifier>* )|<url-token>","url-modifier":"<ident>|<function-token> <any-value> )","number-zero-one":"<number [0,1]>","number-one-or-greater":"<number [1,\u221E]>","color()":"color( <colorspace-params> [/ [<alpha-value>|none]]? )","colorspace-params":"[<predefined-rgb-params>|<xyz-params>]","predefined-rgb-params":"<predefined-rgb> [<number>|<percentage>|none]{3}","predefined-rgb":"srgb|srgb-linear|display-p3|a98-rgb|prophoto-rgb|rec2020","xyz-params":"<xyz-space> [<number>|<percentage>|none]{3}","xyz-space":"xyz|xyz-d50|xyz-d65","oklab()":"oklab( [<percentage>|<number>|none] [<percentage>|<number>|none] [<percentage>|<number>|none] [/ [<alpha-value>|none]]? )","oklch()":"oklch( [<percentage>|<number>|none] [<percentage>|<number>|none] [<hue>|none] [/ [<alpha-value>|none]]? )","offset-path":"<ray()>|<url>|<basic-shape>","rect()":"rect( [<length-percentage>|auto]{4} [round <'border-radius'>]? )","xywh()":"xywh( <length-percentage>{2} <length-percentage [0,\u221E]>{2} [round <'border-radius'>]? )","query-in-parens":"( <container-condition> )|( <size-feature> )|style( <style-query> )|<general-enclosed>","size-feature":"<mf-plain>|<mf-boolean>|<mf-range>","style-feature":"<declaration>","style-query":"<style-condition>|<style-feature>","style-condition":"not <style-in-parens>|<style-in-parens> [[and <style-in-parens>]*|[or <style-in-parens>]*]","style-in-parens":"( <style-condition> )|( <style-feature> )|<general-enclosed>","-non-standard-display":"-ms-inline-flexbox|-ms-grid|-ms-inline-grid|-webkit-flex|-webkit-inline-flex|-webkit-box|-webkit-inline-box|-moz-inline-stack|-moz-box|-moz-inline-box","inset-area":"[[left|center|right|span-left|span-right|x-start|x-end|span-x-start|span-x-end|x-self-start|x-self-end|span-x-self-start|span-x-self-end|span-all]||[top|center|bottom|span-top|span-bottom|y-start|y-end|span-y-start|span-y-end|y-self-start|y-self-end|span-y-self-start|span-y-self-end|span-all]|[block-start|center|block-end|span-block-start|span-block-end|span-all]||[inline-start|center|inline-end|span-inline-start|span-inline-end|span-all]|[self-block-start|self-block-end|span-self-block-start|span-self-block-end|span-all]||[self-inline-start|self-inline-end|span-self-inline-start|span-self-inline-end|span-all]|[start|center|end|span-start|span-end|span-all]{1,2}|[self-start|center|self-end|span-self-start|span-self-end|span-all]{1,2}]","position-area":"[[left|center|right|span-left|span-right|x-start|x-end|span-x-start|span-x-end|x-self-start|x-self-end|span-x-self-start|span-x-self-end|span-all]||[top|center|bottom|span-top|span-bottom|y-start|y-end|span-y-start|span-y-end|y-self-start|y-self-end|span-y-self-start|span-y-self-end|span-all]|[block-start|center|block-end|span-block-start|span-block-end|span-all]||[inline-start|center|inline-end|span-inline-start|span-inline-end|span-all]|[self-block-start|center|self-block-end|span-self-block-start|span-self-block-end|span-all]||[self-inline-start|center|self-inline-end|span-self-inline-start|span-self-inline-end|span-all]|[start|center|end|span-start|span-end|span-all]{1,2}|[self-start|center|self-end|span-self-start|span-self-end|span-all]{1,2}]","anchor()":"anchor( <anchor-element>?&&<anchor-side> , <length-percentage>? )","anchor-side":"inside|outside|top|left|right|bottom|start|end|self-start|self-end|<percentage>|center","anchor-size()":"anchor-size( [<anchor-element>||<anchor-size>]? , <length-percentage>? )","anchor-size":"width|height|block|inline|self-block|self-inline","anchor-element":"<dashed-ident>","try-size":"most-width|most-height|most-block-size|most-inline-size","try-tactic":"flip-block||flip-inline||flip-start","font-variant-css2":"normal|small-caps","font-width-css3":"normal|ultra-condensed|extra-condensed|condensed|semi-condensed|semi-expanded|expanded|extra-expanded|ultra-expanded","system-family-name":"caption|icon|menu|message-box|small-caption|status-bar"},properties:{"--*":"<declaration-value>","-ms-accelerator":"false|true","-ms-block-progression":"tb|rl|bt|lr","-ms-content-zoom-chaining":"none|chained","-ms-content-zooming":"none|zoom","-ms-content-zoom-limit":"<'-ms-content-zoom-limit-min'> <'-ms-content-zoom-limit-max'>","-ms-content-zoom-limit-max":"<percentage>","-ms-content-zoom-limit-min":"<percentage>","-ms-content-zoom-snap":"<'-ms-content-zoom-snap-type'>||<'-ms-content-zoom-snap-points'>","-ms-content-zoom-snap-points":"snapInterval( <percentage> , <percentage> )|snapList( <percentage># )","-ms-content-zoom-snap-type":"none|proximity|mandatory","-ms-filter":"<string>","-ms-flow-from":"[none|<custom-ident>]#","-ms-flow-into":"[none|<custom-ident>]#","-ms-grid-columns":"none|<track-list>|<auto-track-list>","-ms-grid-rows":"none|<track-list>|<auto-track-list>","-ms-high-contrast-adjust":"auto|none","-ms-hyphenate-limit-chars":"auto|<integer>{1,3}","-ms-hyphenate-limit-lines":"no-limit|<integer>","-ms-hyphenate-limit-zone":"<percentage>|<length>","-ms-ime-align":"auto|after","-ms-overflow-style":"auto|none|scrollbar|-ms-autohiding-scrollbar","-ms-scrollbar-3dlight-color":"<color>","-ms-scrollbar-arrow-color":"<color>","-ms-scrollbar-base-color":"<color>","-ms-scrollbar-darkshadow-color":"<color>","-ms-scrollbar-face-color":"<color>","-ms-scrollbar-highlight-color":"<color>","-ms-scrollbar-shadow-color":"<color>","-ms-scrollbar-track-color":"<color>","-ms-scroll-chaining":"chained|none","-ms-scroll-limit":"<'-ms-scroll-limit-x-min'> <'-ms-scroll-limit-y-min'> <'-ms-scroll-limit-x-max'> <'-ms-scroll-limit-y-max'>","-ms-scroll-limit-x-max":"auto|<length>","-ms-scroll-limit-x-min":"<length>","-ms-scroll-limit-y-max":"auto|<length>","-ms-scroll-limit-y-min":"<length>","-ms-scroll-rails":"none|railed","-ms-scroll-snap-points-x":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-points-y":"snapInterval( <length-percentage> , <length-percentage> )|snapList( <length-percentage># )","-ms-scroll-snap-type":"none|proximity|mandatory","-ms-scroll-snap-x":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-x'>","-ms-scroll-snap-y":"<'-ms-scroll-snap-type'> <'-ms-scroll-snap-points-y'>","-ms-scroll-translation":"none|vertical-to-horizontal","-ms-text-autospace":"none|ideograph-alpha|ideograph-numeric|ideograph-parenthesis|ideograph-space","-ms-touch-select":"grippers|none","-ms-user-select":"none|element|text","-ms-wrap-flow":"auto|both|start|end|maximum|clear","-ms-wrap-margin":"<length>","-ms-wrap-through":"wrap|none","-moz-appearance":"none|button|button-arrow-down|button-arrow-next|button-arrow-previous|button-arrow-up|button-bevel|button-focus|caret|checkbox|checkbox-container|checkbox-label|checkmenuitem|dualbutton|groupbox|listbox|listitem|menuarrow|menubar|menucheckbox|menuimage|menuitem|menuitemtext|menulist|menulist-button|menulist-text|menulist-textfield|menupopup|menuradio|menuseparator|meterbar|meterchunk|progressbar|progressbar-vertical|progresschunk|progresschunk-vertical|radio|radio-container|radio-label|radiomenuitem|range|range-thumb|resizer|resizerpanel|scale-horizontal|scalethumbend|scalethumb-horizontal|scalethumbstart|scalethumbtick|scalethumb-vertical|scale-vertical|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|separator|sheet|spinner|spinner-downbutton|spinner-textfield|spinner-upbutton|splitter|statusbar|statusbarpanel|tab|tabpanel|tabpanels|tab-scroll-arrow-back|tab-scroll-arrow-forward|textfield|textfield-multiline|toolbar|toolbarbutton|toolbarbutton-dropdown|toolbargripper|toolbox|tooltip|treeheader|treeheadercell|treeheadersortarrow|treeitem|treeline|treetwisty|treetwistyopen|treeview|-moz-mac-unified-toolbar|-moz-win-borderless-glass|-moz-win-browsertabbar-toolbox|-moz-win-communicationstext|-moz-win-communications-toolbox|-moz-win-exclude-glass|-moz-win-glass|-moz-win-mediatext|-moz-win-media-toolbox|-moz-window-button-box|-moz-window-button-box-maximized|-moz-window-button-close|-moz-window-button-maximize|-moz-window-button-minimize|-moz-window-button-restore|-moz-window-frame-bottom|-moz-window-frame-left|-moz-window-frame-right|-moz-window-titlebar|-moz-window-titlebar-maximized","-moz-binding":"<url>|none","-moz-border-bottom-colors":"<color>+|none","-moz-border-left-colors":"<color>+|none","-moz-border-right-colors":"<color>+|none","-moz-border-top-colors":"<color>+|none","-moz-context-properties":"none|[fill|fill-opacity|stroke|stroke-opacity]#","-moz-float-edge":"border-box|content-box|margin-box|padding-box","-moz-force-broken-image-icon":"0|1","-moz-image-region":"<shape>|auto","-moz-orient":"inline|block|horizontal|vertical","-moz-outline-radius":"<outline-radius>{1,4} [/ <outline-radius>{1,4}]?","-moz-outline-radius-bottomleft":"<outline-radius>","-moz-outline-radius-bottomright":"<outline-radius>","-moz-outline-radius-topleft":"<outline-radius>","-moz-outline-radius-topright":"<outline-radius>","-moz-stack-sizing":"ignore|stretch-to-fit","-moz-text-blink":"none|blink","-moz-user-focus":"ignore|normal|select-after|select-before|select-menu|select-same|select-all|none","-moz-user-input":"auto|none|enabled|disabled","-moz-user-modify":"read-only|read-write|write-only","-moz-window-dragging":"drag|no-drag","-moz-window-shadow":"default|menu|tooltip|sheet|none","-webkit-appearance":"none|button|button-bevel|caps-lock-indicator|caret|checkbox|default-button|inner-spin-button|listbox|listitem|media-controls-background|media-controls-fullscreen-background|media-current-time-display|media-enter-fullscreen-button|media-exit-fullscreen-button|media-fullscreen-button|media-mute-button|media-overlay-play-button|media-play-button|media-seek-back-button|media-seek-forward-button|media-slider|media-sliderthumb|media-time-remaining-display|media-toggle-closed-captions-button|media-volume-slider|media-volume-slider-container|media-volume-sliderthumb|menulist|menulist-button|menulist-text|menulist-textfield|meter|progress-bar|progress-bar-value|push-button|radio|scrollbarbutton-down|scrollbarbutton-left|scrollbarbutton-right|scrollbarbutton-up|scrollbargripper-horizontal|scrollbargripper-vertical|scrollbarthumb-horizontal|scrollbarthumb-vertical|scrollbartrack-horizontal|scrollbartrack-vertical|searchfield|searchfield-cancel-button|searchfield-decoration|searchfield-results-button|searchfield-results-decoration|slider-horizontal|slider-vertical|sliderthumb-horizontal|sliderthumb-vertical|square-button|textarea|textfield|-apple-pay-button","-webkit-border-before":"<'border-width'>||<'border-style'>||<color>","-webkit-border-before-color":"<color>","-webkit-border-before-style":"<'border-style'>","-webkit-border-before-width":"<'border-width'>","-webkit-box-reflect":"[above|below|right|left]? <length>? <image>?","-webkit-line-clamp":"none|<integer>","-webkit-mask":"[<mask-reference>||<position> [/ <bg-size>]?||<repeat-style>||[<box>|border|padding|content|text]||[<box>|border|padding|content]]#","-webkit-mask-attachment":"<attachment>#","-webkit-mask-clip":"[<box>|border|padding|content|text]#","-webkit-mask-composite":"<composite-style>#","-webkit-mask-image":"<mask-reference>#","-webkit-mask-origin":"[<box>|border|padding|content]#","-webkit-mask-position":"<position>#","-webkit-mask-position-x":"[<length-percentage>|left|center|right]#","-webkit-mask-position-y":"[<length-percentage>|top|center|bottom]#","-webkit-mask-repeat":"<repeat-style>#","-webkit-mask-repeat-x":"repeat|no-repeat|space|round","-webkit-mask-repeat-y":"repeat|no-repeat|space|round","-webkit-mask-size":"<bg-size>#","-webkit-overflow-scrolling":"auto|touch","-webkit-tap-highlight-color":"<color>","-webkit-text-fill-color":"<color>","-webkit-text-stroke":"<length>||<color>","-webkit-text-stroke-color":"<color>","-webkit-text-stroke-width":"<length>","-webkit-touch-callout":"default|none","-webkit-user-modify":"read-only|read-write|read-write-plaintext-only","accent-color":"auto|<color>","align-content":"normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>","align-items":"normal|stretch|<baseline-position>|[<overflow-position>? <self-position>]","align-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? <self-position>","align-tracks":"[normal|<baseline-position>|<content-distribution>|<overflow-position>? <content-position>]#",all:"initial|inherit|unset|revert|revert-layer","anchor-name":"none|<dashed-ident>#","anchor-scope":"none|all|<dashed-ident>#",animation:"<single-animation>#","animation-composition":"<single-animation-composition>#","animation-delay":"<time>#","animation-direction":"<single-animation-direction>#","animation-duration":"<time>#","animation-fill-mode":"<single-animation-fill-mode>#","animation-iteration-count":"<single-animation-iteration-count>#","animation-name":"[none|<keyframes-name>]#","animation-play-state":"<single-animation-play-state>#","animation-range":"[<'animation-range-start'> <'animation-range-end'>?]#","animation-range-end":"[normal|<length-percentage>|<timeline-range-name> <length-percentage>?]#","animation-range-start":"[normal|<length-percentage>|<timeline-range-name> <length-percentage>?]#","animation-timing-function":"<easing-function>#","animation-timeline":"<single-animation-timeline>#",appearance:"none|auto|textfield|menulist-button|<compat-auto>","aspect-ratio":"auto||<ratio>",azimuth:"<angle>|[[left-side|far-left|left|center-left|center|center-right|right|far-right|right-side]||behind]|leftwards|rightwards","backdrop-filter":"none|<filter-function-list>","backface-visibility":"visible|hidden",background:"[<bg-layer> ,]* <final-bg-layer>","background-attachment":"<attachment>#","background-blend-mode":"<blend-mode>#","background-clip":"<bg-clip>#","background-color":"<color>","background-image":"<bg-image>#","background-origin":"<box>#","background-position":"<bg-position>#","background-position-x":"[center|[[left|right|x-start|x-end]? <length-percentage>?]!]#","background-position-y":"[center|[[top|bottom|y-start|y-end]? <length-percentage>?]!]#","background-repeat":"<repeat-style>#","background-size":"<bg-size>#","block-size":"<'width'>",border:"<line-width>||<line-style>||<color>","border-block":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-color":"<'border-top-color'>{1,2}","border-block-style":"<'border-top-style'>","border-block-width":"<'border-top-width'>","border-block-end":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-end-color":"<'border-top-color'>","border-block-end-style":"<'border-top-style'>","border-block-end-width":"<'border-top-width'>","border-block-start":"<'border-top-width'>||<'border-top-style'>||<color>","border-block-start-color":"<'border-top-color'>","border-block-start-style":"<'border-top-style'>","border-block-start-width":"<'border-top-width'>","border-bottom":"<line-width>||<line-style>||<color>","border-bottom-color":"<'border-top-color'>","border-bottom-left-radius":"<length-percentage>{1,2}","border-bottom-right-radius":"<length-percentage>{1,2}","border-bottom-style":"<line-style>","border-bottom-width":"<line-width>","border-collapse":"collapse|separate","border-color":"<color>{1,4}","border-end-end-radius":"<length-percentage>{1,2}","border-end-start-radius":"<length-percentage>{1,2}","border-image":"<'border-image-source'>||<'border-image-slice'> [/ <'border-image-width'>|/ <'border-image-width'>? / <'border-image-outset'>]?||<'border-image-repeat'>","border-image-outset":"[<length>|<number>]{1,4}","border-image-repeat":"[stretch|repeat|round|space]{1,2}","border-image-slice":"<number-percentage>{1,4}&&fill?","border-image-source":"none|<image>","border-image-width":"[<length-percentage>|<number>|auto]{1,4}","border-inline":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-end":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-color":"<'border-top-color'>{1,2}","border-inline-style":"<'border-top-style'>","border-inline-width":"<'border-top-width'>","border-inline-end-color":"<'border-top-color'>","border-inline-end-style":"<'border-top-style'>","border-inline-end-width":"<'border-top-width'>","border-inline-start":"<'border-top-width'>||<'border-top-style'>||<color>","border-inline-start-color":"<'border-top-color'>","border-inline-start-style":"<'border-top-style'>","border-inline-start-width":"<'border-top-width'>","border-left":"<line-width>||<line-style>||<color>","border-left-color":"<color>","border-left-style":"<line-style>","border-left-width":"<line-width>","border-radius":"<length-percentage>{1,4} [/ <length-percentage>{1,4}]?","border-right":"<line-width>||<line-style>||<color>","border-right-color":"<color>","border-right-style":"<line-style>","border-right-width":"<line-width>","border-spacing":"<length> <length>?","border-start-end-radius":"<length-percentage>{1,2}","border-start-start-radius":"<length-percentage>{1,2}","border-style":"<line-style>{1,4}","border-top":"<line-width>||<line-style>||<color>","border-top-color":"<color>","border-top-left-radius":"<length-percentage>{1,2}","border-top-right-radius":"<length-percentage>{1,2}","border-top-style":"<line-style>","border-top-width":"<line-width>","border-width":"<line-width>{1,4}",bottom:"<length>|<percentage>|auto","box-align":"start|center|end|baseline|stretch","box-decoration-break":"slice|clone","box-direction":"normal|reverse|inherit","box-flex":"<number>","box-flex-group":"<integer>","box-lines":"single|multiple","box-ordinal-group":"<integer>","box-orient":"horizontal|vertical|inline-axis|block-axis|inherit","box-pack":"start|center|end|justify","box-shadow":"none|<shadow>#","box-sizing":"content-box|border-box","break-after":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-before":"auto|avoid|always|all|avoid-page|page|left|right|recto|verso|avoid-column|column|avoid-region|region","break-inside":"auto|avoid|avoid-page|avoid-column|avoid-region","caption-side":"top|bottom|block-start|block-end|inline-start|inline-end",caret:"<'caret-color'>||<'caret-shape'>","caret-color":"auto|<color>","caret-shape":"auto|bar|block|underscore",clear:"none|left|right|both|inline-start|inline-end",clip:"<shape>|auto","clip-path":"<clip-source>|[<basic-shape>||<geometry-box>]|none","clip-rule":"nonzero|evenodd",color:"<color>","color-interpolation-filters":"auto|sRGB|linearRGB","color-scheme":"normal|[light|dark|<custom-ident>]+&&only?","column-count":"<integer>|auto","column-fill":"auto|balance","column-gap":"normal|<length-percentage>","column-rule":"<'column-rule-width'>||<'column-rule-style'>||<'column-rule-color'>","column-rule-color":"<color>","column-rule-style":"<'border-style'>","column-rule-width":"<'border-width'>","column-span":"none|all","column-width":"<length>|auto",columns:"<'column-width'>||<'column-count'>",contain:"none|strict|content|[[size||inline-size]||layout||style||paint]","contain-intrinsic-size":"[auto? [none|<length>]]{1,2}","contain-intrinsic-block-size":"auto? [none|<length>]","contain-intrinsic-height":"auto? [none|<length>]","contain-intrinsic-inline-size":"auto? [none|<length>]","contain-intrinsic-width":"auto? [none|<length>]",container:"<'container-name'> [/ <'container-type'>]?","container-name":"none|<custom-ident>+","container-type":"normal||[size|inline-size]",content:"normal|none|[<content-replacement>|<content-list>] [/ [<string>|<counter>]+]?","content-visibility":"visible|auto|hidden","counter-increment":"[<counter-name> <integer>?]+|none","counter-reset":"[<counter-name> <integer>?|<reversed-counter-name> <integer>?]+|none","counter-set":"[<counter-name> <integer>?]+|none",cursor:"[[<url> [<x> <y>]? ,]* [auto|default|none|context-menu|help|pointer|progress|wait|cell|crosshair|text|vertical-text|alias|copy|move|no-drop|not-allowed|e-resize|n-resize|ne-resize|nw-resize|s-resize|se-resize|sw-resize|w-resize|ew-resize|ns-resize|nesw-resize|nwse-resize|col-resize|row-resize|all-scroll|zoom-in|zoom-out|grab|grabbing|hand|-webkit-grab|-webkit-grabbing|-webkit-zoom-in|-webkit-zoom-out|-moz-grab|-moz-grabbing|-moz-zoom-in|-moz-zoom-out]]",d:"none|path( <string> )",cx:"<length>|<percentage>",cy:"<length>|<percentage>",direction:"ltr|rtl",display:"[<display-outside>||<display-inside>]|<display-listitem>|<display-internal>|<display-box>|<display-legacy>|<-non-standard-display>","dominant-baseline":"auto|use-script|no-change|reset-size|ideographic|alphabetic|hanging|mathematical|central|middle|text-after-edge|text-before-edge","empty-cells":"show|hide","field-sizing":"content|fixed",fill:"<paint>","fill-opacity":"<number-zero-one>","fill-rule":"nonzero|evenodd",filter:"none|<filter-function-list>|<-ms-filter-function-list>",flex:"none|[<'flex-grow'> <'flex-shrink'>?||<'flex-basis'>]","flex-basis":"content|<'width'>","flex-direction":"row|row-reverse|column|column-reverse","flex-flow":"<'flex-direction'>||<'flex-wrap'>","flex-grow":"<number>","flex-shrink":"<number>","flex-wrap":"nowrap|wrap|wrap-reverse",float:"left|right|none|inline-start|inline-end",font:"[[<'font-style'>||<font-variant-css2>||<'font-weight'>||<font-width-css3>]? <'font-size'> [/ <'line-height'>]? <'font-family'>#]|<system-family-name>|<-non-standard-font>","font-family":"[<family-name>|<generic-family>]#","font-feature-settings":"normal|<feature-tag-value>#","font-kerning":"auto|normal|none","font-language-override":"normal|<string>","font-optical-sizing":"auto|none","font-palette":"normal|light|dark|<palette-identifier>","font-variation-settings":"normal|[<string> <number>]#","font-size":"<absolute-size>|<relative-size>|<length-percentage>","font-size-adjust":"none|[ex-height|cap-height|ch-width|ic-width|ic-height]? [from-font|<number>]","font-smooth":"auto|never|always|<absolute-size>|<length>","font-stretch":"<font-stretch-absolute>","font-style":"normal|italic|oblique <angle>?","font-synthesis":"none|[weight||style||small-caps||position]","font-synthesis-position":"auto|none","font-synthesis-small-caps":"auto|none","font-synthesis-style":"auto|none","font-synthesis-weight":"auto|none","font-variant":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>||stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )||[small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps]||<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero||<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-alternates":"normal|[stylistic( <feature-value-name> )||historical-forms||styleset( <feature-value-name># )||character-variant( <feature-value-name># )||swash( <feature-value-name> )||ornaments( <feature-value-name> )||annotation( <feature-value-name> )]","font-variant-caps":"normal|small-caps|all-small-caps|petite-caps|all-petite-caps|unicase|titling-caps","font-variant-east-asian":"normal|[<east-asian-variant-values>||<east-asian-width-values>||ruby]","font-variant-emoji":"normal|text|emoji|unicode","font-variant-ligatures":"normal|none|[<common-lig-values>||<discretionary-lig-values>||<historical-lig-values>||<contextual-alt-values>]","font-variant-numeric":"normal|[<numeric-figure-values>||<numeric-spacing-values>||<numeric-fraction-values>||ordinal||slashed-zero]","font-variant-position":"normal|sub|super","font-weight":"<font-weight-absolute>|bolder|lighter","forced-color-adjust":"auto|none|preserve-parent-color",gap:"<'row-gap'> <'column-gap'>?",grid:"<'grid-template'>|<'grid-template-rows'> / [auto-flow&&dense?] <'grid-auto-columns'>?|[auto-flow&&dense?] <'grid-auto-rows'>? / <'grid-template-columns'>","grid-area":"<grid-line> [/ <grid-line>]{0,3}","grid-auto-columns":"<track-size>+","grid-auto-flow":"[row|column]||dense","grid-auto-rows":"<track-size>+","grid-column":"<grid-line> [/ <grid-line>]?","grid-column-end":"<grid-line>","grid-column-gap":"<length-percentage>","grid-column-start":"<grid-line>","grid-gap":"<'grid-row-gap'> <'grid-column-gap'>?","grid-row":"<grid-line> [/ <grid-line>]?","grid-row-end":"<grid-line>","grid-row-gap":"<length-percentage>","grid-row-start":"<grid-line>","grid-template":"none|[<'grid-template-rows'> / <'grid-template-columns'>]|[<line-names>? <string> <track-size>? <line-names>?]+ [/ <explicit-track-list>]?","grid-template-areas":"none|<string>+","grid-template-columns":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","grid-template-rows":"none|<track-list>|<auto-track-list>|subgrid <line-name-list>?","hanging-punctuation":"none|[first||[force-end|allow-end]||last]",height:"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","hyphenate-character":"auto|<string>","hyphenate-limit-chars":"[auto|<integer>]{1,3}",hyphens:"none|manual|auto","image-orientation":"from-image|<angle>|[<angle>? flip]","image-rendering":"auto|crisp-edges|pixelated|optimizeSpeed|optimizeQuality|<-non-standard-image-rendering>","image-resolution":"[from-image||<resolution>]&&snap?","ime-mode":"auto|normal|active|inactive|disabled","initial-letter":"normal|[<number> <integer>?]","initial-letter-align":"[auto|alphabetic|hanging|ideographic]","inline-size":"<'width'>","input-security":"auto|none",inset:"<'top'>{1,4}","inset-block":"<'top'>{1,2}","inset-block-end":"<'top'>","inset-block-start":"<'top'>","inset-inline":"<'top'>{1,2}","inset-inline-end":"<'top'>","inset-inline-start":"<'top'>","interpolate-size":"numeric-only|allow-keywords",isolation:"auto|isolate","justify-content":"normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]","justify-items":"normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]|legacy|legacy&&[left|right|center]","justify-self":"auto|normal|stretch|<baseline-position>|<overflow-position>? [<self-position>|left|right]","justify-tracks":"[normal|<content-distribution>|<overflow-position>? [<content-position>|left|right]]#",left:"<length>|<percentage>|auto","letter-spacing":"normal|<length-percentage>","line-break":"auto|loose|normal|strict|anywhere","line-clamp":"none|<integer>","line-height":"normal|<number>|<length>|<percentage>","line-height-step":"<length>","list-style":"<'list-style-type'>||<'list-style-position'>||<'list-style-image'>","list-style-image":"<image>|none","list-style-position":"inside|outside","list-style-type":"<counter-style>|<string>|none",margin:"[<length>|<percentage>|auto]{1,4}","margin-block":"<'margin-left'>{1,2}","margin-block-end":"<'margin-left'>","margin-block-start":"<'margin-left'>","margin-bottom":"<length>|<percentage>|auto","margin-inline":"<'margin-left'>{1,2}","margin-inline-end":"<'margin-left'>","margin-inline-start":"<'margin-left'>","margin-left":"<length>|<percentage>|auto","margin-right":"<length>|<percentage>|auto","margin-top":"<length>|<percentage>|auto","margin-trim":"none|in-flow|all",marker:"none|<url>","marker-end":"none|<url>","marker-mid":"none|<url>","marker-start":"none|<url>",mask:"<mask-layer>#","mask-border":"<'mask-border-source'>||<'mask-border-slice'> [/ <'mask-border-width'>? [/ <'mask-border-outset'>]?]?||<'mask-border-repeat'>||<'mask-border-mode'>","mask-border-mode":"luminance|alpha","mask-border-outset":"[<length>|<number>]{1,4}","mask-border-repeat":"[stretch|repeat|round|space]{1,2}","mask-border-slice":"<number-percentage>{1,4} fill?","mask-border-source":"none|<image>","mask-border-width":"[<length-percentage>|<number>|auto]{1,4}","mask-clip":"[<geometry-box>|no-clip]#","mask-composite":"<compositing-operator>#","mask-image":"<mask-reference>#","mask-mode":"<masking-mode>#","mask-origin":"<geometry-box>#","mask-position":"<position>#","mask-repeat":"<repeat-style>#","mask-size":"<bg-size>#","mask-type":"luminance|alpha","masonry-auto-flow":"[pack|next]||[definite-first|ordered]","math-depth":"auto-add|add( <integer> )|<integer>","math-shift":"normal|compact","math-style":"normal|compact","max-block-size":"<'max-width'>","max-height":"none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","max-inline-size":"<'max-width'>","max-lines":"none|<integer>","max-width":"none|<length-percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","min-block-size":"<'min-width'>","min-height":"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","min-inline-size":"<'min-width'>","min-width":"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","mix-blend-mode":"<blend-mode>|plus-lighter","object-fit":"fill|contain|cover|none|scale-down","object-position":"<position>",offset:"[<'offset-position'>? [<'offset-path'> [<'offset-distance'>||<'offset-rotate'>]?]?]! [/ <'offset-anchor'>]?","offset-anchor":"auto|<position>","offset-distance":"<length-percentage>","offset-path":"none|<offset-path>||<coord-box>","offset-position":"normal|auto|<position>","offset-rotate":"[auto|reverse]||<angle>",opacity:"<alpha-value>",order:"<integer>",orphans:"<integer>",outline:"[<'outline-width'>||<'outline-style'>||<'outline-color'>]","outline-color":"auto|<color>","outline-offset":"<length>","outline-style":"auto|<'border-style'>","outline-width":"<line-width>",overflow:"[visible|hidden|clip|scroll|auto]{1,2}|<-non-standard-overflow>","overflow-anchor":"auto|none","overflow-block":"visible|hidden|clip|scroll|auto","overflow-clip-box":"padding-box|content-box","overflow-clip-margin":"<visual-box>||<length [0,\u221E]>","overflow-inline":"visible|hidden|clip|scroll|auto","overflow-wrap":"normal|break-word|anywhere","overflow-x":"visible|hidden|clip|scroll|auto","overflow-y":"visible|hidden|clip|scroll|auto",overlay:"none|auto","overscroll-behavior":"[contain|none|auto]{1,2}","overscroll-behavior-block":"contain|none|auto","overscroll-behavior-inline":"contain|none|auto","overscroll-behavior-x":"contain|none|auto","overscroll-behavior-y":"contain|none|auto",padding:"[<length>|<percentage>]{1,4}","padding-block":"<'padding-left'>{1,2}","padding-block-end":"<'padding-left'>","padding-block-start":"<'padding-left'>","padding-bottom":"<length>|<percentage>","padding-inline":"<'padding-left'>{1,2}","padding-inline-end":"<'padding-left'>","padding-inline-start":"<'padding-left'>","padding-left":"<length>|<percentage>","padding-right":"<length>|<percentage>","padding-top":"<length>|<percentage>",page:"auto|<custom-ident>","page-break-after":"auto|always|avoid|left|right|recto|verso","page-break-before":"auto|always|avoid|left|right|recto|verso","page-break-inside":"auto|avoid","paint-order":"normal|[fill||stroke||markers]",perspective:"none|<length>","perspective-origin":"<position>","place-content":"<'align-content'> <'justify-content'>?","place-items":"<'align-items'> <'justify-items'>?","place-self":"<'align-self'> <'justify-self'>?","pointer-events":"auto|none|visiblePainted|visibleFill|visibleStroke|visible|painted|fill|stroke|all|inherit",position:"static|relative|absolute|sticky|fixed|-webkit-sticky","position-anchor":"auto|<anchor-name>","position-area":"none|<position-area>","position-try":"<'position-try-order'>? <'position-try-fallbacks'>","position-try-fallbacks":"none|[[<dashed-ident>||<try-tactic>]|<'position-area'>]#","position-try-order":"normal|<try-size>","position-visibility":"always|[anchors-valid||anchors-visible||no-overflow]","print-color-adjust":"economy|exact",quotes:"none|auto|[<string> <string>]+",r:"<length>|<percentage>",resize:"none|both|horizontal|vertical|block|inline",right:"<length>|<percentage>|auto",rotate:"none|<angle>|[x|y|z|<number>{3}]&&<angle>","row-gap":"normal|<length-percentage>","ruby-align":"start|center|space-between|space-around","ruby-merge":"separate|collapse|auto","ruby-position":"[alternate||[over|under]]|inter-character",rx:"<length>|<percentage>",ry:"<length>|<percentage>",scale:"none|[<number>|<percentage>]{1,3}","scrollbar-color":"auto|<color>{2}","scrollbar-gutter":"auto|stable&&both-edges?","scrollbar-width":"auto|thin|none","scroll-behavior":"auto|smooth","scroll-margin":"<length>{1,4}","scroll-margin-block":"<length>{1,2}","scroll-margin-block-start":"<length>","scroll-margin-block-end":"<length>","scroll-margin-bottom":"<length>","scroll-margin-inline":"<length>{1,2}","scroll-margin-inline-start":"<length>","scroll-margin-inline-end":"<length>","scroll-margin-left":"<length>","scroll-margin-right":"<length>","scroll-margin-top":"<length>","scroll-padding":"[auto|<length-percentage>]{1,4}","scroll-padding-block":"[auto|<length-percentage>]{1,2}","scroll-padding-block-start":"auto|<length-percentage>","scroll-padding-block-end":"auto|<length-percentage>","scroll-padding-bottom":"auto|<length-percentage>","scroll-padding-inline":"[auto|<length-percentage>]{1,2}","scroll-padding-inline-start":"auto|<length-percentage>","scroll-padding-inline-end":"auto|<length-percentage>","scroll-padding-left":"auto|<length-percentage>","scroll-padding-right":"auto|<length-percentage>","scroll-padding-top":"auto|<length-percentage>","scroll-snap-align":"[none|start|end|center]{1,2}","scroll-snap-coordinate":"none|<position>#","scroll-snap-destination":"<position>","scroll-snap-points-x":"none|repeat( <length-percentage> )","scroll-snap-points-y":"none|repeat( <length-percentage> )","scroll-snap-stop":"normal|always","scroll-snap-type":"none|[x|y|block|inline|both] [mandatory|proximity]?","scroll-snap-type-x":"none|mandatory|proximity","scroll-snap-type-y":"none|mandatory|proximity","scroll-timeline":"[<'scroll-timeline-name'>||<'scroll-timeline-axis'>]#","scroll-timeline-axis":"[block|inline|x|y]#","scroll-timeline-name":"[none|<dashed-ident>]#","shape-image-threshold":"<alpha-value>","shape-margin":"<length-percentage>","shape-outside":"none|[<shape-box>||<basic-shape>]|<image>","shape-rendering":"auto|optimizeSpeed|crispEdges|geometricPrecision",stroke:"<paint>","stroke-dasharray":"none|[<svg-length>+]#","stroke-dashoffset":"<svg-length>","stroke-linecap":"butt|round|square","stroke-linejoin":"miter|round|bevel","stroke-miterlimit":"<number-one-or-greater>","stroke-opacity":"<'opacity'>","stroke-width":"<svg-length>","tab-size":"<integer>|<length>","table-layout":"auto|fixed","text-align":"start|end|left|right|center|justify|match-parent","text-align-last":"auto|start|end|left|right|center|justify","text-anchor":"start|middle|end","text-combine-upright":"none|all|[digits <integer>?]","text-decoration":"<'text-decoration-line'>||<'text-decoration-style'>||<'text-decoration-color'>||<'text-decoration-thickness'>","text-decoration-color":"<color>","text-decoration-line":"none|[underline||overline||line-through||blink]|spelling-error|grammar-error","text-decoration-skip":"none|[objects||[spaces|[leading-spaces||trailing-spaces]]||edges||box-decoration]","text-decoration-skip-ink":"auto|all|none","text-decoration-style":"solid|double|dotted|dashed|wavy","text-decoration-thickness":"auto|from-font|<length>|<percentage>","text-emphasis":"<'text-emphasis-style'>||<'text-emphasis-color'>","text-emphasis-color":"<color>","text-emphasis-position":"auto|[over|under]&&[right|left]?","text-emphasis-style":"none|[[filled|open]||[dot|circle|double-circle|triangle|sesame]]|<string>","text-indent":"<length-percentage>&&hanging?&&each-line?","text-justify":"auto|inter-character|inter-word|none","text-orientation":"mixed|upright|sideways","text-overflow":"[clip|ellipsis|<string>]{1,2}","text-rendering":"auto|optimizeSpeed|optimizeLegibility|geometricPrecision","text-shadow":"none|<shadow-t>#","text-size-adjust":"none|auto|<percentage>","text-spacing-trim":"space-all|normal|space-first|trim-start|trim-both|trim-all|auto","text-transform":"none|capitalize|uppercase|lowercase|full-width|full-size-kana","text-underline-offset":"auto|<length>|<percentage>","text-underline-position":"auto|from-font|[under||[left|right]]","text-wrap":"<'text-wrap-mode'>||<'text-wrap-style'>","text-wrap-mode":"auto|wrap|nowrap","text-wrap-style":"auto|balance|stable|pretty","timeline-scope":"none|<dashed-ident>#",top:"<length>|<percentage>|auto","touch-action":"auto|none|[[pan-x|pan-left|pan-right]||[pan-y|pan-up|pan-down]||pinch-zoom]|manipulation",transform:"none|<transform-list>","transform-box":"content-box|border-box|fill-box|stroke-box|view-box","transform-origin":"[<length-percentage>|left|center|right|top|bottom]|[[<length-percentage>|left|center|right]&&[<length-percentage>|top|center|bottom]] <length>?","transform-style":"flat|preserve-3d",transition:"<single-transition>#","transition-behavior":"<transition-behavior-value>#","transition-delay":"<time>#","transition-duration":"<time>#","transition-property":"none|<single-transition-property>#","transition-timing-function":"<easing-function>#",translate:"none|<length-percentage> [<length-percentage> <length>?]?","unicode-bidi":"normal|embed|isolate|bidi-override|isolate-override|plaintext|-moz-isolate|-moz-isolate-override|-moz-plaintext|-webkit-isolate|-webkit-isolate-override|-webkit-plaintext","user-select":"auto|text|none|contain|all","vector-effect":"none|non-scaling-stroke|non-scaling-size|non-rotation|fixed-position","vertical-align":"baseline|sub|super|text-top|text-bottom|middle|top|bottom|<percentage>|<length>","view-timeline":"[<'view-timeline-name'> <'view-timeline-axis'>?]#","view-timeline-axis":"[block|inline|x|y]#","view-timeline-inset":"[[auto|<length-percentage>]{1,2}]#","view-timeline-name":"none|<dashed-ident>#","view-transition-name":"none|<custom-ident>",visibility:"visible|hidden|collapse","white-space":"normal|pre|nowrap|pre-wrap|pre-line|break-spaces|[<'white-space-collapse'>||<'text-wrap'>||<'white-space-trim'>]","white-space-collapse":"collapse|discard|preserve|preserve-breaks|preserve-spaces|break-spaces",widows:"<integer>",width:"auto|<length>|<percentage>|min-content|max-content|fit-content|fit-content( <length-percentage> )|stretch|<-non-standard-size>","will-change":"auto|<animateable-feature>#","word-break":"normal|break-all|keep-all|break-word|auto-phrase","word-spacing":"normal|<length>","word-wrap":"normal|break-word","writing-mode":"horizontal-tb|vertical-rl|vertical-lr|sideways-rl|sideways-lr|<svg-writing-mode>",x:"<length>|<percentage>",y:"<length>|<percentage>","z-index":"auto|<integer>",zoom:"normal|reset|<number>|<percentage>","-moz-background-clip":"padding|border","-moz-border-radius-bottomleft":"<'border-bottom-left-radius'>","-moz-border-radius-bottomright":"<'border-bottom-right-radius'>","-moz-border-radius-topleft":"<'border-top-left-radius'>","-moz-border-radius-topright":"<'border-bottom-right-radius'>","-moz-control-character-visibility":"visible|hidden","-moz-osx-font-smoothing":"auto|grayscale","-moz-user-select":"none|text|all|-moz-none","-ms-flex-align":"start|end|center|baseline|stretch","-ms-flex-item-align":"auto|start|end|center|baseline|stretch","-ms-flex-line-pack":"start|end|center|justify|distribute|stretch","-ms-flex-negative":"<'flex-shrink'>","-ms-flex-pack":"start|end|center|justify|distribute","-ms-flex-order":"<integer>","-ms-flex-positive":"<'flex-grow'>","-ms-flex-preferred-size":"<'flex-basis'>","-ms-interpolation-mode":"nearest-neighbor|bicubic","-ms-grid-column-align":"start|end|center|stretch","-ms-grid-row-align":"start|end|center|stretch","-ms-hyphenate-limit-last":"none|always|column|page|spread","-webkit-background-clip":"[<box>|border|padding|content|text]#","-webkit-column-break-after":"always|auto|avoid","-webkit-column-break-before":"always|auto|avoid","-webkit-column-break-inside":"always|auto|avoid","-webkit-font-smoothing":"auto|none|antialiased|subpixel-antialiased","-webkit-mask-box-image":"[<url>|<gradient>|none] [<length-percentage>{4} <-webkit-mask-box-repeat>{2}]?","-webkit-print-color-adjust":"economy|exact","-webkit-text-security":"none|circle|disc|square","-webkit-user-drag":"none|element|auto","-webkit-user-select":"auto|none|text|all","alignment-baseline":"auto|baseline|before-edge|text-before-edge|middle|central|after-edge|text-after-edge|ideographic|alphabetic|hanging|mathematical","baseline-shift":"baseline|sub|super|<svg-length>",behavior:"<url>+",cue:"<'cue-before'> <'cue-after'>?","cue-after":"<url> <decibel>?|none","cue-before":"<url> <decibel>?|none","glyph-orientation-horizontal":"<angle>","glyph-orientation-vertical":"<angle>",kerning:"auto|<svg-length>",pause:"<'pause-before'> <'pause-after'>?","pause-after":"<time>|none|x-weak|weak|medium|strong|x-strong","pause-before":"<time>|none|x-weak|weak|medium|strong|x-strong",rest:"<'rest-before'> <'rest-after'>?","rest-after":"<time>|none|x-weak|weak|medium|strong|x-strong","rest-before":"<time>|none|x-weak|weak|medium|strong|x-strong",src:"[<url> [format( <string># )]?|local( <family-name> )]#",speak:"auto|never|always","speak-as":"normal|spell-out||digits||[literal-punctuation|no-punctuation]","unicode-range":"<urange>#","voice-balance":"<number>|left|center|right|leftwards|rightwards","voice-duration":"auto|<time>","voice-family":"[[<family-name>|<generic-voice>] ,]* [<family-name>|<generic-voice>]|preserve","voice-pitch":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-range":"<frequency>&&absolute|[[x-low|low|medium|high|x-high]||[<frequency>|<semitones>|<percentage>]]","voice-rate":"[normal|x-slow|slow|medium|fast|x-fast]||<percentage>","voice-stress":"normal|strong|moderate|none|reduced","voice-volume":"silent|[[x-soft|soft|medium|loud|x-loud]||<decibel>]","white-space-trim":"none|discard-before||discard-after||discard-inner"},atrules:{charset:{prelude:"<string>",descriptors:null},"counter-style":{prelude:"<counter-style-name>",descriptors:{"additive-symbols":"[<integer>&&<symbol>]#",fallback:"<counter-style-name>",negative:"<symbol> <symbol>?",pad:"<integer>&&<symbol>",prefix:"<symbol>",range:"[[<integer>|infinite]{2}]#|auto","speak-as":"auto|bullets|numbers|words|spell-out|<counter-style-name>",suffix:"<symbol>",symbols:"<symbol>+",system:"cyclic|numeric|alphabetic|symbolic|additive|[fixed <integer>?]|[extends <counter-style-name>]"}},document:{prelude:"[<url>|url-prefix( <string> )|domain( <string> )|media-document( <string> )|regexp( <string> )]#",descriptors:null},"font-palette-values":{prelude:"<dashed-ident>",descriptors:{"base-palette":"light|dark|<integer [0,\u221E]>","font-family":"<family-name>#","override-colors":"[<integer [0,\u221E]> <absolute-color-base>]#"}},"font-face":{prelude:null,descriptors:{"ascent-override":"normal|<percentage>","descent-override":"normal|<percentage>","font-display":"[auto|block|swap|fallback|optional]","font-family":"<family-name>","font-feature-settings":"normal|<feature-tag-value>#","font-variation-settings":"normal|[<string> <number>]#","font-stretch":"<font-stretch-absolute>{1,2}","font-style":"normal|italic|oblique <angle>{0,2}","font-weight":"<font-weight-absolute>{1,2}","line-gap-override":"normal|<percentage>","size-adjust":"<percentage>",src:"[<url> [format( <string># )]?|local( <family-name> )]#","unicode-range":"<urange>#"}},"font-feature-values":{prelude:"<family-name>#",descriptors:null},import:{prelude:"[<string>|<url>] [layer|layer( <layer-name> )]? [supports( [<supports-condition>|<declaration>] )]? <media-query-list>?",descriptors:null},keyframes:{prelude:"<keyframes-name>",descriptors:null},layer:{prelude:"[<layer-name>#|<layer-name>?]",descriptors:null},media:{prelude:"<media-query-list>",descriptors:null},namespace:{prelude:"<namespace-prefix>? [<string>|<url>]",descriptors:null},page:{prelude:"<page-selector-list>",descriptors:{bleed:"auto|<length>",marks:"none|[crop||cross]","page-orientation":"upright|rotate-left|rotate-right",size:"<length>{1,2}|auto|[<page-size>||[portrait|landscape]]"}},"position-try":{prelude:"<dashed-ident>",descriptors:{top:"<'top'>",left:"<'left'>",bottom:"<'bottom'>",right:"<'right'>","inset-block-start":"<'inset-block-start'>","inset-block-end":"<'inset-block-end'>","inset-inline-start":"<'inset-inline-start'>","inset-inline-end":"<'inset-inline-end'>","inset-block":"<'inset-block'>","inset-inline":"<'inset-inline'>",inset:"<'inset'>","margin-top":"<'margin-top'>","margin-left":"<'margin-left'>","margin-bottom":"<'margin-bottom'>","margin-right":"<'margin-right'>","margin-block-start":"<'margin-block-start'>","margin-block-end":"<'margin-block-end'>","margin-inline-start":"<'margin-inline-start'>","margin-inline-end":"<'margin-inline-end'>",margin:"<'margin'>","margin-block":"<'margin-block'>","margin-inline":"<'margin-inline'>",width:"<'width'>",height:"<'height'>","min-width":"<'min-width'>","min-height":"<'min-height'>","max-width":"<'max-width'>","max-height":"<'max-height'>","block-size":"<'block-size'>","inline-size":"<'inline-size'>","min-block-size":"<'min-block-size'>","min-inline-size":"<'min-inline-size'>","max-block-size":"<'max-block-size'>","max-inline-size":"<'max-inline-size'>","align-self":"<'align-self'>|anchor-center","justify-self":"<'justify-self'>|anchor-center"}},property:{prelude:"<custom-property-name>",descriptors:{syntax:"<string>",inherits:"true|false","initial-value":"<declaration-value>?"}},scope:{prelude:"[( <scope-start> )]? [to ( <scope-end> )]?",descriptors:null},"starting-style":{prelude:null,descriptors:null},supports:{prelude:"<supports-condition>",descriptors:null},container:{prelude:"[<container-name>]? <container-condition>",descriptors:null},nest:{prelude:"<complex-selector-list>",descriptors:null}}};var Tt={};x(Tt,{AnPlusB:()=>tn,Atrule:()=>nn,AtrulePrelude:()=>sn,AttributeSelector:()=>cn,Block:()=>pn,Brackets:()=>mn,CDC:()=>dn,CDO:()=>bn,ClassSelector:()=>yn,Combinator:()=>wn,Comment:()=>Sn,Condition:()=>Tn,Declaration:()=>Ln,DeclarationList:()=>Pn,Dimension:()=>Dn,Feature:()=>On,FeatureFunction:()=>Rn,FeatureRange:()=>_n,Function:()=>jn,GeneralEnclosed:()=>Un,Hash:()=>Gn,IdSelector:()=>Qn,Identifier:()=>Kn,Layer:()=>$n,LayerList:()=>Jn,MediaQuery:()=>ti,MediaQueryList:()=>ni,NestingSelector:()=>oi,Nth:()=>ai,Number:()=>ci,Operator:()=>pi,Parentheses:()=>mi,Percentage:()=>di,PseudoClassSelector:()=>bi,PseudoElementSelector:()=>yi,Ratio:()=>wi,Raw:()=>Si,Rule:()=>Ti,Scope:()=>Li,Selector:()=>zi,SelectorList:()=>Ii,String:()=>Fi,StyleSheet:()=>Mi,SupportsDeclaration:()=>_i,TypeSelector:()=>qi,UnicodeRange:()=>Gi,Url:()=>Xi,Value:()=>Zi,WhiteSpace:()=>eo});var tn={};x(tn,{generate:()=>Vc,name:()=>Yc,parse:()=>en,structure:()=>Kc});var de=43,re=45,tr=110,Re=!0,Gc=!1;function rr(e,t){let r=this.tokenStart+e,n=this.charCodeAt(r);for((n===de||n===re)&&(t&&this.error("Number sign is not allowed"),r++);r<this.tokenEnd;r++)j(this.charCodeAt(r))||this.error("Integer is expected",r)}function rt(e){return rr.call(this,0,e)}function Le(e,t){if(!this.cmpChar(this.tokenStart+e,t)){let r="";switch(t){case tr:r="N is expected";break;case re:r="HyphenMinus is expected";break}this.error(r,this.tokenStart+e)}}function Jr(){let e=0,t=0,r=this.tokenType;for(;r===13||r===25;)r=this.lookupType(++e);if(r!==10)if(this.isDelim(de,e)||this.isDelim(re,e)){t=this.isDelim(de,e)?de:re;do r=this.lookupType(++e);while(r===13||r===25);r!==10&&(this.skip(e),rt.call(this,Re))}else return null;return e>0&&this.skip(e),t===0&&(r=this.charCodeAt(this.tokenStart),r!==de&&r!==re&&this.error("Number sign is expected")),rt.call(this,t!==0),t===re?"-"+this.consume(10):this.consume(10)}var Yc="AnPlusB",Kc={a:[String,null],b:[String,null]};function en(){let e=this.tokenStart,t=null,r=null;if(this.tokenType===10)rt.call(this,Gc),r=this.consume(10);else if(this.tokenType===1&&this.cmpChar(this.tokenStart,re))switch(t="-1",Le.call(this,1,tr),this.tokenEnd-this.tokenStart){case 2:this.next(),r=Jr.call(this);break;case 3:Le.call(this,2,re),this.next(),this.skipSC(),rt.call(this,Re),r="-"+this.consume(10);break;default:Le.call(this,2,re),rr.call(this,3,Re),this.next(),r=this.substrToCursor(e+2)}else if(this.tokenType===1||this.isDelim(de)&&this.lookupType(1)===1){let n=0;switch(t="1",this.isDelim(de)&&(n=1,this.next()),Le.call(this,0,tr),this.tokenEnd-this.tokenStart){case 1:this.next(),r=Jr.call(this);break;case 2:Le.call(this,1,re),this.next(),this.skipSC(),rt.call(this,Re),r="-"+this.consume(10);break;default:Le.call(this,1,re),rr.call(this,2,Re),this.next(),r=this.substrToCursor(e+n+1)}}else if(this.tokenType===12){let n=this.charCodeAt(this.tokenStart),i=n===de||n===re,o=this.tokenStart+i;for(;o<this.tokenEnd&&j(this.charCodeAt(o));o++);o===this.tokenStart+i&&this.error("Integer is expected",this.tokenStart+i),Le.call(this,o-this.tokenStart,tr),t=this.substring(e,o),o+1===this.tokenEnd?(this.next(),r=Jr.call(this)):(Le.call(this,o-this.tokenStart+1,re),o+2===this.tokenEnd?(this.next(),this.skipSC(),rt.call(this,Re),r="-"+this.consume(10)):(rr.call(this,o-this.tokenStart+2,Re),this.next(),r=this.substrToCursor(o+1)))}else this.error();return t!==null&&t.charCodeAt(0)===de&&(t=t.substr(1)),r!==null&&r.charCodeAt(0)===de&&(r=r.substr(1)),{type:"AnPlusB",loc:this.getLocation(e,this.tokenStart),a:t,b:r}}function Vc(e){if(e.a){let t=e.a==="+1"&&"n"||e.a==="1"&&"n"||e.a==="-1"&&"-n"||e.a+"n";if(e.b){let r=e.b[0]==="-"||e.b[0]==="+"?e.b:"+"+e.b;this.tokenize(t+r)}else this.tokenize(t)}else this.tokenize(e.b)}var nn={};x(nn,{generate:()=>Jc,name:()=>Xc,parse:()=>rn,structure:()=>Zc,walkContext:()=>$c});function Bs(){return this.Raw(this.consumeUntilLeftCurlyBracketOrSemicolon,!0)}function Qc(){for(let e=1,t;t=this.lookupType(e);e++){if(t===24)return!0;if(t===23||t===3)return!1}return!1}var Xc="Atrule",$c="atrule",Zc={name:String,prelude:["AtrulePrelude","Raw",null],block:["Block",null]};function rn(e=!1){let t=this.tokenStart,r,n,i=null,o=null;switch(this.eat(3),r=this.substrToCursor(t+1),n=r.toLowerCase(),this.skipSC(),this.eof===!1&&this.tokenType!==23&&this.tokenType!==17&&(this.parseAtrulePrelude?i=this.parseWithFallback(this.AtrulePrelude.bind(this,r,e),Bs):i=Bs.call(this,this.tokenIndex),this.skipSC()),this.tokenType){case 17:this.next();break;case 23:hasOwnProperty.call(this.atrule,n)&&typeof this.atrule[n].block=="function"?o=this.atrule[n].block.call(this,e):o=this.Block(Qc.call(this));break}return{type:"Atrule",loc:this.getLocation(t,this.tokenStart),name:r,prelude:i,block:o}}function Jc(e){this.token(3,"@"+e.name),e.prelude!==null&&this.node(e.prelude),e.block?this.node(e.block):this.token(17,";")}var sn={};x(sn,{generate:()=>nu,name:()=>eu,parse:()=>on,structure:()=>ru,walkContext:()=>tu});var eu="AtrulePrelude",tu="atrulePrelude",ru={children:[[]]};function on(e){let t=null;return e!==null&&(e=e.toLowerCase()),this.skipSC(),hasOwnProperty.call(this.atrule,e)&&typeof this.atrule[e].prelude=="function"?t=this.atrule[e].prelude.call(this):t=this.readSequence(this.scope.AtrulePrelude),this.skipSC(),this.eof!==!0&&this.tokenType!==23&&this.tokenType!==17&&this.error("Semicolon or block is expected"),{type:"AtrulePrelude",loc:this.getLocationFromList(t),children:t}}function nu(e){this.children(e)}var cn={};x(cn,{generate:()=>pu,name:()=>cu,parse:()=>ln,structure:()=>uu});var iu=36,_s=42,nr=61,ou=94,an=124,su=126;function au(){this.eof&&this.error("Unexpected end of input");let e=this.tokenStart,t=!1;return this.isDelim(_s)?(t=!0,this.next()):this.isDelim(an)||this.eat(1),this.isDelim(an)?this.charCodeAt(this.tokenStart+1)!==nr?(this.next(),this.eat(1)):t&&this.error("Identifier is expected",this.tokenEnd):t&&this.error("Vertical line is expected"),{type:"Identifier",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e)}}function lu(){let e=this.tokenStart,t=this.charCodeAt(e);return t!==nr&&t!==su&&t!==ou&&t!==iu&&t!==_s&&t!==an&&this.error("Attribute selector (=, ~=, ^=, $=, *=, |=) is expected"),this.next(),t!==nr&&(this.isDelim(nr)||this.error("Equal sign is expected"),this.next()),this.substrToCursor(e)}var cu="AttributeSelector",uu={name:"Identifier",matcher:[String,null],value:["String","Identifier",null],flags:[String,null]};function ln(){let e=this.tokenStart,t,r=null,n=null,i=null;return this.eat(19),this.skipSC(),t=au.call(this),this.skipSC(),this.tokenType!==20&&(this.tokenType!==1&&(r=lu.call(this),this.skipSC(),n=this.tokenType===5?this.String():this.Identifier(),this.skipSC()),this.tokenType===1&&(i=this.consume(1),this.skipSC())),this.eat(20),{type:"AttributeSelector",loc:this.getLocation(e,this.tokenStart),name:t,matcher:r,value:n,flags:i}}function pu(e){this.token(9,"["),this.node(e.name),e.matcher!==null&&(this.tokenize(e.matcher),this.node(e.value)),e.flags!==null&&this.token(1,e.flags),this.token(9,"]")}var pn={};x(pn,{generate:()=>bu,name:()=>fu,parse:()=>un,structure:()=>gu,walkContext:()=>du});var hu=38;function qs(){return this.Raw(null,!0)}function Ws(){return this.parseWithFallback(this.Rule,qs)}function js(){return this.Raw(this.consumeUntilSemicolonIncluded,!0)}function mu(){if(this.tokenType===17)return js.call(this,this.tokenIndex);let e=this.parseWithFallback(this.Declaration,js);return this.tokenType===17&&this.next(),e}var fu="Block",du="block",gu={children:[["Atrule","Rule","Declaration"]]};function un(e){let t=e?mu:Ws,r=this.tokenStart,n=this.createList();this.eat(23);e:for(;!this.eof;)switch(this.tokenType){case 24:break e;case 13:case 25:this.next();break;case 3:n.push(this.parseWithFallback(this.Atrule.bind(this,e),qs));break;default:e&&this.isDelim(hu)?n.push(Ws.call(this)):n.push(t.call(this))}return this.eof||this.eat(24),{type:"Block",loc:this.getLocation(r,this.tokenStart),children:n}}function bu(e){this.token(23,"{"),this.children(e,t=>{t.type==="Declaration"&&this.token(17,";")}),this.token(24,"}")}var mn={};x(mn,{generate:()=>ku,name:()=>xu,parse:()=>hn,structure:()=>yu});var xu="Brackets",yu={children:[[]]};function hn(e,t){let r=this.tokenStart,n=null;return this.eat(19),n=e.call(this,t),this.eof||this.eat(20),{type:"Brackets",loc:this.getLocation(r,this.tokenStart),children:n}}function ku(e){this.token(9,"["),this.children(e),this.token(9,"]")}var dn={};x(dn,{generate:()=>Su,name:()=>wu,parse:()=>fn,structure:()=>vu});var wu="CDC",vu=[];function fn(){let e=this.tokenStart;return this.eat(15),{type:"CDC",loc:this.getLocation(e,this.tokenStart)}}function Su(){this.token(15,"-->")}var bn={};x(bn,{generate:()=>Au,name:()=>Cu,parse:()=>gn,structure:()=>Tu});var Cu="CDO",Tu=[];function gn(){let e=this.tokenStart;return this.eat(14),{type:"CDO",loc:this.getLocation(e,this.tokenStart)}}function Au(){this.token(14,"<!--")}var yn={};x(yn,{generate:()=>Pu,name:()=>Eu,parse:()=>xn,structure:()=>zu});var Lu=46,Eu="ClassSelector",zu={name:String};function xn(){return this.eatDelim(Lu),{type:"ClassSelector",loc:this.getLocation(this.tokenStart-1,this.tokenEnd),name:this.consume(1)}}function Pu(e){this.token(9,"."),this.token(1,e.name)}var wn={};x(wn,{generate:()=>Ru,name:()=>Ou,parse:()=>kn,structure:()=>Fu});var Iu=43,Us=47,Du=62,Nu=126,Ou="Combinator",Fu={name:String};function kn(){let e=this.tokenStart,t;switch(this.tokenType){case 13:t=" ";break;case 9:switch(this.charCodeAt(this.tokenStart)){case Du:case Iu:case Nu:this.next();break;case Us:this.next(),this.eatIdent("deep"),this.eatDelim(Us);break;default:this.error("Combinator is expected")}t=this.substrToCursor(e);break}return{type:"Combinator",loc:this.getLocation(e,this.tokenStart),name:t}}function Ru(e){this.tokenize(e.name)}var Sn={};x(Sn,{generate:()=>ju,name:()=>_u,parse:()=>vn,structure:()=>Wu});var Mu=42,Bu=47,_u="Comment",Wu={value:String};function vn(){let e=this.tokenStart,t=this.tokenEnd;return this.eat(25),t-e+2>=2&&this.charCodeAt(t-2)===Mu&&this.charCodeAt(t-1)===Bu&&(t-=2),{type:"Comment",loc:this.getLocation(e,this.tokenStart),value:this.substring(e+2,t)}}function ju(e){this.token(25,"/*"+e.value+"*/")}var Tn={};x(Tn,{generate:()=>Yu,name:()=>Uu,parse:()=>Cn,structure:()=>Hu});var qu=new Set([16,22,0]),Uu="Condition",Hu={kind:String,children:[["Identifier","Feature","FeatureFunction","FeatureRange","SupportsDeclaration"]]};function Hs(e){return this.lookupTypeNonSC(1)===1&&qu.has(this.lookupTypeNonSC(2))?this.Feature(e):this.FeatureRange(e)}var Gu={media:Hs,container:Hs,supports(){return this.SupportsDeclaration()}};function Cn(e="media"){let t=this.createList();e:for(;!this.eof;)switch(this.tokenType){case 25:case 13:this.next();continue;case 1:t.push(this.Identifier());break;case 21:{let r=this.parseWithFallback(()=>Gu[e].call(this,e),()=>null);r||(r=this.parseWithFallback(()=>{this.eat(21);let n=this.Condition(e);return this.eat(22),n},()=>this.GeneralEnclosed(e))),t.push(r);break}case 2:{let r=this.parseWithFallback(()=>this.FeatureFunction(e),()=>null);r||(r=this.GeneralEnclosed(e)),t.push(r);break}default:break e}return t.isEmpty&&this.error("Condition is expected"),{type:"Condition",loc:this.getLocationFromList(t),kind:e,children:t}}function Yu(e){e.children.forEach(t=>{t.type==="Condition"?(this.token(21,"("),this.node(t),this.token(22,")")):this.node(t)})}var Ln={};x(Ln,{generate:()=>ip,name:()=>tp,parse:()=>An,structure:()=>np,walkContext:()=>rp});var Ys=33,Ku=35,Vu=36,Qu=38,Xu=42,$u=43,Gs=47;function Zu(){return this.Raw(this.consumeUntilExclamationMarkOrSemicolon,!0)}function Ju(){return this.Raw(this.consumeUntilExclamationMarkOrSemicolon,!1)}function ep(){let e=this.tokenIndex,t=this.Value();return t.type!=="Raw"&&this.eof===!1&&this.tokenType!==17&&this.isDelim(Ys)===!1&&this.isBalanceEdge(e)===!1&&this.error(),t}var tp="Declaration",rp="declaration",np={important:[Boolean,String],property:String,value:["Value","Raw"]};function An(){let e=this.tokenStart,t=this.tokenIndex,r=op.call(this),n=ht(r),i=n?this.parseCustomProperty:this.parseValue,o=n?Ju:Zu,a=!1,u;this.skipSC(),this.eat(16);let l=this.tokenIndex;if(n||this.skipSC(),i?u=this.parseWithFallback(ep,o):u=o.call(this,this.tokenIndex),n&&u.type==="Value"&&u.children.isEmpty){for(let s=l-this.tokenIndex;s<=0;s++)if(this.lookupType(s)===13){u.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}}return this.isDelim(Ys)&&(a=sp.call(this),this.skipSC()),this.eof===!1&&this.tokenType!==17&&this.isBalanceEdge(t)===!1&&this.error(),{type:"Declaration",loc:this.getLocation(e,this.tokenStart),important:a,property:r,value:u}}function ip(e){this.token(1,e.property),this.token(16,":"),this.node(e.value),e.important&&(this.token(9,"!"),this.token(1,e.important===!0?"important":e.important))}function op(){let e=this.tokenStart;if(this.tokenType===9)switch(this.charCodeAt(this.tokenStart)){case Xu:case Vu:case $u:case Ku:case Qu:this.next();break;case Gs:this.next(),this.isDelim(Gs)&&this.next();break}return this.tokenType===4?this.eat(4):this.eat(1),this.substrToCursor(e)}function sp(){this.eat(9),this.skipSC();let e=this.consume(1);return e==="important"?!0:e}var Pn={};x(Pn,{generate:()=>up,name:()=>lp,parse:()=>zn,structure:()=>cp});var ap=38;function En(){return this.Raw(this.consumeUntilSemicolonIncluded,!0)}var lp="DeclarationList",cp={children:[["Declaration","Atrule","Rule"]]};function zn(){let e=this.createList();for(;!this.eof;)switch(this.tokenType){case 13:case 25:case 17:this.next();break;case 3:e.push(this.parseWithFallback(this.Atrule.bind(this,!0),En));break;default:this.isDelim(ap)?e.push(this.parseWithFallback(this.Rule,En)):e.push(this.parseWithFallback(this.Declaration,En))}return{type:"DeclarationList",loc:this.getLocationFromList(e),children:e}}function up(e){this.children(e,t=>{t.type==="Declaration"&&this.token(17,";")})}var Dn={};x(Dn,{generate:()=>mp,name:()=>pp,parse:()=>In,structure:()=>hp});var pp="Dimension",hp={value:String,unit:String};function In(){let e=this.tokenStart,t=this.consumeNumber(12);return{type:"Dimension",loc:this.getLocation(e,this.tokenStart),value:t,unit:this.substring(e+t.length,this.tokenStart)}}function mp(e){this.token(12,e.value+e.unit)}var On={};x(On,{generate:()=>bp,name:()=>dp,parse:()=>Nn,structure:()=>gp});var fp=47,dp="Feature",gp={kind:String,name:String,value:["Identifier","Number","Dimension","Ratio","Function",null]};function Nn(e){let t=this.tokenStart,r,n=null;if(this.eat(21),this.skipSC(),r=this.consume(1),this.skipSC(),this.tokenType!==22){switch(this.eat(16),this.skipSC(),this.tokenType){case 10:this.lookupNonWSType(1)===9?n=this.Ratio():n=this.Number();break;case 12:n=this.Dimension();break;case 1:n=this.Identifier();break;case 2:n=this.parseWithFallback(()=>{let i=this.Function(this.readSequence,this.scope.Value);return this.skipSC(),this.isDelim(fp)&&this.error(),i},()=>this.Ratio());break;default:this.error("Number, dimension, ratio or identifier is expected")}this.skipSC()}return this.eof||this.eat(22),{type:"Feature",loc:this.getLocation(t,this.tokenStart),kind:e,name:r,value:n}}function bp(e){this.token(21,"("),this.token(1,e.name),e.value!==null&&(this.token(16,":"),this.node(e.value)),this.token(22,")")}var Rn={};x(Rn,{generate:()=>wp,name:()=>xp,parse:()=>Fn,structure:()=>yp});var xp="FeatureFunction",yp={kind:String,feature:String,value:["Declaration","Selector"]};function kp(e,t){let n=(this.features[e]||{})[t];return typeof n!="function"&&this.error(`Unknown feature ${t}()`),n}function Fn(e="unknown"){let t=this.tokenStart,r=this.consumeFunctionName(),n=kp.call(this,e,r.toLowerCase());this.skipSC();let i=this.parseWithFallback(()=>{let o=this.tokenIndex,a=n.call(this);return this.eof===!1&&this.isBalanceEdge(o)===!1&&this.error(),a},()=>this.Raw(null,!1));return this.eof||this.eat(22),{type:"FeatureFunction",loc:this.getLocation(t,this.tokenStart),kind:e,feature:r,value:i}}function wp(e){this.token(2,e.feature+"("),this.node(e.value),this.token(22,")")}var _n={};x(_n,{generate:()=>Ap,name:()=>Cp,parse:()=>Bn,structure:()=>Tp});var Ks=47,vp=60,Vs=61,Sp=62,Cp="FeatureRange",Tp={kind:String,left:["Identifier","Number","Dimension","Ratio","Function"],leftComparison:String,middle:["Identifier","Number","Dimension","Ratio","Function"],rightComparison:[String,null],right:["Identifier","Number","Dimension","Ratio","Function",null]};function Mn(){switch(this.skipSC(),this.tokenType){case 10:return this.isDelim(Ks,this.lookupOffsetNonSC(1))?this.Ratio():this.Number();case 12:return this.Dimension();case 1:return this.Identifier();case 2:return this.parseWithFallback(()=>{let e=this.Function(this.readSequence,this.scope.Value);return this.skipSC(),this.isDelim(Ks)&&this.error(),e},()=>this.Ratio());default:this.error("Number, dimension, ratio or identifier is expected")}}function Qs(e){if(this.skipSC(),this.isDelim(vp)||this.isDelim(Sp)){let t=this.source[this.tokenStart];return this.next(),this.isDelim(Vs)?(this.next(),t+"="):t}if(this.isDelim(Vs))return"=";this.error(`Expected ${e?'":", ':""}"<", ">", "=" or ")"`)}function Bn(e="unknown"){let t=this.tokenStart;this.skipSC(),this.eat(21);let r=Mn.call(this),n=Qs.call(this,r.type==="Identifier"),i=Mn.call(this),o=null,a=null;return this.lookupNonWSType(0)!==22&&(o=Qs.call(this),a=Mn.call(this)),this.skipSC(),this.eat(22),{type:"FeatureRange",loc:this.getLocation(t,this.tokenStart),kind:e,left:r,leftComparison:n,middle:i,rightComparison:o,right:a}}function Ap(e){this.token(21,"("),this.node(e.left),this.tokenize(e.leftComparison),this.node(e.middle),e.right&&(this.tokenize(e.rightComparison),this.node(e.right)),this.token(22,")")}var jn={};x(jn,{generate:()=>Pp,name:()=>Lp,parse:()=>Wn,structure:()=>zp,walkContext:()=>Ep});var Lp="Function",Ep="function",zp={name:String,children:[[]]};function Wn(e,t){let r=this.tokenStart,n=this.consumeFunctionName(),i=n.toLowerCase(),o;return o=t.hasOwnProperty(i)?t[i].call(this,t):e.call(this,t),this.eof||this.eat(22),{type:"Function",loc:this.getLocation(r,this.tokenStart),name:n,children:o}}function Pp(e){this.token(2,e.name+"("),this.children(e),this.token(22,")")}var Un={};x(Un,{generate:()=>Np,name:()=>Ip,parse:()=>qn,structure:()=>Dp});var Ip="GeneralEnclosed",Dp={kind:String,function:[String,null],children:[[]]};function qn(e){let t=this.tokenStart,r=null;this.tokenType===2?r=this.consumeFunctionName():this.eat(21);let n=this.parseWithFallback(()=>{let i=this.tokenIndex,o=this.readSequence(this.scope.Value);return this.eof===!1&&this.isBalanceEdge(i)===!1&&this.error(),o},()=>this.createSingleNodeList(this.Raw(null,!1)));return this.eof||this.eat(22),{type:"GeneralEnclosed",loc:this.getLocation(t,this.tokenStart),kind:e,function:r,children:n}}function Np(e){e.function?this.token(2,e.function+"("):this.token(21,"("),this.children(e),this.token(22,")")}var Gn={};x(Gn,{generate:()=>Mp,name:()=>Fp,parse:()=>Hn,structure:()=>Rp,xxx:()=>Op});var Op="XXX",Fp="Hash",Rp={value:String};function Hn(){let e=this.tokenStart;return this.eat(4),{type:"Hash",loc:this.getLocation(e,this.tokenStart),value:this.substrToCursor(e+1)}}function Mp(e){this.token(4,"#"+e.value)}var Kn={};x(Kn,{generate:()=>Wp,name:()=>Bp,parse:()=>Yn,structure:()=>_p});var Bp="Identifier",_p={name:String};function Yn(){return{type:"Identifier",loc:this.getLocation(this.tokenStart,this.tokenEnd),name:this.consume(1)}}function Wp(e){this.token(1,e.name)}var Qn={};x(Qn,{generate:()=>Up,name:()=>jp,parse:()=>Vn,structure:()=>qp});var jp="IdSelector",qp={name:String};function Vn(){let e=this.tokenStart;return this.eat(4),{type:"IdSelector",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e+1)}}function Up(e){this.token(9,"#"+e.name)}var $n={};x($n,{generate:()=>Kp,name:()=>Gp,parse:()=>Xn,structure:()=>Yp});var Hp=46,Gp="Layer",Yp={name:String};function Xn(){let e=this.tokenStart,t=this.consume(1);for(;this.isDelim(Hp);)this.eat(9),t+="."+this.consume(1);return{type:"Layer",loc:this.getLocation(e,this.tokenStart),name:t}}function Kp(e){this.tokenize(e.name)}var Jn={};x(Jn,{generate:()=>Xp,name:()=>Vp,parse:()=>Zn,structure:()=>Qp});var Vp="LayerList",Qp={children:[["Layer"]]};function Zn(){let e=this.createList();for(this.skipSC();!this.eof&&(e.push(this.Layer()),this.lookupTypeNonSC(0)===18);)this.skipSC(),this.next(),this.skipSC();return{type:"LayerList",loc:this.getLocationFromList(e),children:e}}function Xp(e){this.children(e,()=>this.token(18,","))}var ti={};x(ti,{generate:()=>Jp,name:()=>$p,parse:()=>ei,structure:()=>Zp});var $p="MediaQuery",Zp={modifier:[String,null],mediaType:[String,null],condition:["Condition",null]};function ei(){let e=this.tokenStart,t=null,r=null,n=null;if(this.skipSC(),this.tokenType===1&&this.lookupTypeNonSC(1)!==21){let i=this.consume(1),o=i.toLowerCase();switch(o==="not"||o==="only"?(this.skipSC(),t=o,r=this.consume(1)):r=i,this.lookupTypeNonSC(0)){case 1:{this.skipSC(),this.eatIdent("and"),n=this.Condition("media");break}case 23:case 17:case 18:case 0:break;default:this.error("Identifier or parenthesis is expected")}}else switch(this.tokenType){case 1:case 21:case 2:{n=this.Condition("media");break}case 23:case 17:case 0:break;default:this.error("Identifier or parenthesis is expected")}return{type:"MediaQuery",loc:this.getLocation(e,this.tokenStart),modifier:t,mediaType:r,condition:n}}function Jp(e){e.mediaType?(e.modifier&&this.token(1,e.modifier),this.token(1,e.mediaType),e.condition&&(this.token(1,"and"),this.node(e.condition))):e.condition&&this.node(e.condition)}var ni={};x(ni,{generate:()=>rh,name:()=>eh,parse:()=>ri,structure:()=>th});var eh="MediaQueryList",th={children:[["MediaQuery"]]};function ri(){let e=this.createList();for(this.skipSC();!this.eof&&(e.push(this.MediaQuery()),this.tokenType===18);)this.next();return{type:"MediaQueryList",loc:this.getLocationFromList(e),children:e}}function rh(e){this.children(e,()=>this.token(18,","))}var oi={};x(oi,{generate:()=>sh,name:()=>ih,parse:()=>ii,structure:()=>oh});var nh=38,ih="NestingSelector",oh={};function ii(){let e=this.tokenStart;return this.eatDelim(nh),{type:"NestingSelector",loc:this.getLocation(e,this.tokenStart)}}function sh(){this.token(9,"&")}var ai={};x(ai,{generate:()=>ch,name:()=>ah,parse:()=>si,structure:()=>lh});var ah="Nth",lh={nth:["AnPlusB","Identifier"],selector:["SelectorList",null]};function si(){this.skipSC();let e=this.tokenStart,t=e,r=null,n;return this.lookupValue(0,"odd")||this.lookupValue(0,"even")?n=this.Identifier():n=this.AnPlusB(),t=this.tokenStart,this.skipSC(),this.lookupValue(0,"of")&&(this.next(),r=this.SelectorList(),t=this.tokenStart),{type:"Nth",loc:this.getLocation(e,t),nth:n,selector:r}}function ch(e){this.node(e.nth),e.selector!==null&&(this.token(1,"of"),this.node(e.selector))}var ci={};x(ci,{generate:()=>hh,name:()=>uh,parse:()=>li,structure:()=>ph});var uh="Number",ph={value:String};function li(){return{type:"Number",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consume(10)}}function hh(e){this.token(10,e.value)}var pi={};x(pi,{generate:()=>dh,name:()=>mh,parse:()=>ui,structure:()=>fh});var mh="Operator",fh={value:String};function ui(){let e=this.tokenStart;return this.next(),{type:"Operator",loc:this.getLocation(e,this.tokenStart),value:this.substrToCursor(e)}}function dh(e){this.tokenize(e.value)}var mi={};x(mi,{generate:()=>xh,name:()=>gh,parse:()=>hi,structure:()=>bh});var gh="Parentheses",bh={children:[[]]};function hi(e,t){let r=this.tokenStart,n=null;return this.eat(21),n=e.call(this,t),this.eof||this.eat(22),{type:"Parentheses",loc:this.getLocation(r,this.tokenStart),children:n}}function xh(e){this.token(21,"("),this.children(e),this.token(22,")")}var di={};x(di,{generate:()=>wh,name:()=>yh,parse:()=>fi,structure:()=>kh});var yh="Percentage",kh={value:String};function fi(){return{type:"Percentage",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:this.consumeNumber(11)}}function wh(e){this.token(11,e.value+"%")}var bi={};x(bi,{generate:()=>Th,name:()=>vh,parse:()=>gi,structure:()=>Ch,walkContext:()=>Sh});var vh="PseudoClassSelector",Sh="function",Ch={name:String,children:[["Raw"],null]};function gi(){let e=this.tokenStart,t=null,r,n;return this.eat(16),this.tokenType===2?(r=this.consumeFunctionName(),n=r.toLowerCase(),this.lookupNonWSType(0)==22?t=this.createList():hasOwnProperty.call(this.pseudo,n)?(this.skipSC(),t=this.pseudo[n].call(this),this.skipSC()):(t=this.createList(),t.push(this.Raw(null,!1))),this.eat(22)):r=this.consume(1),{type:"PseudoClassSelector",loc:this.getLocation(e,this.tokenStart),name:r,children:t}}function Th(e){this.token(16,":"),e.children===null?this.token(1,e.name):(this.token(2,e.name+"("),this.children(e),this.token(22,")"))}var yi={};x(yi,{generate:()=>zh,name:()=>Ah,parse:()=>xi,structure:()=>Eh,walkContext:()=>Lh});var Ah="PseudoElementSelector",Lh="function",Eh={name:String,children:[["Raw"],null]};function xi(){let e=this.tokenStart,t=null,r,n;return this.eat(16),this.eat(16),this.tokenType===2?(r=this.consumeFunctionName(),n=r.toLowerCase(),this.lookupNonWSType(0)==22?t=this.createList():hasOwnProperty.call(this.pseudo,n)?(this.skipSC(),t=this.pseudo[n].call(this),this.skipSC()):(t=this.createList(),t.push(this.Raw(null,!1))),this.eat(22)):r=this.consume(1),{type:"PseudoElementSelector",loc:this.getLocation(e,this.tokenStart),name:r,children:t}}function zh(e){this.token(16,":"),this.token(16,":"),e.children===null?this.token(1,e.name):(this.token(2,e.name+"("),this.children(e),this.token(22,")"))}var wi={};x(wi,{generate:()=>Dh,name:()=>Ph,parse:()=>ki,structure:()=>Ih});var Xs=47;function $s(){switch(this.skipSC(),this.tokenType){case 10:return this.Number();case 2:return this.Function(this.readSequence,this.scope.Value);default:this.error("Number of function is expected")}}var Ph="Ratio",Ih={left:["Number","Function"],right:["Number","Function",null]};function ki(){let e=this.tokenStart,t=$s.call(this),r=null;return this.skipSC(),this.isDelim(Xs)&&(this.eatDelim(Xs),r=$s.call(this)),{type:"Ratio",loc:this.getLocation(e,this.tokenStart),left:t,right:r}}function Dh(e){this.node(e.left),this.token(9,"/"),e.right?this.node(e.right):this.node(10,1)}var Si={};x(Si,{generate:()=>Rh,name:()=>Oh,parse:()=>vi,structure:()=>Fh});function Nh(){return this.tokenIndex>0&&this.lookupType(-1)===13?this.tokenIndex>1?this.getTokenStart(this.tokenIndex-1):this.firstCharOffset:this.tokenStart}var Oh="Raw",Fh={value:String};function vi(e,t){let r=this.getTokenStart(this.tokenIndex),n;return this.skipUntilBalanced(this.tokenIndex,e||this.consumeUntilBalanceEnd),t&&this.tokenStart>r?n=Nh.call(this):n=this.tokenStart,{type:"Raw",loc:this.getLocation(r,n),value:this.substring(r,n)}}function Rh(e){this.tokenize(e.value)}var Ti={};x(Ti,{generate:()=>jh,name:()=>Bh,parse:()=>Ci,structure:()=>Wh,walkContext:()=>_h});function Zs(){return this.Raw(this.consumeUntilLeftCurlyBracket,!0)}function Mh(){let e=this.SelectorList();return e.type!=="Raw"&&this.eof===!1&&this.tokenType!==23&&this.error(),e}var Bh="Rule",_h="rule",Wh={prelude:["SelectorList","Raw"],block:["Block"]};function Ci(){let e=this.tokenIndex,t=this.tokenStart,r,n;return this.parseRulePrelude?r=this.parseWithFallback(Mh,Zs):r=Zs.call(this,e),n=this.Block(!0),{type:"Rule",loc:this.getLocation(t,this.tokenStart),prelude:r,block:n}}function jh(e){this.node(e.prelude),this.node(e.block)}var Li={};x(Li,{generate:()=>Hh,name:()=>qh,parse:()=>Ai,structure:()=>Uh});var qh="Scope",Uh={root:["SelectorList","Raw",null],limit:["SelectorList","Raw",null]};function Ai(){let e=null,t=null;this.skipSC();let r=this.tokenStart;return this.tokenType===21&&(this.next(),this.skipSC(),e=this.parseWithFallback(this.SelectorList,()=>this.Raw(!1,!0)),this.skipSC(),this.eat(22)),this.lookupNonWSType(0)===1&&(this.skipSC(),this.eatIdent("to"),this.skipSC(),this.eat(21),this.skipSC(),t=this.parseWithFallback(this.SelectorList,()=>this.Raw(!1,!0)),this.skipSC(),this.eat(22)),{type:"Scope",loc:this.getLocation(r,this.tokenStart),root:e,limit:t}}function Hh(e){e.root&&(this.token(21,"("),this.node(e.root),this.token(22,")")),e.limit&&(this.token(1,"to"),this.token(21,"("),this.node(e.limit),this.token(22,")"))}var zi={};x(zi,{generate:()=>Kh,name:()=>Gh,parse:()=>Ei,structure:()=>Yh});var Gh="Selector",Yh={children:[["TypeSelector","IdSelector","ClassSelector","AttributeSelector","PseudoClassSelector","PseudoElementSelector","Combinator"]]};function Ei(){let e=this.readSequence(this.scope.Selector);return this.getFirstListNode(e)===null&&this.error("Selector is expected"),{type:"Selector",loc:this.getLocationFromList(e),children:e}}function Kh(e){this.children(e)}var Ii={};x(Ii,{generate:()=>$h,name:()=>Vh,parse:()=>Pi,structure:()=>Xh,walkContext:()=>Qh});var Vh="SelectorList",Qh="selector",Xh={children:[["Selector","Raw"]]};function Pi(){let e=this.createList();for(;!this.eof;){if(e.push(this.Selector()),this.tokenType===18){this.next();continue}break}return{type:"SelectorList",loc:this.getLocationFromList(e),children:e}}function $h(e){this.children(e,()=>this.token(18,","))}var Fi={};x(Fi,{generate:()=>em,name:()=>Zh,parse:()=>Oi,structure:()=>Jh});var ir={};x(ir,{decode:()=>St,encode:()=>Ni});var Di=92,Js=34,ea=39;function St(e){let t=e.length,r=e.charCodeAt(0),n=r===Js||r===ea?1:0,i=n===1&&t>1&&e.charCodeAt(t-1)===r?t-2:t-1,o="";for(let a=n;a<=i;a++){let u=e.charCodeAt(a);if(u===Di){if(a===i){a!==t-1&&(o=e.substr(a+1));break}if(u=e.charCodeAt(++a),Z(Di,u)){let l=a-1,s=ae(e,l);a=s-1,o+=Ue(e.substring(l+1,s))}else u===13&&e.charCodeAt(a+1)===10&&a++}else o+=e[a]}return o}function Ni(e,t){let r=t?"'":'"',n=t?ea:Js,i="",o=!1;for(let a=0;a<e.length;a++){let u=e.charCodeAt(a);if(u===0){i+="\uFFFD";continue}if(u<=31||u===127){i+="\\"+u.toString(16),o=!0;continue}u===n||u===Di?(i+="\\"+e.charAt(a),o=!1):(o&&(te(u)||pe(u))&&(i+=" "),i+=e.charAt(a),o=!1)}return r+i+r}var Zh="String",Jh={value:String};function Oi(){return{type:"String",loc:this.getLocation(this.tokenStart,this.tokenEnd),value:St(this.consume(5))}}function em(e){this.token(5,Ni(e.value))}var Mi={};x(Mi,{generate:()=>om,name:()=>rm,parse:()=>Ri,structure:()=>im,walkContext:()=>nm});var tm=33;function ta(){return this.Raw(null,!1)}var rm="StyleSheet",nm="stylesheet",im={children:[["Comment","CDO","CDC","Atrule","Rule","Raw"]]};function Ri(){let e=this.tokenStart,t=this.createList(),r;for(;!this.eof;){switch(this.tokenType){case 13:this.next();continue;case 25:if(this.charCodeAt(this.tokenStart+2)!==tm){this.next();continue}r=this.Comment();break;case 14:r=this.CDO();break;case 15:r=this.CDC();break;case 3:r=this.parseWithFallback(this.Atrule,ta);break;default:r=this.parseWithFallback(this.Rule,ta)}t.push(r)}return{type:"StyleSheet",loc:this.getLocation(e,this.tokenStart),children:t}}function om(e){this.children(e)}var _i={};x(_i,{generate:()=>lm,name:()=>sm,parse:()=>Bi,structure:()=>am});var sm="SupportsDeclaration",am={declaration:"Declaration"};function Bi(){let e=this.tokenStart;this.eat(21),this.skipSC();let t=this.Declaration();return this.eof||this.eat(22),{type:"SupportsDeclaration",loc:this.getLocation(e,this.tokenStart),declaration:t}}function lm(e){this.token(21,"("),this.node(e.declaration),this.token(22,")")}var qi={};x(qi,{generate:()=>hm,name:()=>um,parse:()=>ji,structure:()=>pm});var cm=42,ra=124;function Wi(){this.tokenType!==1&&this.isDelim(cm)===!1&&this.error("Identifier or asterisk is expected"),this.next()}var um="TypeSelector",pm={name:String};function ji(){let e=this.tokenStart;return this.isDelim(ra)?(this.next(),Wi.call(this)):(Wi.call(this),this.isDelim(ra)&&(this.next(),Wi.call(this))),{type:"TypeSelector",loc:this.getLocation(e,this.tokenStart),name:this.substrToCursor(e)}}function hm(e){this.tokenize(e.name)}var Gi={};x(Gi,{generate:()=>bm,name:()=>dm,parse:()=>Hi,structure:()=>gm});var na=43,ia=45,Ui=63;function Ct(e,t){let r=0;for(let n=this.tokenStart+e;n<this.tokenEnd;n++){let i=this.charCodeAt(n);if(i===ia&&t&&r!==0)return Ct.call(this,e+r+1,!1),-1;te(i)||this.error(t&&r!==0?"Hyphen minus"+(r<6?" or hex digit":"")+" is expected":r<6?"Hex digit is expected":"Unexpected input",n),++r>6&&this.error("Too many hex digits",n)}return this.next(),r}function or(e){let t=0;for(;this.isDelim(Ui);)++t>e&&this.error("Too many question marks"),this.next()}function mm(e){this.charCodeAt(this.tokenStart)!==e&&this.error((e===na?"Plus sign":"Hyphen minus")+" is expected")}function fm(){let e=0;switch(this.tokenType){case 10:if(e=Ct.call(this,1,!0),this.isDelim(Ui)){or.call(this,6-e);break}if(this.tokenType===12||this.tokenType===10){mm.call(this,ia),Ct.call(this,1,!1);break}break;case 12:e=Ct.call(this,1,!0),e>0&&or.call(this,6-e);break;default:if(this.eatDelim(na),this.tokenType===1){e=Ct.call(this,0,!0),e>0&&or.call(this,6-e);break}if(this.isDelim(Ui)){this.next(),or.call(this,5);break}this.error("Hex digit or question mark is expected")}}var dm="UnicodeRange",gm={value:String};function Hi(){let e=this.tokenStart;return this.eatIdent("u"),fm.call(this),{type:"UnicodeRange",loc:this.getLocation(e,this.tokenStart),value:this.substrToCursor(e)}}function bm(e){this.tokenize(e.value)}var Xi={};x(Xi,{generate:()=>Cm,name:()=>vm,parse:()=>Qi,structure:()=>Sm});var sr={};x(sr,{decode:()=>Ki,encode:()=>Vi});var xm=32,Yi=92,ym=34,km=39,wm=40,oa=41;function Ki(e){let t=e.length,r=4,n=e.charCodeAt(t-1)===oa?t-2:t-1,i="";for(;r<n&&pe(e.charCodeAt(r));)r++;for(;r<n&&pe(e.charCodeAt(n));)n--;for(let o=r;o<=n;o++){let a=e.charCodeAt(o);if(a===Yi){if(o===n){o!==t-1&&(i=e.substr(o+1));break}if(a=e.charCodeAt(++o),Z(Yi,a)){let u=o-1,l=ae(e,u);o=l-1,i+=Ue(e.substring(u+1,l))}else a===13&&e.charCodeAt(o+1)===10&&o++}else i+=e[o]}return i}function Vi(e){let t="",r=!1;for(let n=0;n<e.length;n++){let i=e.charCodeAt(n);if(i===0){t+="\uFFFD";continue}if(i<=31||i===127){t+="\\"+i.toString(16),r=!0;continue}i===xm||i===Yi||i===ym||i===km||i===wm||i===oa?(t+="\\"+e.charAt(n),r=!1):(r&&te(i)&&(t+=" "),t+=e.charAt(n),r=!1)}return"url("+t+")"}var vm="Url",Sm={value:String};function Qi(){let e=this.tokenStart,t;switch(this.tokenType){case 7:t=Ki(this.consume(7));break;case 2:this.cmpStr(this.tokenStart,this.tokenEnd,"url(")||this.error("Function name must be `url`"),this.eat(2),this.skipSC(),t=St(this.consume(5)),this.skipSC(),this.eof||this.eat(22);break;default:this.error("Url or Function is expected")}return{type:"Url",loc:this.getLocation(e,this.tokenStart),value:t}}function Cm(e){this.token(7,Vi(e.value))}var Zi={};x(Zi,{generate:()=>Lm,name:()=>Tm,parse:()=>$i,structure:()=>Am});var Tm="Value",Am={children:[[]]};function $i(){let e=this.tokenStart,t=this.readSequence(this.scope.Value);return{type:"Value",loc:this.getLocation(e,this.tokenStart),children:t}}function Lm(e){this.children(e)}var eo={};x(eo,{generate:()=>Im,name:()=>zm,parse:()=>Ji,structure:()=>Pm});var Em=Object.freeze({type:"WhiteSpace",loc:null,value:" "}),zm="WhiteSpace",Pm={value:String};function Ji(){return this.eat(13),Em}function Im(e){this.token(13,e.value)}var sa={generic:!0,cssWideKeywords:$e,...Ms,node:Tt};var to={};x(to,{AtrulePrelude:()=>la,Selector:()=>ua,Value:()=>fa});var Dm=35,Nm=42,aa=43,Om=45,Fm=47,Rm=117;function At(e){switch(this.tokenType){case 4:return this.Hash();case 18:return this.Operator();case 21:return this.Parentheses(this.readSequence,e.recognizer);case 19:return this.Brackets(this.readSequence,e.recognizer);case 5:return this.String();case 12:return this.Dimension();case 11:return this.Percentage();case 10:return this.Number();case 2:return this.cmpStr(this.tokenStart,this.tokenEnd,"url(")?this.Url():this.Function(this.readSequence,e.recognizer);case 7:return this.Url();case 1:return this.cmpChar(this.tokenStart,Rm)&&this.cmpChar(this.tokenStart+1,aa)?this.UnicodeRange():this.Identifier();case 9:{let t=this.charCodeAt(this.tokenStart);if(t===Fm||t===Nm||t===aa||t===Om)return this.Operator();t===Dm&&this.error("Hex or identifier is expected",this.tokenStart+1);break}}}var la={getNode:At};var Mm=35,Bm=38,_m=42,Wm=43,jm=47,ca=46,qm=62,Um=124,Hm=126;function Gm(e,t){t.last!==null&&t.last.type!=="Combinator"&&e!==null&&e.type!=="Combinator"&&t.push({type:"Combinator",loc:null,name:" "})}function Ym(){switch(this.tokenType){case 19:return this.AttributeSelector();case 4:return this.IdSelector();case 16:return this.lookupType(1)===16?this.PseudoElementSelector():this.PseudoClassSelector();case 1:return this.TypeSelector();case 10:case 11:return this.Percentage();case 12:this.charCodeAt(this.tokenStart)===ca&&this.error("Identifier is expected",this.tokenStart+1);break;case 9:{switch(this.charCodeAt(this.tokenStart)){case Wm:case qm:case Hm:case jm:return this.Combinator();case ca:return this.ClassSelector();case _m:case Um:return this.TypeSelector();case Mm:return this.IdSelector();case Bm:return this.NestingSelector()}break}}}var ua={onWhiteSpace:Gm,getNode:Ym};function pa(){return this.createSingleNodeList(this.Raw(null,!1))}function ha(){let e=this.createList();if(this.skipSC(),e.push(this.Identifier()),this.skipSC(),this.tokenType===18){e.push(this.Operator());let t=this.tokenIndex,r=this.parseCustomProperty?this.Value(null):this.Raw(this.consumeUntilExclamationMarkOrSemicolon,!1);if(r.type==="Value"&&r.children.isEmpty){for(let n=t-this.tokenIndex;n<=0;n++)if(this.lookupType(n)===13){r.children.appendData({type:"WhiteSpace",loc:null,value:" "});break}}e.push(r)}return e}function ma(e){return e!==null&&e.type==="Operator"&&(e.value[e.value.length-1]==="-"||e.value[e.value.length-1]==="+")}var fa={getNode:At,onWhiteSpace(e,t){ma(e)&&(e.value=" "+e.value),ma(t.last)&&(t.last.value+=" ")},expression:pa,var:ha};var Km=new Set(["none","and","not","or"]),da={parse:{prelude(){let e=this.createList();if(this.tokenType===1){let t=this.substring(this.tokenStart,this.tokenEnd);Km.has(t.toLowerCase())||e.push(this.Identifier())}return e.push(this.Condition("container")),e},block(e=!1){return this.Block(e)}}};var ga={parse:{prelude:null,block(){return this.Block(!0)}}};function ro(e,t){return this.parseWithFallback(()=>{try{return e.call(this)}finally{this.skipSC(),this.lookupNonWSType(0)!==22&&this.error()}},t||(()=>this.Raw(null,!0)))}var ba={layer(){this.skipSC();let e=this.createList(),t=ro.call(this,this.Layer);return(t.type!=="Raw"||t.value!=="")&&e.push(t),e},supports(){this.skipSC();let e=this.createList(),t=ro.call(this,this.Declaration,()=>ro.call(this,()=>this.Condition("supports")));return(t.type!=="Raw"||t.value!=="")&&e.push(t),e}},xa={parse:{prelude(){let e=this.createList();switch(this.tokenType){case 5:e.push(this.String());break;case 7:case 2:e.push(this.Url());break;default:this.error("String or url() is expected")}return this.skipSC(),this.tokenType===1&&this.cmpStr(this.tokenStart,this.tokenEnd,"layer")?e.push(this.Identifier()):this.tokenType===2&&this.cmpStr(this.tokenStart,this.tokenEnd,"layer(")&&e.push(this.Function(null,ba)),this.skipSC(),this.tokenType===2&&this.cmpStr(this.tokenStart,this.tokenEnd,"supports(")&&e.push(this.Function(null,ba)),(this.lookupNonWSType(0)===1||this.lookupNonWSType(0)===21)&&e.push(this.MediaQueryList()),e},block:null}};var ya={parse:{prelude(){return this.createSingleNodeList(this.LayerList())},block(){return this.Block(!1)}}};var ka={parse:{prelude(){return this.createSingleNodeList(this.MediaQueryList())},block(e=!1){return this.Block(e)}}};var wa={parse:{prelude(){return this.createSingleNodeList(this.SelectorList())},block(){return this.Block(!0)}}};var va={parse:{prelude(){return this.createSingleNodeList(this.SelectorList())},block(){return this.Block(!0)}}};var Sa={parse:{prelude(){return this.createSingleNodeList(this.Scope())},block(e=!1){return this.Block(e)}}};var Ca={parse:{prelude:null,block(e=!1){return this.Block(e)}}};var Ta={parse:{prelude(){return this.createSingleNodeList(this.Condition("supports"))},block(e=!1){return this.Block(e)}}};var Aa={container:da,"font-face":ga,import:xa,layer:ya,media:ka,nest:wa,page:va,scope:Sa,"starting-style":Ca,supports:Ta};function La(){let e=this.createList();this.skipSC();e:for(;!this.eof;){switch(this.tokenType){case 1:e.push(this.Identifier());break;case 5:e.push(this.String());break;case 18:e.push(this.Operator());break;case 22:break e;default:this.error("Identifier, string or comma is expected")}this.skipSC()}return e}var Me={parse(){return this.createSingleNodeList(this.SelectorList())}},no={parse(){return this.createSingleNodeList(this.Selector())}},Vm={parse(){return this.createSingleNodeList(this.Identifier())}},Qm={parse:La},ar={parse(){return this.createSingleNodeList(this.Nth())}},Ea={dir:Vm,has:Me,lang:Qm,matches:Me,is:Me,"-moz-any":Me,"-webkit-any":Me,where:Me,not:Me,"nth-child":ar,"nth-last-child":ar,"nth-last-of-type":ar,"nth-of-type":ar,slotted:no,host:no,"host-context":no};var io={};x(io,{AnPlusB:()=>en,Atrule:()=>rn,AtrulePrelude:()=>on,AttributeSelector:()=>ln,Block:()=>un,Brackets:()=>hn,CDC:()=>fn,CDO:()=>gn,ClassSelector:()=>xn,Combinator:()=>kn,Comment:()=>vn,Condition:()=>Cn,Declaration:()=>An,DeclarationList:()=>zn,Dimension:()=>In,Feature:()=>Nn,FeatureFunction:()=>Fn,FeatureRange:()=>Bn,Function:()=>Wn,GeneralEnclosed:()=>qn,Hash:()=>Hn,IdSelector:()=>Vn,Identifier:()=>Yn,Layer:()=>Xn,LayerList:()=>Zn,MediaQuery:()=>ei,MediaQueryList:()=>ri,NestingSelector:()=>ii,Nth:()=>si,Number:()=>li,Operator:()=>ui,Parentheses:()=>hi,Percentage:()=>fi,PseudoClassSelector:()=>gi,PseudoElementSelector:()=>xi,Ratio:()=>ki,Raw:()=>vi,Rule:()=>Ci,Scope:()=>Ai,Selector:()=>Ei,SelectorList:()=>Pi,String:()=>Oi,StyleSheet:()=>Ri,SupportsDeclaration:()=>Bi,TypeSelector:()=>ji,UnicodeRange:()=>Hi,Url:()=>Qi,Value:()=>$i,WhiteSpace:()=>Ji});var za={parseContext:{default:"StyleSheet",stylesheet:"StyleSheet",atrule:"Atrule",atrulePrelude(e){return this.AtrulePrelude(e.atrule?String(e.atrule):null)},mediaQueryList:"MediaQueryList",mediaQuery:"MediaQuery",condition(e){return this.Condition(e.kind)},rule:"Rule",selectorList:"SelectorList",selector:"Selector",block(){return this.Block(!0)},declarationList:"DeclarationList",declaration:"Declaration",value:"Value"},features:{supports:{selector(){return this.Selector()}},container:{style(){return this.Declaration()}}},scope:to,atrule:Aa,pseudo:Ea,node:io};var Pa={node:Tt};var Ia=er({...sa,...za,...Pa});var Xm="3.0.1";function lr(e){let t={};for(let r of Object.keys(e)){let n=e[r];n&&(Array.isArray(n)||n instanceof X?n=n.map(lr):n.constructor===Object&&(n=lr(n))),t[r]=n}return t}var oo={};x(oo,{decode:()=>$m,encode:()=>Zm});var Da=92;function $m(e){let t=e.length-1,r="";for(let n=0;n<e.length;n++){let i=e.charCodeAt(n);if(i===Da){if(n===t)break;if(i=e.charCodeAt(++n),Z(Da,i)){let o=n-1,a=ae(e,o);n=a-1,r+=Ue(e.substring(o+1,a))}else i===13&&e.charCodeAt(n+1)===10&&n++}else r+=e[n]}return r}function Zm(e){let t="";if(e.length===1&&e.charCodeAt(0)===45)return"\\-";for(let r=0;r<e.length;r++){let n=e.charCodeAt(r);if(n===0){t+="\uFFFD";continue}if(n<=31||n===127||n>=48&&n<=57&&(r===0||r===1&&e.charCodeAt(0)===45)){t+="\\"+n.toString(16)+" ";continue}We(n)?t+=e.charAt(r):t+="\\"+e.charAt(r)}return t}var{tokenize:Jm,parse:ef,generate:tf,lexer:rf,createLexer:nf,walk:of,find:sf,findLast:af,findAll:lf,toPlainObject:cf,fromPlainObject:uf,fork:pf}=Ia;return _a(hf);})();

'use strict';

const container = require('./container.cjs');
const fontFace = require('./font-face.cjs');
const _import = require('./import.cjs');
const layer = require('./layer.cjs');
const media = require('./media.cjs');
const nest = require('./nest.cjs');
const page = require('./page.cjs');
const scope = require('./scope.cjs');
const startingStyle = require('./starting-style.cjs');
const supports = require('./supports.cjs');

const atrule = {
    container,
    'font-face': fontFace,
    import: _import,
    layer,
    media,
    nest,
    page,
    scope,
    'starting-style': startingStyle,
    supports
};

module.exports = atrule;

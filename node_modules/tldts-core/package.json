{"name": "tldts-core", "version": "7.0.15", "description": "tldts core primitives (internal module)", "author": {"name": "<PERSON><PERSON><PERSON>"}, "contributors": ["<PERSON> <alexe<PERSON><PERSON>ahoodot<PERSON>@gmail.com>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON><PERSON><PERSON> <<EMAIL>>"], "publishConfig": {"access": "public"}, "license": "MIT", "homepage": "https://github.com/remusao/tldts#readme", "bugs": {"url": "https://github.com/remusao/tldts/issues"}, "repository": {"type": "git", "url": "git+ssh://**************/remusao/tldts.git"}, "main": "dist/cjs/index.js", "module": "dist/es6/index.js", "types": "dist/types/index.d.ts", "files": ["dist", "src", "index.ts"], "scripts": {"clean": "<PERSON><PERSON><PERSON> dist", "build": "tsc --build ./tsconfig.json", "bundle": "tsc --build ./tsconfig.bundle.json && rollup --config ./rollup.config.ts --configPlugin typescript", "prepack": "yarn run bundle", "test": "nyc mocha --config ../../.mocharc.cjs"}, "devDependencies": {"@rollup/plugin-node-resolve": "^16.0.0", "@rollup/plugin-typescript": "^12.1.0", "@types/chai": "^4.2.18", "@types/mocha": "^10.0.0", "@types/node": "^24.3.1", "chai": "^4.4.1", "mocha": "^11.0.1", "nyc": "^17.0.0", "rimraf": "^5.0.1", "rollup": "^4.1.0", "rollup-plugin-sourcemaps": "^0.6.1", "typescript": "^5.0.4"}, "gitHead": "9bd8795ac141bc4ed53d007562b7de3cfa9c84f8"}
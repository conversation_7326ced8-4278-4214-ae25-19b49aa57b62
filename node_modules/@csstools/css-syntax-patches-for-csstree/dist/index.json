{"next": {"atrules": {"color-profile": {"descriptors": {"components": "<ident>#", "rendering-intent": "relative-colorimetric | absolute-colorimetric | perceptual | saturation", "src": "<url>"}}, "counter-style": {"descriptors": {"additive-symbols": "[ <integer [0,∞]> && <symbol> ]#", "pad": "<integer [0,∞]> && <symbol>"}}, "font-face": {"descriptors": {"ascent-override": "[ normal | <percentage [0,∞]> ]{1,2}", "descent-override": "[ normal | <percentage [0,∞]> ]{1,2}", "font-language-override": "normal | <string>", "font-named-instance": "auto | <string>", "font-size": "auto | [ <number> ]{1,2}", "font-style": "auto | normal | italic | left | right | oblique [ <angle [-90deg,90deg]>{1,2} ]?", "font-weight": "| auto", "font-width": "auto | <'font-width'>{1,2}", "line-gap-override": "[ normal | <percentage [0,∞]> ]{1,2}", "size-adjust": "<percentage [0,∞]>", "src": "<font-src-list>", "subscript-position-override": "[ normal | from-font | <percentage> ]{1,2}", "subscript-size-override": "[ normal | from-font | <percentage [0,∞]> ]{1,2}", "superscript-position-override": "[ normal | from-font | <percentage> ]{1,2}", "superscript-size-override": "[ normal | from-font | <percentage [0,∞]> ]{1,2}", "unicode-range": "<unicode-range-token>#"}}, "font-feature-values": {"descriptors": {"@annotation": "@annotation { <declaration-list> }", "@character-variant": "@character-variant { <declaration-list> }", "@historical-forms": "@historical-forms { <declaration-list> }", "@ornaments": "@ornaments { <declaration-list> }", "@styleset": "@styleset { <declaration-list> }", "@stylistic": "@stylistic { <declaration-list> }", "@swash": "@swash { <declaration-list> }", "font-display": "auto | block | swap | fallback | optional"}}, "font-palette-values": {"descriptors": {"override-colors": "[ <integer [0,∞]> <color> ]#"}}, "function": {"descriptors": {"result": "<declaration-value>?"}}, "page": {"descriptors": {"size": "<length [0,∞]>{1,2} | auto | [ <page-size> || [ portrait | landscape ] ]"}}, "view-transition": {"descriptors": {"navigation": "auto | none", "types": "none | <custom-ident>+"}}}, "properties": {"align-items": "| anchor-center", "align-self": "| anchor-center", "alignment-baseline": "| text-bottom | text-top", "animation-duration": "[ auto | <time [0s,∞]> ]#", "animation-trigger": "<single-animation-trigger>#", "animation-trigger-behavior": "<single-animation-trigger-behavior>#", "animation-trigger-exit-range": "[ <'animation-trigger-exit-range-start'> <'animation-trigger-exit-range-end'>? ]#", "animation-trigger-exit-range-end": "[ auto | normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]#", "animation-trigger-exit-range-start": "[ auto | normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]#", "animation-trigger-range": "[ <'animation-trigger-range-start'> <'animation-trigger-range-end'>? ]#", "animation-trigger-range-end": "[ normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]#", "animation-trigger-range-start": "[ normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]#", "animation-trigger-timeline": "<single-animation-timeline>#", "appearance": "none | auto | base | <compat-auto> | <compat-special> | base", "backdrop-filter": "none | <filter-value-list>", "background": "<bg-layer>#? , <final-bg-layer>", "background-blend-mode": "<'mix-blend-mode'>#", "background-origin": "<visual-box>#", "background-position-block": "[ center | [ [ start | end ]? <length-percentage>? ]! ]#", "background-position-inline": "[ center | [ [ start | end ]? <length-percentage>? ]! ]#", "background-repeat-block": "<repetition>#", "background-repeat-inline": "<repetition>#", "background-repeat-x": "<repetition>#", "background-repeat-y": "<repetition>#", "baseline-shift": "<length-percentage> | sub | super | top | center | bottom", "baseline-source": "auto | first | last", "block-ellipsis": "none | auto | <string>", "block-step": "<'block-step-size'> || <'block-step-insert'> || <'block-step-align'> || <'block-step-round'>", "block-step-align": "auto | center | start | end", "block-step-insert": "margin-box | padding-box | content-box", "block-step-round": "up | down | nearest", "block-step-size": "none | <length [0,∞]>", "bookmark-label": "<content-list>", "bookmark-level": "none | <integer [1,∞]>", "bookmark-state": "open | closed", "border-block": "<'border-block-start'>", "border-block-end": "<line-width> || <line-style> || <color>", "border-block-end-color": "<color> | <image-1D>", "border-block-end-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-block-end-style": "<line-style>", "border-block-end-width": "<line-width>", "border-block-start": "<line-width> || <line-style> || <color>", "border-block-start-color": "<color> | <image-1D>", "border-block-start-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-block-start-style": "<line-style>", "border-block-start-width": "<line-width>", "border-block-style": "<'border-top-style'>{1,2}", "border-block-width": "<'border-top-width'>{1,2}", "border-bottom-color": "<color> | <image-1D>", "border-bottom-left-radius": "<length-percentage [0,∞]>{1,2}", "border-bottom-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-bottom-right-radius": "<length-percentage [0,∞]>{1,2}", "border-boundary": "none | parent | display", "border-clip": "normal | [ <length-percentage [0,∞]> | <flex> ]+", "border-clip-bottom": "normal | [ <length-percentage [0,∞]> | <flex> ]+", "border-clip-left": "normal | [ <length-percentage [0,∞]> | <flex> ]+", "border-clip-right": "normal | [ <length-percentage [0,∞]> | <flex> ]+", "border-clip-top": "normal | [ <length-percentage [0,∞]> | <flex> ]+", "border-color": "[ <color> | <image-1D> ]{1,4}", "border-end-end-radius": "<length-percentage [0,∞]>{1,2}", "border-end-start-radius": "<length-percentage [0,∞]>{1,2}", "border-image-outset": "[ <length [0,∞]> | <number [0,∞]> ]{1,4}", "border-image-slice": "[ <number [0,∞]> | <percentage [0,∞]> ]{1,4} && fill?", "border-image-width": "[ <length-percentage [0,∞]> | <number [0,∞]> | auto ]{1,4}", "border-inline": "<'border-block-start'>", "border-inline-end": "<line-width> || <line-style> || <color>", "border-inline-end-color": "<color> | <image-1D>", "border-inline-end-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-inline-end-style": "<line-style>", "border-inline-end-width": "<line-width>", "border-inline-start": "<line-width> || <line-style> || <color>", "border-inline-start-color": "<color> | <image-1D>", "border-inline-start-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-inline-start-style": "<line-style>", "border-inline-start-width": "<line-width>", "border-inline-style": "<'border-top-style'>{1,2}", "border-inline-width": "<'border-top-width'>{1,2}", "border-left-color": "<color> | <image-1D>", "border-left-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-limit": "all | [ sides | corners ] <length-percentage [0,∞]>? | [ top | right | bottom | left ] <length-percentage [0,∞]>", "border-radius": "<length-percentage [0,∞]>{1,4} [ / <length-percentage [0,∞]>{1,4} ]?", "border-right-color": "<color> | <image-1D>", "border-right-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-shape": "none | [ <basic-shape> <geometry-box>? ]{1,2}", "border-spacing": "<length>{1,2}", "border-start-end-radius": "<length-percentage [0,∞]>{1,2}", "border-start-start-radius": "<length-percentage [0,∞]>{1,2}", "border-top-color": "<color> | <image-1D>", "border-top-left-radius": "<length-percentage [0,∞]>{1,2}", "border-top-radius": "<length-percentage [0,∞]>{1,2} [ / <length-percentage [0,∞]>{1,2} ]?", "border-top-right-radius": "<length-percentage [0,∞]>{1,2}", "bottom": "| <anchor()> | <anchor-size()>", "box-shadow": "<spread-shadow>#", "box-shadow-blur": "<length [0,∞]>#", "box-shadow-color": "<color>#", "box-shadow-offset": "[ none | <length>{2} ]#", "box-shadow-position": "[ outset | inset ]#", "box-shadow-spread": "<length>#", "box-snap": "none | block-start | block-end | center | baseline | last-baseline", "caret": "<'caret-color'> || <'caret-animation'> || <'caret-shape'>", "caret-animation": "auto | manual", "clear": "| block-start | block-end | top | bottom | both-inline | both-block", "color-adjust": "<'print-color-adjust'>", "color-interpolation": "auto | sRGB | linearRGB", "column-count": "auto | <integer [1,∞]>", "column-fill": "| balance-all", "column-gap": "normal | <length-percentage [0,∞]>", "column-height": "auto | <length [0,∞]>", "column-rule": "<gap-rule-list> | <gap-auto-rule-list>", "column-rule-break": "none | spanning-item | intersection", "column-rule-color": "<line-color-list> | <auto-line-color-list>", "column-rule-outset": "<length-percentage>", "column-rule-style": "<line-style-list> | <auto-line-style-list>", "column-rule-width": "<line-width-list> | <auto-line-width-list>", "column-span": "| <integer [1,∞]> | auto", "column-width": "auto | <length [0,∞]> | min-content | max-content | fit-content( <length-percentage> )", "column-wrap": "auto | nowrap | wrap", "columns": "<'column-width'> || <'column-count'> [ / <'column-height'> ]?", "contain": "none | strict | content | [ [ size | inline-size ] || layout || style || paint ]", "contain-intrinsic-block-size": "auto? [ none | <length [0,∞]> ]", "contain-intrinsic-height": "auto? [ none | <length [0,∞]> ]", "contain-intrinsic-inline-size": "auto? [ none | <length [0,∞]> ]", "contain-intrinsic-width": "auto? [ none | <length [0,∞]> ]", "container-type": "normal | [ [ size | inline-size ] || scroll-state ]", "content": "normal | none | [ <content-replacement> | <content-list> ] [ / [ <string> | <counter> | <attr()> ]+ ]? | <element()>", "continue": "auto | discard | collapse | -webkit-legacy | overflow | paginate | fragments", "copy-into": "none | [ [ <custom-ident> <content-level> ] [, <custom-ident> <content-level> ]* ]?", "corner-block-end-shape": "<corner-shape-value>", "corner-block-start-shape": "<corner-shape-value>", "corner-bottom-left-shape": "<corner-shape-value>", "corner-bottom-right-shape": "<corner-shape-value>", "corner-bottom-shape": "<corner-shape-value>", "corner-end-end-shape": "<corner-shape-value>", "corner-end-start-shape": "<corner-shape-value>", "corner-inline-end-shape": "<corner-shape-value>", "corner-inline-start-shape": "<corner-shape-value>", "corner-left-shape": "<corner-shape-value>", "corner-right-shape": "<corner-shape-value>", "corner-shape": "<corner-shape-value>{1,4}", "corner-start-end-shape": "<corner-shape-value>", "corner-start-start-shape": "<corner-shape-value>", "corner-top-left-shape": "<corner-shape-value>", "corner-top-right-shape": "<corner-shape-value>", "corner-top-shape": "<corner-shape-value>", "cursor": "[ [ <url> | <url-set()> ] [ <x> <y> ]? ]#? [ auto | default | none | context-menu | help | pointer | progress | wait | cell | crosshair | text | vertical-text | alias | copy | move | no-drop | not-allowed | grab | grabbing | e-resize | n-resize | ne-resize | nw-resize | s-resize | se-resize | sw-resize | w-resize | ew-resize | ns-resize | nesw-resize | nwse-resize | col-resize | row-resize | all-scroll | zoom-in | zoom-out | -webkit-grab | -webkit-grabbing | -webkit-zoom-in | -webkit-zoom-out | -moz-grab | -moz-grabbing | -moz-zoom-in | -moz-zoom-out ]", "cx": "<length-percentage>", "cy": "<length-percentage>", "display": "[ <display-outside> || <display-inside> ] | <display-listitem> | <display-internal> | <display-box> | <display-legacy> | <display-outside> | <-non-standard-display> || [ <display-inside> | math ]", "dominant-baseline": "auto | text-bottom | text-top | use-script | no-change | reset-size | ideographic | alphabetic | hanging | mathematical | central | middle | text-after-edge | text-before-edge", "dynamic-range-limit": "standard | no-limit | constrained | <dynamic-range-limit-mix()>", "fill-break": "bounding-box | slice | clone", "fill-color": "<color>", "fill-image": "<paint>#", "fill-opacity": "<'opacity'>", "fill-origin": "match-parent | fill-box | stroke-box | content-box | padding-box | border-box", "fill-position": "<position>#", "fill-repeat": "<repeat-style>#", "fill-size": "<bg-size>#", "filter": "none | <filter-value-list> | <-ms-filter-function-list>", "flex-grow": "<number [0,∞]>", "flex-shrink": "<number [0,∞]>", "float": "| block-start | block-end | snap-block | snap-block( <length> , [ start | end | near ]? ) | snap-inline | snap-inline( <length> , [ left | right | near ]? ) | top | bottom | footnote", "float-defer": "<integer> | last | none", "float-offset": "<length-percentage>", "float-reference": "inline | column | region | page", "flood-color": "<color>", "flood-opacity": "<'opacity'>", "flow-from": "<ident> | none", "flow-into": "none | <ident> [ element | content ]?", "font-palette": "| <palette-mix()>", "font-size": "<absolute-size> | <relative-size> | <length-percentage [0,∞]> | math", "font-size-adjust": "none | [ ex-height | cap-height | ch-width | ic-width | ic-height ]? [ from-font | <number [0,∞]> ]", "font-style": "normal | italic | left | right | oblique <angle [-90deg,90deg]>?", "font-synthesis-style": "auto | none | oblique-only", "font-variant": "normal | none | [ [ <common-lig-values> || <discretionary-lig-values> || <historical-lig-values> || <contextual-alt-values> ] || [ small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps ] || [ stylistic( <feature-value-name> ) || historical-forms || styleset( <feature-value-name># ) || character-variant( <feature-value-name># ) || swash( <feature-value-name> ) || ornaments( <feature-value-name> ) || annotation( <feature-value-name> ) ] || [ <numeric-figure-values> || <numeric-spacing-values> || <numeric-fraction-values> || ordinal || slashed-zero ] || [ <east-asian-variant-values> || <east-asian-width-values> || ruby ] || [ sub | super ] || [ text | emoji | unicode ] ]", "font-variation-settings": "normal | [ <opentype-tag> <number> ]#", "font-width": "normal | <percentage [0,∞]> | ultra-condensed | extra-condensed | condensed | semi-condensed | semi-expanded | expanded | extra-expanded | ultra-expanded", "footnote-display": "block | inline | compact", "footnote-policy": "auto | line | block", "glyph-orientation-vertical": "auto | 0deg | 90deg | 0 | 90", "height": "auto | <length-percentage [0,∞]> | min-content | max-content | fit-content( <length-percentage [0,∞]> ) | <calc-size()> | stretch | fit-content | contain | <anchor-size()> | <-non-standard-size>", "hyphenate-limit-last": "none | always | column | page | spread", "hyphenate-limit-lines": "no-limit | <integer>", "hyphenate-limit-zone": "<length-percentage>", "image-orientation": "from-image | none | [ <angle> || flip ]", "image-rendering": "auto | smooth | high-quality | pixelated | crisp-edges | <-non-standard-image-rendering>", "initial-letter": "normal | <number [1,∞]> <integer [1,∞]> | <number [1,∞]> && [ drop | raise ]?", "initial-letter-align": "[ border-box? [ alphabetic | ideographic | hanging | leading ]? ]!", "initial-letter-wrap": "none | first | all | grid | <length-percentage>", "inline-sizing": "normal | stretch", "interactivity": "auto | inert", "isolation": "<isolation-mode>", "item-cross": "[ auto | nowrap | wrap ] || [ normal | reverse ] | wrap-reverse", "item-direction": "auto | row | column | row-reverse | column-reverse", "item-flow": "<'item-direction'> || <'item-wrap'> || <'item-pack'> || <'item-slack'>", "item-pack": "normal | dense || balance", "item-slack": "<length-percentage> | infinite", "item-track": "auto | row | column | row-reverse | column-reverse", "item-wrap": "[ auto | nowrap | wrap ] || [ normal | reverse ] | wrap-reverse", "justify-items": "| anchor-center", "justify-self": "| anchor-center", "left": "| <anchor()> | <anchor-size()>", "lighting-color": "<color>", "line-clamp": "none | [ <integer [1,∞]> || <'block-ellipsis'> ] -webkit-legacy?", "-webkit-line-clamp": "none | <integer [1,∞]>", "line-fit-edge": "leading | <text-edge>", "line-grid": "match-parent | create", "line-height": "normal | <number [0,∞]> | <length-percentage [0,∞]>", "line-height-step": "<length [0,∞]>", "line-padding": "<length>", "line-snap": "none | baseline | contain", "link-parameters": "none | <link-param>+", "list-style": "<'list-style-position'> || <'list-style-image'> || <'list-style-type'>", "margin": "<'margin-top'>{1,4}", "margin-block": "<'margin-top'>{1,2}", "margin-block-end": "<'margin-top'>", "margin-block-start": "<'margin-top'>", "margin-bottom": "| <anchor-size()>", "margin-break": "auto | keep | discard", "margin-inline": "<'margin-top'>{1,2}", "margin-inline-end": "<'margin-top'>", "margin-inline-start": "<'margin-top'>", "margin-left": "| <anchor-size()>", "margin-right": "| <anchor-size()>", "margin-top": "| <anchor-size()>", "margin-trim": "none | [ block || inline ] | [ block-start || inline-start || block-end || inline-end ]", "marker": "none | <marker-ref>", "marker-end": "none | <marker-ref>", "marker-mid": "none | <marker-ref>", "marker-side": "match-self | match-parent", "marker-start": "none | <marker-ref>", "mask-border-slice": "[ <number> | <percentage> ]{1,4} fill?", "mask-clip": "[ <coord-box> | no-clip ]#", "mask-origin": "<coord-box>#", "max-height": "none | <length-percentage [0,∞]> | min-content | max-content | fit-content( <length-percentage [0,∞]> ) | <calc-size()> | stretch | <-non-standard-size> | fit-content | contain | <anchor-size()>", "max-lines": "none | <integer [1,∞]>", "max-width": "none | <length-percentage [0,∞]> | min-content | max-content | fit-content( <length-percentage [0,∞]> ) | <calc-size()> | stretch | <-non-standard-size> | fit-content | contain | <anchor-size()>", "min-height": "auto | <length-percentage [0,∞]> | min-content | max-content | fit-content( <length-percentage [0,∞]> ) | <calc-size()> | stretch | <-non-standard-size> | fit-content | contain | <anchor-size()>", "min-intrinsic-sizing": "legacy | zero-if-scroll || zero-if-extrinsic", "min-width": "auto | <length-percentage [0,∞]> | min-content | max-content | fit-content( <length-percentage [0,∞]> ) | <calc-size()> | stretch | <-non-standard-size> | fit-content | contain | <anchor-size()>", "mix-blend-mode": "<blend-mode> | plus-darker | plus-lighter", "object-fit": "fill | none | [ contain | cover ] || scale-down", "object-view-box": "none | <basic-shape-rect>", "opacity": "<opacity-value>", "orphans": "<integer [1,∞]>", "outline": "<'outline-width'> || <'outline-style'> || <'outline-color'>", "outline-color": "| <image-1D>", "outline-style": "auto | none | dotted | dashed | solid | double | groove | ridge | inset | outset", "overflow": "<'overflow-block'>{1,2} | <-non-standard-overflow>", "overflow-clip-margin-block": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-block-end": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-block-start": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-bottom": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-inline": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-inline-end": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-inline-start": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-left": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-right": "<visual-box> || <length [0,∞]>", "overflow-clip-margin-top": "<visual-box> || <length [0,∞]>", "padding": "<'padding-top'>{1,4}", "padding-block": "<'padding-top'>{1,2}", "padding-block-end": "<'padding-top'>", "padding-block-start": "<'padding-top'>", "padding-bottom": "<length-percentage [0,∞]>", "padding-inline": "<'padding-top'>{1,2}", "padding-inline-end": "<'padding-top'>", "padding-inline-start": "<'padding-top'>", "padding-left": "<length-percentage [0,∞]>", "padding-right": "<length-percentage [0,∞]>", "padding-top": "<length-percentage [0,∞]>", "pause-after": "<time [0s,∞]> | none | x-weak | weak | medium | strong | x-strong", "pause-before": "<time [0s,∞]> | none | x-weak | weak | medium | strong | x-strong", "perspective": "none | <length [0,∞]>", "pointer-events": "| bounding-box", "position": "| <running()>", "quotes": "| match-parent", "r": "<length-percentage>", "reading-flow": "normal | source-order | flex-visual | flex-flow | grid-rows | grid-columns | grid-order", "reading-order": "<integer>", "region-fragment": "auto | break", "rest-after": "<time [0s,∞]> | none | x-weak | weak | medium | strong | x-strong", "rest-before": "<time [0s,∞]> | none | x-weak | weak | medium | strong | x-strong", "right": "| <anchor()> | <anchor-size()>", "row-gap": "normal | <length-percentage [0,∞]>", "row-rule": "<gap-rule-list> | <gap-auto-rule-list>", "row-rule-break": "none | spanning-item | intersection", "row-rule-color": "<line-color-list> | <auto-line-color-list>", "row-rule-outset": "<length-percentage>", "row-rule-style": "<line-style-list> | <auto-line-style-list>", "row-rule-width": "<line-width-list> | <auto-line-width-list>", "ruby-merge": "separate | merge | auto", "ruby-overhang": "auto | none", "rule": "<'column-rule'>", "rule-break": "<'column-rule-break'>", "rule-color": "<'column-rule-color'>", "rule-outset": "<'column-rule-outset'>", "rule-paint-order": "row-over-column | column-over-row", "rule-style": "<'column-rule-style'>", "rule-width": "<'column-rule-width'>", "rx": "<length-percentage> | auto", "ry": "<length-percentage> | auto", "scroll-initial-target": "none | nearest", "scroll-marker-group": "none | before | after", "scroll-padding": "[ auto | <length-percentage [0,∞]> ]{1,4}", "scroll-padding-block": "[ auto | <length-percentage [0,∞]> ]{1,2}", "scroll-padding-block-end": "auto | <length-percentage [0,∞]>", "scroll-padding-block-start": "auto | <length-percentage [0,∞]>", "scroll-padding-bottom": "auto | <length-percentage [0,∞]>", "scroll-padding-inline": "[ auto | <length-percentage [0,∞]> ]{1,2}", "scroll-padding-inline-end": "auto | <length-percentage [0,∞]>", "scroll-padding-inline-start": "auto | <length-percentage [0,∞]>", "scroll-padding-left": "auto | <length-percentage [0,∞]>", "scroll-padding-right": "auto | <length-percentage [0,∞]>", "scroll-padding-top": "auto | <length-percentage [0,∞]>", "scroll-target-group": "none | auto", "scroll-timeline": "[ <'scroll-timeline-name'> <'scroll-timeline-axis'>? ]#", "shape-image-threshold": "<opacity-value>", "shape-inside": "auto | outside-shape | [ <basic-shape> || shape-box ] | <image> | display", "shape-margin": "<length-percentage [0,∞]>", "shape-padding": "<length-percentage [0,∞]>", "shape-subtract": "none | [ <basic-shape> | <url> ]+", "slider-orientation": "auto | left-to-right | right-to-left | top-to-bottom | bottom-to-top", "spatial-navigation-action": "auto | focus | scroll", "spatial-navigation-contain": "auto | contain", "spatial-navigation-function": "normal | grid", "string-set": "none | [ <custom-ident> <string>+ ]#", "stroke-align": "center | inset | outset", "stroke-alignment": "center | inner | outer", "stroke-break": "bounding-box | slice | clone", "stroke-color": "<color>#", "stroke-dash-corner": "none | <length>", "stroke-dash-justify": "none | [ stretch | compress ] || [ dashes || gaps ]", "stroke-dashadjust": "none | [ stretch | compress ] [ dashes | gaps ]?", "stroke-dasharray": "none | [ <length-percentage> | <number> ]+#", "stroke-dashcorner": "none | <length>", "stroke-dashoffset": "<length-percentage> | <number>", "stroke-image": "<paint>#", "stroke-linejoin": "[ crop | arcs | miter ] || [ bevel | round | fallback ]", "stroke-miterlimit": "<number>", "stroke-origin": "match-parent | fill-box | stroke-box | content-box | padding-box | border-box", "stroke-position": "<position>#", "stroke-repeat": "<repeat-style>#", "stroke-size": "<bg-size>#", "stroke-width": "[ <length-percentage> | <number> ]#", "tab-size": "<number [0,∞]> | <length [0,∞]>", "text-align": "| <string> | justify-all", "text-align-all": "start | end | left | right | center | <string> | justify | match-parent", "text-align-last": "| match-parent", "text-autospace": "normal | <autospace> | auto", "text-box": "normal | <'text-box-trim'> || <'text-box-edge'>", "text-box-edge": "auto | <text-edge>", "text-box-trim": "none | trim-start | trim-end | trim-both", "text-combine-upright": "none | all | [ digits <integer [2,4]>? ]", "text-decoration-skip": "none | auto", "text-decoration-skip-box": "none | all", "text-decoration-skip-self": "auto | skip-all | [ skip-underline || skip-overline || skip-line-through ] | no-skip", "text-decoration-skip-spaces": "none | all | [ start || end ]", "text-decoration-thickness": "auto | from-font | <length-percentage>", "text-decoration-trim": "<length>{1,2} | auto", "text-emphasis-skip": "spaces || punctuation || symbols || narrow", "text-group-align": "none | start | end | left | right | center", "text-indent": "[ <length-percentage> ] && hanging? && each-line?", "text-justify": "[ auto | none | inter-word | inter-character | ruby ] || no-compress", "text-overflow": "[ clip | ellipsis | <string> | fade | fade( [ <length> | <percentage> ] ) ]{1,2}", "text-shadow": "none | <shadow>#", "text-size-adjust": "auto | none | <percentage [0,∞]>", "text-spacing": "none | auto | <spacing-trim> || <autospace>", "text-spacing-trim": "<spacing-trim> | auto", "-webkit-text-stroke": "<line-width> || <color>", "-webkit-text-stroke-width": "<line-width>", "text-transform": "none | [ capitalize | uppercase | lowercase ] || full-width || full-size-kana | math-auto", "text-underline-offset": "auto | <length-percentage>", "text-underline-position": "auto | [ from-font | under ] || [ left | right ]", "text-wrap-mode": "wrap | nowrap", "text-wrap-style": "auto | balance | stable | pretty | avoid-orphans", "timeline-scope": "| all", "top": "| <anchor()> | <anchor-size()>", "transform-origin": "[ left | center | right | top | bottom | <length-percentage> ] | [ left | center | right | <length-percentage> ] [ top | center | bottom | <length-percentage> ] <length>? | [ [ center | left | right ] && [ center | top | bottom ] ] <length>?", "transition-duration": "<time [0s,∞]>#", "vertical-align": "[ first | last ] || <'alignment-baseline'> || <'baseline-shift'>", "view-timeline": "[ <'view-timeline-name'> [ <'view-timeline-axis'> || <'view-timeline-inset'> ]? ]#", "view-timeline-name": "[ none | <dashed-ident> ]#", "view-transition-class": "none | <custom-ident>+", "view-transition-group": "normal | contain | nearest | <custom-ident>", "visibility": "visible | hidden | force-hidden | collapse", "voice-duration": "auto | <time [0s,∞]>", "voice-pitch": "<frequency [0Hz,∞]> && absolute | [ [ x-low | low | medium | high | x-high ] || [ <frequency> | <semitones> | <percentage> ] ]", "voice-range": "<frequency [0Hz,∞]> && absolute | [ [ x-low | low | medium | high | x-high ] || [ <frequency> | <semitones> | <percentage> ] ]", "voice-rate": "[ normal | x-slow | slow | medium | fast | x-fast ] || <percentage [0,∞]>", "widows": "<integer [1,∞]>", "width": "auto | <length-percentage [0,∞]> | min-content | max-content | fit-content( <length-percentage [0,∞]> ) | <calc-size()> | stretch | <-non-standard-size> | fit-content | contain | <anchor-size()>", "word-break": "| manual", "word-space-transform": "none | [ space | ideographic-space ] && auto-phrase?", "word-spacing": "normal | <length-percentage>", "word-wrap": "| anywhere", "wrap-after": "auto | avoid | avoid-line | avoid-flex | line | flex", "wrap-before": "auto | avoid | avoid-line | avoid-flex | line | flex", "wrap-flow": "auto | both | start | end | minimum | maximum | clear", "wrap-inside": "auto | avoid", "wrap-through": "wrap | none", "x": "<length-percentage>", "y": "<length-percentage>"}, "types": {"dashed-ident": "<custom-property-name>", "unicode-range-token": "<urange>", "anchor-size()": "anchor-size( [ <anchor-name> || <anchor-size> ]? , <length-percentage>? )", "anchor()": "anchor( <anchor-name>? && <anchor-side> , <length-percentage>? )", "angle-percentage": "[ <angle> | <percentage> ]", "angular-color-hint": "| <zero>", "angular-color-stop": "<color> <color-stop-angle>?", "angular-color-stop-list": "<angular-color-stop> , [ <angular-color-hint>? , <angular-color-stop> ]#?", "arc-command": "arc <command-end-point> [ [ of <length-percentage>{1,2} ] && <arc-sweep>? && <arc-size>? && [ rotate <angle> ]? ]", "arc-size": "large | small", "arc-sweep": "cw | ccw", "attr-args": "attr( <declaration-value> , <declaration-value>? )", "attr-name": "[ [ <ident-token>? '|' ]? <ident-token> ]", "attr-type": "type( <syntax> ) | raw-string | <attr-unit>", "attr-unit": "'%' | em | ex | cap | ch | ic | rem | lh | rlh | vw | vh | vi | vb | vmin | vmax | cm | mm | q | in | pc | pt | px | deg | grad | rad | turn | s | ms | hz | khz | fr", "attr()": "attr( <attr-name> <attr-type>? , <declaration-value>? )", "auto-line-color-list": "[ <line-color-or-repeat> ]* <auto-repeat-line-color> [ <line-color-or-repeat> ]*", "auto-line-style-list": "[ <line-style-or-repeat> ]* <auto-repeat-line-style> [ <line-style-or-repeat> ]*", "auto-line-width-list": "[ <line-width-or-repeat> ]* <auto-repeat-line-width> [ <line-width-or-repeat> ]*", "auto-repeat-line-color": "repeat( auto , [ <color> ]+ )", "auto-repeat-line-style": "repeat( auto , [ <line-style> ]+ )", "auto-repeat-line-width": "repeat( auto , [ <line-width> ]+ )", "autospace": "no-autospace | [ ideograph-alpha || ideograph-numeric || punctuation ] || [ insert | replace ]", "baseline-position": "[ first | last ]? && baseline", "basic-shape": "inset( <length-percentage>{1,4} [ round <'border-radius'> ]? ) | xywh( <length-percentage>{2} <length-percentage [0,∞]>{2} [ round <'border-radius'> ]? ) | rect( [ <length-percentage> | auto ]{4} [ round <'border-radius'> ]? ) | circle( <radial-size>? [ at <position> ]? ) | ellipse( [ <radial-size>{2} ]? [ at <position> ]? ) | polygon( <'fill-rule'>? [ round <length> ]? , [ <length-percentage> <length-percentage> ]# ) | path( <'fill-rule'>? , <string> )", "basic-shape-rect": "<inset()> | rect( [ <length-percentage> | auto ]{4} [ round <'border-radius'> ]? ) | xywh( <length-percentage>{2} <length-percentage [0,∞]>{2} [ round <'border-radius'> ]? )", "bg-clip": "<visual-box> | border-area | text", "bg-layer": "<bg-image> || <bg-position> [ / <bg-size> ]? || <repeat-style> || <attachment> || <visual-box> || <visual-box>", "bg-position": "<position> | <position-three>", "bg-size": "[ <length-percentage [0,∞]> | auto ]{1,2} | cover | contain", "border-style": "none | hidden | dotted | dashed | solid | double | groove | ridge | inset | outset", "border-width": "thin | medium | thick | <length>", "bottom": "<length-percentage> | auto", "box": "| margin-box | fill-box | stroke-box | view-box", "calc-keyword": "e | pi | infinity | -infinity | NaN", "calc-mix()": "calc-mix( <progress> , <calc-sum> , <calc-sum> )", "calc-product": "<calc-value> [ [ '*' | / ] <calc-value> ]*", "calc-size-basis": "[ <size-keyword> | <calc-size()> | any | <calc-sum> ]", "calc-size()": "calc-size( <calc-size-basis> , <calc-sum> )", "calc-value": "<number> | <dimension> | <percentage> | <calc-keyword> | ( <calc-sum> )", "cf-image": "[ <image> | <color> ] && <percentage [0,100]>?", "clamp()": "clamp( [ <calc-sum> | none ] , <calc-sum> , [ <calc-sum> | none ] )", "color": "<color-base> | currentColor | <system-color> | <contrast-color()> | <device-cmyk()> | <light-dark()> | <-non-standard-color>", "color-font-tech": "[ color-COLRv0 | color-COLRv1 | color-SVG | color-sbix | color-CBDT ]", "color-function": "<rgb()> | <rgba()> | <hsl()> | <hsla()> | <hwb()> | <lab()> | <lch()> | <oklab()> | <oklch()> | <ictcp()> | <jzazbz()> | <jzczhz()> | <color()>", "color-layers()": "color-layers( [ <blend-mode> , ]? <color># )", "color-mix()": "color-mix( <color-interpolation-method> , [ <color> && <percentage [0,100]>? ]# )", "color-stop-angle": "[ <angle-percentage> | <zero> ]{1,2}", "color-stop-list": "<linear-color-stop> , [ <linear-color-hint>? , <linear-color-stop> ]#?", "color-stripe": "<color> && [ <length-percentage> | <flex> ]?", "color()": "color( [ from <color> ]? <colorspace-params> [ / [ <alpha-value> | none ] ]? )", "colorspace-params": "[ <custom-params> | <predefined-rgb-params> | <xyz-params> ]", "command-end-point": "[ to <position> | by <coordinate-pair> ]", "compat-special": "textfield | menulist-button", "composite-mode": "clear | copy | source-over | destination-over | source-in | destination-in | source-out | destination-out | source-atop | destination-atop | xor | lighter | plus-darker | plus-lighter", "conic-gradient-syntax": "[ [ [ from [ <angle> | <zero> ] ]? [ at <position> ]? ] || <color-interpolation-method> ]? , <angular-color-stop-list>", "conic-gradient()": "conic-gradient( [ <conic-gradient-syntax> ] )", "container-condition": "[ <container-name>? <container-query>? ]!", "container-query": "not <query-in-parens> | <query-in-parens> [ [ and <query-in-parens> ]* | [ or <query-in-parens> ]* ]", "content-level": "element | content | text | <attr()> | <counter>", "content-list": "[ <string> | <image> | <attr()> | contents | <quote> | <leader()> | <target> | <string()> | <content()> | <counter> | <counter()> | <counters()> ]+", "content()": "content( [ text | before | after | first-letter | marker ]? )", "contrast-color()": "contrast-color( <color> )", "control-point": "[ <position> | <relative-control-point> ]", "control-value()": "control-value( <type>? )", "coord-box": "<paint-box> | view-box", "coordinate-pair": "<length-percentage>{2}", "corner-shape-value": "round | scoop | bevel | notch | square | squircle | superellipse( <number [-∞,∞]> | infinity | -infinity )", "counter-style": "<counter-style-name> | <symbols()>", "counter-style-name": "| decimal | decimal-leading-zero | arabic-indic | armenian | upper-armenian | lower-armenian | bengali | cambodian | khmer | cjk-decimal | devanagari | georgian | gujarati | gurmukhi | hebrew | kannada | lao | malayalam | mongolian | myanmar | oriya | persian | lower-roman | upper-roman | tamil | telugu | thai | tibetan | lower-alpha | lower-latin | upper-alpha | upper-latin | lower-greek | hiragana | hiragana-iroha | katakana | katakana-iroha | disc | circle | square | disclosure-open | disclosure-closed | cjk-earthly-branch | cjk-heavenly-stem | japanese-informal | japanese-formal | korean-hangul-formal | korean-hanja-informal | korean-hanja-formal | ethiopic-numeric", "cross-fade()": "cross-fade( <cf-image># )", "crossorigin-modifier": "crossorigin( anonymous | use-credentials )", "css-type": "<syntax-component> | <type()>", "cubic-bezier-easing-function": "ease | ease-in | ease-out | ease-in-out | <cubic-bezier()>", "cubic-bezier()": "cubic-bezier( [ <number [0,1]> , <number> ]#{2} )", "curve-command": "curve [ [ to <position> with <control-point> [ / <control-point> ]? ] | [ by <coordinate-pair> with <relative-control-point> [ / <relative-control-point> ]? ] ]", "custom-arg": "$ <ident-token> ;", "custom-params": "<dashed-ident> [ <number> | <percentage> | none ]+", "dasharray": "[ [ <length-percentage> | <number> ]+ ]#", "deprecated-color": "ActiveBorder | ActiveCaption | AppWorkspace | Background | ButtonHighlight | ButtonShadow | CaptionText | InactiveBorder | InactiveCaption | InactiveCaptionText | InfoBackground | InfoText | Menu | MenuText | Scrollbar | ThreeDDarkShadow | ThreeDFace | ThreeDHighlight | ThreeDLightShadow | ThreeDShadow | Window | WindowFrame | WindowText", "dynamic-range-limit-mix()": "dynamic-range-limit-mix( [ <'dynamic-range-limit'> && <percentage [0,100]> ]#{2,} )", "easing-function": "<linear-easing-function> | <cubic-bezier-easing-function> | <step-easing-function>", "element()": "element( <id-selector> )", "env()": "env( <custom-ident> <integer [0,∞]>* , <declaration-value>? )", "feature-value-name": "<ident>", "filter-value-list": "[ <filter-function> | <url> ]+", "filter()": "filter( [ <image> | <string> ] , <filter-value-list> )", "final-bg-layer": "<bg-image> || <bg-position> [ / <bg-size> ]? || <repeat-style> || <attachment> || <visual-box> || <visual-box> || <'background-color'>", "first-valid()": "first-valid( <declaration-value># )", "fixed-breadth": "<length-percentage [0,∞]>", "font-features-tech": "[ features-opentype | features-aat | features-graphite ]", "font-format": "[ <string> | collection | embedded-opentype | opentype | svg | truetype | woff | woff2 ]", "font-src": "<url> [ format( <font-format> ) ]? [ tech( <font-tech># ) ]? | local( <family-name> )", "font-src-list": "[ <url> [ format( <font-format> ) ]? [ tech( <font-tech># ) ]? | local( <family-name> ) ]#", "font-tech": "[ <font-features-tech> | <color-font-tech> | variations | palettes | incremental ]", "font-weight-absolute": "[ normal | bold | <number [1,1000]> ]", "frequency-percentage": "[ <frequency> | <percentage> ]", "function-parameter": "<custom-property-name> <css-type>? [ : <declaration-value> ]?", "gap-auto-repeat-rule": "repeat( auto , <gap-rule># )", "gap-auto-rule-list": "<gap-rule-or-repeat>#? , <gap-auto-repeat-rule> , <gap-rule-or-repeat>#?", "gap-repeat-rule": "repeat( <integer [1,∞]> , <gap-rule># )", "gap-rule": "<line-width> || <line-style> || <color>", "gap-rule-list": "<gap-rule-or-repeat>#", "gap-rule-or-repeat": "<gap-rule> | <gap-repeat-rule>", "generic-script-specific": "generic( fangsong ) | generic( kai ) | generic( khmer-mul ) | generic( nastaliq )", "gradient": "[ <linear-gradient()> | <repeating-linear-gradient()> | <radial-gradient()> | <repeating-radial-gradient()> | <conic-gradient()> | <repeating-conic-gradient()> | <-legacy-gradient> ]", "grid-line": "auto | <custom-ident> | [ [ <integer [-∞,-1]> | <integer [1,∞]> ] && <custom-ident>? ] | [ span && [ <integer [1,∞]> || <custom-ident> ] ]", "hdr-color()": "color-hdr( [ <color> && <number [0,∞]>? ]#{2} )", "horizontal-line-command": "hline [ to [ <length-percentage> | left | center | right | x-start | x-end ] | by <length-percentage> ]", "hsl()": "[ <legacy-hsl-syntax> | <modern-hsl-syntax> ]", "hsla()": "[ <legacy-hsla-syntax> | <modern-hsla-syntax> ]", "hwb()": "hwb( [ from <color> ]? [ <hue> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "ictcp()": "ictcp( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "ident-arg": "<string> | <integer> | <ident>", "ident()": "ident( <ident-arg>+ )", "if-args": "if( [ <if-args-branch> ; ]* <if-args-branch> ;? )", "if-args-branch": "<declaration-value> : <declaration-value>?", "if-branch": "<if-condition> : <declaration-value>?", "if-condition": "<boolean-expr[<if-test>]> | else", "if-test": "supports( [ <ident> : <declaration-value> ] | <supports-condition> ) | media( <media-feature> | <media-condition> ) | style( <style-query> )", "if()": "if( [ <if-branch> ; ]* <if-branch> ;? )", "image-1D": "<stripes()>", "image-set-option": "[ <image> | <string> ] [ <resolution> || type( <string> ) ]?", "image-src": "[ <url> | <string> ]", "image-tags": "[ ltr | rtl ]", "import-conditions": "[ supports( [ <supports-condition> | <declaration> ] ) ]? <media-query-list>?", "inflexible-breadth": "<length-percentage [0,∞]> | min-content | max-content | auto", "inherit-args": "inherit( <declaration-value> , <declaration-value>? )", "inherit()": "inherit( <custom-property-name> , <declaration-value>? )", "integrity-modifier": "integrity( <string> )", "isolation-mode": "auto | isolate", "jzazbz()": "jzazbz( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "jzczhz()": "jzczhz( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <hue> | none ] [ / [ <alpha-value> | none ] ]? )", "keyframe-selector": "from | to | <percentage [0,100]> | <timeline-range-name> <percentage>", "lab()": "lab( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "layout-box": "<visual-box> | margin-box", "lch()": "lch( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <hue> | none ] [ / [ <alpha-value> | none ] ]? )", "left": "<length-percentage> | auto", "legacy-hsl-syntax": "hsl( <hue> , <percentage> , <percentage> , <alpha-value>? )", "legacy-hsla-syntax": "hsla( <hue> , <percentage> , <percentage> , <alpha-value>? )", "legacy-pseudo-element-selector": ": [ before | after | first-line | first-letter ]", "legacy-rgb-syntax": "rgb( <percentage>#{3} , <alpha-value>? ) | rgb( <number>#{3} , <alpha-value>? )", "legacy-rgba-syntax": "rgba( <percentage>#{3} , <alpha-value>? ) | rgba( <number>#{3} , <alpha-value>? )", "length-percentage": "[ <length> | <percentage> ]", "line-color-list": "[ <line-color-or-repeat> ]+", "line-color-or-repeat": "[ <color> | <repeat-line-color> ]", "line-command": "line <command-end-point>", "line-style-list": "[ <line-style-or-repeat> ]+", "line-style-or-repeat": "[ <line-style> | <repeat-line-style> ]", "line-width": "<length [0,∞]> | thin | medium | thick", "line-width-list": "[ <line-width-or-repeat> ]+", "line-width-or-repeat": "[ <line-width> | <repeat-line-width> ]", "linear-easing-function": "linear | <linear()>", "linear-gradient-syntax": "[ [ <angle> | <zero> | to <side-or-corner> ] || <color-interpolation-method> ]? , <color-stop-list>", "linear-gradient()": "linear-gradient( [ <linear-gradient-syntax> ] )", "linear()": "linear( [ <number> && <percentage>{0,2} ]# )", "link-param": "param( <custom-property-name> <declaration-value>? )", "marker-ref": "<url>", "media-and": "and <media-in-parens>", "media-condition": "<media-not> | <media-in-parens> [ <media-and>* | <media-or>* ]", "media-condition-without-or": "<media-not> | <media-in-parens> <media-and>*", "media-or": "or <media-in-parens>", "media()": "media( [ <mf-plain> | <mf-boolean> | <mf-range> ] )", "mf-comparison": "<mf-lt> | <mf-gt> | <mf-eq>", "mf-eq": "'='", "mf-gt": "'>' '='?", "mf-lt": "'<' '='?", "mf-range": "<mf-name> <mf-comparison> <mf-value> | <mf-value> <mf-comparison> <mf-name> | <mf-value> <mf-lt> <mf-name> <mf-lt> <mf-value> | <mf-value> <mf-gt> <mf-name> <mf-gt> <mf-value>", "mix()": "mix( <progress> , <any-value> , <any-value> ) | mix( <progress> && of <keyframes-name> )", "modern-hsl-syntax": "hsl( [ from <color> ]? [ <hue> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "modern-hsla-syntax": "hsla( [ from <color> ]? [ <hue> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "modern-rgb-syntax": "rgb( [ from <color> ]? [ <number> | <percentage> | none ]{3} [ / [ <alpha-value> | none ] ]? )", "modern-rgba-syntax": "rgba( [ from <color> ]? [ <number> | <percentage> | none ]{3} [ / [ <alpha-value> | none ] ]? )", "move-command": "move <command-end-point>", "mq-boolean": "<integer [0,1]>", "number-optional-number": "<number> <number>?", "oklab()": "oklab( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ / [ <alpha-value> | none ] ]? )", "oklch()": "oklch( [ from <color> ]? [ <percentage> | <number> | none ] [ <percentage> | <number> | none ] [ <hue> | none ] [ / [ <alpha-value> | none ] ]? )", "opacity-value": "<number> | <percentage>", "opentype-tag": "<string>", "padding-width": "<length> | <percentage>", "page-selector": "[ <ident-token>? <pseudo-page>* ]!", "page-selector-list": "<page-selector>#", "paint": "none | <color> | <url> [ none | <color> ]? | context-fill | context-stroke | <image> | <svg-paint>", "paint-box": "<visual-box> | fill-box | stroke-box", "palette-mix()": "palette-mix( <color-interpolation-method> , [ [ normal | light | dark | <palette-identifier> | <palette-mix()> ] && <percentage [0,100]>? ]#{2} )", "points": "[ <number>+ ]#", "position": "<position-one> | <position-two> | <position-four>", "position-four": "[ [ [ left | right | x-start | x-end ] <length-percentage> ] && [ [ top | bottom | y-start | y-end ] <length-percentage> ] | [ [ block-start | block-end ] <length-percentage> ] && [ [ inline-start | inline-end ] <length-percentage> ] | [ [ start | end ] <length-percentage> ]{2} ]", "position-one": "[ left | center | right | top | bottom | x-start | x-end | y-start | y-end | block-start | block-end | inline-start | inline-end | <length-percentage> ]", "position-three": "[ [ left | center | right ] && [ [ top | bottom ] <length-percentage> ] | [ [ left | right ] <length-percentage> ] && [ top | center | bottom ] ]", "position-two": "[ [ left | center | right | x-start | x-end ] && [ top | center | bottom | y-start | y-end ] | [ left | center | right | x-start | x-end | <length-percentage> ] [ top | center | bottom | y-start | y-end | <length-percentage> ] | [ block-start | center | block-end ] && [ inline-start | center | inline-end ] | [ start | center | end ]{2} ]", "predefined-rgb": "srgb | srgb-linear | display-p3 | a98-rgb | prophoto-rgb | rec2020 | rec2100-pq | rec2100-hlg | rec2100-linear", "progress": "[ <percentage-token> | <number> | <'animation-timeline'> ] && [ by <easing-function> ]?", "progress()": "progress( <calc-sum> , <calc-sum> , <calc-sum> )", "pseudo-class-selector": ": <ident-token> | : <function-token> <any-value> )", "pseudo-element-selector": ": <pseudo-class-selector> | <legacy-pseudo-element-selector>", "pt-class-selector": "[ '.' <custom-ident> ]+", "pt-name-and-class-selector": "<pt-name-selector> <pt-class-selector>? | <pt-class-selector>", "pt-name-selector": "'*' | <custom-ident>", "query-in-parens": "( <container-query> ) | ( <size-feature> ) | style( <style-query> ) | scroll-state( <scroll-state-query> ) | <general-enclosed>", "radial-extent": "closest-corner | closest-side | farthest-corner | farthest-side", "radial-gradient-syntax": "[ [ [ <radial-shape> || <radial-size> ]? [ at <position> ]? ] || <color-interpolation-method> ]? , <color-stop-list>", "radial-gradient()": "radial-gradient( [ <radial-gradient-syntax> ] )", "radial-shape": "circle | ellipse", "radial-size": "<radial-extent> | <length [0,∞]> | <length-percentage [0,∞]>", "random-item-args": "random-item( <declaration-value> , [ <declaration-value>? ]# )", "random-item()": "random-item( <random-value-sharing> , [ <declaration-value>? ]# )", "random-value-sharing": "[ [ auto | <dashed-ident> ] || element-shared ] | fixed <number [0,1]>", "random()": "random( <random-value-sharing>? , <calc-sum> , <calc-sum> , <calc-sum>? )", "ray()": "ray( [ <angle> && <ray-size>? && contain? && [ at <position> ]? ] )", "referrerpolicy-modifier": "referrerpolicy( no-referrer | no-referrer-when-downgrade | same-origin | origin | strict-origin | origin-when-cross-origin | strict-origin-when-cross-origin | unsafe-url )", "relative-control-point": "<coordinate-pair> [ from [ start | end | origin ] ]?", "repeat-line-color": "repeat( [ <integer [1,∞]> ] , [ <color> ]+ )", "repeat-line-style": "repeat( [ <integer [1,∞]> ] , [ <line-style> ]+ )", "repeat-line-width": "repeat( [ <integer [1,∞]> ] , [ <line-width> ]+ )", "repeat-style": "repeat-x | repeat-y | <repetition>{1,2}", "repeating-conic-gradient()": "repeating-conic-gradient( [ <conic-gradient-syntax> ] )", "repeating-linear-gradient()": "repeating-linear-gradient( [ <linear-gradient-syntax> ] )", "repeating-radial-gradient()": "repeating-radial-gradient( [ <radial-gradient-syntax> ] )", "repetition": "repeat | space | round | no-repeat", "request-url-modifier": "<crossorigin-modifier> | <integrity-modifier> | <referrerpolicy-modifier>", "rgb()": "[ <legacy-rgb-syntax> | <modern-rgb-syntax> ]", "rgba()": "[ <legacy-rgba-syntax> | <modern-rgba-syntax> ]", "right": "<length-percentage> | auto", "round()": "round( <rounding-strategy>? , <calc-sum> , <calc-sum>? )", "running()": "running( <custom-ident> )", "scroll-button-direction": "up | down | left | right | block-start | block-end | inline-start | inline-end | prev | next", "scroll-state-feature": "<ident> : <ident>", "scroll-state-in-parens": "( <scroll-state-query> ) | ( <scroll-state-feature> ) | <general-enclosed>", "scroll-state-query": "not <scroll-state-in-parens> | <scroll-state-in-parens> [ [ and <scroll-state-in-parens> ]* | [ or <scroll-state-in-parens> ]* ] | <scroll-state-feature>", "scroll()": "scroll( [ <scroller> || <axis> ]? )", "shadow": "<color>? && [ <length>{2} <length [0,∞]>? <length>? ] && inset?", "shape-box": "<visual-box> | margin-box", "shape-command": "<move-command> | <line-command> | close | <horizontal-line-command> | <vertical-line-command> | <curve-command> | <smooth-command> | <arc-command>", "shape()": "shape( <'fill-rule'>? from <position> , <shape-command># )", "single-animation-iteration-count": "infinite | <number [0,∞]>", "single-animation-trigger": "<single-animation-trigger-behavior> || [ none | auto | [ [ <dashed-ident> | <scroll()> | <view()> ] [ normal | <length-percentage> | <timeline-range-name> <length-percentage>? ]{0,4} ] ]", "single-animation-trigger-behavior": "once | repeat | alternate | state", "size-keyword": "auto | max-content | min-content | stretch", "smooth-command": "smooth [ [ to <position> [ with <control-point> ]? ] | [ by <coordinate-pair> [ with <relative-control-point> ]? ] ]", "source-size": "<media-condition> <source-size-value> | auto", "source-size-list": "<source-size>#? , <source-size-value>", "source-size-value": "<length> | auto", "spacing-trim": "space-all | normal | space-first | trim-start | trim-both | trim-all", "spread-shadow": "<'box-shadow-color'>? && [ <'box-shadow-offset'> [ <'box-shadow-blur'> <'box-shadow-spread'>? ]? ] && <'box-shadow-position'>?", "src()": "src( <string> <url-modifier>* )", "step-easing-function": "step-start | step-end | <steps()>", "steps()": "steps( <integer> , <step-position>? )", "string()": "string( <custom-ident> , [ first | start | last | first-except ]? )", "stripes()": "stripes( <color-stripe># )", "style-in-parens": "( <style-query> ) | ( <style-feature> ) | <general-enclosed>", "style-query": "not <style-in-parens> | <style-in-parens> [ [ and <style-in-parens> ]* | [ or <style-in-parens> ]* ] | <style-feature>", "superellipse()": "superellipse( <number> | infinity | -infinity )", "supports-feature": "<supports-selector-fn> | <supports-font-tech-fn> | <supports-font-format-fn> | <supports-decl>", "supports-font-format-fn": "font-format( <font-format> )", "supports-font-tech-fn": "font-tech( <font-tech> )", "supports()": "supports( <declaration> )", "svg-paint": "child | child( <integer> )", "symbols-type": "cyclic | numeric | alphabetic | symbolic | fixed", "symbols()": "symbols( <symbols-type>? [ <string> | <image> ]+ )", "syntax": "'*' | <syntax-component> [ <syntax-combinator> <syntax-component> ]* | <syntax-string>", "syntax-combinator": "'|'", "syntax-component": "<syntax-single-component> <syntax-multiplier>? | '<' transform-list '>'", "syntax-multiplier": "[ '#' | '+' ]", "syntax-single-component": "'<' <syntax-type-name> '>' | <ident>", "syntax-string": "<string>", "syntax-type-name": "angle | color | custom-ident | image | integer | length | length-percentage | number | percentage | resolution | string | time | url | transform-function", "target-contrast": "<wcag2>", "text-edge": "[ text | ideographic | ideographic-ink ] | [ text | ideographic | ideographic-ink | cap | ex ] [ text | ideographic | ideographic-ink | alphabetic ]", "time-percentage": "[ <time> | <percentage> ]", "toggle()": "toggle( <any-value># )", "top": "<length-percentage> | auto", "track-breadth": "<length-percentage [0,∞]> | <flex [0,∞]> | min-content | max-content | auto", "track-size": "<track-breadth> | minmax( <inflexible-breadth> , <track-breadth> ) | fit-content( <length-percentage [0,∞]> )", "transform-mix()": "transform-mix( <progress> , <transform-list> , <transform-list> )", "type": "'<' [ number | string ] '>'", "type()": "type( <syntax> )", "url": "<url()> | <src()> | <url-token>", "url-set-option": "[ <url> | <string> ] [ <resolution> || type( <string> ) ]?", "url-set()": "url-set( <url-set-option># )", "url()": "url( <string> <url-modifier>* ) | <url-token>", "var-args": "var( <declaration-value> , <declaration-value>? )", "vertical-line-command": "vline [ to [ <length-percentage> | top | center | bottom | y-start | y-end ] | by <length-percentage> ]", "wcag2": "wcag2 | wcag2( [ <number> | [ aa | aaa ] && large? ] )", "xyz": "xyz | xyz-d50 | xyz-d65", "xyz-params": "<xyz> [ <number> | <percentage> | none ]{3}"}}}
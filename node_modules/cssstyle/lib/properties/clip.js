"use strict";
// deprecated
// @see https://drafts.fxtf.org/css-masking/#clip-property

const parsers = require("../parsers");

const property = "clip";

module.exports.parse = function parse(v, opt = {}) {
  const { globalObject } = opt;
  if (v === "") {
    return v;
  }
  const value = parsers.parsePropertyValue(property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{ name, type, value: itemValue }] = value;
    switch (type) {
      case "Function": {
        const values = parsers.splitValue(itemValue, {
          delimiter: ","
        });
        const parsedValues = [];
        for (const item of values) {
          const parsedValue = parsers.parseCSS(item, { context: "value" }, true);
          const val = parsers.parseLengthPercentage(parsedValue.children);
          if (val) {
            parsedValues.push(val);
          } else {
            return;
          }
        }
        return `${name}(${parsedValues.join(", ")})`;
      }
      case "GlobalKeyword":
      case "Identifier": {
        return name;
      }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};

module.exports.definition = {
  set(v) {
    v = parsers.prepareValue(v, this._global);
    if (parsers.hasVarFunc(v)) {
      this._setProperty(property, v);
    } else {
      const val = module.exports.parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(property) ?? "";
        this._setProperty(property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(property);
  },
  enumerable: true,
  configurable: true
};

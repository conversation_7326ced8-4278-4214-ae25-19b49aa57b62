"use strict";
// autogenerated - 2025-09-21
// https://www.w3.org/Style/CSS/all-properties.en.html

var external_dependency_parsers_0 = require("../parsers.js");
var backgroundImage_export_parse, backgroundImage_export_definition;
const backgroundImage_local_var_property = "background-image";
const backgroundImage_local_var_shorthand = "background";
backgroundImage_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundImage_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        name,
        type
      }] = value;
      switch (type) {
        case "GlobalKeyword":
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        case "Url":
          {
            const parsedValue = external_dependency_parsers_0.parseUrl(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.push(parsedValue);
            break;
          }
        default:
          {
            const parsedValue = external_dependency_parsers_0.parseGradient(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.push(parsedValue);
          }
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    } else {
      return;
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundImage_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundImage_local_var_shorthand, "");
      this._setProperty(backgroundImage_local_var_property, v);
    } else {
      const val = backgroundImage_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundImage_local_var_shorthand);
        const prior = this._priorities.get(backgroundImage_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundImage_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundImage_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundPosition_export_parse, backgroundPosition_export_definition;
const backgroundPosition_local_var_property = "background-position";
const backgroundPosition_local_var_shorthand = "background";
const backgroundPosition_local_var_keyX = ["left", "right"];
const backgroundPosition_local_var_keyY = ["top", "bottom"];
const backgroundPosition_local_var_keywordsX = ["center", ...backgroundPosition_local_var_keyX];
const backgroundPosition_local_var_keywordsY = ["center", ...backgroundPosition_local_var_keyY];
const backgroundPosition_local_var_keywords = ["center", ...backgroundPosition_local_var_keyX, ...backgroundPosition_local_var_keyY];
backgroundPosition_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundPosition_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length) {
      let parsedValue = "";
      switch (value.length) {
        case 1:
          {
            const [part1] = value;
            const val1 = part1.type === "Identifier" ? part1.name : external_dependency_parsers_0.parseLengthPercentage([part1]);
            if (val1) {
              if (val1 === "center") {
                parsedValue = `${val1} ${val1}`;
              } else if (val1 === "top" || val1 === "bottom") {
                parsedValue = `center ${val1}`;
              } else {
                parsedValue = `${val1} center`;
              }
            }
            break;
          }
        case 2:
          {
            const [part1, part2] = value;
            const val1 = part1.type === "Identifier" ? part1.name : external_dependency_parsers_0.parseLengthPercentage([part1]);
            const val2 = part2.type === "Identifier" ? part2.name : external_dependency_parsers_0.parseLengthPercentage([part2]);
            if (val1 && val2) {
              if (backgroundPosition_local_var_keywordsX.includes(val1) && backgroundPosition_local_var_keywordsY.includes(val2)) {
                parsedValue = `${val1} ${val2}`;
              } else if (backgroundPosition_local_var_keywordsY.includes(val1) && backgroundPosition_local_var_keywordsX.includes(val2)) {
                parsedValue = `${val2} ${val1}`;
              } else if (backgroundPosition_local_var_keywordsX.includes(val1)) {
                if (val2 === "center" || !backgroundPosition_local_var_keywordsX.includes(val2)) {
                  parsedValue = `${val1} ${val2}`;
                }
              } else if (backgroundPosition_local_var_keywordsY.includes(val2)) {
                if (!backgroundPosition_local_var_keywordsY.includes(val1)) {
                  parsedValue = `${val1} ${val2}`;
                }
              } else if (!backgroundPosition_local_var_keywordsY.includes(val1) && !backgroundPosition_local_var_keywordsX.includes(val2)) {
                parsedValue = `${val1} ${val2}`;
              }
            }
            break;
          }
        case 3:
          {
            const [part1, part2, part3] = value;
            const val1 = part1.type === "Identifier" && part1.name;
            const val2 = part2.type === "Identifier" ? part2.name : external_dependency_parsers_0.parseLengthPercentage([part2]);
            const val3 = part3.type === "Identifier" ? part3.name : external_dependency_parsers_0.parseLengthPercentage([part3]);
            if (val1 && val2 && val3) {
              let posX = "";
              let offX = "";
              let posY = "";
              let offY = "";
              if (backgroundPosition_local_var_keywordsX.includes(val1)) {
                if (backgroundPosition_local_var_keyY.includes(val2)) {
                  if (!backgroundPosition_local_var_keywords.includes(val3)) {
                    posX = val1;
                    posY = val2;
                    offY = val3;
                  }
                } else if (backgroundPosition_local_var_keyY.includes(val3)) {
                  if (!backgroundPosition_local_var_keywords.includes(val2)) {
                    posX = val1;
                    offX = val2;
                    posY = val3;
                  }
                }
              } else if (backgroundPosition_local_var_keywordsY.includes(val1)) {
                if (backgroundPosition_local_var_keyX.includes(val2)) {
                  if (!backgroundPosition_local_var_keywords.includes(val3)) {
                    posX = val2;
                    offX = val3;
                    posY = val1;
                  }
                } else if (backgroundPosition_local_var_keyX.includes(val3)) {
                  if (!backgroundPosition_local_var_keywords.includes(val2)) {
                    posX = val3;
                    posY = val1;
                    offY = val2;
                  }
                }
              }
              if (posX && posY) {
                if (offX) {
                  parsedValue = `${posX} ${offX} ${posY}`;
                } else if (offY) {
                  parsedValue = `${posX} ${posY} ${offY}`;
                }
              }
            }
            break;
          }
        case 4:
          {
            const [part1, part2, part3, part4] = value;
            const val1 = part1.type === "Identifier" && part1.name;
            const val2 = external_dependency_parsers_0.parseLengthPercentage([part2]);
            const val3 = part3.type === "Identifier" && part3.name;
            const val4 = external_dependency_parsers_0.parseLengthPercentage([part4]);
            if (val1 && val2 && val3 && val4) {
              let posX = "";
              let offX = "";
              let posY = "";
              let offY = "";
              if (backgroundPosition_local_var_keywordsX.includes(val1) && backgroundPosition_local_var_keyY.includes(val3)) {
                posX = val1;
                offX = val2;
                posY = val3;
                offY = val4;
              } else if (backgroundPosition_local_var_keyX.includes(val1) && backgroundPosition_local_var_keywordsY.includes(val3)) {
                posX = val1;
                offX = val2;
                posY = val3;
                offY = val4;
              } else if (backgroundPosition_local_var_keyY.includes(val1) && backgroundPosition_local_var_keywordsX.includes(val3)) {
                posX = val3;
                offX = val4;
                posY = val1;
                offY = val2;
              }
              if (posX && offX && posY && offY) {
                parsedValue = `${posX} ${offX} ${posY} ${offY}`;
              }
            }
            break;
          }
        default:
      }
      if (parsedValue) {
        parsedValues.push(parsedValue);
      } else {
        return;
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundPosition_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundPosition_local_var_shorthand, "");
      this._setProperty(backgroundPosition_local_var_property, v);
    } else {
      const val = backgroundPosition_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundPosition_local_var_shorthand);
        const prior = this._priorities.get(backgroundPosition_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundPosition_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundPosition_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundSize_export_parse, backgroundSize_export_definition;
const backgroundSize_local_var_property = "background-size";
const backgroundSize_local_var_shorthand = "background";
backgroundSize_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundSize_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length) {
      if (value.length === 1) {
        const [{
          isNumber,
          name,
          type,
          value: itemValue
        }] = value;
        switch (type) {
          case "Calc":
            {
              if (isNumber) {
                return;
              }
              parsedValues.push(`${name}(${itemValue})`);
              break;
            }
          case "GlobalKeyword":
          case "Identifier":
            {
              parsedValues.push(name);
              break;
            }
          default:
            {
              const parsedValue = external_dependency_parsers_0.parseLengthPercentage(value);
              if (!parsedValue) {
                return;
              }
              parsedValues.push(parsedValue);
            }
        }
      } else {
        const [val1, val2] = value;
        const parts = [];
        if (val1.type === "Calc" && !val1.isNumber) {
          parts.push(`${val1.name}(${val1.value})`);
        } else if (val1.type === "Identifier") {
          parts.push(val1.name);
        } else if (val1.type === "Dimension") {
          parts.push(`${val1.value}${val1.unit}`);
        } else if (val1.type === "Percentage") {
          parts.push(`${val1.value}%`);
        } else {
          return;
        }
        switch (val2.type) {
          case "Calc":
            {
              if (val2.isNumber) {
                return;
              }
              parts.push(`${val2.name}(${val2.value})`);
              break;
            }
          case "Dimension":
            {
              parts.push(`${val2.value}${val2.unit}`);
              break;
            }
          case "Identifier":
            {
              if (val2.name !== "auto") {
                parts.push(val2.name);
              }
              break;
            }
          case "Percentage":
            {
              parts.push(`${val2.value}%`);
              break;
            }
          default:
            {
              return;
            }
        }
        parsedValues.push(parts.join(" "));
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundSize_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundSize_local_var_shorthand, "");
      this._setProperty(backgroundSize_local_var_property, v);
    } else {
      const val = backgroundSize_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundSize_local_var_shorthand);
        const prior = this._priorities.get(backgroundSize_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundSize_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundSize_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundRepeat_export_parse, backgroundRepeat_export_definition;
const backgroundRepeat_local_var_property = "background-repeat";
const backgroundRepeat_local_var_shorthand = "background";
backgroundRepeat_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundRepeat_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length) {
      let parsedValue = "";
      switch (value.length) {
        case 1:
          {
            const [part1] = value;
            const val1 = part1.type === "Identifier" && part1.name;
            if (val1) {
              parsedValue = val1;
            }
            break;
          }
        case 2:
          {
            const [part1, part2] = value;
            const val1 = part1.type === "Identifier" && part1.name;
            const val2 = part2.type === "Identifier" && part2.name;
            if (val1 && val2) {
              if (val1 === "repeat" && val2 === "no-repeat") {
                parsedValue = "repeat-x";
              } else if (val1 === "no-repeat" && val2 === "repeat") {
                parsedValue = "repeat-y";
              } else if (val1 === val2) {
                parsedValue = val1;
              } else {
                parsedValue = `${val1} ${val2}`;
              }
            }
            break;
          }
        default:
      }
      if (parsedValue) {
        parsedValues.push(parsedValue);
      } else {
        return;
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundRepeat_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundRepeat_local_var_shorthand, "");
      this._setProperty(backgroundRepeat_local_var_property, v);
    } else {
      const val = backgroundRepeat_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundRepeat_local_var_shorthand);
        const prior = this._priorities.get(backgroundRepeat_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundRepeat_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundRepeat_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundOrigin_export_parse, backgroundOrigin_export_definition;
const backgroundOrigin_local_var_property = "background-origin";
const backgroundOrigin_local_var_shorthand = "background";
backgroundOrigin_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundOrigin_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        name,
        type
      }] = value;
      switch (type) {
        case "GlobalKeyword":
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            return;
          }
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundOrigin_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundOrigin_local_var_shorthand, "");
      this._setProperty(backgroundOrigin_local_var_property, v);
    } else {
      const val = backgroundOrigin_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundOrigin_local_var_shorthand);
        const prior = this._priorities.get(backgroundOrigin_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundOrigin_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundOrigin_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundClip_export_parse, backgroundClip_export_definition;
const backgroundClip_local_var_property = "background-clip";
const backgroundClip_local_var_shorthand = "background";
backgroundClip_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundClip_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        name,
        type
      }] = value;
      switch (type) {
        case "GlobalKeyword":
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            return;
          }
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundClip_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundClip_local_var_shorthand, "");
      this._setProperty(backgroundClip_local_var_property, v);
    } else {
      const val = backgroundClip_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundClip_local_var_shorthand);
        const prior = this._priorities.get(backgroundClip_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundClip_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundClip_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundAttachment_export_parse, backgroundAttachment_export_definition;
const backgroundAttachment_local_var_property = "background-attachment";
const backgroundAttachment_local_var_shorthand = "background";
backgroundAttachment_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(backgroundAttachment_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        name,
        type
      }] = value;
      switch (type) {
        case "GlobalKeyword":
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            return;
          }
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
backgroundAttachment_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundAttachment_local_var_shorthand, "");
      this._setProperty(backgroundAttachment_local_var_property, v);
    } else {
      const val = backgroundAttachment_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundAttachment_local_var_shorthand);
        const prior = this._priorities.get(backgroundAttachment_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundAttachment_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundAttachment_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var backgroundColor_export_parse, backgroundColor_export_definition;
const backgroundColor_local_var_property = "background-color";
const backgroundColor_local_var_shorthand = "background";
backgroundColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(backgroundColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
backgroundColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(backgroundColor_local_var_shorthand, "");
      this._setProperty(backgroundColor_local_var_property, v);
    } else {
      const val = backgroundColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(backgroundColor_local_var_shorthand);
        const prior = this._priorities.get(backgroundColor_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(backgroundColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(backgroundColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var background_export_shorthandFor, background_export_parse, background_export_definition;
const background_local_var_property = "background";
const background_local_var_initialValues = new Map([["background-image", "none"], ["background-position", "0% 0%"], ["background-size", "auto"], ["background-repeat", "repeat"], ["background-origin", "padding-box"], ["background-clip", "border-box"], ["background-attachment", "scroll"], ["background-color", "transparent"]]);
background_export_shorthandFor = new Map([["background-image", {
  parse: backgroundImage_export_parse,
  definition: backgroundImage_export_definition
}], ["background-position", {
  parse: backgroundPosition_export_parse,
  definition: backgroundPosition_export_definition
}], ["background-size", {
  parse: backgroundSize_export_parse,
  definition: backgroundSize_export_definition
}], ["background-repeat", {
  parse: backgroundRepeat_export_parse,
  definition: backgroundRepeat_export_definition
}], ["background-origin", {
  parse: backgroundOrigin_export_parse,
  definition: backgroundOrigin_export_definition
}], ["background-clip", {
  parse: backgroundClip_export_parse,
  definition: backgroundClip_export_definition
}], ["background-attachment", {
  parse: backgroundAttachment_export_parse,
  definition: backgroundAttachment_export_definition
}], ["background-color", {
  parse: backgroundColor_export_parse,
  definition: backgroundColor_export_definition
}]]);
background_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  } else if (external_dependency_parsers_0.hasCalcFunc(v)) {
    v = external_dependency_parsers_0.resolveCalc(v);
  }
  if (!external_dependency_parsers_0.isValidPropertyValue(background_local_var_property, v)) {
    return;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const bgValues = [];
  const l = values.length;
  for (let i = 0; i < l; i++) {
    let bg = {
      "background-image": background_local_var_initialValues.get("background-image"),
      "background-position": background_local_var_initialValues.get("background-position"),
      "background-size": background_local_var_initialValues.get("background-size"),
      "background-repeat": background_local_var_initialValues.get("background-repeat"),
      "background-origin": background_local_var_initialValues.get("background-origin"),
      "background-clip": background_local_var_initialValues.get("background-clip"),
      "background-attachment": background_local_var_initialValues.get("background-attachment"),
      "background-color": background_local_var_initialValues.get("background-color")
    };
    if (l > 1 && i !== l - 1) {
      bg = {
        "background-image": background_local_var_initialValues.get("background-image"),
        "background-position": background_local_var_initialValues.get("background-position"),
        "background-size": background_local_var_initialValues.get("background-size"),
        "background-repeat": background_local_var_initialValues.get("background-repeat"),
        "background-origin": background_local_var_initialValues.get("background-origin"),
        "background-clip": background_local_var_initialValues.get("background-clip"),
        "background-attachment": background_local_var_initialValues.get("background-attachment")
      };
    }
    const bgPosition = [];
    const bgSize = [];
    const bgRepeat = [];
    const bgBox = [];
    const bgParts = external_dependency_parsers_0.splitValue(values[i], {
      delimiter: "/"
    });
    if (!bgParts.length || bgParts.length > 2) {
      return;
    }
    const [bgPart1, bgPart2 = ""] = bgParts;
    const parts1 = external_dependency_parsers_0.splitValue(bgPart1);
    for (const part of parts1) {
      let partValid = false;
      for (const [longhand, value] of background_export_shorthandFor) {
        if (external_dependency_parsers_0.isValidPropertyValue(longhand, part)) {
          partValid = true;
          switch (longhand) {
            case "background-clip":
            case "background-origin":
              {
                const parsedValue = value.parse(part, {
                  globalObject
                });
                if (parsedValue) {
                  bgBox.push(parsedValue);
                }
                break;
              }
            case "background-color":
              {
                if (i !== values.length - 1) {
                  return;
                }
                const parsedValue = value.parse(part, {
                  globalObject
                });
                if (parsedValue) {
                  bg[longhand] = parsedValue;
                }
                break;
              }
            case "background-position":
              {
                const parsedValue = value.parse(part, {
                  globalObject
                });
                if (parsedValue) {
                  bgPosition.push(parsedValue);
                }
                break;
              }
            case "background-repeat":
              {
                const parsedValue = value.parse(part, {
                  globalObject
                });
                if (parsedValue) {
                  bgRepeat.push(parsedValue);
                }
                break;
              }
            case "background-size":
              {
                break;
              }
            default:
              {
                const parsedValue = value.parse(part, {
                  globalObject
                });
                if (parsedValue) {
                  bg[longhand] = parsedValue;
                }
              }
          }
        }
      }
      if (!partValid) {
        return;
      }
    }
    if (bgPart2) {
      const parts2 = external_dependency_parsers_0.splitValue(bgPart2);
      for (const part of parts2) {
        let partValid = false;
        for (const [longhand, value] of background_export_shorthandFor) {
          if (external_dependency_parsers_0.isValidPropertyValue(longhand, part)) {
            partValid = true;
            switch (longhand) {
              case "background-clip":
              case "background-origin":
                {
                  const parsedValue = value.parse(part, {
                    globalObject
                  });
                  if (parsedValue) {
                    bgBox.push(parsedValue);
                  }
                  break;
                }
              case "background-color":
                {
                  if (i !== l - 1) {
                    return;
                  }
                  const parsedValue = value.parse(part, {
                    globalObject
                  });
                  if (parsedValue) {
                    bg[longhand] = parsedValue;
                  }
                  break;
                }
              case "background-position":
                {
                  break;
                }
              case "background-repeat":
                {
                  const parsedValue = value.parse(part, {
                    globalObject
                  });
                  if (parsedValue) {
                    bgRepeat.push(parsedValue);
                  }
                  break;
                }
              case "background-size":
                {
                  const parsedValue = value.parse(part, {
                    globalObject
                  });
                  if (parsedValue) {
                    bgSize.push(parsedValue);
                  }
                  break;
                }
              default:
                {
                  const parsedValue = value.parse(part, {
                    globalObject
                  });
                  if (parsedValue) {
                    bg[longhand] = parsedValue;
                  }
                }
            }
          }
        }
        if (!partValid) {
          return;
        }
      }
    }
    if (bgPosition.length) {
      const {
        parse: parser
      } = background_export_shorthandFor.get("background-position");
      const value = parser(bgPosition.join(" "), {
        globalObject
      });
      if (value) {
        bg["background-position"] = value;
      }
    }
    if (bgSize.length) {
      const {
        parse: parser
      } = background_export_shorthandFor.get("background-size");
      const value = parser(bgSize.join(" "), {
        globalObject
      });
      if (value) {
        bg["background-size"] = value;
      }
    }
    if (bgRepeat.length) {
      const {
        parse: parser
      } = background_export_shorthandFor.get("background-repeat");
      const value = parser(bgRepeat.join(" "), {
        globalObject
      });
      if (value) {
        bg["background-repeat"] = value;
      }
    }
    if (bgBox.length) {
      switch (bgBox.length) {
        case 1:
          {
            const [value] = bgBox;
            bg["background-origin"] = value;
            bg["background-clip"] = value;
            break;
          }
        case 2:
          {
            const [value1, value2] = bgBox;
            bg["background-origin"] = value1;
            bg["background-clip"] = value2;
            break;
          }
        default:
          {
            return;
          }
      }
    }
    bgValues.push(bg);
  }
  return bgValues;
};
background_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (v === "" || external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of background_export_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty(background_local_var_property, v);
    } else {
      const bgValues = background_export_parse(v, {
        globalObject: this._global
      });
      if (!Array.isArray(bgValues)) {
        return;
      }
      const bgMap = new Map([["background-image", []], ["background-position", []], ["background-size", []], ["background-repeat", []], ["background-origin", []], ["background-clip", []], ["background-attachment", []], ["background-color", []]]);
      const backgrounds = [];
      for (const bgValue of bgValues) {
        const bg = [];
        for (const [longhand, value] of Object.entries(bgValue)) {
          if (value) {
            const arr = bgMap.get(longhand);
            arr.push(value);
            bgMap.set(longhand, arr);
            if (value !== background_local_var_initialValues.get(longhand)) {
              if (longhand === "background-size") {
                bg.push(`/ ${value}`);
              } else {
                bg.push(value);
              }
            } else if (longhand === "background-image") {
              if (v === "none") {
                bg.push(value);
              }
            } else if (longhand === "background-color") {
              if (v === "transparent") {
                bg.push(value);
              }
            }
          }
        }
        backgrounds.push(bg.join(" "));
      }
      const priority = this._priorities.get(background_local_var_property) ?? "";
      for (const [longhand, value] of bgMap) {
        this._setProperty(longhand, value.join(", "), priority);
      }
      this._setProperty(background_local_var_property, backgrounds.join(", "), priority);
    }
  },
  get() {
    const v = this.getPropertyValue(background_local_var_property);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      return v;
    }
    const bgMap = new Map();
    let l = 0;
    for (const [longhand] of background_export_shorthandFor) {
      const val = this.getPropertyValue(longhand);
      if (longhand === "background-image") {
        if (val === "none" && v === "none" && this.getPropertyValue("background-color") === "transparent") {
          return val;
        }
        if (val !== background_local_var_initialValues.get(longhand)) {
          const imgValues = external_dependency_parsers_0.splitValue(val, {
            delimiter: ","
          });
          l = imgValues.length;
          bgMap.set(longhand, imgValues);
        }
      } else if (longhand === "background-color") {
        if (val !== background_local_var_initialValues.get(longhand) || v.includes(val)) {
          bgMap.set(longhand, [val]);
        }
      } else if (val !== background_local_var_initialValues.get(longhand)) {
        bgMap.set(longhand, external_dependency_parsers_0.splitValue(val, {
          delimiter: ","
        }));
      }
    }
    if (l === 0) {
      const [background] = bgMap.get("background-color");
      if (background) {
        return background;
      }
      return "";
    }
    const bgValues = [];
    for (let i = 0; i < l; i++) {
      bgValues[i] = [];
    }
    for (const [longhand, values] of bgMap) {
      for (let i = 0; i < l; i++) {
        switch (longhand) {
          case "background-color":
            {
              if (i === l - 1) {
                const value = values[0];
                if (external_dependency_parsers_0.hasVarFunc(value)) {
                  return "";
                }
                if (value && value !== background_local_var_initialValues.get(longhand)) {
                  const bgValue = bgValues[i];
                  bgValue.push(value);
                }
              }
              break;
            }
          case "background-size":
            {
              const value = values[i];
              if (external_dependency_parsers_0.hasVarFunc(value)) {
                return "";
              }
              if (value && value !== background_local_var_initialValues.get(longhand)) {
                const bgValue = bgValues[i];
                bgValue.push(`/ ${value}`);
              }
              break;
            }
          default:
            {
              const value = values[i];
              if (external_dependency_parsers_0.hasVarFunc(value)) {
                return "";
              }
              if (value && value !== background_local_var_initialValues.get(longhand)) {
                const bgValue = bgValues[i];
                bgValue.push(value);
              }
            }
        }
      }
    }
    const backgrounds = [];
    for (const bgValue of bgValues) {
      backgrounds.push(bgValue.join(" "));
    }
    return backgrounds.join(", ");
  },
  enumerable: true,
  configurable: true
};
var borderTopWidth_export_parse, borderTopWidth_export_definition;
const borderTopWidth_local_var_property = "border-top-width";
const borderTopWidth_local_var_lineShorthand = "border-width";
const borderTopWidth_local_var_positionShorthand = "border-top";
const borderTopWidth_local_var_shorthand = "border";
borderTopWidth_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderTopWidth_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLength(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderTopWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderTopWidth_local_var_property, v, "");
    } else {
      const val = borderTopWidth_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderTopWidth_local_var_shorthand);
        const linePriority = this._priorities.get(borderTopWidth_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderTopWidth_local_var_positionShorthand);
        let priority = this._priorities.get(borderTopWidth_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderTopWidth_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderTopWidth_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderRightWidth_export_parse, borderRightWidth_export_definition;
const borderRightWidth_local_var_property = "border-right-width";
const borderRightWidth_local_var_lineShorthand = "border-width";
const borderRightWidth_local_var_positionShorthand = "border-right";
const borderRightWidth_local_var_shorthand = "border";
borderRightWidth_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderRightWidth_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLength(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderRightWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderRightWidth_local_var_property, v, "");
    } else {
      const val = borderRightWidth_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderRightWidth_local_var_shorthand);
        const linePriority = this._priorities.get(borderRightWidth_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderRightWidth_local_var_positionShorthand);
        let priority = this._priorities.get(borderRightWidth_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderRightWidth_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderRightWidth_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderBottomWidth_export_parse, borderBottomWidth_export_definition;
const borderBottomWidth_local_var_property = "border-bottom-width";
const borderBottomWidth_local_var_lineShorthand = "border-width";
const borderBottomWidth_local_var_positionShorthand = "border-bottom";
const borderBottomWidth_local_var_shorthand = "border";
borderBottomWidth_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderBottomWidth_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLength(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderBottomWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderBottomWidth_local_var_property, v, "");
    } else {
      const val = borderBottomWidth_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderBottomWidth_local_var_shorthand);
        const linePriority = this._priorities.get(borderBottomWidth_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderBottomWidth_local_var_positionShorthand);
        let priority = this._priorities.get(borderBottomWidth_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderBottomWidth_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderBottomWidth_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderLeftWidth_export_parse, borderLeftWidth_export_definition;
const borderLeftWidth_local_var_property = "border-left-width";
const borderLeftWidth_local_var_lineShorthand = "border-width";
const borderLeftWidth_local_var_positionShorthand = "border-left";
const borderLeftWidth_local_var_shorthand = "border";
borderLeftWidth_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderLeftWidth_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLength(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderLeftWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderLeftWidth_local_var_property, v, "");
    } else {
      const val = borderLeftWidth_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderLeftWidth_local_var_shorthand);
        const linePriority = this._priorities.get(borderLeftWidth_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderLeftWidth_local_var_positionShorthand);
        let priority = this._priorities.get(borderLeftWidth_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderLeftWidth_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderLeftWidth_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderWidth_export_shorthandFor, borderWidth_export_parse, borderWidth_export_definition;
const borderWidth_local_var_property = "border-width";
const borderWidth_local_var_shorthand = "border";
borderWidth_export_shorthandFor = new Map([["border-top-width", {
  parse: borderTopWidth_export_parse,
  definition: borderTopWidth_export_definition
}], ["border-right-width", {
  parse: borderRightWidth_export_parse,
  definition: borderRightWidth_export_definition
}], ["border-bottom-width", {
  parse: borderBottomWidth_export_parse,
  definition: borderBottomWidth_export_definition
}], ["border-left-width", {
  parse: borderLeftWidth_export_parse,
  definition: borderLeftWidth_export_definition
}]]);
borderWidth_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.parsePropertyValue(borderWidth_local_var_property, v, {
    globalObject,
    inArray: true
  });
  const parsedValues = [];
  if (Array.isArray(values) && values.length) {
    if (values.length > 4) {
      return;
    }
    for (const value of values) {
      const {
        isNumber,
        name,
        type,
        value: itemValue
      } = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber) {
              return;
            }
            parsedValues.push(`${name}(${itemValue})`);
            break;
          }
        case "GlobalKeyword":
          {
            if (values.length !== 1) {
              return;
            }
            return name;
          }
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            const parsedValue = external_dependency_parsers_0.parseLength([value], {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.push(parsedValue);
          }
      }
    }
  } else if (typeof values === "string") {
    parsedValues.push(values);
  }
  if (parsedValues.length) {
    switch (parsedValues.length) {
      case 1:
        {
          return parsedValues;
        }
      case 2:
        {
          const [val1, val2] = parsedValues;
          if (val1 === val2) {
            return [val1];
          }
          return parsedValues;
        }
      case 3:
        {
          const [val1, val2, val3] = parsedValues;
          if (val1 === val3) {
            if (val1 === val2) {
              return [val1];
            }
            return [val1, val2];
          }
          return parsedValues;
        }
      case 4:
        {
          const [val1, val2, val3, val4] = parsedValues;
          if (val2 === val4) {
            if (val1 === val3) {
              if (val1 === val2) {
                return [val1];
              }
              return [val1, val2];
            }
            return [val1, val2, val3];
          }
          return parsedValues;
        }
      default:
    }
  }
};
borderWidth_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderWidth_local_var_property, v, "");
    } else {
      const val = borderWidth_export_parse(v, {
        globalObject: this._global
      });
      if (Array.isArray(val) || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderWidth_local_var_shorthand);
        const prior = this._priorities.get(borderWidth_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderWidth_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderWidth_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderTopStyle_export_parse, borderTopStyle_export_definition;
const borderTopStyle_local_var_property = "border-top-style";
const borderTopStyle_local_var_lineShorthand = "border-style";
const borderTopStyle_local_var_positionShorthand = "border-top";
const borderTopStyle_local_var_shorthand = "border";
borderTopStyle_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderTopStyle_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderTopStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderTopStyle_local_var_property, v, "");
    } else {
      const val = borderTopStyle_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderTopStyle_local_var_shorthand);
        const linePriority = this._priorities.get(borderTopStyle_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderTopStyle_local_var_positionShorthand);
        let priority = this._priorities.get(borderTopStyle_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderTopStyle_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderTopStyle_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderRightStyle_export_parse, borderRightStyle_export_definition;
const borderRightStyle_local_var_property = "border-right-style";
const borderRightStyle_local_var_lineShorthand = "border-style";
const borderRightStyle_local_var_positionShorthand = "border-right";
const borderRightStyle_local_var_shorthand = "border";
borderRightStyle_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderRightStyle_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderRightStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderRightStyle_local_var_property, v, "");
    } else {
      const val = borderRightStyle_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderRightStyle_local_var_shorthand);
        const linePriority = this._priorities.get(borderRightStyle_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderRightStyle_local_var_positionShorthand);
        let priority = this._priorities.get(borderRightStyle_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderRightStyle_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderRightStyle_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderBottomStyle_export_parse, borderBottomStyle_export_definition;
const borderBottomStyle_local_var_property = "border-bottom-style";
const borderBottomStyle_local_var_lineShorthand = "border-style";
const borderBottomStyle_local_var_positionShorthand = "border-bottom";
const borderBottomStyle_local_var_shorthand = "border";
borderBottomStyle_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderBottomStyle_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderBottomStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderBottomStyle_local_var_property, v, "");
    } else {
      const val = borderBottomStyle_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderBottomStyle_local_var_shorthand);
        const linePriority = this._priorities.get(borderBottomStyle_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderBottomStyle_local_var_positionShorthand);
        let priority = this._priorities.get(borderBottomStyle_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderBottomStyle_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderBottomStyle_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderLeftStyle_export_parse, borderLeftStyle_export_definition;
const borderLeftStyle_local_var_property = "border-left-style";
const borderLeftStyle_local_var_lineShorthand = "border-style";
const borderLeftStyle_local_var_positionShorthand = "border-left";
const borderLeftStyle_local_var_shorthand = "border";
borderLeftStyle_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderLeftStyle_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderLeftStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderLeftStyle_local_var_property, v, "");
    } else {
      const val = borderLeftStyle_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderLeftStyle_local_var_shorthand);
        const linePriority = this._priorities.get(borderLeftStyle_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderLeftStyle_local_var_positionShorthand);
        let priority = this._priorities.get(borderLeftStyle_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderLeftStyle_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderLeftStyle_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderStyle_export_shorthandFor, borderStyle_export_parse, borderStyle_export_definition;
const borderStyle_local_var_property = "border-style";
const borderStyle_local_var_shorthand = "border";
borderStyle_export_shorthandFor = new Map([["border-top-style", {
  parse: borderTopStyle_export_parse,
  definition: borderTopStyle_export_definition
}], ["border-right-style", {
  parse: borderRightStyle_export_parse,
  definition: borderRightStyle_export_definition
}], ["border-bottom-style", {
  parse: borderBottomStyle_export_parse,
  definition: borderBottomStyle_export_definition
}], ["border-left-style", {
  parse: borderLeftStyle_export_parse,
  definition: borderLeftStyle_export_definition
}]]);
borderStyle_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.parsePropertyValue(borderStyle_local_var_property, v, {
    globalObject,
    inArray: true
  });
  const parsedValues = [];
  if (Array.isArray(values) && values.length) {
    if (values.length > 4) {
      return;
    }
    for (const value of values) {
      const {
        name,
        type
      } = value;
      switch (type) {
        case "GlobalKeyword":
          {
            if (values.length !== 1) {
              return;
            }
            return name;
          }
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            return;
          }
      }
    }
  } else if (typeof values === "string") {
    parsedValues.push(values);
  }
  if (parsedValues.length) {
    switch (parsedValues.length) {
      case 1:
        {
          return parsedValues;
        }
      case 2:
        {
          const [val1, val2] = parsedValues;
          if (val1 === val2) {
            return [val1];
          }
          return parsedValues;
        }
      case 3:
        {
          const [val1, val2, val3] = parsedValues;
          if (val1 === val3) {
            if (val1 === val2) {
              return [val1];
            }
            return [val1, val2];
          }
          return parsedValues;
        }
      case 4:
        {
          const [val1, val2, val3, val4] = parsedValues;
          if (val2 === val4) {
            if (val1 === val3) {
              if (val1 === val2) {
                return [val1];
              }
              return [val1, val2];
            }
            return [val1, val2, val3];
          }
          return parsedValues;
        }
      default:
    }
  }
};
borderStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderStyle_local_var_property, v, "");
    } else {
      const val = borderStyle_export_parse(v, {
        globalObject: this._global
      });
      if (Array.isArray(val) || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderStyle_local_var_shorthand);
        const prior = this._priorities.get(borderStyle_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderStyle_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderStyle_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderTopColor_export_parse, borderTopColor_export_definition;
const borderTopColor_local_var_property = "border-top-color";
const borderTopColor_local_var_lineShorthand = "border-color";
const borderTopColor_local_var_positionShorthand = "border-top";
const borderTopColor_local_var_shorthand = "border";
borderTopColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderTopColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderTopColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderTopColor_local_var_property, v, "");
    } else {
      const val = borderTopColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderTopColor_local_var_shorthand);
        const linePriority = this._priorities.get(borderTopColor_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderTopColor_local_var_positionShorthand);
        let priority = this._priorities.get(borderTopColor_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderTopColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderTopColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderRightColor_export_parse, borderRightColor_export_definition;
const borderRightColor_local_var_property = "border-right-color";
const borderRightColor_local_var_lineShorthand = "border-color";
const borderRightColor_local_var_positionShorthand = "border-right";
const borderRightColor_local_var_shorthand = "border";
borderRightColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderRightColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderRightColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderRightColor_local_var_property, v, "");
    } else {
      const val = borderRightColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderRightColor_local_var_shorthand);
        const linePriority = this._priorities.get(borderRightColor_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderRightColor_local_var_positionShorthand);
        let priority = this._priorities.get(borderRightColor_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderRightColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderRightColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderBottomColor_export_parse, borderBottomColor_export_definition;
const borderBottomColor_local_var_property = "border-bottom-color";
const borderBottomColor_local_var_lineShorthand = "border-color";
const borderBottomColor_local_var_positionShorthand = "border-bottom";
const borderBottomColor_local_var_shorthand = "border";
borderBottomColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderBottomColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderBottomColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderBottomColor_local_var_property, v, "");
    } else {
      const val = borderBottomColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderBottomColor_local_var_shorthand);
        const linePriority = this._priorities.get(borderBottomColor_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderBottomColor_local_var_positionShorthand);
        let priority = this._priorities.get(borderBottomColor_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderBottomColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderBottomColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderLeftColor_export_parse, borderLeftColor_export_definition;
const borderLeftColor_local_var_property = "border-left-color";
const borderLeftColor_local_var_lineShorthand = "border-color";
const borderLeftColor_local_var_positionShorthand = "border-left";
const borderLeftColor_local_var_shorthand = "border";
borderLeftColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderLeftColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderLeftColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderLeftColor_local_var_property, v, "");
    } else {
      const val = borderLeftColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderLeftColor_local_var_shorthand);
        const linePriority = this._priorities.get(borderLeftColor_local_var_lineShorthand);
        const positionPriority = this._priorities.get(borderLeftColor_local_var_positionShorthand);
        let priority = this._priorities.get(borderLeftColor_local_var_property) ?? "";
        if ((shorthandPriority || linePriority || positionPriority) && priority) {
          priority = "";
        }
        this._borderSetter(borderLeftColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderLeftColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderColor_export_shorthandFor, borderColor_export_parse, borderColor_export_definition;
const borderColor_local_var_property = "border-color";
const borderColor_local_var_shorthand = "border";
borderColor_export_shorthandFor = new Map([["border-top-color", {
  parse: borderTopColor_export_parse,
  definition: borderTopColor_export_definition
}], ["border-right-color", {
  parse: borderRightColor_export_parse,
  definition: borderRightColor_export_definition
}], ["border-bottom-color", {
  parse: borderBottomColor_export_parse,
  definition: borderBottomColor_export_definition
}], ["border-left-color", {
  parse: borderLeftColor_export_parse,
  definition: borderLeftColor_export_definition
}]]);
borderColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.parsePropertyValue(borderColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  const parsedValues = [];
  if (Array.isArray(values) && values.length) {
    if (values.length > 4) {
      return;
    }
    for (const value of values) {
      const {
        name,
        type
      } = value;
      switch (type) {
        case "GlobalKeyword":
          {
            if (values.length !== 1) {
              return;
            }
            return name;
          }
        default:
          {
            const parsedValue = external_dependency_parsers_0.parseColor([value]);
            if (!parsedValue) {
              return;
            }
            parsedValues.push(parsedValue);
          }
      }
    }
  } else if (typeof values === "string") {
    parsedValues.push(values);
  }
  if (parsedValues.length) {
    switch (parsedValues.length) {
      case 1:
        {
          return parsedValues;
        }
      case 2:
        {
          const [val1, val2] = parsedValues;
          if (val1 === val2) {
            return [val1];
          }
          return parsedValues;
        }
      case 3:
        {
          const [val1, val2, val3] = parsedValues;
          if (val1 === val3) {
            if (val1 === val2) {
              return [val1];
            }
            return [val1, val2];
          }
          return parsedValues;
        }
      case 4:
        {
          const [val1, val2, val3, val4] = parsedValues;
          if (val2 === val4) {
            if (val1 === val3) {
              if (val1 === val2) {
                return [val1];
              }
              return [val1, val2];
            }
            return [val1, val2, val3];
          }
          return parsedValues;
        }
      default:
    }
  }
};
borderColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderColor_local_var_property, v, "");
    } else {
      const val = borderColor_export_parse(v, {
        globalObject: this._global
      });
      if (Array.isArray(val) || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderColor_local_var_shorthand);
        const prior = this._priorities.get(borderColor_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderTop_export_initialValues, borderTop_export_shorthandFor, borderTop_export_parse, borderTop_export_definition;
const borderTop_local_var_property = "border-top";
const borderTop_local_var_shorthand = "border";
borderTop_export_initialValues = new Map([["border-top-width", "medium"], ["border-top-style", "none"], ["border-top-color", "currentcolor"]]);
borderTop_export_shorthandFor = new Map([["border-top-width", {
  parse: borderTopWidth_export_parse,
  definition: borderTopWidth_export_definition
}], ["border-top-style", {
  parse: borderTopStyle_export_parse,
  definition: borderTopStyle_export_definition
}], ["border-top-color", {
  parse: borderTopColor_export_parse,
  definition: borderTopColor_export_definition
}]]);
borderTop_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v);
  const parsedValues = new Map();
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(borderTop_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        isNumber,
        name,
        type,
        value: itemValue
      }] = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber || parsedValues.has("border-top-width")) {
              return;
            }
            parsedValues.set("border-top-width", `${name}(${itemValue})`);
            break;
          }
        case "Dimension":
        case "Number":
          {
            if (parsedValues.has("border-top-width")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseLength(value, {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-top-width", parsedValue);
            break;
          }
        case "Function":
          {
            if (parsedValues.has("border-top-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-top-color", parsedValue);
            break;
          }
        case "GlobalKeyword":
          {
            return name;
          }
        case "Hash":
          {
            if (parsedValues.has("border-top-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(`#${itemValue}`);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-top-color", parsedValue);
            break;
          }
        case "Identifier":
          {
            if (external_dependency_parsers_0.isValidPropertyValue("border-top-width", name)) {
              if (parsedValues.has("border-top-width")) {
                return;
              }
              parsedValues.set("border-top-width", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-top-style", name)) {
              if (parsedValues.has("border-top-style")) {
                return;
              }
              parsedValues.set("border-top-style", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-top-color", name)) {
              if (parsedValues.has("border-top-color")) {
                return;
              }
              parsedValues.set("border-top-color", name);
              break;
            }
            return;
          }
        default:
          {
            return;
          }
      }
    } else {
      return;
    }
  }
  if (parsedValues.size) {
    const keys = borderTop_export_shorthandFor.keys();
    const obj = {
      "border-top-width": "medium"
    };
    for (const key of keys) {
      if (parsedValues.has(key)) {
        const parsedValue = parsedValues.get(key);
        if (parsedValue !== borderTop_export_initialValues.get(key)) {
          obj[key] = parsedValues.get(key);
          if (obj["border-top-width"] && obj["border-top-width"] === "medium") {
            delete obj["border-top-width"];
          }
        }
      }
    }
    return obj;
  }
};
borderTop_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderTop_local_var_property, v, "");
    } else {
      const val = borderTop_export_parse(v, {
        globalObject: this._global
      });
      if (val || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderTop_local_var_shorthand);
        const prior = this._priorities.get(borderTop_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderTop_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderTop_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderRight_export_initialValues, borderRight_export_shorthandFor, borderRight_export_parse, borderRight_export_definition;
const borderRight_local_var_property = "border-right";
const borderRight_local_var_shorthand = "border";
borderRight_export_initialValues = new Map([["border-right-width", "medium"], ["border-right-style", "none"], ["border-right-color", "currentcolor"]]);
borderRight_export_shorthandFor = new Map([["border-right-width", {
  parse: borderRightWidth_export_parse,
  definition: borderRightWidth_export_definition
}], ["border-right-style", {
  parse: borderRightStyle_export_parse,
  definition: borderRightStyle_export_definition
}], ["border-right-color", {
  parse: borderRightColor_export_parse,
  definition: borderRightColor_export_definition
}]]);
borderRight_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v);
  const parsedValues = new Map();
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(borderRight_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        isNumber,
        name,
        type,
        value: itemValue
      }] = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber || parsedValues.has("border-right-width")) {
              return;
            }
            parsedValues.set("border-right-width", `${name}(${itemValue})`);
            break;
          }
        case "Dimension":
        case "Number":
          {
            if (parsedValues.has("border-right-width")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseLength(value, {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-right-width", parsedValue);
            break;
          }
        case "Function":
          {
            if (parsedValues.has("border-right-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-right-color", parsedValue);
            break;
          }
        case "GlobalKeyword":
          {
            return name;
          }
        case "Hash":
          {
            if (parsedValues.has("border-right-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(`#${itemValue}`);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-right-color", parsedValue);
            break;
          }
        case "Identifier":
          {
            if (external_dependency_parsers_0.isValidPropertyValue("border-right-width", name)) {
              if (parsedValues.has("border-right-width")) {
                return;
              }
              parsedValues.set("border-right-width", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-right-style", name)) {
              if (parsedValues.has("border-right-style")) {
                return;
              }
              parsedValues.set("border-right-style", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-right-color", name)) {
              if (parsedValues.has("border-right-color")) {
                return;
              }
              parsedValues.set("border-right-color", name);
              break;
            }
            return;
          }
        default:
          {
            return;
          }
      }
    } else {
      return;
    }
  }
  if (parsedValues.size) {
    const keys = borderRight_export_shorthandFor.keys();
    const obj = {
      "border-right-width": "medium"
    };
    for (const key of keys) {
      if (parsedValues.has(key)) {
        const parsedValue = parsedValues.get(key);
        if (parsedValue !== borderRight_export_initialValues.get(key)) {
          obj[key] = parsedValues.get(key);
          if (obj["border-right-width"] && obj["border-right-width"] === "medium") {
            delete obj["border-right-width"];
          }
        }
      }
    }
    return obj;
  }
};
borderRight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderRight_local_var_property, v, "");
    } else {
      const val = borderRight_export_parse(v, {
        globalObject: this._global
      });
      if (val || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderRight_local_var_shorthand);
        const prior = this._priorities.get(borderRight_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderRight_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderRight_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderBottom_export_initialValues, borderBottom_export_shorthandFor, borderBottom_export_parse, borderBottom_export_definition;
const borderBottom_local_var_property = "border-bottom";
const borderBottom_local_var_shorthand = "border";
borderBottom_export_initialValues = new Map([["border-bottom-width", "medium"], ["border-bottom-style", "none"], ["border-bottom-color", "currentcolor"]]);
borderBottom_export_shorthandFor = new Map([["border-bottom-width", {
  parse: borderBottomWidth_export_parse,
  definition: borderBottomWidth_export_definition
}], ["border-bottom-style", {
  parse: borderBottomStyle_export_parse,
  definition: borderBottomStyle_export_definition
}], ["border-bottom-color", {
  parse: borderBottomColor_export_parse,
  definition: borderBottomColor_export_definition
}]]);
borderBottom_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v);
  const parsedValues = new Map();
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(borderBottom_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        isNumber,
        name,
        type,
        value: itemValue
      }] = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber || parsedValues.has("border-bottom-width")) {
              return;
            }
            parsedValues.set("border-bottom-width", `${name}(${itemValue})`);
            break;
          }
        case "Dimension":
        case "Number":
          {
            if (parsedValues.has("border-bottom-width")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseLength(value, {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-bottom-width", parsedValue);
            break;
          }
        case "Function":
          {
            if (parsedValues.has("border-bottom-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-bottom-color", parsedValue);
            break;
          }
        case "GlobalKeyword":
          {
            return name;
          }
        case "Hash":
          {
            if (parsedValues.has("border-bottom-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(`#${itemValue}`);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-bottom-color", parsedValue);
            break;
          }
        case "Identifier":
          {
            if (external_dependency_parsers_0.isValidPropertyValue("border-bottom-width", name)) {
              if (parsedValues.has("border-bottom-width")) {
                return;
              }
              parsedValues.set("border-bottom-width", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-bottom-style", name)) {
              if (parsedValues.has("border-bottom-style")) {
                return;
              }
              parsedValues.set("border-bottom-style", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-bottom-color", name)) {
              if (parsedValues.has("border-bottom-color")) {
                return;
              }
              parsedValues.set("border-bottom-color", name);
              break;
            }
            return;
          }
        default:
          {
            return;
          }
      }
    } else {
      return;
    }
  }
  if (parsedValues.size) {
    const keys = borderBottom_export_shorthandFor.keys();
    const obj = {
      "border-bottom-width": "medium"
    };
    for (const key of keys) {
      if (parsedValues.has(key)) {
        const parsedValue = parsedValues.get(key);
        if (parsedValue !== borderBottom_export_initialValues.get(key)) {
          obj[key] = parsedValues.get(key);
          if (obj["border-bottom-width"] && obj["border-bottom-width"] === "medium") {
            delete obj["border-bottom-width"];
          }
        }
      }
    }
    return obj;
  }
};
borderBottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderBottom_local_var_property, v, "");
    } else {
      const val = borderBottom_export_parse(v, {
        globalObject: this._global
      });
      if (val || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderBottom_local_var_shorthand);
        const prior = this._priorities.get(borderBottom_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderBottom_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderBottom_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderLeft_export_initialValues, borderLeft_export_shorthandFor, borderLeft_export_parse, borderLeft_export_definition;
const borderLeft_local_var_property = "border-left";
const borderLeft_local_var_shorthand = "border";
borderLeft_export_initialValues = new Map([["border-left-width", "medium"], ["border-left-style", "none"], ["border-left-color", "currentcolor"]]);
borderLeft_export_shorthandFor = new Map([["border-left-width", {
  parse: borderLeftWidth_export_parse,
  definition: borderLeftWidth_export_definition
}], ["border-left-style", {
  parse: borderLeftStyle_export_parse,
  definition: borderLeftStyle_export_definition
}], ["border-left-color", {
  parse: borderLeftColor_export_parse,
  definition: borderLeftColor_export_definition
}]]);
borderLeft_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v);
  const parsedValues = new Map();
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(borderLeft_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        isNumber,
        name,
        type,
        value: itemValue
      }] = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber || parsedValues.has("border-left-width")) {
              return;
            }
            parsedValues.set("border-left-width", `${name}(${itemValue})`);
            break;
          }
        case "Dimension":
        case "Number":
          {
            if (parsedValues.has("border-left-width")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseLength(value, {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-left-width", parsedValue);
            break;
          }
        case "Function":
          {
            if (parsedValues.has("border-left-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-left-color", parsedValue);
            break;
          }
        case "GlobalKeyword":
          {
            return name;
          }
        case "Hash":
          {
            if (parsedValues.has("border-left-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(`#${itemValue}`);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-left-color", parsedValue);
            break;
          }
        case "Identifier":
          {
            if (external_dependency_parsers_0.isValidPropertyValue("border-left-width", name)) {
              if (parsedValues.has("border-left-width")) {
                return;
              }
              parsedValues.set("border-left-width", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-left-style", name)) {
              if (parsedValues.has("border-left-style")) {
                return;
              }
              parsedValues.set("border-left-style", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-left-color", name)) {
              if (parsedValues.has("border-left-color")) {
                return;
              }
              parsedValues.set("border-left-color", name);
              break;
            }
            return;
          }
        default:
          {
            return;
          }
      }
    } else {
      return;
    }
  }
  if (parsedValues.size) {
    const keys = borderLeft_export_shorthandFor.keys();
    const obj = {
      "border-left-width": "medium"
    };
    for (const key of keys) {
      if (parsedValues.has(key)) {
        const parsedValue = parsedValues.get(key);
        if (parsedValue !== borderLeft_export_initialValues.get(key)) {
          obj[key] = parsedValues.get(key);
          if (obj["border-left-width"] && obj["border-left-width"] === "medium") {
            delete obj["border-left-width"];
          }
        }
      }
    }
    return obj;
  }
};
borderLeft_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(borderLeft_local_var_property, v, "");
    } else {
      const val = borderLeft_export_parse(v, {
        globalObject: this._global
      });
      if (val || typeof val === "string") {
        const shorthandPriority = this._priorities.get(borderLeft_local_var_shorthand);
        const prior = this._priorities.get(borderLeft_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._borderSetter(borderLeft_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderLeft_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var border_export_initialValues, border_export_shorthandFor, border_export_positionShorthandFor, border_export_parse, border_export_definition;
const border_local_var_property = "border";
border_export_initialValues = new Map([["border-width", "medium"], ["border-style", "none"], ["border-color", "currentcolor"]]);
border_export_shorthandFor = new Map([["border-width", {
  shorthandFor: borderWidth_export_shorthandFor,
  parse: borderWidth_export_parse,
  definition: borderWidth_export_definition
}], ["border-style", {
  shorthandFor: borderStyle_export_shorthandFor,
  parse: borderStyle_export_parse,
  definition: borderStyle_export_definition
}], ["border-color", {
  shorthandFor: borderColor_export_shorthandFor,
  parse: borderColor_export_parse,
  definition: borderColor_export_definition
}]]);
border_export_positionShorthandFor = new Map([["border-top", {
  initialValues: borderTop_export_initialValues,
  shorthandFor: borderTop_export_shorthandFor,
  parse: borderTop_export_parse,
  definition: borderTop_export_definition
}], ["border-right", {
  initialValues: borderRight_export_initialValues,
  shorthandFor: borderRight_export_shorthandFor,
  parse: borderRight_export_parse,
  definition: borderRight_export_definition
}], ["border-bottom", {
  initialValues: borderBottom_export_initialValues,
  shorthandFor: borderBottom_export_shorthandFor,
  parse: borderBottom_export_parse,
  definition: borderBottom_export_definition
}], ["border-left", {
  initialValues: borderLeft_export_initialValues,
  shorthandFor: borderLeft_export_shorthandFor,
  parse: borderLeft_export_parse,
  definition: borderLeft_export_definition
}]]);
border_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "" || external_dependency_parsers_0.hasVarFunc(v)) {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v);
  const parsedValues = new Map();
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(border_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        isNumber,
        name,
        type,
        value: itemValue
      }] = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber || parsedValues.has("border-width")) {
              return;
            }
            parsedValues.set("border-width", `${name}(${itemValue})`);
            break;
          }
        case "Dimension":
        case "Number":
          {
            if (parsedValues.has("border-width")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseLength(value, {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-width", parsedValue);
            break;
          }
        case "Function":
          {
            if (parsedValues.has("border-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(value);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-color", parsedValue);
            break;
          }
        case "GlobalKeyword":
          {
            return name;
          }
        case "Hash":
          {
            if (parsedValues.has("border-color")) {
              return;
            }
            const parsedValue = external_dependency_parsers_0.parseColor(`#${itemValue}`);
            if (!parsedValue) {
              return;
            }
            parsedValues.set("border-color", parsedValue);
            break;
          }
        case "Identifier":
          {
            if (external_dependency_parsers_0.isValidPropertyValue("border-width", name)) {
              if (parsedValues.has("border-width")) {
                return;
              }
              parsedValues.set("border-width", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-style", name)) {
              if (parsedValues.has("border-style")) {
                return;
              }
              parsedValues.set("border-style", name);
              break;
            } else if (external_dependency_parsers_0.isValidPropertyValue("border-color", name)) {
              if (parsedValues.has("border-color")) {
                return;
              }
              parsedValues.set("border-color", name);
              break;
            }
            return;
          }
        default:
          {
            return;
          }
      }
    } else {
      return;
    }
  }
  if (parsedValues.size) {
    const keys = border_export_shorthandFor.keys();
    const obj = {
      "border-width": "medium"
    };
    for (const key of keys) {
      if (parsedValues.has(key)) {
        const parsedValue = parsedValues.get(key);
        if (parsedValue !== border_export_initialValues.get(key)) {
          obj[key] = parsedValues.get(key);
          if (obj["border-width"] && obj["border-width"] === "medium") {
            delete obj["border-width"];
          }
        }
      }
    }
    return obj;
  }
};
border_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._borderSetter(border_local_var_property, v, "");
    } else {
      const val = border_export_parse(v, {
        globalObject: this._global
      });
      if (val || typeof val === "string") {
        const priority = this._priorities.get(border_local_var_property) ?? "";
        this._borderSetter(border_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(border_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderCollapse_export_parse, borderCollapse_export_definition;
const borderCollapse_local_var_property = "border-collapse";
borderCollapse_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderCollapse_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderCollapse_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(borderCollapse_local_var_property, v);
    } else {
      const val = borderCollapse_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(borderCollapse_local_var_property) ?? "";
        this._setProperty(borderCollapse_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderCollapse_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var borderSpacing_export_parse, borderSpacing_export_definition;
const borderSpacing_local_var_property = "border-spacing";
borderSpacing_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(borderSpacing_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length) {
    switch (value.length) {
      case 1:
        {
          const [part1] = value;
          const val1 = external_dependency_parsers_0.parseLength([part1]);
          if (val1) {
            return val1;
          }
          break;
        }
      case 2:
        {
          const [part1, part2] = value;
          const val1 = external_dependency_parsers_0.parseLength([part1]);
          const val2 = external_dependency_parsers_0.parseLength([part2]);
          if (val1 && val2) {
            return `${val1} ${val2}`;
          }
          break;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
borderSpacing_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(borderSpacing_local_var_property, v);
    } else {
      const val = borderSpacing_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(borderSpacing_local_var_property) ?? "";
        this._setProperty(borderSpacing_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(borderSpacing_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var bottom_export_parse, bottom_export_definition;
const bottom_local_var_property = "bottom";
bottom_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(bottom_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      raw,
      type
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (!isNumber) {
            return raw;
          }
          break;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
bottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(bottom_local_var_property, v);
    } else {
      const val = bottom_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(bottom_local_var_property) ?? "";
        this._setProperty(bottom_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(bottom_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var clear_export_parse, clear_export_definition;
const clear_local_var_property = "clear";
clear_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(clear_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
clear_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(clear_local_var_property, v);
    } else {
      const val = clear_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(clear_local_var_property) ?? "";
        this._setProperty(clear_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(clear_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var clip_export_parse, clip_export_definition;
// deprecated
// @see https://drafts.fxtf.org/css-masking/#clip-property

const clip_local_var_property = "clip";
clip_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(clip_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Function":
        {
          const values = external_dependency_parsers_0.splitValue(itemValue, {
            delimiter: ","
          });
          const parsedValues = [];
          for (const item of values) {
            const parsedValue = external_dependency_parsers_0.parseCSS(item, {
              context: "value"
            }, true);
            const val = external_dependency_parsers_0.parseLengthPercentage(parsedValue.children);
            if (val) {
              parsedValues.push(val);
            } else {
              return;
            }
          }
          return `${name}(${parsedValues.join(", ")})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
clip_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(clip_local_var_property, v);
    } else {
      const val = clip_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(clip_local_var_property) ?? "";
        this._setProperty(clip_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(clip_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var color_export_parse, color_export_definition;
const color_local_var_property = "color";
color_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(color_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
color_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(color_local_var_property, v);
    } else {
      const val = color_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(color_local_var_property) ?? "";
        this._setProperty(color_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(color_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var display_export_parse, display_export_definition;
const display_local_var_property = "display";

/* keywords */
const display_local_var_displayOutside = ["block", "inline", "run-in"];
const display_local_var_displayFlow = ["flow", "flow-root"];
display_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(display_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length) {
    switch (value.length) {
      case 1:
        {
          const [{
            name,
            type
          }] = value;
          switch (type) {
            case "GlobalKeyword":
              {
                return name;
              }
            case "Identifier":
              {
                if (name === "flow") {
                  return "block";
                }
                return name;
              }
            default:
          }
          break;
        }
      case 2:
        {
          const [part1, part2] = value;
          const val1 = part1.type === "Identifier" && part1.name;
          const val2 = part2.type === "Identifier" && part2.name;
          if (val1 && val2) {
            let outerValue = "";
            let innerValue = "";
            if (val1 === "list-item") {
              outerValue = val2;
              innerValue = val1;
            } else if (val2 === "list-item") {
              outerValue = val1;
              innerValue = val2;
            } else if (display_local_var_displayOutside.includes(val1)) {
              outerValue = val1;
              innerValue = val2;
            } else if (display_local_var_displayOutside.includes(val2)) {
              outerValue = val2;
              innerValue = val1;
            }
            if (innerValue === "list-item") {
              switch (outerValue) {
                case "block":
                case "flow":
                  {
                    return innerValue;
                  }
                case "flow-root":
                case "inline":
                case "run-in":
                  {
                    return `${outerValue} ${innerValue}`;
                  }
                default:
              }
            } else if (outerValue === "block") {
              switch (innerValue) {
                case "flow":
                  {
                    return outerValue;
                  }
                case "flow-root":
                case "flex":
                case "grid":
                case "table":
                  {
                    return innerValue;
                  }
                case "ruby":
                  {
                    return `${outerValue} ${innerValue}`;
                  }
                default:
              }
            } else if (outerValue === "inline") {
              switch (innerValue) {
                case "flow":
                  {
                    return outerValue;
                  }
                case "flow-root":
                  {
                    return `${outerValue}-block`;
                  }
                case "flex":
                case "grid":
                case "table":
                  {
                    return `${outerValue}-${innerValue}`;
                  }
                case "ruby":
                  {
                    return innerValue;
                  }
                default:
              }
            } else if (outerValue === "run-in") {
              switch (innerValue) {
                case "flow":
                  {
                    return outerValue;
                  }
                case "flow-root":
                case "flex":
                case "grid":
                case "table":
                case "ruby":
                  {
                    return `${outerValue} ${innerValue}`;
                  }
                default:
              }
            }
          }
          break;
        }
      case 3:
        {
          const [part1, part2, part3] = value;
          const val1 = part1.type === "Identifier" && part1.name;
          const val2 = part2.type === "Identifier" && part2.name;
          const val3 = part3.type === "Identifier" && part3.name;
          if (val1 && val2 && part3) {
            let outerValue = "";
            let flowValue = "";
            let listItemValue = "";
            if (val1 === "list-item") {
              listItemValue = val1;
              if (display_local_var_displayFlow.includes(val2)) {
                flowValue = val2;
                outerValue = val3;
              } else if (display_local_var_displayFlow.includes(val3)) {
                flowValue = val3;
                outerValue = val2;
              }
            } else if (val2 === "list-item") {
              listItemValue = val2;
              if (display_local_var_displayFlow.includes(val1)) {
                flowValue = val1;
                outerValue = val3;
              } else if (display_local_var_displayFlow.includes(val3)) {
                flowValue = val3;
                outerValue = val1;
              }
            } else if (val3 === "list-item") {
              listItemValue = val3;
              if (display_local_var_displayFlow.includes(val1)) {
                flowValue = val1;
                outerValue = val2;
              } else if (display_local_var_displayFlow.includes(val2)) {
                flowValue = val2;
                outerValue = val1;
              }
            }
            if (outerValue && flowValue && listItemValue) {
              switch (outerValue) {
                case "block":
                  {
                    if (flowValue === "flow") {
                      return listItemValue;
                    }
                    return `${flowValue} ${listItemValue}`;
                  }
                case "inline":
                case "run-in":
                  {
                    if (flowValue === "flow") {
                      return `${outerValue} ${listItemValue}`;
                    }
                    return `${outerValue} ${flowValue} ${listItemValue}`;
                  }
              }
            }
          }
          break;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
display_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(display_local_var_property, v);
    } else {
      const val = display_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(display_local_var_property) ?? "";
        this._setProperty(display_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(display_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var flexGrow_export_parse, flexGrow_export_definition;
const flexGrow_local_var_property = "flex-grow";
const flexGrow_local_var_shorthand = "flex";
flexGrow_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue("flex-grow", v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return `${name}(${itemValue})`;
          }
          break;
        }
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseNumber(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
flexGrow_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(flexGrow_local_var_shorthand, "");
      this._setProperty(flexGrow_local_var_property, v);
    } else {
      const val = flexGrow_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(flexGrow_local_var_shorthand);
        const prior = this._priorities.get(flexGrow_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._flexBoxSetter(flexGrow_local_var_property, val, priority, flexGrow_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(flexGrow_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var flexShrink_export_parse, flexShrink_export_definition;
const flexShrink_local_var_property = "flex-shrink";
const flexShrink_local_var_shorthand = "flex";
flexShrink_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(flexShrink_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return `${name}(${itemValue})`;
          }
          break;
        }
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseNumber(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
flexShrink_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(flexShrink_local_var_shorthand, "");
      this._setProperty(flexShrink_local_var_property, v);
    } else {
      const val = flexShrink_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(flexShrink_local_var_shorthand);
        const prior = this._priorities.get(flexShrink_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._flexBoxSetter(flexShrink_local_var_property, val, priority, flexShrink_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(flexShrink_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var flexBasis_export_parse, flexBasis_export_definition;
const flexBasis_local_var_property = "flex-basis";
const flexBasis_local_var_shorthand = "flex";
flexBasis_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(flexBasis_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
flexBasis_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(flexBasis_local_var_shorthand, "");
      this._setProperty(flexBasis_local_var_property, v);
    } else {
      const val = flexBasis_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(flexBasis_local_var_shorthand);
        const prior = this._priorities.get(flexBasis_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._flexBoxSetter(flexBasis_local_var_property, val, priority, flexBasis_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(flexBasis_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var flex_export_initialValues, flex_export_shorthandFor, flex_export_parse, flex_export_definition;
const flex_local_var_property = "flex";
flex_export_initialValues = new Map([["flex-grow", "0"], ["flex-shrink", "1"], ["flex-basis", "auto"]]);
flex_export_shorthandFor = new Map([["flex-grow", {
  parse: flexGrow_export_parse,
  definition: flexGrow_export_definition
}], ["flex-shrink", {
  parse: flexShrink_export_parse,
  definition: flexShrink_export_definition
}], ["flex-basis", {
  parse: flexBasis_export_parse,
  definition: flexBasis_export_definition
}]]);
flex_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(flex_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length) {
    const flex = {
      "flex-grow": "1",
      "flex-shrink": "1",
      "flex-basis": "0%"
    };
    if (value.length === 1) {
      const [{
        isNumber,
        name,
        type,
        unit,
        value: itemValue
      }] = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber) {
              flex["flex-grow"] = `${name}(${itemValue})`;
              return flex;
            }
            flex["flex-basis"] = `${name}(${itemValue})`;
            return flex;
          }
        case "Dimension":
          {
            flex["flex-basis"] = `${itemValue}${unit}`;
            return flex;
          }
        case "GlobalKeyword":
          {
            return name;
          }
        case "Identifier":
          {
            if (name === "none") {
              return {
                "flex-grow": "0",
                "flex-shrink": "0",
                "flex-basis": "auto"
              };
            }
            flex["flex-basis"] = name;
            return flex;
          }
        case "Number":
          {
            flex["flex-grow"] = itemValue;
            return flex;
          }
        case "Percentage":
          {
            flex["flex-basis"] = `${itemValue}%`;
            return flex;
          }
        default:
      }
    } else {
      const [val1, val2, val3] = value;
      if (val1.type === "Calc" && val1.isNumber) {
        flex["flex-grow"] = `${val1.name}(${val1.value})`;
      } else if (val1.type === "Number") {
        flex["flex-grow"] = val1.value;
      } else {
        return;
      }
      if (val3) {
        if (val2.type === "Calc" && val2.isNumber) {
          flex["flex-shrink"] = `${val2.name}(${val2.value})`;
        } else if (val2.type === "Number") {
          flex["flex-shrink"] = val2.value;
        } else {
          return;
        }
        if (val3.type === "GlobalKeyword" || val3.type === "Identifier") {
          flex["flex-basis"] = val3.name;
        } else if (val3.type === "Calc" && !val3.isNumber) {
          flex["flex-basis"] = `${val3.name}(${val3.value})`;
        } else if (val3.type === "Dimension") {
          flex["flex-basis"] = `${val3.value}${val3.unit}`;
        } else if (val3.type === "Percentage") {
          flex["flex-basis"] = `${val3.value}%`;
        } else {
          return;
        }
      } else {
        switch (val2.type) {
          case "Calc":
            {
              if (val2.isNumber) {
                flex["flex-shrink"] = `${val2.name}(${val2.value})`;
              } else {
                flex["flex-basis"] = `${val2.name}(${val2.value})`;
              }
              break;
            }
          case "Dimension":
            {
              flex["flex-basis"] = `${val2.value}${val2.unit}`;
              break;
            }
          case "Number":
            {
              flex["flex-shrink"] = val2.value;
              break;
            }
          case "Percentage":
            {
              flex["flex-basis"] = `${val2.value}%`;
              break;
            }
          case "Identifier":
            {
              flex["flex-basis"] = val2.name;
              break;
            }
          default:
            {
              return;
            }
        }
      }
      return flex;
      // return [...Object.values(flex)].join(" ");
    }
  } else if (typeof value === "string") {
    return value;
  }
};
flex_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [longhand] of flex_export_shorthandFor) {
        this._setProperty(longhand, "");
      }
      this._setProperty(flex_local_var_property, v);
    } else {
      const val = flex_export_parse(v, {
        globalObject: this._global
      });
      const priority = this._priorities.get(flex_local_var_property) ?? "";
      if (typeof val === "string") {
        for (const [longhand] of flex_export_shorthandFor) {
          this._setProperty(longhand, val, priority);
        }
        this._setProperty(flex_local_var_property, val, priority);
      } else if (val) {
        const values = [];
        for (const [longhand, value] of Object.entries(val)) {
          values.push(value);
          this._setProperty(longhand, value, priority);
        }
        this._setProperty(flex_local_var_property, values.join(" "), priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(flex_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var float_export_parse, float_export_definition;
const float_local_var_property = "float";
float_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(float_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
float_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(float_local_var_property, v);
    } else {
      const val = float_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(float_local_var_property) ?? "";
        this._setProperty(float_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(float_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var floodColor_export_parse, floodColor_export_definition;
const floodColor_local_var_property = "flood-color";
floodColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(floodColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
floodColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(floodColor_local_var_property, v);
    } else {
      const val = floodColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(floodColor_local_var_property) ?? "";
        this._setProperty(floodColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(floodColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var fontStyle_export_parse, fontStyle_export_definition;
const fontStyle_local_var_property = "font-style";
const fontStyle_local_var_shorthand = "font";
fontStyle_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(fontStyle_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length) {
    if (value.length === 1) {
      const [{
        name,
        type
      }] = value;
      switch (type) {
        case "GlobalKeyword":
        case "Identifier":
          {
            return name;
          }
        default:
      }
    } else if (value.length === 2) {
      const [part1, part2] = value;
      const val1 = part1.type === "Identifier" && part1.name;
      const val2 = external_dependency_parsers_0.parseAngle([part2]);
      if (val1 && val1 === "oblique" && val2) {
        return `${val1} ${val2}`;
      }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
fontStyle_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(fontStyle_local_var_shorthand, "");
      this._setProperty(fontStyle_local_var_property, v);
    } else {
      const val = fontStyle_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(fontStyle_local_var_shorthand);
        const prior = this._priorities.get(fontStyle_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(fontStyle_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(fontStyle_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var fontVariant_export_parse, fontVariant_export_definition;
const fontVariant_local_var_property = "font-variant";
const fontVariant_local_var_shorthand = "font";
fontVariant_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v);
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(fontVariant_local_var_property, val, {
      globalObject,
      inArray: true
    });
    if (Array.isArray(value) && value.length === 1) {
      const [{
        name,
        type,
        value: itemValue
      }] = value;
      switch (type) {
        case "Function":
          {
            parsedValues.push(`${name}(${itemValue})`);
            break;
          }
        case "GlobalKeyword":
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            return;
          }
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    }
  }
  if (parsedValues.length) {
    if (parsedValues.length > 1) {
      if (parsedValues.includes("normal") || parsedValues.includes("none")) {
        return;
      }
    }
    return parsedValues.join(" ");
  }
};
fontVariant_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(fontVariant_local_var_shorthand, "");
      this._setProperty(fontVariant_local_var_property, v);
    } else {
      const val = fontVariant_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(fontVariant_local_var_shorthand);
        const prior = this._priorities.get(fontVariant_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(fontVariant_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(fontVariant_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var fontWeight_export_parse, fontWeight_export_definition;
const fontWeight_local_var_property = "font-weight";
const fontWeight_local_var_shorthand = "font";
fontWeight_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(fontWeight_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return `${name}(${itemValue})`;
          }
          break;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          const parsedValue = external_dependency_parsers_0.parseNumber(value, {
            min: 1,
            max: 1000
          });
          if (parsedValue) {
            return parsedValue;
          }
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
fontWeight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(fontWeight_local_var_shorthand, "");
      this._setProperty(fontWeight_local_var_property, v);
    } else {
      const val = fontWeight_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(fontWeight_local_var_shorthand);
        const prior = this._priorities.get(fontWeight_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(fontWeight_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(fontWeight_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var fontSize_export_parse, fontSize_export_definition;
const fontSize_local_var_property = "font-size";
const fontSize_local_var_shorthand = "font";
fontSize_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(fontSize_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
fontSize_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(fontSize_local_var_shorthand, "");
      this._setProperty(fontSize_local_var_property, v);
    } else {
      const val = fontSize_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(fontSize_local_var_shorthand);
        const prior = this._priorities.get(fontSize_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(fontSize_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(fontSize_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var lineHeight_export_parse, lineHeight_export_definition;
const lineHeight_local_var_property = "line-height";
const lineHeight_local_var_shorthand = "font";
lineHeight_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(lineHeight_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      case "Number":
        {
          return external_dependency_parsers_0.parseNumber(value, {
            min: 0
          });
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
lineHeight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(lineHeight_local_var_shorthand, "");
      this._setProperty(lineHeight_local_var_property, v);
    } else {
      const val = lineHeight_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(lineHeight_local_var_shorthand);
        const prior = this._priorities.get(lineHeight_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(lineHeight_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(lineHeight_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var fontFamily_export_parse, fontFamily_export_definition;
const fontFamily_local_var_property = "font-family";
const fontFamily_local_var_shorthand = "font";
fontFamily_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const parsedValues = [];
  for (const val of values) {
    const value = external_dependency_parsers_0.parsePropertyValue(fontFamily_local_var_property, val, {
      globalObject,
      caseSensitive: true,
      inArray: true
    });
    if (Array.isArray(value) && value.length) {
      if (value.length === 1) {
        const [{
          name,
          type,
          value: itemValue
        }] = value;
        switch (type) {
          case "Function":
            {
              parsedValues.push(`${name}(${itemValue})`);
              break;
            }
          case "GlobalKeyword":
          case "Identifier":
            {
              if (name !== "undefined") {
                parsedValues.push(name);
              }
              break;
            }
          case "String":
            {
              const parsedValue = itemValue.replaceAll("\\", "").replaceAll('"', '\\"');
              parsedValues.push(`"${parsedValue}"`);
              break;
            }
          default:
            {
              return;
            }
        }
      } else {
        const parts = [];
        for (const item of value) {
          const {
            name,
            type
          } = item;
          if (type === "Identifier") {
            parts.push(name);
          } else {
            return;
          }
        }
        const parsedValue = parts.join(" ").replaceAll("\\", "").replaceAll('"', '\\"');
        parsedValues.push(`"${parsedValue}"`);
      }
    } else if (typeof value === "string") {
      parsedValues.push(value);
    } else {
      return;
    }
  }
  if (parsedValues.length) {
    return parsedValues.join(", ");
  }
};
fontFamily_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(fontFamily_local_var_shorthand, "");
      this._setProperty(fontFamily_local_var_property, v);
    } else {
      const val = fontFamily_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(fontFamily_local_var_shorthand);
        const prior = this._priorities.get(fontFamily_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._setProperty(fontFamily_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(fontFamily_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var font_export_shorthandFor, font_export_parse, font_export_definition;
const font_local_var_property = "font";
font_export_shorthandFor = new Map([["font-style", {
  parse: fontStyle_export_parse,
  definition: fontStyle_export_definition
}], ["font-variant", {
  parse: fontVariant_export_parse,
  definition: fontVariant_export_definition
}], ["font-weight", {
  parse: fontWeight_export_parse,
  definition: fontWeight_export_definition
}], ["font-size", {
  parse: fontSize_export_parse,
  definition: fontSize_export_definition
}], ["line-height", {
  parse: lineHeight_export_parse,
  definition: lineHeight_export_definition
}], ["font-family", {
  parse: fontFamily_export_parse,
  definition: fontFamily_export_definition
}]]);
font_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  } else if (external_dependency_parsers_0.hasCalcFunc(v)) {
    v = external_dependency_parsers_0.resolveCalc(v);
  }
  if (!external_dependency_parsers_0.isValidPropertyValue(font_local_var_property, v)) {
    return;
  }
  const [fontBlock, ...families] = external_dependency_parsers_0.splitValue(v, {
    delimiter: ","
  });
  const [fontBlockA, fontBlockB] = external_dependency_parsers_0.splitValue(fontBlock, {
    delimiter: "/"
  });
  const font = {
    "font-style": "normal",
    "font-variant": "normal",
    "font-weight": "normal"
  };
  const fontFamilies = new Set();
  if (fontBlockB) {
    const [lineB, ...familiesB] = fontBlockB.trim().split(" ");
    if (!lineB || !familiesB.length) {
      return;
    }
    const lineHeightB = {
      parse: lineHeight_export_parse,
      definition: lineHeight_export_definition
    }.parse(lineB, {
      global
    });
    if (typeof lineHeightB !== "string") {
      return;
    }
    const familyB = {
      parse: fontFamily_export_parse,
      definition: fontFamily_export_definition
    }.parse(familiesB.join(" "), {
      globalObject,
      caseSensitive: true
    });
    if (typeof familyB === "string") {
      fontFamilies.add(familyB);
    } else {
      return;
    }
    const parts = external_dependency_parsers_0.splitValue(fontBlockA.trim());
    const properties = ["font-style", "font-variant", "font-weight", "font-size"];
    for (const part of parts) {
      if (part === "normal") {
        continue;
      } else {
        for (const longhand of properties) {
          switch (longhand) {
            case "font-size":
              {
                const parsedValue = {
                  parse: fontSize_export_parse,
                  definition: fontSize_export_definition
                }.parse(part, {
                  globalObject
                });
                if (typeof parsedValue === "string") {
                  font[longhand] = parsedValue;
                }
                break;
              }
            case "font-style":
            case "font-weight":
              {
                if (font[longhand] === "normal") {
                  const longhandItem = font_export_shorthandFor.get(longhand);
                  const parsedValue = longhandItem.parse(part, {
                    globalObject
                  });
                  if (typeof parsedValue === "string") {
                    font[longhand] = parsedValue;
                  }
                }
                break;
              }
            case "font-variant":
              {
                if (font[longhand] === "normal") {
                  const parsedValue = {
                    parse: fontVariant_export_parse,
                    definition: fontVariant_export_definition
                  }.parse(part, {
                    globalObject
                  });
                  if (typeof parsedValue === "string") {
                    if (parsedValue === "small-cap") {
                      font[longhand] = parsedValue;
                    } else if (parsedValue !== "normal") {
                      return;
                    }
                  }
                }
                break;
              }
            default:
          }
        }
      }
    }
    if (Object.hasOwn(font, "font-size")) {
      font["line-height"] = lineHeightB;
    } else {
      return;
    }
  } else {
    const revParts = external_dependency_parsers_0.splitValue(fontBlockA.trim()).toReversed();
    if (revParts.length === 1) {
      const [part] = revParts;
      const value = external_dependency_parsers_0.parsePropertyValue(font_local_var_property, part, {
        globalObject,
        inArray: true
      });
      if (Array.isArray(value) && value.length === 1) {
        const [{
          name,
          type
        }] = value;
        if (type === "GlobalKeyword") {
          return {
            "font-style": name,
            "font-variant": name,
            "font-weight": name,
            "font-size": name,
            "line-height": name,
            "font-family": name
          };
        }
      }
      return;
    }
    const properties = ["font-style", "font-variant", "font-weight", "line-height"];
    for (const longhand of properties) {
      font[longhand] = "normal";
    }
    const revFontFamily = [];
    let fontSizeA;
    for (const part of revParts) {
      if (fontSizeA) {
        if (/^normal$/i.test(part)) {
          continue;
        } else {
          for (const longhand of properties) {
            switch (longhand) {
              case "font-style":
              case "font-weight":
              case "line-height":
                {
                  if (font[longhand] === "normal") {
                    const longhandItem = font_export_shorthandFor.get(longhand);
                    const parsedValue = longhandItem.parse(part, {
                      globalObject
                    });
                    if (typeof parsedValue === "string") {
                      font[longhand] = parsedValue;
                    }
                  }
                  break;
                }
              case "font-variant":
                {
                  if (font[longhand] === "normal") {
                    const parsedValue = {
                      parse: fontVariant_export_parse,
                      definition: fontVariant_export_definition
                    }.parse(part, {
                      globalObject
                    });
                    if (typeof parsedValue === "string") {
                      if (parsedValue === "small-cap") {
                        font[longhand] = parsedValue;
                      } else if (parsedValue !== "normal") {
                        return;
                      }
                    }
                  }
                  break;
                }
              default:
            }
          }
        }
      } else {
        const parsedFontSize = {
          parse: fontSize_export_parse,
          definition: fontSize_export_definition
        }.parse(part, {
          globalObject
        });
        if (typeof parsedFontSize === "string") {
          fontSizeA = parsedFontSize;
        } else {
          const parsedFontFamily = {
            parse: fontFamily_export_parse,
            definition: fontFamily_export_definition
          }.parse(part, {
            globalObject,
            caseSensitive: true
          });
          if (typeof parsedFontFamily === "string") {
            revFontFamily.push(parsedFontFamily);
          } else {
            return;
          }
        }
      }
    }
    const family = {
      parse: fontFamily_export_parse,
      definition: fontFamily_export_definition
    }.parse(revFontFamily.toReversed().join(" "), {
      globalObject,
      caseSensitive: true
    });
    if (fontSizeA && family) {
      font["font-size"] = fontSizeA;
      fontFamilies.add({
        parse: fontFamily_export_parse,
        definition: fontFamily_export_definition
      }.parse(family));
    } else {
      return;
    }
  }
  for (const family of families) {
    const parsedFontFamily = {
      parse: fontFamily_export_parse,
      definition: fontFamily_export_definition
    }.parse(family, {
      globalObject,
      caseSensitive: true
    });
    if (parsedFontFamily) {
      fontFamilies.add(parsedFontFamily);
    } else {
      return;
    }
  }
  font["font-family"] = [...fontFamilies].join(", ");
  return font;
};
font_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (v === "" || external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [key] of font_export_shorthandFor) {
        this._setProperty(key, "");
      }
      this._setProperty(font_local_var_property, v);
    } else {
      const obj = font_export_parse(v, {
        globalObject: this._global
      });
      if (!obj) {
        return;
      }
      const priority = this._priorities.get(font_local_var_property) ?? "";
      const str = new Set();
      for (const [key] of font_export_shorthandFor) {
        const val = obj[key];
        if (typeof val === "string") {
          this._setProperty(key, val, priority);
          if (val && val !== "normal" && !str.has(val)) {
            if (key === "line-height") {
              str.add(`/ ${val}`);
            } else {
              str.add(val);
            }
          }
        }
      }
      this._setProperty(font_local_var_property, [...str].join(" "), priority);
    }
  },
  get() {
    const val = this.getPropertyValue(font_local_var_property);
    if (external_dependency_parsers_0.hasVarFunc(val)) {
      return val;
    }
    const str = new Set();
    for (const [key] of font_export_shorthandFor) {
      const v = this.getPropertyValue(key);
      if (external_dependency_parsers_0.hasVarFunc(v)) {
        return "";
      }
      if (v && v !== "normal" && !str.has(v)) {
        if (key === "line-height") {
          str.add(`/ ${v}`);
        } else {
          str.add(`${v}`);
        }
      }
    }
    return [...str].join(" ");
  },
  enumerable: true,
  configurable: true
};
var height_export_parse, height_export_definition;
const height_local_var_property = "height";
height_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(height_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
height_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(height_local_var_property, v);
    } else {
      const val = height_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(height_local_var_property) ?? "";
        this._setProperty(height_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(height_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var left_export_parse, left_export_definition;
const left_local_var_property = "left";
left_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(left_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
left_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(left_local_var_property, v);
    } else {
      const val = left_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(left_local_var_property) ?? "";
        this._setProperty(left_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(left_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var lightingColor_export_parse, lightingColor_export_definition;
const lightingColor_local_var_property = "lighting-color";
lightingColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(lightingColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
lightingColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(lightingColor_local_var_property, v);
    } else {
      const val = lightingColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(lightingColor_local_var_property) ?? "";
        this._setProperty(lightingColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(lightingColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var marginTop_export_position, marginTop_export_parse, marginTop_export_definition;
const marginTop_local_var_property = "margin-top";
const marginTop_local_var_shorthand = "margin";
marginTop_export_position = "top";
marginTop_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(marginTop_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
marginTop_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(marginTop_local_var_shorthand, "");
      this._setProperty(marginTop_local_var_property, v);
    } else {
      const val = marginTop_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(marginTop_local_var_shorthand);
        const prior = this._priorities.get(marginTop_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(marginTop_local_var_property, val, priority, marginTop_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(marginTop_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var marginRight_export_position, marginRight_export_parse, marginRight_export_definition;
const marginRight_local_var_property = "margin-right";
const marginRight_local_var_shorthand = "margin";
marginRight_export_position = "right";
marginRight_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(marginRight_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
marginRight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(marginRight_local_var_shorthand, "");
      this._setProperty(marginRight_local_var_property, v);
    } else {
      const val = marginRight_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(marginRight_local_var_shorthand);
        const prior = this._priorities.get(marginRight_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(marginRight_local_var_property, val, priority, marginRight_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(marginRight_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var marginBottom_export_position, marginBottom_export_parse, marginBottom_export_definition;
const marginBottom_local_var_property = "margin-bottom";
const marginBottom_local_var_shorthand = "margin";
marginBottom_export_position = "bottom";
marginBottom_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(marginBottom_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
marginBottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(marginBottom_local_var_shorthand, "");
      this._setProperty(marginBottom_local_var_property, v);
    } else {
      const val = marginBottom_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(marginBottom_local_var_shorthand);
        const prior = this._priorities.get(marginBottom_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(marginBottom_local_var_property, val, priority, marginBottom_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(marginBottom_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var marginLeft_export_position, marginLeft_export_parse, marginLeft_export_definition;
const marginLeft_local_var_property = "margin-left";
const marginLeft_local_var_shorthand = "margin";
marginLeft_export_position = "left";
marginLeft_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(marginLeft_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
marginLeft_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(marginLeft_local_var_shorthand, "");
      this._setProperty(marginLeft_local_var_property, v);
    } else {
      const val = marginLeft_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(marginLeft_local_var_shorthand);
        const prior = this._priorities.get(marginLeft_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(marginLeft_local_var_property, val, priority, marginLeft_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(marginLeft_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var margin_export_position, margin_export_shorthandFor, margin_export_parse, margin_export_definition;
const margin_local_var_property = "margin";
margin_export_position = "edges";
margin_export_shorthandFor = new Map([["margin-top", {
  position: marginTop_export_position,
  parse: marginTop_export_parse,
  definition: marginTop_export_definition
}], ["margin-right", {
  position: marginRight_export_position,
  parse: marginRight_export_parse,
  definition: marginRight_export_definition
}], ["margin-bottom", {
  position: marginBottom_export_position,
  parse: marginBottom_export_parse,
  definition: marginBottom_export_definition
}], ["margin-left", {
  position: marginLeft_export_position,
  parse: marginLeft_export_parse,
  definition: marginLeft_export_definition
}]]);
margin_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.parsePropertyValue(margin_local_var_property, v, {
    globalObject,
    inArray: true
  });
  const parsedValues = [];
  if (Array.isArray(values) && values.length) {
    if (values.length > 4) {
      return;
    }
    for (const value of values) {
      const {
        isNumber,
        name,
        type,
        value: itemValue
      } = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber) {
              return;
            }
            parsedValues.push(`${name}(${itemValue})`);
            break;
          }
        case "GlobalKeyword":
          {
            if (values.length !== 1) {
              return;
            }
            parsedValues.push(name);
            break;
          }
        case "Identifier":
          {
            parsedValues.push(name);
            break;
          }
        default:
          {
            const parsedValue = external_dependency_parsers_0.parseLengthPercentage([value]);
            if (!parsedValue) {
              return;
            }
            parsedValues.push(parsedValue);
          }
      }
    }
  } else if (typeof values === "string") {
    parsedValues.push(values);
  }
  if (parsedValues.length) {
    return parsedValues;
  }
};
margin_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [longhand] of margin_export_shorthandFor) {
        this._setProperty(longhand, "");
      }
      this._setProperty(margin_local_var_property, v);
    } else {
      const val = margin_export_parse(v, {
        globalObject: this._global
      });
      if (Array.isArray(val) || typeof val === "string") {
        const priority = this._priorities.get(margin_local_var_property) ?? "";
        this._positionShorthandSetter(margin_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(margin_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var opacity_export_parse, opacity_export_definition;
const opacity_local_var_property = "opacity";
opacity_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(opacity_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      case "Number":
        {
          return external_dependency_parsers_0.parseNumber(value);
        }
      case "Percentage":
        {
          return external_dependency_parsers_0.parsePercentage(value);
        }
      default:
    }
  } else if (typeof value === "string") {
    return value;
  }
};
opacity_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(opacity_local_var_property, v);
    } else {
      const val = opacity_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(opacity_local_var_property) ?? "";
        this._setProperty(opacity_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(opacity_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var outlineColor_export_parse, outlineColor_export_definition;
const outlineColor_local_var_property = "outline-color";
outlineColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(outlineColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
outlineColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(outlineColor_local_var_property, v);
    } else {
      const val = outlineColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(outlineColor_local_var_property) ?? "";
        this._setProperty(outlineColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(outlineColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var paddingTop_export_position, paddingTop_export_parse, paddingTop_export_definition;
const paddingTop_local_var_property = "padding-top";
const paddingTop_local_var_shorthand = "padding";
paddingTop_export_position = "top";
paddingTop_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(paddingTop_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
paddingTop_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(paddingTop_local_var_shorthand, "");
      this._setProperty(paddingTop_local_var_property, v);
    } else {
      const val = paddingTop_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(paddingTop_local_var_shorthand);
        const prior = this._priorities.get(paddingTop_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(paddingTop_local_var_property, val, priority, paddingTop_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(paddingTop_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var paddingRight_export_position, paddingRight_export_parse, paddingRight_export_definition;
const paddingRight_local_var_property = "padding-right";
const paddingRight_local_var_shorthand = "padding";
paddingRight_export_position = "right";
paddingRight_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(paddingRight_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
paddingRight_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(paddingRight_local_var_shorthand, "");
      this._setProperty(paddingRight_local_var_property, v);
    } else {
      const val = paddingRight_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(paddingRight_local_var_shorthand);
        const prior = this._priorities.get(paddingRight_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(paddingRight_local_var_property, val, priority, paddingRight_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(paddingRight_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var paddingBottom_export_position, paddingBottom_export_parse, paddingBottom_export_definition;
const paddingBottom_local_var_property = "padding-bottom";
const paddingBottom_local_var_shorthand = "padding";
paddingBottom_export_position = "bottom";
paddingBottom_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(paddingBottom_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
paddingBottom_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(paddingBottom_local_var_shorthand, "");
      this._setProperty(paddingBottom_local_var_property, v);
    } else {
      const val = paddingBottom_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(paddingBottom_local_var_shorthand);
        const prior = this._priorities.get(paddingBottom_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(paddingBottom_local_var_property, val, priority, paddingBottom_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(paddingBottom_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var paddingLeft_export_position, paddingLeft_export_parse, paddingLeft_export_definition;
const paddingLeft_local_var_property = "padding-left";
const paddingLeft_local_var_shorthand = "padding";
paddingLeft_export_position = "left";
paddingLeft_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(paddingLeft_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value, {
            min: 0
          });
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
paddingLeft_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(paddingLeft_local_var_shorthand, "");
      this._setProperty(paddingLeft_local_var_property, v);
    } else {
      const val = paddingLeft_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const shorthandPriority = this._priorities.get(paddingLeft_local_var_shorthand);
        const prior = this._priorities.get(paddingLeft_local_var_property) ?? "";
        const priority = shorthandPriority && prior ? "" : prior;
        this._positionLonghandSetter(paddingLeft_local_var_property, val, priority, paddingLeft_local_var_shorthand);
      }
    }
  },
  get() {
    return this.getPropertyValue(paddingLeft_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var padding_export_position, padding_export_shorthandFor, padding_export_parse, padding_export_definition;
const padding_local_var_property = "padding";
padding_export_position = "edges";
padding_export_shorthandFor = new Map([["padding-top", {
  position: paddingTop_export_position,
  parse: paddingTop_export_parse,
  definition: paddingTop_export_definition
}], ["padding-right", {
  position: paddingRight_export_position,
  parse: paddingRight_export_parse,
  definition: paddingRight_export_definition
}], ["padding-bottom", {
  position: paddingBottom_export_position,
  parse: paddingBottom_export_parse,
  definition: paddingBottom_export_definition
}], ["padding-left", {
  position: paddingLeft_export_position,
  parse: paddingLeft_export_parse,
  definition: paddingLeft_export_definition
}]]);
padding_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const values = external_dependency_parsers_0.parsePropertyValue(padding_local_var_property, v, {
    globalObject,
    inArray: true
  });
  const parsedValues = [];
  if (Array.isArray(values) && values.length) {
    if (values.length > 4) {
      return;
    }
    for (const value of values) {
      const {
        isNumber,
        name,
        type,
        value: itemValue
      } = value;
      switch (type) {
        case "Calc":
          {
            if (isNumber) {
              return;
            }
            parsedValues.push(`${name}(${itemValue})`);
            break;
          }
        case "GlobalKeyword":
          {
            if (values.length !== 1) {
              return;
            }
            parsedValues.push(name);
            break;
          }
        default:
          {
            const parsedValue = external_dependency_parsers_0.parseLengthPercentage([value], {
              min: 0
            });
            if (!parsedValue) {
              return;
            }
            parsedValues.push(parsedValue);
          }
      }
    }
  } else if (typeof values === "string") {
    parsedValues.push(values);
  }
  if (parsedValues.length) {
    return parsedValues;
  }
};
padding_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      for (const [longhand] of padding_export_shorthandFor) {
        this._setProperty(longhand, "");
      }
      this._setProperty(padding_local_var_property, v);
    } else {
      const val = padding_export_parse(v, {
        globalObject: this._global
      });
      if (Array.isArray(val) || typeof val === "string") {
        const priority = this._priorities.get(padding_local_var_property) ?? "";
        this._positionShorthandSetter(padding_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(padding_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var right_export_parse, right_export_definition;
const right_local_var_property = "right";
right_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(right_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
right_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(right_local_var_property, v);
    } else {
      const val = right_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(right_local_var_property) ?? "";
        this._setProperty(right_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(right_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var stopColor_export_parse, stopColor_export_definition;
const stopColor_local_var_property = "stop-color";
stopColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(stopColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
stopColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(stopColor_local_var_property, v);
    } else {
      const val = stopColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(stopColor_local_var_property) ?? "";
        this._setProperty(stopColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(stopColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var top_export_parse, top_export_definition;
const top_local_var_property = "top";
top_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(top_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
top_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(top_local_var_property, v);
    } else {
      const val = top_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(top_local_var_property) ?? "";
        this._setProperty(top_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(top_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitBorderAfterColor_export_parse, webkitBorderAfterColor_export_definition;
const webkitBorderAfterColor_local_var_property = "-webkit-border-after-color";
webkitBorderAfterColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitBorderAfterColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitBorderAfterColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitBorderAfterColor_local_var_property, v);
    } else {
      const val = webkitBorderAfterColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitBorderAfterColor_local_var_property) ?? "";
        this._setProperty(webkitBorderAfterColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitBorderAfterColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitBorderBeforeColor_export_parse, webkitBorderBeforeColor_export_definition;
const webkitBorderBeforeColor_local_var_property = "-webkit-border-before-color";
webkitBorderBeforeColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitBorderBeforeColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitBorderBeforeColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitBorderBeforeColor_local_var_property, v);
    } else {
      const val = webkitBorderBeforeColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitBorderBeforeColor_local_var_property) ?? "";
        this._setProperty(webkitBorderBeforeColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitBorderBeforeColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitBorderEndColor_export_parse, webkitBorderEndColor_export_definition;
const webkitBorderEndColor_local_var_property = "-webkit-border-end-color";
webkitBorderEndColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitBorderEndColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitBorderEndColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitBorderEndColor_local_var_property, v);
    } else {
      const val = webkitBorderEndColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitBorderEndColor_local_var_property) ?? "";
        this._setProperty(webkitBorderEndColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitBorderEndColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitBorderStartColor_export_parse, webkitBorderStartColor_export_definition;
const webkitBorderStartColor_local_var_property = "-webkit-border-start-color";
webkitBorderStartColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitBorderStartColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitBorderStartColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitBorderStartColor_local_var_property, v);
    } else {
      const val = webkitBorderStartColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitBorderStartColor_local_var_property) ?? "";
        this._setProperty(webkitBorderStartColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitBorderStartColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitColumnRuleColor_export_parse, webkitColumnRuleColor_export_definition;
const webkitColumnRuleColor_local_var_property = "-webkit-column-rule-color";
webkitColumnRuleColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitColumnRuleColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitColumnRuleColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitColumnRuleColor_local_var_property, v);
    } else {
      const val = webkitColumnRuleColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitColumnRuleColor_local_var_property) ?? "";
        this._setProperty(webkitColumnRuleColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitColumnRuleColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitTapHighlightColor_export_parse, webkitTapHighlightColor_export_definition;
const webkitTapHighlightColor_local_var_property = "-webkit-tap-highlight-color";
webkitTapHighlightColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitTapHighlightColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitTapHighlightColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitTapHighlightColor_local_var_property, v);
    } else {
      const val = webkitTapHighlightColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitTapHighlightColor_local_var_property) ?? "";
        this._setProperty(webkitTapHighlightColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitTapHighlightColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitTextEmphasisColor_export_parse, webkitTextEmphasisColor_export_definition;
const webkitTextEmphasisColor_local_var_property = "-webkit-text-emphasis-color";
webkitTextEmphasisColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitTextEmphasisColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitTextEmphasisColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitTextEmphasisColor_local_var_property, v);
    } else {
      const val = webkitTextEmphasisColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitTextEmphasisColor_local_var_property) ?? "";
        this._setProperty(webkitTextEmphasisColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitTextEmphasisColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitTextFillColor_export_parse, webkitTextFillColor_export_definition;
const webkitTextFillColor_local_var_property = "-webkit-text-fill-color";
webkitTextFillColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitTextFillColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitTextFillColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitTextFillColor_local_var_property, v);
    } else {
      const val = webkitTextFillColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitTextFillColor_local_var_property) ?? "";
        this._setProperty(webkitTextFillColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitTextFillColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var webkitTextStrokeColor_export_parse, webkitTextStrokeColor_export_definition;
const webkitTextStrokeColor_local_var_property = "-webkit-text-stroke-color";
webkitTextStrokeColor_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(webkitTextStrokeColor_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      name,
      type
    }] = value;
    switch (type) {
      case "GlobalKeyword":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseColor(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
webkitTextStrokeColor_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(webkitTextStrokeColor_local_var_property, v);
    } else {
      const val = webkitTextStrokeColor_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(webkitTextStrokeColor_local_var_property) ?? "";
        this._setProperty(webkitTextStrokeColor_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(webkitTextStrokeColor_local_var_property);
  },
  enumerable: true,
  configurable: true
};
var width_export_parse, width_export_definition;
const width_local_var_property = "width";
width_export_parse = function parse(v, opt = {}) {
  const {
    globalObject
  } = opt;
  if (v === "") {
    return v;
  }
  const value = external_dependency_parsers_0.parsePropertyValue(width_local_var_property, v, {
    globalObject,
    inArray: true
  });
  if (Array.isArray(value) && value.length === 1) {
    const [{
      isNumber,
      name,
      type,
      value: itemValue
    }] = value;
    switch (type) {
      case "Calc":
        {
          if (isNumber) {
            return;
          }
          return `${name}(${itemValue})`;
        }
      case "GlobalKeyword":
      case "Identifier":
        {
          return name;
        }
      default:
        {
          return external_dependency_parsers_0.parseLengthPercentage(value);
        }
    }
  } else if (typeof value === "string") {
    return value;
  }
};
width_export_definition = {
  set(v) {
    v = external_dependency_parsers_0.prepareValue(v, this._global);
    if (external_dependency_parsers_0.hasVarFunc(v)) {
      this._setProperty(width_local_var_property, v);
    } else {
      const val = width_export_parse(v, {
        globalObject: this._global
      });
      if (typeof val === "string") {
        const priority = this._priorities.get(width_local_var_property) ?? "";
        this._setProperty(width_local_var_property, val, priority);
      }
    }
  },
  get() {
    return this.getPropertyValue(width_local_var_property);
  },
  enumerable: true,
  configurable: true
};
module.exports = {
  backgroundImage: backgroundImage_export_definition,
  "background-image": backgroundImage_export_definition,
  backgroundPosition: backgroundPosition_export_definition,
  "background-position": backgroundPosition_export_definition,
  backgroundSize: backgroundSize_export_definition,
  "background-size": backgroundSize_export_definition,
  backgroundRepeat: backgroundRepeat_export_definition,
  "background-repeat": backgroundRepeat_export_definition,
  backgroundOrigin: backgroundOrigin_export_definition,
  "background-origin": backgroundOrigin_export_definition,
  backgroundClip: backgroundClip_export_definition,
  "background-clip": backgroundClip_export_definition,
  backgroundAttachment: backgroundAttachment_export_definition,
  "background-attachment": backgroundAttachment_export_definition,
  backgroundColor: backgroundColor_export_definition,
  "background-color": backgroundColor_export_definition,
  background: background_export_definition,
  borderTopWidth: borderTopWidth_export_definition,
  "border-top-width": borderTopWidth_export_definition,
  borderRightWidth: borderRightWidth_export_definition,
  "border-right-width": borderRightWidth_export_definition,
  borderBottomWidth: borderBottomWidth_export_definition,
  "border-bottom-width": borderBottomWidth_export_definition,
  borderLeftWidth: borderLeftWidth_export_definition,
  "border-left-width": borderLeftWidth_export_definition,
  borderWidth: borderWidth_export_definition,
  "border-width": borderWidth_export_definition,
  borderTopStyle: borderTopStyle_export_definition,
  "border-top-style": borderTopStyle_export_definition,
  borderRightStyle: borderRightStyle_export_definition,
  "border-right-style": borderRightStyle_export_definition,
  borderBottomStyle: borderBottomStyle_export_definition,
  "border-bottom-style": borderBottomStyle_export_definition,
  borderLeftStyle: borderLeftStyle_export_definition,
  "border-left-style": borderLeftStyle_export_definition,
  borderStyle: borderStyle_export_definition,
  "border-style": borderStyle_export_definition,
  borderTopColor: borderTopColor_export_definition,
  "border-top-color": borderTopColor_export_definition,
  borderRightColor: borderRightColor_export_definition,
  "border-right-color": borderRightColor_export_definition,
  borderBottomColor: borderBottomColor_export_definition,
  "border-bottom-color": borderBottomColor_export_definition,
  borderLeftColor: borderLeftColor_export_definition,
  "border-left-color": borderLeftColor_export_definition,
  borderColor: borderColor_export_definition,
  "border-color": borderColor_export_definition,
  borderTop: borderTop_export_definition,
  "border-top": borderTop_export_definition,
  borderRight: borderRight_export_definition,
  "border-right": borderRight_export_definition,
  borderBottom: borderBottom_export_definition,
  "border-bottom": borderBottom_export_definition,
  borderLeft: borderLeft_export_definition,
  "border-left": borderLeft_export_definition,
  border: border_export_definition,
  borderCollapse: borderCollapse_export_definition,
  "border-collapse": borderCollapse_export_definition,
  borderSpacing: borderSpacing_export_definition,
  "border-spacing": borderSpacing_export_definition,
  bottom: bottom_export_definition,
  clear: clear_export_definition,
  clip: clip_export_definition,
  color: color_export_definition,
  display: display_export_definition,
  flexGrow: flexGrow_export_definition,
  "flex-grow": flexGrow_export_definition,
  flexShrink: flexShrink_export_definition,
  "flex-shrink": flexShrink_export_definition,
  flexBasis: flexBasis_export_definition,
  "flex-basis": flexBasis_export_definition,
  flex: flex_export_definition,
  float: float_export_definition,
  floodColor: floodColor_export_definition,
  "flood-color": floodColor_export_definition,
  fontStyle: fontStyle_export_definition,
  "font-style": fontStyle_export_definition,
  fontVariant: fontVariant_export_definition,
  "font-variant": fontVariant_export_definition,
  fontWeight: fontWeight_export_definition,
  "font-weight": fontWeight_export_definition,
  fontSize: fontSize_export_definition,
  "font-size": fontSize_export_definition,
  lineHeight: lineHeight_export_definition,
  "line-height": lineHeight_export_definition,
  fontFamily: fontFamily_export_definition,
  "font-family": fontFamily_export_definition,
  font: font_export_definition,
  height: height_export_definition,
  left: left_export_definition,
  lightingColor: lightingColor_export_definition,
  "lighting-color": lightingColor_export_definition,
  marginTop: marginTop_export_definition,
  "margin-top": marginTop_export_definition,
  marginRight: marginRight_export_definition,
  "margin-right": marginRight_export_definition,
  marginBottom: marginBottom_export_definition,
  "margin-bottom": marginBottom_export_definition,
  marginLeft: marginLeft_export_definition,
  "margin-left": marginLeft_export_definition,
  margin: margin_export_definition,
  opacity: opacity_export_definition,
  outlineColor: outlineColor_export_definition,
  "outline-color": outlineColor_export_definition,
  paddingTop: paddingTop_export_definition,
  "padding-top": paddingTop_export_definition,
  paddingRight: paddingRight_export_definition,
  "padding-right": paddingRight_export_definition,
  paddingBottom: paddingBottom_export_definition,
  "padding-bottom": paddingBottom_export_definition,
  paddingLeft: paddingLeft_export_definition,
  "padding-left": paddingLeft_export_definition,
  padding: padding_export_definition,
  right: right_export_definition,
  stopColor: stopColor_export_definition,
  "stop-color": stopColor_export_definition,
  top: top_export_definition,
  webkitBorderAfterColor: webkitBorderAfterColor_export_definition,
  "-webkit-border-after-color": webkitBorderAfterColor_export_definition,
  "WebkitBorderAfterColor": webkitBorderAfterColor_export_definition,
  webkitBorderBeforeColor: webkitBorderBeforeColor_export_definition,
  "-webkit-border-before-color": webkitBorderBeforeColor_export_definition,
  "WebkitBorderBeforeColor": webkitBorderBeforeColor_export_definition,
  webkitBorderEndColor: webkitBorderEndColor_export_definition,
  "-webkit-border-end-color": webkitBorderEndColor_export_definition,
  "WebkitBorderEndColor": webkitBorderEndColor_export_definition,
  webkitBorderStartColor: webkitBorderStartColor_export_definition,
  "-webkit-border-start-color": webkitBorderStartColor_export_definition,
  "WebkitBorderStartColor": webkitBorderStartColor_export_definition,
  webkitColumnRuleColor: webkitColumnRuleColor_export_definition,
  "-webkit-column-rule-color": webkitColumnRuleColor_export_definition,
  "WebkitColumnRuleColor": webkitColumnRuleColor_export_definition,
  webkitTapHighlightColor: webkitTapHighlightColor_export_definition,
  "-webkit-tap-highlight-color": webkitTapHighlightColor_export_definition,
  "WebkitTapHighlightColor": webkitTapHighlightColor_export_definition,
  webkitTextEmphasisColor: webkitTextEmphasisColor_export_definition,
  "-webkit-text-emphasis-color": webkitTextEmphasisColor_export_definition,
  "WebkitTextEmphasisColor": webkitTextEmphasisColor_export_definition,
  webkitTextFillColor: webkitTextFillColor_export_definition,
  "-webkit-text-fill-color": webkitTextFillColor_export_definition,
  "WebkitTextFillColor": webkitTextFillColor_export_definition,
  webkitTextStrokeColor: webkitTextStrokeColor_export_definition,
  "-webkit-text-stroke-color": webkitTextStrokeColor_export_definition,
  "WebkitTextStrokeColor": webkitTextStrokeColor_export_definition,
  width: width_export_definition
};

"use strict";
// autogenerated - 2025-09-21

module.exports = new Map([
  [
    "-webkit-border-after-color",
    null
  ],
  [
    "-webkit-border-before-color",
    null
  ],
  [
    "-webkit-border-end-color",
    null
  ],
  [
    "-webkit-border-start-color",
    null
  ],
  [
    "-webkit-column-rule-color",
    null
  ],
  [
    "-webkit-tap-highlight-color",
    null
  ],
  [
    "-webkit-text-emphasis-color",
    null
  ],
  [
    "-webkit-text-fill-color",
    {
      "name": "-webkit-text-fill-color",
      "href": "https://compat.spec.whatwg.org/#propdef--webkit-text-fill-color",
      "value": "<color>",
      "initial": "currentcolor",
      "appliesTo": "all elements",
      "inherited": "yes",
      "percentages": "N/A",
      "computedValue": "an RGBA color",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "media": "visual",
      "styleDeclaration": [
        "-webkit-text-fill-color",
        "WebkitTextFillColor",
        "webkitTextFillColor"
      ]
    }
  ],
  [
    "-webkit-text-stroke-color",
    {
      "name": "-webkit-text-stroke-color",
      "href": "https://compat.spec.whatwg.org/#propdef--webkit-text-stroke-color",
      "value": "<color>",
      "initial": "currentcolor",
      "appliesTo": "all elements",
      "inherited": "yes",
      "percentages": "N/A",
      "computedValue": "an RGBA color",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "media": "visual",
      "styleDeclaration": [
        "-webkit-text-stroke-color",
        "WebkitTextStrokeColor",
        "webkitTextStrokeColor"
      ]
    }
  ],
  [
    "background",
    {
      "name": "background",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background",
      "value": "<bg-layer>#? , <final-bg-layer>",
      "initial": "see individual properties",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "see individual properties",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "see individual properties",
      "styleDeclaration": [
        "background"
      ]
    }
  ],
  [
    "background-attachment",
    {
      "name": "background-attachment",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-attachment",
      "value": "<attachment>#",
      "initial": "scroll",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "list, each item the keyword as specified",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "values": [
        {
          "name": "fixed",
          "prose": "The background is fixed with regard to the viewport. In paged media where there is no viewport, a fixed background is fixed with respect to the page box and therefore replicated on every page.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-attachment-fixed",
          "type": "value",
          "value": "fixed"
        },
        {
          "name": "local",
          "prose": "The background is fixed with regard to the box’s contents: if the box has a scrolling mechanism, the background scrolls with the box’s contents, and the background painting area and background positioning area are relative to the scrollable overflow area of the box rather than to the border framing them. Because the scrollable overflow area does not include the border area, for scroll containers the border-box value of background-clip may be treated the same as padding-box.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-attachment-local",
          "type": "value",
          "value": "local"
        },
        {
          "name": "scroll",
          "prose": "The background is fixed with regard to the box itself and does not scroll with its contents. (It is effectively attached to the box’s border.)",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-attachment-scroll",
          "type": "value",
          "value": "scroll"
        }
      ],
      "styleDeclaration": [
        "background-attachment",
        "backgroundAttachment"
      ]
    }
  ],
  [
    "background-clip",
    {
      "name": "background-clip",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-clip",
      "value": "<visual-box>#",
      "initial": "border-box",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "list, each item a keyword as specified",
      "canonicalOrder": "per grammar",
      "animationType": "repeatable list",
      "values": [
        {
          "name": "border-box",
          "prose": "The background is painted within (clipped to) the border box.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-clip-border-box",
          "type": "value",
          "value": "border-box"
        },
        {
          "name": "padding-box",
          "prose": "The background is painted within (clipped to) the padding box.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-clip-padding-box",
          "type": "value",
          "value": "padding-box"
        },
        {
          "name": "content-box",
          "prose": "The background is painted within (clipped to) the content box.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-clip-content-box",
          "type": "value",
          "value": "content-box"
        }
      ],
      "styleDeclaration": [
        "background-clip",
        "backgroundClip"
      ]
    }
  ],
  [
    "background-color",
    {
      "name": "background-color",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-color",
      "value": "<color>",
      "initial": "transparent",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "computed color",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "styleDeclaration": [
        "background-color",
        "backgroundColor"
      ]
    }
  ],
  [
    "background-image",
    {
      "name": "background-image",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-image",
      "value": "<bg-image>#",
      "initial": "none",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "list, each item either an <image> or the keyword none",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "values": [
        {
          "name": "none",
          "prose": "A value of none counts as a background image layer but draws nothing. An image that is empty (zero width or zero height), that fails to download, or that cannot be displayed (e.g., because it is not in a supported image format) likewise counts as a layer but draws nothing.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-image-none",
          "type": "value",
          "value": "none"
        }
      ],
      "styleDeclaration": [
        "background-image",
        "backgroundImage"
      ]
    }
  ],
  [
    "background-origin",
    {
      "name": "background-origin",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-origin",
      "value": "<visual-box>#",
      "initial": "padding-box",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "list, each item a keyword as specified",
      "canonicalOrder": "per grammar",
      "animationType": "repeatable list",
      "values": [
        {
          "name": "padding-box",
          "prose": "The position is relative to the padding box. (For single boxes 0 0 is the upper left corner of the padding edge, 100% 100% is the lower right corner.)",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-origin-padding-box",
          "type": "value",
          "value": "padding-box"
        },
        {
          "name": "border-box",
          "prose": "The position is relative to the border box.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-origin-border-box",
          "type": "value",
          "value": "border-box"
        },
        {
          "name": "content-box",
          "prose": "The position is relative to the content box.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-origin-content-box",
          "type": "value",
          "value": "content-box"
        }
      ],
      "styleDeclaration": [
        "background-origin",
        "backgroundOrigin"
      ]
    }
  ],
  [
    "background-position",
    {
      "name": "background-position",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-position",
      "value": "<bg-position>#",
      "initial": "0% 0%",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "refer to size of background positioning area minus size of background image; see text",
      "computedValue": "list, each item a pair of offsets (horizontal and vertical) from the top left origin each given as a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "repeatable list",
      "values": [
        {
          "name": "<percentage>",
          "prose": "A percentage for the horizontal offset is relative to (width of background positioning area - width of background image). A percentage for the vertical offset is relative to (height of background positioning area - height of background image), where the size of the image is the size given by background-size. For example, with a value pair of 0% 0%, the upper left corner of the image is aligned with the upper left corner of, usually, the box’s padding edge. A value pair of 100% 100% places the lower right corner of the image in the lower right corner of the area. With a value pair of 75% 50%, the point 75% across and 50% down the image is to be placed at the point 75% across and 50% down the area. Diagram of the meaning of background-position: 75% 50%.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-percentage",
          "type": "value",
          "value": "<percentage>"
        },
        {
          "name": "<length>",
          "prose": "A length value gives a fixed length as the offset. For example, with a value pair of 2cm 1cm, the upper left corner of the image is placed 2cm to the right and 1cm below the upper left corner of the background positioning area.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-length",
          "type": "value",
          "value": "<length>"
        },
        {
          "name": "top",
          "prose": "Computes to 0% for the vertical position if one or two values are given, otherwise specifies the top edge as the origin for the next offset.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-top",
          "type": "value",
          "value": "top"
        },
        {
          "name": "right",
          "prose": "Computes to 100% for the horizontal position if one or two values are given, otherwise specifies the right edge as the origin for the next offset.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-right",
          "type": "value",
          "value": "right"
        },
        {
          "name": "bottom",
          "prose": "Computes to 100% for the vertical position if one or two values are given, otherwise specifies the bottom edge as the origin for the next offset.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-bottom",
          "type": "value",
          "value": "bottom"
        },
        {
          "name": "left",
          "prose": "Computes to 0% for the horizontal position if one or two values are given, otherwise specifies the left edge as the origin for the next offset.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-left",
          "type": "value",
          "value": "left"
        },
        {
          "name": "center",
          "prose": "Computes to 50% (left 50%) for the horizontal position if the horizontal position is not otherwise specified, or 50% (top 50%) for the vertical position if it is.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-position-center",
          "type": "value",
          "value": "center"
        }
      ],
      "styleDeclaration": [
        "background-position",
        "backgroundPosition"
      ]
    }
  ],
  [
    "background-repeat",
    {
      "name": "background-repeat",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-repeat",
      "value": "<repeat-style>#",
      "initial": "repeat",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "list, each item a pair of keywords, one per dimension",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "values": [
        {
          "name": "repeat-x",
          "prose": "Computes to repeat no-repeat.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-repeat-repeat-x",
          "type": "value",
          "value": "repeat-x"
        },
        {
          "name": "repeat-y",
          "prose": "Computes to no-repeat repeat.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-repeat-repeat-y",
          "type": "value",
          "value": "repeat-y"
        },
        {
          "name": "repeat",
          "prose": "The image is repeated in this direction as often as needed to cover the background painting area.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-repeat-repeat",
          "type": "value",
          "value": "repeat"
        },
        {
          "name": "space",
          "prose": "The image is repeated as often as will fit within the background positioning area without being clipped, and then the images are spaced out to fill the area. The first and last images touch the edges of the area. If the background painting area is larger than the background positioning area, then the pattern repeats to fill the background painting area. The value of background-position for this direction is ignored unless there is not enough space for two copies of the image in this axis, in which case only one image is placed, and background-position determines its position in this axis.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-repeat-space",
          "type": "value",
          "value": "space"
        },
        {
          "name": "round",
          "prose": "The image is repeated as often as will fit within the background positioning area. If it doesn’t fit a whole number of times, it is rescaled so that it does. See the formula under background-size. If the background painting area is larger than the background positioning area, then the pattern repeats to fill the background painting area.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-repeat-round",
          "type": "value",
          "value": "round"
        },
        {
          "name": "no-repeat",
          "prose": "The image is placed once and not repeated in this direction.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-repeat-no-repeat",
          "type": "value",
          "value": "no-repeat"
        }
      ],
      "styleDeclaration": [
        "background-repeat",
        "backgroundRepeat"
      ]
    }
  ],
  [
    "background-size",
    {
      "name": "background-size",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-background-size",
      "value": "<bg-size>#",
      "initial": "auto",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "see text",
      "computedValue": "list, each item a pair of sizes (one per axis) each represented as either a keyword or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "repeatable list",
      "values": [
        {
          "name": "contain",
          "prose": "Scale the image, while preserving its natural aspect ratio (if any), to the largest size such that both its width and its height can fit inside the background positioning area.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-size-contain",
          "type": "value",
          "value": "contain"
        },
        {
          "name": "cover",
          "prose": "Scale the image, while preserving its natural aspect ratio (if any), to the smallest size such that both its width and its height can completely cover the background positioning area.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-size-cover",
          "type": "value",
          "value": "cover"
        },
        {
          "name": "<length-percentage [0,∞]>",
          "prose": "The first value gives the width of the corresponding image, the second value its height. If only one value is given the second is assumed to be auto. A <percentage> is relative to the background positioning area. An auto value for one dimension is resolved by using the image’s natural aspect ratio and the size of the other dimension, or failing that, using the image’s natural size, or failing that, treating it as 100%. If both values are auto then the natural width and/or height of the image should be used, if any, the missing dimension (if any) behaving as auto as described above. If the image has neither natural size, its size is determined as for contain. Negative values are invalid.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-size-length-percentage-0",
          "type": "value",
          "value": "<length-percentage [0,∞]>"
        },
        {
          "name": "auto",
          "prose": "The first value gives the width of the corresponding image, the second value its height. If only one value is given the second is assumed to be auto. A <percentage> is relative to the background positioning area. An auto value for one dimension is resolved by using the image’s natural aspect ratio and the size of the other dimension, or failing that, using the image’s natural size, or failing that, treating it as 100%. If both values are auto then the natural width and/or height of the image should be used, if any, the missing dimension (if any) behaving as auto as described above. If the image has neither natural size, its size is determined as for contain. Negative values are invalid.",
          "href": "https://drafts.csswg.org/css-backgrounds-3/#valdef-background-size-auto",
          "type": "value",
          "value": "auto"
        }
      ],
      "styleDeclaration": [
        "background-size",
        "backgroundSize"
      ]
    }
  ],
  [
    "border",
    {
      "name": "border",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-border",
      "value": "<line-width> || <line-style> || <color>",
      "initial": "See individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "see individual properties",
      "styleDeclaration": [
        "border"
      ]
    }
  ],
  [
    "border-bottom",
    {
      "name": "border-bottom",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-bottom",
      "value": "<line-width> || <line-style> || <color>",
      "initial": "See individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see individual properties",
      "animationType": "see individual properties",
      "canonicalOrder": "per grammar",
      "styleDeclaration": [
        "border-bottom",
        "borderBottom"
      ]
    }
  ],
  [
    "border-bottom-color",
    {
      "name": "border-bottom-color",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-bottom-color",
      "value": "<color> | <image-1D>",
      "initial": "currentcolor",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "the computed color and/or a one-dimensional image function",
      "canonicalOrder": "per grammar",
      "animationType": "see prose",
      "logicalPropertyGroup": "border-color",
      "styleDeclaration": [
        "border-bottom-color",
        "borderBottomColor"
      ]
    }
  ],
  [
    "border-bottom-style",
    {
      "name": "border-bottom-style",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-bottom-style",
      "value": "<line-style>",
      "initial": "none",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "specified keyword",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "logicalPropertyGroup": "border-style",
      "styleDeclaration": [
        "border-bottom-style",
        "borderBottomStyle"
      ]
    }
  ],
  [
    "border-bottom-width",
    {
      "name": "border-bottom-width",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-bottom-width",
      "value": "<line-width>",
      "initial": "medium",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "absolute length, snapped as a border width; zero if the border style is none or hidden",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "logicalPropertyGroup": "border-width",
      "styleDeclaration": [
        "border-bottom-width",
        "borderBottomWidth"
      ]
    }
  ],
  [
    "border-collapse",
    {
      "name": "border-collapse",
      "href": "https://drafts.csswg.org/css-tables-3/#propdef-border-collapse",
      "value": "separate | collapse",
      "initial": "separate",
      "appliesTo": "table grid boxes",
      "inherited": "yes",
      "percentages": "n/a",
      "computedValue": "specified keyword",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "styleDeclaration": [
        "border-collapse",
        "borderCollapse"
      ]
    }
  ],
  [
    "border-color",
    {
      "name": "border-color",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-color",
      "value": "[ <color> | <image-1D> ]{1,4}",
      "initial": "see individual properties",
      "appliesTo": "see individual properties",
      "inherited": "see individual properties",
      "percentages": "see individual properties",
      "computedValue": "see individual properties",
      "animationType": "see individual properties",
      "canonicalOrder": "per grammar",
      "styleDeclaration": [
        "border-color",
        "borderColor"
      ]
    }
  ],
  [
    "border-left",
    {
      "name": "border-left",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-left",
      "value": "<line-width> || <line-style> || <color>",
      "initial": "See individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see individual properties",
      "animationType": "see individual properties",
      "canonicalOrder": "per grammar",
      "styleDeclaration": [
        "border-left",
        "borderLeft"
      ]
    }
  ],
  [
    "border-left-color",
    {
      "name": "border-left-color",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-left-color",
      "value": "<color> | <image-1D>",
      "initial": "currentcolor",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "the computed color and/or a one-dimensional image function",
      "canonicalOrder": "per grammar",
      "animationType": "see prose",
      "logicalPropertyGroup": "border-color",
      "styleDeclaration": [
        "border-left-color",
        "borderLeftColor"
      ]
    }
  ],
  [
    "border-left-style",
    {
      "name": "border-left-style",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-left-style",
      "value": "<line-style>",
      "initial": "none",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "specified keyword",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "logicalPropertyGroup": "border-style",
      "styleDeclaration": [
        "border-left-style",
        "borderLeftStyle"
      ]
    }
  ],
  [
    "border-left-width",
    {
      "name": "border-left-width",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-left-width",
      "value": "<line-width>",
      "initial": "medium",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "absolute length, snapped as a border width; zero if the border style is none or hidden",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "logicalPropertyGroup": "border-width",
      "styleDeclaration": [
        "border-left-width",
        "borderLeftWidth"
      ]
    }
  ],
  [
    "border-right",
    {
      "name": "border-right",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-right",
      "value": "<line-width> || <line-style> || <color>",
      "initial": "See individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see individual properties",
      "animationType": "see individual properties",
      "canonicalOrder": "per grammar",
      "styleDeclaration": [
        "border-right",
        "borderRight"
      ]
    }
  ],
  [
    "border-right-color",
    {
      "name": "border-right-color",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-right-color",
      "value": "<color> | <image-1D>",
      "initial": "currentcolor",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "the computed color and/or a one-dimensional image function",
      "canonicalOrder": "per grammar",
      "animationType": "see prose",
      "logicalPropertyGroup": "border-color",
      "styleDeclaration": [
        "border-right-color",
        "borderRightColor"
      ]
    }
  ],
  [
    "border-right-style",
    {
      "name": "border-right-style",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-right-style",
      "value": "<line-style>",
      "initial": "none",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "specified keyword",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "logicalPropertyGroup": "border-style",
      "styleDeclaration": [
        "border-right-style",
        "borderRightStyle"
      ]
    }
  ],
  [
    "border-right-width",
    {
      "name": "border-right-width",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-right-width",
      "value": "<line-width>",
      "initial": "medium",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "absolute length, snapped as a border width; zero if the border style is none or hidden",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "logicalPropertyGroup": "border-width",
      "styleDeclaration": [
        "border-right-width",
        "borderRightWidth"
      ]
    }
  ],
  [
    "border-spacing",
    {
      "name": "border-spacing",
      "href": "https://drafts.csswg.org/css-tables-3/#propdef-border-spacing",
      "value": "<length>{1,2}",
      "initial": "0px 0px",
      "appliesTo": "table grid boxes when border-collapse is separate",
      "inherited": "yes",
      "percentages": "n/a",
      "computedValue": "two absolute lengths",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "styleDeclaration": [
        "border-spacing",
        "borderSpacing"
      ]
    }
  ],
  [
    "border-style",
    {
      "name": "border-style",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-border-style",
      "value": "<line-style>{1,4}",
      "initial": "see individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "see individual properties",
      "styleDeclaration": [
        "border-style",
        "borderStyle"
      ]
    }
  ],
  [
    "border-top",
    {
      "name": "border-top",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-top",
      "value": "<line-width> || <line-style> || <color>",
      "initial": "See individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see individual properties",
      "animationType": "see individual properties",
      "canonicalOrder": "per grammar",
      "styleDeclaration": [
        "border-top",
        "borderTop"
      ]
    }
  ],
  [
    "border-top-color",
    {
      "name": "border-top-color",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-top-color",
      "value": "<color> | <image-1D>",
      "initial": "currentcolor",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "the computed color and/or a one-dimensional image function",
      "canonicalOrder": "per grammar",
      "animationType": "see prose",
      "logicalPropertyGroup": "border-color",
      "styleDeclaration": [
        "border-top-color",
        "borderTopColor"
      ]
    }
  ],
  [
    "border-top-style",
    {
      "name": "border-top-style",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-top-style",
      "value": "<line-style>",
      "initial": "none",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "specified keyword",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "logicalPropertyGroup": "border-style",
      "styleDeclaration": [
        "border-top-style",
        "borderTopStyle"
      ]
    }
  ],
  [
    "border-top-width",
    {
      "name": "border-top-width",
      "href": "https://drafts.csswg.org/css-borders-4/#propdef-border-top-width",
      "value": "<line-width>",
      "initial": "medium",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "absolute length, snapped as a border width; zero if the border style is none or hidden",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "logicalPropertyGroup": "border-width",
      "styleDeclaration": [
        "border-top-width",
        "borderTopWidth"
      ]
    }
  ],
  [
    "border-width",
    {
      "name": "border-width",
      "href": "https://drafts.csswg.org/css-backgrounds-3/#propdef-border-width",
      "value": "<line-width>{1,4}",
      "initial": "see individual properties",
      "appliesTo": "all elements except ruby base containers and ruby annotation containers",
      "inherited": "no",
      "percentages": "see individual properties",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "see individual properties",
      "styleDeclaration": [
        "border-width",
        "borderWidth"
      ]
    }
  ],
  [
    "bottom",
    {
      "name": "bottom",
      "href": "https://drafts.csswg.org/css-position-3/#propdef-bottom",
      "value": "auto | <length-percentage>",
      "initial": "auto",
      "appliesTo": "positioned elements",
      "inherited": "no",
      "percentages": "refer to size of containing block; see prose",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "inset",
      "values": [
        {
          "name": "<length>",
          "prose": "The inset is a fixed distance from the reference edge. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-length",
          "type": "value",
          "value": "<length>"
        },
        {
          "name": "<percentage>",
          "prose": "The inset is a percentage relative to the containing block’s size in the corresponding axis (e.g. width for left or right, height for top and bottom). For sticky positioned boxes, the inset is instead relative to the relevant scrollport’s size. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-percentage",
          "type": "value",
          "value": "<percentage>"
        },
        {
          "name": "auto",
          "prose": "Represents an unconstrained inset; the exact meaning depends on the positioning scheme.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-auto",
          "type": "value",
          "value": "auto"
        }
      ],
      "styleDeclaration": [
        "bottom"
      ]
    }
  ],
  [
    "clear",
    {
      "name": "clear",
      "href": "https://drafts.csswg.org/css-page-floats-3/#propdef-clear",
      "value": "inline-start | inline-end | block-start | block-end | left | right | top | bottom | both-inline | both-block | both | none",
      "initial": "none",
      "appliesTo": "block-level elements, floats, regions, pages",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "specified keyword",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "values": [
        {
          "name": "inline-start",
          "prose": "If applied to an inline float, requires that the block-start outer edge of the box comes after the block-end outer edge of any inline-start-floats with an inline-start-float-reference that resulted from elements earlier in the source document. If applied to a page float, the float reference in which the page float is placed will be seen as full when determining whether it can host subsequent page floats that float in the inline-start direction.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-inline-start",
          "type": "value",
          "value": "inline-start"
        },
        {
          "name": "inline-end",
          "prose": "If applied to a block-level element or an inline float, requires that the block-start outer edge of the box comes after the block-end outer edge of any inline-end-floats with an inline-end-float-reference that resulted from elements earlier in the source document. If applied to a page float, the float reference in which the page float is placed will be seen as full when determining whether it can host subsequent page floats that float in the inline-end direction.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-inline-end",
          "type": "value",
          "value": "inline-end"
        },
        {
          "name": "block-start",
          "prose": "If applied to a block-level element or an inline float, behaves like inline-start. If applied to a page float, the float reference in which the page float is placed will be seen as full when determining whether it can host subsequent page floats that float in the block-start direction.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-block-start",
          "type": "value",
          "value": "block-start"
        },
        {
          "name": "block-end",
          "prose": "If applied to a block-level element or an inline float, behaves like inline-end. If applied to a page float, the float reference in which the page float is placed will be seen as full when determining whether it can host subsequent page floats that float in the block-end direction.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-block-end",
          "type": "value",
          "value": "block-end"
        },
        {
          "name": "left",
          "prose": "Behave like block-end, inline-start or inline-end depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-left",
          "type": "value",
          "value": "left"
        },
        {
          "name": "right",
          "prose": "Behave like block-start, inline-start or inline-end depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-right",
          "type": "value",
          "value": "right"
        },
        {
          "name": "top",
          "prose": "Behave like block-start or inline-start depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-top",
          "type": "value",
          "value": "top"
        },
        {
          "name": "bottom",
          "prose": "Behave like block-end or inline-end depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-bottom",
          "type": "value",
          "value": "bottom"
        },
        {
          "name": "both-inline",
          "prose": "Behave like inline-start and inline-end.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-both-inline",
          "type": "value",
          "value": "both-inline"
        },
        {
          "name": "both-block",
          "prose": "Behave like block-start and block-end.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-both-block",
          "type": "value",
          "value": "both-block"
        },
        {
          "name": "both",
          "prose": "Behave like both-inline.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-both",
          "type": "value",
          "value": "both"
        },
        {
          "name": "all",
          "prose": "Behave like both-block and both-inline.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-clear-all",
          "type": "value",
          "value": "all"
        }
      ],
      "styleDeclaration": [
        "clear"
      ]
    }
  ],
  [
    "clip",
    {
      "name": "clip",
      "href": "https://drafts.fxtf.org/css-masking-1/#propdef-clip",
      "value": "<rect()> | auto",
      "initial": "auto",
      "appliesTo": "Absolutely positioned elements. In SVG, it applies to elements which establish a new viewport, pattern elements and mask elements.",
      "inherited": "no",
      "percentages": "n/a",
      "computedValue": "as specified",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "media": "visual",
      "values": [
        {
          "name": "rect()",
          "value": "rect( <top>, <right>, <bottom>, <left> )",
          "href": "https://drafts.fxtf.org/css-masking-1/#funcdef-clip-rect",
          "type": "function"
        },
        {
          "name": "<top>",
          "prose": "<top>, <right>, <bottom>, and <left> may either have a <length> value or auto. Negative lengths are permitted. The value auto means that a given edge of the clipping region will be the same as the edge of the element’s generated border box (i.e., auto means the same as 0 for <top> and <left>, the same as the used value of the height plus the sum of vertical padding and border widths for <bottom>, and the same as the used value of the width plus the sum of the horizontal padding and border widths for <right>, such that four auto values result in the clipping region being the same as the element’s border box).",
          "href": "https://drafts.fxtf.org/css-masking-1/#typedef-clip-top",
          "type": "type"
        },
        {
          "name": "<right>",
          "prose": "<top>, <right>, <bottom>, and <left> may either have a <length> value or auto. Negative lengths are permitted. The value auto means that a given edge of the clipping region will be the same as the edge of the element’s generated border box (i.e., auto means the same as 0 for <top> and <left>, the same as the used value of the height plus the sum of vertical padding and border widths for <bottom>, and the same as the used value of the width plus the sum of the horizontal padding and border widths for <right>, such that four auto values result in the clipping region being the same as the element’s border box).",
          "href": "https://drafts.fxtf.org/css-masking-1/#typedef-clip-right",
          "type": "type"
        },
        {
          "name": "<bottom>",
          "prose": "<top>, <right>, <bottom>, and <left> may either have a <length> value or auto. Negative lengths are permitted. The value auto means that a given edge of the clipping region will be the same as the edge of the element’s generated border box (i.e., auto means the same as 0 for <top> and <left>, the same as the used value of the height plus the sum of vertical padding and border widths for <bottom>, and the same as the used value of the width plus the sum of the horizontal padding and border widths for <right>, such that four auto values result in the clipping region being the same as the element’s border box).",
          "href": "https://drafts.fxtf.org/css-masking-1/#typedef-clip-bottom",
          "type": "type"
        },
        {
          "name": "<left>",
          "prose": "<top>, <right>, <bottom>, and <left> may either have a <length> value or auto. Negative lengths are permitted. The value auto means that a given edge of the clipping region will be the same as the edge of the element’s generated border box (i.e., auto means the same as 0 for <top> and <left>, the same as the used value of the height plus the sum of vertical padding and border widths for <bottom>, and the same as the used value of the width plus the sum of the horizontal padding and border widths for <right>, such that four auto values result in the clipping region being the same as the element’s border box).",
          "href": "https://drafts.fxtf.org/css-masking-1/#typedef-clip-left",
          "type": "type"
        }
      ],
      "styleDeclaration": [
        "clip"
      ]
    }
  ],
  [
    "color",
    {
      "name": "color",
      "href": "https://drafts.csswg.org/css-color-4/#propdef-color",
      "value": "<color>",
      "initial": "CanvasText",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "N/A",
      "computedValue": "computed color, see resolving color values",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "styleDeclaration": [
        "color"
      ]
    }
  ],
  [
    "display",
    {
      "name": "display",
      "newValues": "<display-outside> || [ <display-inside> | math ]",
      "styleDeclaration": [
        "display"
      ]
    }
  ],
  [
    "flex",
    {
      "name": "flex",
      "href": "https://drafts.csswg.org/css-flexbox-1/#propdef-flex",
      "value": "none | [ <'flex-grow'> <'flex-shrink'>? || <'flex-basis'> ]",
      "initial": "0 1 auto",
      "appliesTo": "flex items",
      "inherited": "no",
      "percentages": "see individual properties",
      "computedValue": "see individual properties",
      "animationType": "by computed value type",
      "canonicalOrder": "per grammar",
      "values": [
        {
          "name": "<'flex-grow'>",
          "prose": "This <number [0,∞]> component sets flex-grow longhand and specifies the flex grow factor, which determines how much the flex item will grow relative to the rest of the flex items in the flex container when positive free space is distributed. When omitted, it is set to 1.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-flex-grow",
          "type": "value",
          "value": "<'flex-grow'>"
        },
        {
          "name": "<'flex-shrink'>",
          "prose": "This <number [0,∞]> component sets flex-shrink longhand and specifies the flex shrink factor, which determines how much the flex item will shrink relative to the rest of the flex items in the flex container when negative free space is distributed. When omitted, it is set to 1.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-flex-shrink",
          "type": "value",
          "value": "<'flex-shrink'>"
        },
        {
          "name": "<'flex-basis'>",
          "prose": "This component sets the flex-basis longhand, which specifies the flex basis: the initial main size of the flex item, before free space is distributed according to the flex factors. <'flex-basis'> accepts the same values as the width and height properties (except that auto is treated differently) plus the content keyword: When omitted from the flex shorthand, its specified value is 0.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-flex-basis",
          "type": "value",
          "value": "<'flex-basis'>"
        },
        {
          "name": "none",
          "prose": "The keyword none expands to 0 0 auto.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-none",
          "type": "value",
          "value": "none"
        }
      ],
      "styleDeclaration": [
        "flex"
      ]
    }
  ],
  [
    "flex-basis",
    {
      "name": "flex-basis",
      "href": "https://drafts.csswg.org/css-flexbox-1/#propdef-flex-basis",
      "value": "content | <'width'>",
      "initial": "auto",
      "appliesTo": "flex items",
      "inherited": "no",
      "percentages": "relative to the flex container’s inner main size",
      "computedValue": "specified keyword or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "auto",
          "prose": "When specified on a flex item, the auto keyword retrieves the value of the main size property as the used flex-basis. If that value is itself auto, then the used value is content.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-basis-auto",
          "type": "value",
          "value": "auto"
        },
        {
          "name": "content",
          "prose": "Indicates an automatic size based on the flex item’s content. (This is typically equivalent to the max-content size, but with adjustments to handle preferred aspect ratios, intrinsic sizing constraints, and orthogonal flows; see details in § 9 Flex Layout Algorithm.)",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-basis-content",
          "type": "value",
          "value": "content"
        }
      ],
      "styleDeclaration": [
        "flex-basis",
        "flexBasis"
      ]
    }
  ],
  [
    "flex-grow",
    {
      "name": "flex-grow",
      "href": "https://drafts.csswg.org/css-flexbox-1/#propdef-flex-grow",
      "value": "<number [0,∞]>",
      "initial": "0",
      "appliesTo": "flex items",
      "inherited": "no",
      "percentages": "n/a",
      "computedValue": "specified number",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "<number>",
          "prose": "The flex-grow property sets the flex grow factor to the provided <number>. Negative values are not allowed.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-grow-number",
          "type": "value",
          "value": "<number>"
        }
      ],
      "styleDeclaration": [
        "flex-grow",
        "flexGrow"
      ]
    }
  ],
  [
    "flex-shrink",
    {
      "name": "flex-shrink",
      "href": "https://drafts.csswg.org/css-flexbox-1/#propdef-flex-shrink",
      "value": "<number [0,∞]>",
      "initial": "1",
      "appliesTo": "flex items",
      "inherited": "no",
      "percentages": "n/a",
      "computedValue": "specified value",
      "canonicalOrder": "per grammar",
      "animationType": "number",
      "values": [
        {
          "name": "<number>",
          "prose": "The flex-shrink property sets the flex shrink factor to the provided <number>. Negative values are not allowed.",
          "href": "https://drafts.csswg.org/css-flexbox-1/#valdef-flex-shrink-number",
          "type": "value",
          "value": "<number>"
        }
      ],
      "styleDeclaration": [
        "flex-shrink",
        "flexShrink"
      ]
    }
  ],
  [
    "float",
    {
      "name": "float",
      "href": "https://drafts.csswg.org/css-page-floats-3/#propdef-float",
      "value": "block-start | block-end | inline-start | inline-end | snap-block | <snap-block()> | snap-inline | <snap-inline()> | left | right | top | bottom | none",
      "initial": "none",
      "appliesTo": "all elements.",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "as specified",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "inline-start",
          "prose": "If the float reference is a line box, the element generates a box that is floated to the line-start outer edge of the float reference and content flows on the line-end side of the box. If the float reference is not a line box, the element generates a box that is floated to the line-start and block-start outer edges of the float reference.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-inline-start",
          "type": "value",
          "value": "inline-start"
        },
        {
          "name": "inline-end",
          "prose": "If the float reference is a line box, the element generates a box that is floated to the line-ebd outer edge of the float reference and content flows on the line-start side of the box. If the float reference is not a line box, the element generates a box that is floated to the line-end and block-end outer edges of the float reference.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-inline-end",
          "type": "value",
          "value": "inline-end"
        },
        {
          "name": "block-start",
          "prose": "If the float reference is a line box, block-start behaves like inline-start. If the float reference is not a line box, the element generates a box that is floated to the block-start and line-start outer edges of the float reference. The initial value of the max-width or max-height property that refers to the inline size of the float is '100%'. Content flows on the block-end side of the box.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-block-start",
          "type": "value",
          "value": "block-start"
        },
        {
          "name": "block-end",
          "prose": "If the float reference is a line box, block-end behaves like inline-end. If the float reference is not a line box, the element generates a box that is floated to the block-end and line-end outer edges of the float reference. The initial value of the max-width or max-height property that refers to the inline size of the float is '100%'. Content flows on the block-start side of the box.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-block-end",
          "type": "value",
          "value": "block-end"
        },
        {
          "name": "left",
          "prose": "If the float reference is a line box, behaves like inline-start or inline-end, whichever corresponds to line-left for the float reference. Otherwise, behaves like block-end, inline-start or inline-end depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-left",
          "type": "value",
          "value": "left"
        },
        {
          "name": "right",
          "prose": "If the float reference is a line box, behaves like inline-start or inline-end, whichever corresponds to line-right for the float reference. Otherwise, behaves like block-start, inline-start or inline-end depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-right",
          "type": "value",
          "value": "right"
        },
        {
          "name": "top",
          "prose": "Behave like block-start or inline-start depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-top",
          "type": "value",
          "value": "top"
        },
        {
          "name": "bottom",
          "prose": "Behave like block-end or inline-end depending on the float containing block’s direction and writing-mode.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-bottom",
          "type": "value",
          "value": "bottom"
        },
        {
          "name": "snap-block()",
          "href": "https://drafts.csswg.org/css-page-floats-3/#funcdef-float-snap-block",
          "type": "function",
          "value": "snap-block( <length> , [ start | end | near ]? )"
        },
        {
          "name": "snap-block",
          "prose": "Behaves as snap-block(2em, near)",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-snap-block",
          "type": "value",
          "value": "snap-block"
        },
        {
          "name": "snap-inline()",
          "href": "https://drafts.csswg.org/css-page-floats-3/#funcdef-float-snap-inline",
          "type": "function",
          "value": "snap-inline( <length> , [ left | right | near ]? )"
        },
        {
          "name": "snap-inline",
          "prose": "same as snap-inline(2em, near)",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-snap-inline",
          "type": "value",
          "value": "snap-inline"
        },
        {
          "name": "none",
          "prose": "The box is not floated.",
          "href": "https://drafts.csswg.org/css-page-floats-3/#valdef-float-none",
          "type": "value",
          "value": "none"
        }
      ],
      "styleDeclaration": [
        "float"
      ]
    }
  ],
  [
    "flood-color",
    {
      "name": "flood-color",
      "href": "https://drafts.fxtf.org/filter-effects-1/#propdef-flood-color",
      "value": "<color>",
      "initial": "black",
      "appliesTo": "feFlood and feDropShadow elements",
      "inherited": "no",
      "percentages": "n/a",
      "computedValue": "as specified",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "media": "visual",
      "styleDeclaration": [
        "flood-color",
        "floodColor"
      ]
    }
  ],
  [
    "font",
    {
      "name": "font",
      "href": "https://drafts.csswg.org/css-fonts-4/#propdef-font",
      "value": "[ [ <'font-style'> || <font-variant-css2> || <'font-weight'> || <font-width-css3> ]? <'font-size'> [ / <'line-height'> ]? <'font-family'># ] | <system-family-name>",
      "initial": "see individual properties",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "see individual properties",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "see individual properties",
      "values": [
        {
          "name": "caption",
          "prose": "The font used for captioned controls (e.g., buttons, drop-downs, etc.).",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-caption",
          "type": "value",
          "value": "caption"
        },
        {
          "name": "icon",
          "prose": "The font used to label icons.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-icon",
          "type": "value",
          "value": "icon"
        },
        {
          "name": "menu",
          "prose": "The font used in menus (e.g., dropdown menus and menu lists).",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-menu",
          "type": "value",
          "value": "menu"
        },
        {
          "name": "message-box",
          "prose": "The font used in dialog boxes.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-message-box",
          "type": "value",
          "value": "message-box"
        },
        {
          "name": "small-caption",
          "prose": "The font used for labeling small controls.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-small-caption",
          "type": "value",
          "value": "small-caption"
        },
        {
          "name": "status-bar",
          "prose": "The font used in window status bars.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-status-bar",
          "type": "value",
          "value": "status-bar"
        }
      ],
      "styleDeclaration": [
        "font"
      ]
    }
  ],
  [
    "font-family",
    {
      "name": "font-family",
      "href": "https://drafts.csswg.org/css-fonts-4/#propdef-font-family",
      "value": "[ <family-name> | <generic-family> ]#",
      "initial": "depends on user agent",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "n/a",
      "computedValue": "list, each item a string and/or <generic-family> keywords",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "styleDeclaration": [
        "font-family",
        "fontFamily"
      ]
    }
  ],
  [
    "font-size",
    {
      "name": "font-size",
      "href": "https://drafts.csswg.org/css-fonts-4/#propdef-font-size",
      "value": "<absolute-size> | <relative-size> | <length-percentage [0,∞]> | math",
      "initial": "medium",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "refer to parent element’s font size",
      "computedValue": "an absolute length",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "<absolute-size>",
          "value": "<absolute-size>",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-size-absolute-size",
          "type": "value"
        },
        {
          "name": "<relative-size>",
          "value": "<relative-size>",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-size-relative-size",
          "type": "value"
        },
        {
          "name": "<length-percentage [0,∞]>",
          "prose": "A length value specifies an absolute font size (independent of the user agent’s font table). Negative lengths are invalid. A percentage value specifies an absolute font size relative to the parent element’s computed font-size. Negative percentages are invalid.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-size-length-percentage-0",
          "type": "value",
          "value": "<length-percentage [0,∞]>"
        },
        {
          "name": "math",
          "prose": "Special mathematical scaling rules must be applied when determining the computed value of the font-size property.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-size-math",
          "type": "value",
          "value": "math"
        }
      ],
      "styleDeclaration": [
        "font-size",
        "fontSize"
      ]
    }
  ],
  [
    "font-style",
    {
      "name": "font-style",
      "href": "https://drafts.csswg.org/css-fonts-4/#propdef-font-style",
      "value": "normal | italic | left | right | oblique <angle [-90deg,90deg]>?",
      "initial": "normal",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "n/a",
      "computedValue": "the keyword specified, plus angle in degrees if specified",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type;normal animates as oblique 0deg",
      "values": [
        {
          "name": "normal",
          "prose": "Matches against a face that is classified as a normal face, one that is neither italic or obliqued. This represents an oblique value of \"0\".",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-style-normal",
          "type": "value",
          "value": "normal"
        },
        {
          "name": "italic",
          "prose": "Matches against a font that is labeled as an italic face, or an oblique face if one does not exist. The angle and direction of slant is unspecified.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-style-italic",
          "type": "value",
          "value": "italic"
        },
        {
          "name": "left",
          "prose": "Matches against a font that is labeled as an italic face, with a positive (clockwise) slant; or an oblique face with positive slant, if one does not exist.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-style-left",
          "type": "value",
          "value": "left"
        },
        {
          "name": "right",
          "prose": "Matches against a font that is labeled as an italic face, with a negative (counter-clockwise) slant; or an oblique face with negative slant, if one does not exist.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-style-right",
          "type": "value",
          "value": "right"
        },
        {
          "name": "oblique <angle [-90deg,90deg]>?",
          "prose": "Controls matching against an oblique face. Positive angles represent a clockwise slant; negative angles represent a counter-clockwise slant. The lack of an <angle> represents 14deg. (Note that a font might internally provide its own mapping for \"oblique\", but the mapping within the font is disregarded.) Fractional and negative values are accepted; however, values less than -90deg or values greater than 90deg are invalid. If no oblique faces exist, and font-synthesis-style has the value auto, a synthetic oblique face will be generated.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-style-oblique-angle--90deg-90deg",
          "type": "value",
          "value": "oblique <angle [-90deg,90deg]>?"
        }
      ],
      "styleDeclaration": [
        "font-style",
        "fontStyle"
      ]
    }
  ],
  [
    "font-variant",
    {
      "name": "font-variant",
      "href": "https://drafts.csswg.org/css-fonts-4/#propdef-font-variant",
      "value": "normal | none | [ [ <common-lig-values> || <discretionary-lig-values> || <historical-lig-values> || <contextual-alt-values> ] || [ small-caps | all-small-caps | petite-caps | all-petite-caps | unicase | titling-caps ] || [ stylistic(<feature-value-name>) || historical-forms || styleset(<feature-value-name>#) || character-variant(<feature-value-name>#) || swash(<feature-value-name>) || ornaments(<feature-value-name>) || annotation(<feature-value-name>) ] || [ <numeric-figure-values> || <numeric-spacing-values> || <numeric-fraction-values> || ordinal || slashed-zero ] || [ <east-asian-variant-values> || <east-asian-width-values> || ruby ] || [ sub | super ] || [ text | emoji | unicode ] ]",
      "initial": "normal",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "n/a",
      "computedValue": "as specified",
      "canonicalOrder": "per grammar",
      "animationType": "discrete",
      "values": [
        {
          "name": "normal",
          "prose": "The value normal resets all subproperties of font-variant to their initial value. The none value sets font-variant-ligatures to none and resets all other font feature properties to their initial value. Like other shorthands, using font-variant resets unspecified font-variant subproperties to their initial values.",
          "href": "https://drafts.csswg.org/css-fonts-4/#font-variant-normal-value",
          "type": "value",
          "value": "normal"
        },
        {
          "name": "none",
          "prose": "The value normal resets all subproperties of font-variant to their initial value. The none value sets font-variant-ligatures to none and resets all other font feature properties to their initial value. Like other shorthands, using font-variant resets unspecified font-variant subproperties to their initial values.",
          "href": "https://drafts.csswg.org/css-fonts-4/#font-variant-none-value",
          "type": "value",
          "value": "none"
        }
      ],
      "styleDeclaration": [
        "font-variant",
        "fontVariant"
      ]
    }
  ],
  [
    "font-weight",
    {
      "name": "font-weight",
      "href": "https://drafts.csswg.org/css-fonts-4/#propdef-font-weight",
      "value": "<font-weight-absolute> | bolder | lighter",
      "initial": "normal",
      "appliesTo": "all elements and text",
      "inherited": "yes",
      "percentages": "n/a",
      "computedValue": "a number, see below",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "<number [1,1000]>",
          "prose": "Each number indicates a weight that is at least as dark as its predecessor. Only values greater than or equal to 1, and less than or equal to 1000, are valid, and all other values are invalid. Numeric values typically correspond to the commonly used weight names below. 100 - Thin 200 - Extra Light (Ultra Light) 300 - Light 400 - Normal 500 - Medium 600 - Semi Bold (Demi Bold) 700 - Bold 800 - Extra Bold (Ultra Bold) 900 - Black (Heavy)",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-weight-number-1-1000",
          "type": "value",
          "value": "<number [1,1000]>"
        },
        {
          "name": "normal",
          "prose": "Same as 400.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-weight-normal",
          "type": "value",
          "value": "normal"
        },
        {
          "name": "bold",
          "prose": "Same as 700.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-weight-bold",
          "type": "value",
          "value": "bold"
        },
        {
          "name": "bolder",
          "prose": "Specifies a bolder weight than the inherited value. See § 2.2.1 Relative Weights.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-weight-bolder",
          "type": "value",
          "value": "bolder"
        },
        {
          "name": "lighter",
          "prose": "Specifies a lighter weight than the inherited value. See § 2.2.1 Relative Weights.",
          "href": "https://drafts.csswg.org/css-fonts-4/#valdef-font-weight-lighter",
          "type": "value",
          "value": "lighter"
        }
      ],
      "styleDeclaration": [
        "font-weight",
        "fontWeight"
      ]
    }
  ],
  [
    "height",
    {
      "name": "height",
      "href": "https://drafts.csswg.org/css-sizing-3/#propdef-height",
      "value": "auto | <length-percentage [0,∞]> | min-content | max-content | fit-content(<length-percentage [0,∞]>) | <calc-size()>",
      "initial": "auto",
      "appliesTo": "all elements except non-replaced inlines",
      "inherited": "no",
      "percentages": "relative to width/height of containing block",
      "computedValue": "as specified, with <length-percentage> values computed",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type, recursing into fit-content()",
      "logicalPropertyGroup": "size",
      "values": [
        {
          "name": "<length-percentage [0,∞]>",
          "prose": "Specifies the size of the box using <length> and/or <percentage>. The box-sizing property indicates whether the content box or border box is measured. Percentages are resolved against the width/height, as appropriate, of the box’s containing block. If, in a particular axis, the containing block’s size depends on the box’s size, see the relevant layout module for special rules on how to resolve percentages. Negative values are invalid.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-length-percentage-0",
          "type": "value",
          "value": "<length-percentage [0,∞]>"
        },
        {
          "name": "auto",
          "prose": "For width/height, specifies an automatic size (automatic block size/automatic inline size). See the relevant layout module for how to calculate this. For min-width/min-height, specifies an automatic minimum size. Unless otherwise defined by the relevant layout module, however, it resolves to a used value of 0. For backwards-compatibility, the resolved value of this keyword is zero for boxes of all [CSS2] display types: block and inline boxes, inline blocks, and all the table layout boxes. It also resolves to zero when no box is generated.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-auto",
          "type": "value",
          "value": "auto"
        },
        {
          "name": "min-content",
          "prose": "Use the min-content size in the relevant axis; for a box’s block size, unless otherwise specified, this is equivalent to its automatic size.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-min-content",
          "type": "value",
          "value": "min-content"
        },
        {
          "name": "max-content",
          "prose": "Use the max-content size in the relevant axis; for a box’s block size, unless otherwise specified, this is equivalent to its automatic size.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-max-content",
          "type": "value",
          "value": "max-content"
        },
        {
          "name": "fit-content()",
          "value": "fit-content(<length-percentage [0,∞]>)",
          "href": "https://drafts.csswg.org/css-sizing-3/#funcdef-width-fit-content",
          "type": "function"
        },
        {
          "name": "calc-size()",
          "href": "https://drafts.csswg.org/css-sizing-3/#funcdef-width-calc-size",
          "type": "function"
        }
      ],
      "styleDeclaration": [
        "height"
      ]
    }
  ],
  [
    "left",
    {
      "name": "left",
      "href": "https://drafts.csswg.org/css-position-3/#propdef-left",
      "value": "auto | <length-percentage>",
      "initial": "auto",
      "appliesTo": "positioned elements",
      "inherited": "no",
      "percentages": "refer to size of containing block; see prose",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "inset",
      "values": [
        {
          "name": "<length>",
          "prose": "The inset is a fixed distance from the reference edge. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-length",
          "type": "value",
          "value": "<length>"
        },
        {
          "name": "<percentage>",
          "prose": "The inset is a percentage relative to the containing block’s size in the corresponding axis (e.g. width for left or right, height for top and bottom). For sticky positioned boxes, the inset is instead relative to the relevant scrollport’s size. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-percentage",
          "type": "value",
          "value": "<percentage>"
        },
        {
          "name": "auto",
          "prose": "Represents an unconstrained inset; the exact meaning depends on the positioning scheme.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-auto",
          "type": "value",
          "value": "auto"
        }
      ],
      "styleDeclaration": [
        "left"
      ]
    }
  ],
  [
    "lighting-color",
    {
      "name": "lighting-color",
      "href": "https://drafts.fxtf.org/filter-effects-1/#propdef-lighting-color",
      "value": "<color>",
      "initial": "white",
      "appliesTo": "feDiffuseLighting and feSpecularLighting elements",
      "inherited": "no",
      "percentages": "n/a",
      "computedValue": "as specified",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "media": "visual",
      "styleDeclaration": [
        "lighting-color",
        "lightingColor"
      ]
    }
  ],
  [
    "line-height",
    {
      "name": "line-height",
      "href": "https://drafts.csswg.org/css-inline-3/#propdef-line-height",
      "value": "normal | <number [0,∞]> | <length-percentage [0,∞]>",
      "initial": "normal",
      "appliesTo": "non-replaced inline boxes and SVG text content elements",
      "inherited": "yes",
      "percentages": "computed relative to 1em",
      "computedValue": "the specified keyword, a number, or a computed <length> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "normal",
          "prose": "Determine the preferred line height automatically based on font metrics.",
          "href": "https://drafts.csswg.org/css-inline-3/#valdef-line-height-normal",
          "type": "value",
          "value": "normal"
        },
        {
          "name": "<length [0,∞]>",
          "prose": "The specified length is used as the preferred line height. Negative values are illegal.",
          "href": "https://drafts.csswg.org/css-inline-3/#valdef-line-height-length-0",
          "type": "value",
          "value": "<length [0,∞]>"
        },
        {
          "name": "<number [0,∞]>",
          "prose": "The preferred line height is this number multiplied by the element’s computed font-size. Negative values are illegal. The computed value is the same as the specified value.",
          "href": "https://drafts.csswg.org/css-inline-3/#valdef-line-height-number-0",
          "type": "value",
          "value": "<number [0,∞]>"
        },
        {
          "name": "<percentage [0,∞]>",
          "prose": "The preferred line height and computed value of the property is this percentage of the element’s computed font-size. Negative values are illegal.",
          "href": "https://drafts.csswg.org/css-inline-3/#valdef-line-height-percentage-0",
          "type": "value",
          "value": "<percentage [0,∞]>"
        }
      ],
      "styleDeclaration": [
        "line-height",
        "lineHeight"
      ]
    }
  ],
  [
    "margin",
    {
      "name": "margin",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-margin",
      "value": "<'margin-top'>{1,4}",
      "initial": "0",
      "appliesTo": "all elements except internal table elements, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "styleDeclaration": [
        "margin"
      ]
    }
  ],
  [
    "margin-bottom",
    {
      "name": "margin-bottom",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-margin-bottom",
      "value": "<length-percentage> | auto",
      "initial": "0",
      "appliesTo": "all elements except internal table elements, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "margin",
      "styleDeclaration": [
        "margin-bottom",
        "marginBottom"
      ]
    }
  ],
  [
    "margin-left",
    {
      "name": "margin-left",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-margin-left",
      "value": "<length-percentage> | auto",
      "initial": "0",
      "appliesTo": "all elements except internal table elements, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "margin",
      "styleDeclaration": [
        "margin-left",
        "marginLeft"
      ]
    }
  ],
  [
    "margin-right",
    {
      "name": "margin-right",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-margin-right",
      "value": "<length-percentage> | auto",
      "initial": "0",
      "appliesTo": "all elements except internal table elements, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "margin",
      "styleDeclaration": [
        "margin-right",
        "marginRight"
      ]
    }
  ],
  [
    "margin-top",
    {
      "name": "margin-top",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-margin-top",
      "value": "<length-percentage> | auto",
      "initial": "0",
      "appliesTo": "all elements except internal table elements, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "margin",
      "styleDeclaration": [
        "margin-top",
        "marginTop"
      ]
    }
  ],
  [
    "opacity",
    {
      "name": "opacity",
      "href": "https://drafts.csswg.org/css-color-4/#propdef-opacity",
      "value": "<opacity-value>",
      "initial": "1",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "map to the range [0,1]",
      "computedValue": "specified number, clamped to the range [0,1]",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "values": [
        {
          "name": "<opacity-value>",
          "prose": "The opacity to be applied to the element. The resulting opacity is applied to the entire element, rather than a particular color. Opacity values outside the range [0,1] are not invalid, and are preserved in specified values, but are clamped to the range [0, 1] in computed values.",
          "href": "https://drafts.csswg.org/css-color-4/#typedef-opacity-opacity-value",
          "type": "type",
          "value": "<number> | <percentage>"
        }
      ],
      "styleDeclaration": [
        "opacity"
      ]
    }
  ],
  [
    "outline-color",
    {
      "name": "outline-color",
      "href": "https://drafts.csswg.org/css-ui-4/#propdef-outline-color",
      "value": "auto | <color> | <image-1D>",
      "initial": "auto",
      "appliesTo": "all elements",
      "inherited": "no",
      "percentages": "N/A",
      "computedValue": "see below",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value",
      "values": [
        {
          "name": "auto",
          "prose": "When outline-style is auto, outline-color: auto computes to auto and represents the accent color. Otherwise, outline-color: auto computes to currentColor.",
          "href": "https://drafts.csswg.org/css-ui-4/#valdef-outline-color-auto",
          "type": "value",
          "value": "auto"
        }
      ],
      "styleDeclaration": [
        "outline-color",
        "outlineColor"
      ]
    }
  ],
  [
    "padding",
    {
      "name": "padding",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-padding",
      "value": "<'padding-top'>{1,4}",
      "initial": "0",
      "appliesTo": "all elements except: internal table elements other than table cells, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "see individual properties",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "styleDeclaration": [
        "padding"
      ]
    }
  ],
  [
    "padding-bottom",
    {
      "name": "padding-bottom",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-padding-bottom",
      "value": "<length-percentage [0,∞]>",
      "initial": "0",
      "appliesTo": "all elements except: internal table elements other than table cells, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "padding",
      "styleDeclaration": [
        "padding-bottom",
        "paddingBottom"
      ]
    }
  ],
  [
    "padding-left",
    {
      "name": "padding-left",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-padding-left",
      "value": "<length-percentage [0,∞]>",
      "initial": "0",
      "appliesTo": "all elements except: internal table elements other than table cells, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "padding",
      "styleDeclaration": [
        "padding-left",
        "paddingLeft"
      ]
    }
  ],
  [
    "padding-right",
    {
      "name": "padding-right",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-padding-right",
      "value": "<length-percentage [0,∞]>",
      "initial": "0",
      "appliesTo": "all elements except: internal table elements other than table cells, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "padding",
      "styleDeclaration": [
        "padding-right",
        "paddingRight"
      ]
    }
  ],
  [
    "padding-top",
    {
      "name": "padding-top",
      "href": "https://drafts.csswg.org/css-box-4/#propdef-padding-top",
      "value": "<length-percentage [0,∞]>",
      "initial": "0",
      "appliesTo": "all elements except: internal table elements other than table cells, ruby base containers, and ruby annotation containers",
      "inherited": "no",
      "percentages": "refer to logical width of containing block",
      "computedValue": "a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "padding",
      "styleDeclaration": [
        "padding-top",
        "paddingTop"
      ]
    }
  ],
  [
    "right",
    {
      "name": "right",
      "href": "https://drafts.csswg.org/css-position-3/#propdef-right",
      "value": "auto | <length-percentage>",
      "initial": "auto",
      "appliesTo": "positioned elements",
      "inherited": "no",
      "percentages": "refer to size of containing block; see prose",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "inset",
      "values": [
        {
          "name": "<length>",
          "prose": "The inset is a fixed distance from the reference edge. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-length",
          "type": "value",
          "value": "<length>"
        },
        {
          "name": "<percentage>",
          "prose": "The inset is a percentage relative to the containing block’s size in the corresponding axis (e.g. width for left or right, height for top and bottom). For sticky positioned boxes, the inset is instead relative to the relevant scrollport’s size. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-percentage",
          "type": "value",
          "value": "<percentage>"
        },
        {
          "name": "auto",
          "prose": "Represents an unconstrained inset; the exact meaning depends on the positioning scheme.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-auto",
          "type": "value",
          "value": "auto"
        }
      ],
      "styleDeclaration": [
        "right"
      ]
    }
  ],
  [
    "stop-color",
    {
      "name": "stop-color",
      "href": "https://svgwg.org/svg2-draft/pservers.html#StopColorProperty",
      "styleDeclaration": [
        "stop-color",
        "stopColor"
      ]
    }
  ],
  [
    "top",
    {
      "name": "top",
      "href": "https://drafts.csswg.org/css-position-3/#propdef-top",
      "value": "auto | <length-percentage>",
      "initial": "auto",
      "appliesTo": "positioned elements",
      "inherited": "no",
      "percentages": "refer to size of containing block; see prose",
      "computedValue": "the keyword auto or a computed <length-percentage> value",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type",
      "logicalPropertyGroup": "inset",
      "values": [
        {
          "name": "<length>",
          "prose": "The inset is a fixed distance from the reference edge. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-length",
          "type": "value",
          "value": "<length>"
        },
        {
          "name": "<percentage>",
          "prose": "The inset is a percentage relative to the containing block’s size in the corresponding axis (e.g. width for left or right, height for top and bottom). For sticky positioned boxes, the inset is instead relative to the relevant scrollport’s size. Negative values are allowed.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-percentage",
          "type": "value",
          "value": "<percentage>"
        },
        {
          "name": "auto",
          "prose": "Represents an unconstrained inset; the exact meaning depends on the positioning scheme.",
          "href": "https://drafts.csswg.org/css-position-3/#valdef-top-auto",
          "type": "value",
          "value": "auto"
        }
      ],
      "styleDeclaration": [
        "top"
      ]
    }
  ],
  [
    "width",
    {
      "name": "width",
      "href": "https://drafts.csswg.org/css-sizing-3/#propdef-width",
      "value": "auto | <length-percentage [0,∞]> | min-content | max-content | fit-content(<length-percentage [0,∞]>) | <calc-size()>",
      "initial": "auto",
      "appliesTo": "all elements except non-replaced inlines",
      "inherited": "no",
      "percentages": "relative to width/height of containing block",
      "computedValue": "as specified, with <length-percentage> values computed",
      "canonicalOrder": "per grammar",
      "animationType": "by computed value type, recursing into fit-content()",
      "logicalPropertyGroup": "size",
      "values": [
        {
          "name": "<length-percentage [0,∞]>",
          "prose": "Specifies the size of the box using <length> and/or <percentage>. The box-sizing property indicates whether the content box or border box is measured. Percentages are resolved against the width/height, as appropriate, of the box’s containing block. If, in a particular axis, the containing block’s size depends on the box’s size, see the relevant layout module for special rules on how to resolve percentages. Negative values are invalid.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-length-percentage-0",
          "type": "value",
          "value": "<length-percentage [0,∞]>"
        },
        {
          "name": "auto",
          "prose": "For width/height, specifies an automatic size (automatic block size/automatic inline size). See the relevant layout module for how to calculate this. For min-width/min-height, specifies an automatic minimum size. Unless otherwise defined by the relevant layout module, however, it resolves to a used value of 0. For backwards-compatibility, the resolved value of this keyword is zero for boxes of all [CSS2] display types: block and inline boxes, inline blocks, and all the table layout boxes. It also resolves to zero when no box is generated.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-auto",
          "type": "value",
          "value": "auto"
        },
        {
          "name": "min-content",
          "prose": "Use the min-content size in the relevant axis; for a box’s block size, unless otherwise specified, this is equivalent to its automatic size.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-min-content",
          "type": "value",
          "value": "min-content"
        },
        {
          "name": "max-content",
          "prose": "Use the max-content size in the relevant axis; for a box’s block size, unless otherwise specified, this is equivalent to its automatic size.",
          "href": "https://drafts.csswg.org/css-sizing-3/#valdef-width-max-content",
          "type": "value",
          "value": "max-content"
        },
        {
          "name": "fit-content()",
          "value": "fit-content(<length-percentage [0,∞]>)",
          "href": "https://drafts.csswg.org/css-sizing-3/#funcdef-width-fit-content",
          "type": "function"
        },
        {
          "name": "calc-size()",
          "href": "https://drafts.csswg.org/css-sizing-3/#funcdef-width-calc-size",
          "type": "function"
        }
      ],
      "styleDeclaration": [
        "width"
      ]
    }
  ]
]);

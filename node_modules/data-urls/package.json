{"name": "data-urls", "description": "Parses data: URLs", "keywords": ["data url", "data uri", "data:", "http", "fetch", "whatwg"], "version": "6.0.0", "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/data-urls.git"}, "main": "lib/parser.js", "files": ["lib/"], "scripts": {"test": "node --test", "coverage": "c8 node --test --experimental-test-coverage", "lint": "eslint .", "pretest": "node scripts/get-latest-platform-tests.js"}, "dependencies": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^15.0.0"}, "devDependencies": {"@domenic/eslint-config": "^4.0.1", "c8": "^10.1.3", "eslint": "^9.35.0", "globals": "^16.4.0"}, "engines": {"node": ">=20"}, "c8": {"reporter": ["text", "html"], "exclude": ["scripts/", "test/"]}}
{"name": "tr46", "version": "6.0.0", "engines": {"node": ">=20"}, "description": "An implementation of the Unicode UTS #46: Unicode IDNA Compatibility Processing", "main": "index.js", "files": ["index.js", "lib/"], "scripts": {"test": "node --test", "lint": "eslint", "pretest": "node scripts/getLatestTests.js", "prepublish": "node scripts/generateMappingTable.js && node scripts/generateRegexes.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/tr46.git"}, "keywords": ["unicode", "tr46", "uts46", "punycode", "url", "whatwg"], "author": "<PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>"], "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "devDependencies": {"@domenic/eslint-config": "^4.0.1", "@unicode/unicode-17.0.0": "^1.6.12", "eslint": "^9.35.0", "globals": "^16.4.0", "regenerate": "^1.4.2"}, "unicodeVersion": "17.0.0"}
{"name": "webidl-conversions", "version": "8.0.0", "description": "Implements the WebIDL algorithms for converting to and from JavaScript values", "main": "lib/index.js", "scripts": {"lint": "eslint .", "test": "node --test", "coverage": "c8 node --test --experimental-test-coverage test/*.js"}, "repository": {"type": "git", "url": "git+https://github.com/jsdom/webidl-conversions.git"}, "keywords": ["webidl", "web", "types"], "files": ["lib/"], "author": "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "license": "BSD-2-<PERSON><PERSON>", "devDependencies": {"@domenic/eslint-config": "^4.0.1", "c8": "^10.1.3", "eslint": "^9.35.0", "globals": "^16.3.0"}, "engines": {"node": ">=20"}}
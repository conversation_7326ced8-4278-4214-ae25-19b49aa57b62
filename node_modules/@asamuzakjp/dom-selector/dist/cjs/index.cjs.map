{"version": 3, "sources": ["../../src/index.js", "../../src/js/parser.js", "../../src/js/utility.js", "../../src/js/constant.js", "../../src/js/matcher.js", "../../src/js/finder.js"], "sourcesContent": ["/*!\n * DOM Selector - A CSS selector engine.\n * @license MIT\n * @copyright asamuzaK (Ka<PERSON>)\n * @see {@link https://github.com/asamuzaK/domSelector/blob/main/LICENSE}\n */\n\n/* import */\nimport { Finder } from './js/finder.js';\nimport { filterSelector, getType, initNwsapi } from './js/utility.js';\n\n/* constants */\nimport {\n  COMBO, COMPOUND_I, DESCEND, DOCUMENT_NODE, ELEMENT_NODE, SIBLING,\n  TAG_ID_CLASS, TARGET_ALL, TARGET_FIRST, TARGET_LINEAL, TARGET_SELF\n} from './js/constant.js';\nconst REG_COMPLEX = new RegExp(`${COMPOUND_I}${COMBO}${COMPOUND_I}`, 'i');\nconst REG_DESCEND = new RegExp(`${COMPOUND_I}${DESCEND}${COMPOUND_I}`, 'i');\nconst REG_SIBLING = new RegExp(`${COMPOUND_I}${SIBLING}${COMPOUND_I}`, 'i');\nconst REG_SIMPLE = new RegExp(`^${TAG_ID_CLASS}$`);\n\n/* DOMSelector */\nexport class DOMSelector {\n  /* private fields */\n  #window;\n  #document;\n  #domSymbolTree;\n  #finder;\n  #idlUtils;\n  #nwsapi;\n\n  /**\n   * construct\n   * @param {object} window - window\n   * @param {object} document - document\n   * @param {object} [opt] - options\n   */\n  constructor(window, document, opt = {}) {\n    const { domSymbolTree, idlUtils } = opt;\n    this.#window = window;\n    this.#document = document ?? window.document;\n    this.#domSymbolTree = domSymbolTree;\n    this.#finder = new Finder(window);\n    this.#idlUtils = idlUtils;\n    this.#nwsapi = initNwsapi(window, document);\n  }\n\n  /**\n   * @typedef CheckResult\n   * @type {object}\n   * @property {boolean} match - match result excluding pseudo-element selector\n   * @property {string?} pseudoElement - pseudo-element selector\n   */\n\n  /**\n   * check\n   * @param {string} selector - CSS selector\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {CheckResult} - check result\n   */\n  check(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      this.#finder.onError(e, opt);\n    } else if (node.nodeType !== ELEMENT_NODE) {\n      const e = new this.#window.TypeError(`Unexpected node ${node.nodeName}`);\n      this.#finder.onError(e, opt);\n    }\n    const document = this.#domSymbolTree\n      ? node._ownerDocument\n      : node.ownerDocument;\n    if (document === this.#document && document.contentType === 'text/html' &&\n        document.documentElement && node.parentNode) {\n      const filterOpt = {\n        complex: REG_COMPLEX.test(selector),\n        compound: false,\n        descend: false,\n        simple: false,\n        target: TARGET_SELF\n      };\n      if (filterSelector(selector, filterOpt)) {\n        try {\n          const n = this.#idlUtils ? this.#idlUtils.wrapperForImpl(node) : node;\n          const match = this.#nwsapi.match(selector, n);\n          return {\n            match,\n            pseudoElement: null\n          };\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      // FIXME: remove later\n      if (this.#idlUtils) {\n        node = this.#idlUtils.wrapperForImpl(node);\n      }\n      opt.domSymbolTree = this.#domSymbolTree;\n      opt.check = true;\n      opt.noexept = true;\n      opt.warn = false;\n      this.#finder.setup(selector, node, opt);\n      res = this.#finder.find(TARGET_SELF);\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res;\n  }\n\n  /**\n   * matches\n   * @param {string} selector - CSS selector\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {boolean} - `true` if matched `false` otherwise\n   */\n  matches(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      this.#finder.onError(e, opt);\n    } else if (node.nodeType !== ELEMENT_NODE) {\n      const e = new this.#window.TypeError(`Unexpected node ${node.nodeName}`);\n      this.#finder.onError(e, opt);\n    }\n    const document = this.#domSymbolTree\n      ? node._ownerDocument\n      : node.ownerDocument;\n    if (document === this.#document && document.contentType === 'text/html' &&\n        document.documentElement && node.parentNode) {\n      const filterOpt = {\n        complex: REG_COMPLEX.test(selector),\n        compound: false,\n        descend: false,\n        simple: false,\n        target: TARGET_SELF\n      };\n      if (filterSelector(selector, filterOpt)) {\n        try {\n          const n = this.#idlUtils ? this.#idlUtils.wrapperForImpl(node) : node;\n          const res = this.#nwsapi.match(selector, n);\n          return res;\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      // FIXME: remove later\n      if (this.#idlUtils) {\n        node = this.#idlUtils.wrapperForImpl(node);\n      }\n      opt.domSymbolTree = this.#domSymbolTree;\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_SELF);\n      res = nodes.size;\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return !!res;\n  }\n\n  /**\n   * closest\n   * @param {string} selector - CSS selector\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {?object} - matched node\n   */\n  closest(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      this.#finder.onError(e, opt);\n    } else if (node.nodeType !== ELEMENT_NODE) {\n      const e = new this.#window.TypeError(`Unexpected node ${node.nodeName}`);\n      this.#finder.onError(e, opt);\n    }\n    const document = this.#domSymbolTree\n      ? node._ownerDocument\n      : node.ownerDocument;\n    if (document === this.#document && document.contentType === 'text/html' &&\n        document.documentElement && node.parentNode) {\n      const filterOpt = {\n        complex: REG_COMPLEX.test(selector),\n        compound: false,\n        descend: false,\n        simple: false,\n        target: TARGET_LINEAL\n      };\n      if (filterSelector(selector, filterOpt)) {\n        try {\n          const n = this.#idlUtils ? this.#idlUtils.wrapperForImpl(node) : node;\n          const res = this.#nwsapi.closest(selector, n);\n          return res;\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      // FIXME: remove later\n      if (this.#idlUtils) {\n        node = this.#idlUtils.wrapperForImpl(node);\n      }\n      opt.domSymbolTree = this.#domSymbolTree;\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_LINEAL);\n      if (nodes.size) {\n        let refNode = node;\n        while (refNode) {\n          if (nodes.has(refNode)) {\n            res = refNode;\n            break;\n          }\n          refNode = refNode.parentNode;\n        }\n      }\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * query selector\n   * @param {string} selector - CSS selector\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @returns {?object} - matched node\n   */\n  querySelector(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      this.#finder.onError(e, opt);\n    }\n    let res;\n    try {\n      // FIXME: remove later\n      if (this.#idlUtils) {\n        node = this.#idlUtils.wrapperForImpl(node);\n      }\n      opt.domSymbolTree = this.#domSymbolTree;\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_FIRST);\n      if (nodes.size) {\n        [res] = [...nodes];\n      }\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res ?? null;\n  }\n\n  /**\n   * query selector all\n   * NOTE: returns Array, not NodeList\n   * @param {string} selector - CSS selector\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @returns {Array.<object|undefined>} - collection of matched nodes\n   */\n  querySelectorAll(selector, node, opt = {}) {\n    if (!node?.nodeType) {\n      const e = new this.#window.TypeError(`Unexpected type ${getType(node)}`);\n      this.#finder.onError(e, opt);\n    }\n    let document;\n    if (this.#domSymbolTree) {\n      document = node._ownerDocument;\n    } else if (node.nodeType === DOCUMENT_NODE) {\n      document = node;\n    } else {\n      document = node.ownerDocument;\n    }\n    if (document === this.#document && document.contentType === 'text/html' &&\n        document.documentElement) {\n      const filterOpt = {\n        complex: false,\n        compound: false,\n        descend: REG_DESCEND.test(selector) && !REG_SIBLING.test(selector),\n        simple: REG_SIMPLE.test(selector),\n        target: TARGET_ALL\n      };\n      if (filterSelector(selector, filterOpt)) {\n        try {\n          const n = this.#idlUtils ? this.#idlUtils.wrapperForImpl(node) : node;\n          const res = this.#nwsapi.select(selector, n);\n          return res;\n        } catch (e) {\n          // fall through\n        }\n      }\n    }\n    let res;\n    try {\n      // FIXME: remove later\n      if (this.#idlUtils) {\n        node = this.#idlUtils.wrapperForImpl(node);\n      }\n      opt.domSymbolTree = this.#domSymbolTree;\n      this.#finder.setup(selector, node, opt);\n      const nodes = this.#finder.find(TARGET_ALL);\n      if (nodes.size) {\n        res = [...nodes];\n      }\n    } catch (e) {\n      this.#finder.onError(e, opt);\n    }\n    return res ?? [];\n  }\n}\n", "/**\n * parser.js\n */\n\n/* import */\nimport { findAll, parse, toPlainObject, walk } from 'css-tree';\nimport { getType } from './utility.js';\n\n/* constants */\nimport {\n  ATTR_SELECTOR, BIT_01, BIT_02, BIT_04, BIT_08, BIT_16, BIT_32, BIT_FFFF,\n  CLASS_SELECTOR, DUO, HEX, ID_SELECTOR, KEY_LOGICAL, KEY_PS_STATE,\n  KEY_SHADOW_HOST, NTH, PS_CLASS_SELECTOR, PS_ELEMENT_SELECTOR, SELECTOR,\n  SYNTAX_ERR, TYPE_SELECTOR\n} from './constant.js';\nconst REG_EMPTY_PS_FUNC = /(?<=:(?:dir|has|host(?:-context)?|is|lang|not|nth-(?:last-)?(?:child|of-type)|where))\\(\\s+\\)/g;\nconst REG_SHADOW_PS_ELEMENT = /^part|slotted$/;\nconst U_FFFD = '\\uFFFD';\n\n/**\n * unescape selector\n * @param {string} selector - CSS selector\n * @returns {?string} - unescaped selector\n */\nexport const unescapeSelector = (selector = '') => {\n  if (typeof selector === 'string' && selector.indexOf('\\\\', 0) >= 0) {\n    const arr = selector.split('\\\\');\n    const l = arr.length;\n    for (let i = 1; i < l; i++) {\n      let item = arr[i];\n      if (item === '' && i === l - 1) {\n        item = U_FFFD;\n      } else {\n        const hexExists = /^([\\da-f]{1,6}\\s?)/i.exec(item);\n        if (hexExists) {\n          const [, hex] = hexExists;\n          let str;\n          try {\n            const low = parseInt('D800', HEX);\n            const high = parseInt('DFFF', HEX);\n            const deci = parseInt(hex, HEX);\n            if (deci === 0 || (deci >= low && deci <= high)) {\n              str = U_FFFD;\n            } else {\n              str = String.fromCodePoint(deci);\n            }\n          } catch (e) {\n            str = U_FFFD;\n          }\n          let postStr = '';\n          if (item.length > hex.length) {\n            postStr = item.substring(hex.length);\n          }\n          item = `${str}${postStr}`;\n        // whitespace\n        } else if (/^[\\n\\r\\f]/.test(item)) {\n          item = '\\\\' + item;\n        }\n      }\n      arr[i] = item;\n    }\n    selector = arr.join('');\n  }\n  return selector;\n};\n\n/**\n * preprocess\n * @see https://drafts.csswg.org/css-syntax-3/#input-preprocessing\n * @param {...*} args - arguments\n * @returns {string} - filtered selector string\n */\nexport const preprocess = (...args) => {\n  if (!args.length) {\n    throw new TypeError('1 argument required, but only 0 present.');\n  }\n  let [selector] = args;\n  if (typeof selector === 'string') {\n    let index = 0;\n    while (index >= 0) {\n      // @see https://drafts.csswg.org/selectors/#id-selectors\n      index = selector.indexOf('#', index);\n      if (index < 0) {\n        break;\n      }\n      const preHash = selector.substring(0, index + 1);\n      let postHash = selector.substring(index + 1);\n      const codePoint = postHash.codePointAt(0);\n      if (codePoint > BIT_FFFF) {\n        const str = `\\\\${codePoint.toString(HEX)} `;\n        if (postHash.length === DUO) {\n          postHash = str;\n        } else {\n          postHash = `${str}${postHash.substring(DUO)}`;\n        }\n      }\n      selector = `${preHash}${postHash}`;\n      index++;\n    }\n    selector = selector.replace(/\\f|\\r\\n?/g, '\\n')\n      .replace(/[\\0\\uD800-\\uDFFF]|\\\\$/g, U_FFFD);\n  } else if (selector === undefined || selector === null) {\n    selector = getType(selector).toLowerCase();\n  } else if (Array.isArray(selector)) {\n    selector = selector.join(',');\n  } else if (Object.hasOwn(selector, 'toString')) {\n    selector = selector.toString();\n  } else {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  return selector.replace(/\\x26/g, ':scope');\n};\n\n/**\n * create AST from CSS selector\n * @param {string} selector - CSS selector\n * @returns {object} - AST\n */\nexport const parseSelector = selector => {\n  selector = preprocess(selector);\n  // invalid selectors\n  if (/^$|^\\s*>|,\\s*$/.test(selector)) {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  let res;\n  try {\n    const ast = parse(selector, {\n      context: 'selectorList',\n      parseCustomProperty: true\n    });\n    res = toPlainObject(ast);\n  } catch (e) {\n    const { message } = e;\n    if (/^(?:\"\\]\"|Attribute selector [()\\s,=~^$*|]+) is expected$/.test(message) &&\n        !selector.endsWith(']')) {\n      const index = selector.lastIndexOf('[');\n      const sel = selector.substring(index);\n      if (sel.includes('\"')) {\n        const quotes = sel.match(/\"/g).length;\n        if (quotes % 2) {\n          res = parseSelector(`${selector}\"]`);\n        } else {\n          res = parseSelector(`${selector}]`);\n        }\n      } else {\n        res = parseSelector(`${selector}]`);\n      }\n    } else if (message === '\")\" is expected') {\n      // workaround for https://github.com/csstree/csstree/issues/283\n      if (REG_EMPTY_PS_FUNC.test(selector)) {\n        res = parseSelector(`${selector.replaceAll(REG_EMPTY_PS_FUNC, '()')}`);\n      } else if (!selector.endsWith(')')) {\n        res = parseSelector(`${selector})`);\n      } else {\n        throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n      }\n    } else {\n      throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n    }\n  }\n  return res;\n};\n\n/**\n * walk AST\n * @param {object} ast - AST\n * @returns {object} - AST branches and info\n */\nexport const walkAST = (ast = {}) => {\n  const branches = new Set();\n  const info = new Map();\n  const opt = {\n    enter: node => {\n      switch (node.type) {\n        case CLASS_SELECTOR: {\n          if (/^-?\\d/.test(node.name)) {\n            throw new DOMException(`Invalid selector .${node.name}`,\n              SYNTAX_ERR);\n          }\n          break;\n        }\n        case ID_SELECTOR: {\n          if (/^-?\\d/.test(node.name)) {\n            throw new DOMException(`Invalid selector #${node.name}`,\n              SYNTAX_ERR);\n          }\n          break;\n        }\n        case PS_CLASS_SELECTOR: {\n          if (KEY_LOGICAL.includes(node.name)) {\n            info.set('hasNestedSelector', true);\n            info.set('hasLogicalPseudoFunc', true);\n            if (node.name === 'has') {\n              info.set('hasHasPseudoFunc', true);\n            }\n          } else if (KEY_PS_STATE.includes(node.name)) {\n            info.set('hasStatePseudoClass', true);\n          } else if (KEY_SHADOW_HOST.includes(node.name) &&\n                     Array.isArray(node.children) && node.children.length) {\n            info.set('hasNestedSelector', true);\n          }\n          break;\n        }\n        case PS_ELEMENT_SELECTOR: {\n          if (REG_SHADOW_PS_ELEMENT.test(node.name)) {\n            info.set('hasNestedSelector', true);\n          }\n          break;\n        }\n        case NTH: {\n          if (node.selector) {\n            info.set('hasNestedSelector', true);\n            info.set('hasNthChildOfSelector', true);\n          }\n          break;\n        }\n        case SELECTOR: {\n          branches.add(node.children);\n          break;\n        }\n        default:\n      }\n    }\n  };\n  walk(ast, opt);\n  if (info.get('hasNestedSelector')) {\n    findAll(ast, (node, item, list) => {\n      if (list) {\n        if (node.type === PS_CLASS_SELECTOR &&\n            KEY_LOGICAL.includes(node.name)) {\n          const itemList = list.filter(i => {\n            const { name, type } = i;\n            return type === PS_CLASS_SELECTOR && KEY_LOGICAL.includes(name);\n          });\n          for (const { children } of itemList) {\n            // SelectorList\n            for (const { children: grandChildren } of children) {\n              // Selector\n              for (const { children: greatGrandChildren } of grandChildren) {\n                if (branches.has(greatGrandChildren)) {\n                  branches.delete(greatGrandChildren);\n                }\n              }\n            }\n          }\n        } else if (node.type === PS_CLASS_SELECTOR &&\n                   KEY_SHADOW_HOST.includes(node.name) &&\n                   Array.isArray(node.children) && node.children.length) {\n          const itemList = list.filter(i => {\n            const { children, name, type } = i;\n            const res =\n              type === PS_CLASS_SELECTOR && KEY_SHADOW_HOST.includes(name) &&\n              Array.isArray(children) && children.length;\n            return res;\n          });\n          for (const { children } of itemList) {\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        } else if (node.type === PS_ELEMENT_SELECTOR &&\n                   REG_SHADOW_PS_ELEMENT.test(node.name)) {\n          const itemList = list.filter(i => {\n            const { name, type } = i;\n            const res =\n              type === PS_ELEMENT_SELECTOR && REG_SHADOW_PS_ELEMENT.test(name);\n            return res;\n          });\n          for (const { children } of itemList) {\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        } else if (node.type === NTH && node.selector) {\n          const itemList = list.filter(i => {\n            const { selector, type } = i;\n            const res = type === NTH && selector;\n            return res;\n          });\n          for (const { selector } of itemList) {\n            const { children } = selector;\n            // Selector\n            for (const { children: grandChildren } of children) {\n              if (branches.has(grandChildren)) {\n                branches.delete(grandChildren);\n              }\n            }\n          }\n        }\n      }\n    });\n  }\n  return {\n    branches: [...branches],\n    info: Object.fromEntries(info)\n  };\n};\n\n/**\n * sort AST\n * @param {Array.<object>} asts - collection of AST\n * @returns {Array.<object>} - collection of sorted AST\n */\nexport const sortAST = asts => {\n  const arr = [...asts];\n  if (arr.length > 1) {\n    const order = new Map([\n      [PS_ELEMENT_SELECTOR, BIT_01],\n      [ID_SELECTOR, BIT_02],\n      [CLASS_SELECTOR, BIT_04],\n      [TYPE_SELECTOR, BIT_08],\n      [ATTR_SELECTOR, BIT_16],\n      [PS_CLASS_SELECTOR, BIT_32]\n    ]);\n    arr.sort((a, b) => {\n      const { type: typeA } = a;\n      const { type: typeB } = b;\n      const bitA = order.get(typeA);\n      const bitB = order.get(typeB);\n      let res;\n      if (bitA === bitB) {\n        res = 0;\n      } else if (bitA > bitB) {\n        res = 1;\n      } else {\n        res = -1;\n      }\n      return res;\n    });\n  }\n  return arr;\n};\n\n/**\n * parse AST name - e.g. ns|E -> { prefix: ns, localName: E }\n * @param {string} selector - type selector\n * @returns {object} - node properties\n */\nexport const parseAstName = selector => {\n  let prefix;\n  let localName;\n  if (selector && typeof selector === 'string') {\n    if (selector.indexOf('|') > -1) {\n      [prefix, localName] = selector.split('|');\n    } else {\n      prefix = '*';\n      localName = selector;\n    }\n  } else {\n    throw new DOMException(`Invalid selector ${selector}`, SYNTAX_ERR);\n  }\n  return {\n    prefix,\n    localName\n  };\n};\n\n/* export */\nexport { find as findAST, generate as generateCSS } from 'css-tree';\n", "/**\n * utility.js\n */\n\n/* import */\nimport nwsapi from '@asamuzakjp/nwsapi';\nimport bidiFactory from 'bidi-js';\nimport { generate, parse, walk } from 'css-tree';\nimport isCustomElementName from 'is-potential-custom-element-name';\n\n/* constants */\nimport {\n  ATRULE, DOCUMENT_FRAGMENT_NODE, DOCUMENT_NODE, DOCUMENT_POSITION_CONTAINS,\n  DOCUMENT_POSITION_PRECEDING, ELEMENT_NODE, HAS_COMPOUND, KEY_INPUT_BUTTON,\n  KEY_INPUT_EDIT, KEY_INPUT_TEXT, LOGIC_COMPLEX, LOGIC_COMPOUND, N_TH,\n  PSEUDO_CLASS, RULE, SCOPE, SELECTOR_LIST, TARGET_LINEAL, TARGET_SELF,\n  TEXT_NODE, TYPE_FROM, TYPE_TO\n} from './constant.js';\nconst REG_LOGIC_COMPLEX =\n  new RegExp(`:(?!${PSEUDO_CLASS}|${N_TH}|${LOGIC_COMPLEX})`);\nconst REG_LOGIC_COMPOUND =\n  new RegExp(`:(?!${PSEUDO_CLASS}|${N_TH}|${LOGIC_COMPOUND})`);\nconst REG_LOGIC_HAS_COMPOUND =\n  new RegExp(`:(?!${PSEUDO_CLASS}|${N_TH}|${LOGIC_COMPOUND}|${HAS_COMPOUND})`);\nconst REG_END_WITH_HAS = new RegExp(`:${HAS_COMPOUND}$`);\nconst REG_WO_LOGICAL = new RegExp(`:(?!${PSEUDO_CLASS}|${N_TH})`);\n\n/**\n * get type\n * @param {*} o - object to check\n * @returns {string} - type of object\n */\nexport const getType = o =>\n  Object.prototype.toString.call(o).slice(TYPE_FROM, TYPE_TO);\n\n/**\n * verify array contents\n * @param {Array} arr - array\n * @param {string} type - expected type, e.g. 'String'\n * @throws {TypeError} - TypeError\n * @returns {Array} - verified array\n */\nexport const verifyArray = (arr, type) => {\n  if (!Array.isArray(arr)) {\n    throw new TypeError(`Unexpected type ${getType(arr)}`);\n  }\n  if (typeof type !== 'string') {\n    throw new TypeError(`Unexpected type ${getType(type)}`);\n  }\n  for (const item of arr) {\n    if (getType(item) !== type) {\n      throw new TypeError(`Unexpected type ${getType(item)}`);\n    }\n  }\n  return arr;\n};\n\n/**\n * resolve content document, root node and tree walker, is in shadow\n * @param {object} node - Document, DocumentFragment, Element node\n * @returns {Array.<object|boolean>}\n *   - array of document, root node , tree walker, node is in shadow\n */\nexport const resolveContent = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  let document;\n  let root;\n  let shadow;\n  switch (node.nodeType) {\n    case DOCUMENT_NODE: {\n      document = node;\n      root = node;\n      break;\n    }\n    case DOCUMENT_FRAGMENT_NODE: {\n      const { host, mode, ownerDocument } = node;\n      document = ownerDocument;\n      root = node;\n      shadow = host && (mode === 'close' || mode === 'open');\n      break;\n    }\n    case ELEMENT_NODE: {\n      document = node.ownerDocument;\n      let refNode = node;\n      while (refNode) {\n        const { host, mode, nodeType, parentNode } = refNode;\n        if (nodeType === DOCUMENT_FRAGMENT_NODE) {\n          shadow = host && (mode === 'close' || mode === 'open');\n          break;\n        } else if (parentNode) {\n          refNode = parentNode;\n        } else {\n          break;\n        }\n      }\n      root = refNode;\n      break;\n    }\n    default : {\n      throw new TypeError(`Unexpected node ${node.nodeName}`);\n    }\n  }\n  return [\n    document,\n    root,\n    !!shadow\n  ];\n};\n\n/**\n * traverse node tree\n * @param {object} node - node\n * @param {object} walker - tree walker\n * @param {boolean} [force] - traverse only to next node\n * @returns {?object} - current node\n */\nexport const traverseNode = (node, walker, force = false) => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (!walker) {\n    return null;\n  }\n  let refNode = walker.currentNode;\n  if (refNode === node) {\n    return refNode;\n  } else if (force || refNode.contains(node)) {\n    refNode = walker.nextNode();\n    while (refNode) {\n      if (refNode === node) {\n        break;\n      }\n      refNode = walker.nextNode();\n    }\n    return refNode;\n  } else {\n    if (refNode !== walker.root) {\n      let bool;\n      while (refNode) {\n        if (refNode === node) {\n          bool = true;\n          break;\n        } else if (refNode === walker.root || refNode.contains(node)) {\n          break;\n        }\n        refNode = walker.parentNode();\n      }\n      if (bool) {\n        return refNode;\n      }\n    }\n    if (node.nodeType === ELEMENT_NODE) {\n      let bool;\n      while (refNode) {\n        if (refNode === node) {\n          bool = true;\n          break;\n        }\n        refNode = walker.nextNode();\n      }\n      if (bool) {\n        return refNode;\n      }\n    }\n  }\n  return null;\n};\n\n/**\n * is custom element\n * @param {object} node - Element node\n * @param {object} [opt] - options\n * @returns {boolean} - result\n */\nexport const isCustomElement = (node, opt = {}) => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const { localName, ownerDocument } = node;\n  const { formAssociated } = opt;\n  const window = ownerDocument.defaultView;\n  let elmConstructor;\n  const attr = node.getAttribute('is');\n  if (attr) {\n    elmConstructor =\n      isCustomElementName(attr) && window.customElements.get(attr);\n  } else {\n    elmConstructor =\n      isCustomElementName(localName) && window.customElements.get(localName);\n  }\n  if (elmConstructor) {\n    if (formAssociated) {\n      return !!elmConstructor.formAssociated;\n    }\n    return true;\n  }\n  return false;\n};\n\n/**\n * get slotted text content\n * @param {object} node - Element node\n * @returns {?string} - text content\n */\nexport const getSlottedTextContent = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (typeof node.assignedNodes !== 'function') {\n    return null;\n  }\n  const nodes = node.assignedNodes();\n  if (nodes.length) {\n    let text;\n    for (const item of nodes) {\n      text = item.textContent.trim();\n      if (text) {\n        break;\n      }\n    }\n    return text;\n  }\n  return node.textContent.trim();\n};\n\n/**\n * get directionality of node\n * @see https://html.spec.whatwg.org/multipage/dom.html#the-dir-attribute\n * @param {object} node - Element node\n * @returns {?string} - 'ltr' / 'rtl'\n */\nexport const getDirectionality = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return null;\n  }\n  const { dir: dirAttr, localName, parentNode } = node;\n  const { getEmbeddingLevels } = bidiFactory();\n  if (dirAttr === 'ltr' || dirAttr === 'rtl') {\n    return dirAttr;\n  } else if (dirAttr === 'auto') {\n    let text;\n    switch (localName) {\n      case 'input': {\n        const valueKeys = [...KEY_INPUT_BUTTON, ...KEY_INPUT_TEXT, 'hidden'];\n        if (!node.type || valueKeys.includes(node.type)) {\n          text = node.value;\n        } else {\n          const ltrKeys = [\n            'checkbox', 'color', 'date', 'image', 'number', 'range', 'radio',\n            'time'\n          ];\n          if (ltrKeys.includes(node.type)) {\n            return 'ltr';\n          }\n        }\n        break;\n      }\n      case 'slot': {\n        text = getSlottedTextContent(node);\n        break;\n      }\n      case 'textarea': {\n        text = node.value;\n        break;\n      }\n      default: {\n        const items = [].slice.call(node.childNodes);\n        for (const item of items) {\n          const {\n            dir: itemDir, localName: itemLocalName, nodeType: itemNodeType,\n            textContent: itemTextContent\n          } = item;\n          if (itemNodeType === TEXT_NODE) {\n            text = itemTextContent.trim();\n          } else if (itemNodeType === ELEMENT_NODE) {\n            const keys = ['bdi', 'script', 'style', 'textarea'];\n            if (!keys.includes(itemLocalName) &&\n                (!itemDir || (itemDir !== 'ltr' && itemDir !== 'rtl'))) {\n              if (itemLocalName === 'slot') {\n                text = getSlottedTextContent(item);\n              } else {\n                text = itemTextContent.trim();\n              }\n            }\n          }\n          if (text) {\n            break;\n          }\n        }\n      }\n    }\n    if (text) {\n      const { paragraphs: [{ level }] } = getEmbeddingLevels(text);\n      if (level % 2 === 1) {\n        return 'rtl';\n      }\n    } else if (parentNode) {\n      const { nodeType: parentNodeType } = parentNode;\n      if (parentNodeType === ELEMENT_NODE) {\n        return getDirectionality(parentNode);\n      }\n    }\n  } else if (localName === 'input' && node.type === 'tel') {\n    return 'ltr';\n  } else if (localName === 'bdi') {\n    const text = node.textContent.trim();\n    if (text) {\n      const { paragraphs: [{ level }] } = getEmbeddingLevels(text);\n      if (level % 2 === 1) {\n        return 'rtl';\n      }\n    }\n  } else if (parentNode) {\n    if (localName === 'slot') {\n      const text = getSlottedTextContent(node);\n      if (text) {\n        const { paragraphs: [{ level }] } = getEmbeddingLevels(text);\n        if (level % 2 === 1) {\n          return 'rtl';\n        }\n        return 'ltr';\n      }\n    }\n    const { nodeType: parentNodeType } = parentNode;\n    if (parentNodeType === ELEMENT_NODE) {\n      return getDirectionality(parentNode);\n    }\n  }\n  return 'ltr';\n};\n\n/**\n * is content editable\n * NOTE: not implemented in jsdom https://github.com/jsdom/jsdom/issues/1670\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isContentEditable = node => {\n  if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (node.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  if (typeof node.isContentEditable === 'boolean') {\n    return node.isContentEditable;\n  } else if (node.ownerDocument.designMode === 'on') {\n    return true;\n  } else {\n    let attr;\n    if (node.hasAttribute('contenteditable')) {\n      attr = node.getAttribute('contenteditable');\n    } else {\n      attr = 'inherit';\n    }\n    switch (attr) {\n      case '':\n      case 'true': {\n        return true;\n      }\n      case 'plaintext-only': {\n        // FIXME:\n        // @see https://github.com/w3c/editing/issues/470\n        // @see https://github.com/whatwg/html/issues/10651\n        return true;\n      }\n      case 'false': {\n        return false;\n      }\n      default: {\n        if (node?.parentNode?.nodeType === ELEMENT_NODE) {\n          return isContentEditable(node.parentNode);\n        }\n        return false;\n      }\n    }\n  }\n};\n\n/**\n * is node visible\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isVisible = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const window = node.ownerDocument.defaultView;\n  const { display, visibility } = window.getComputedStyle(node);\n  if (display !== 'none' && visibility === 'visible') {\n    return true;\n  }\n  return false;\n};\n\n/**\n * is focus visible\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isFocusVisible = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const { localName, type } = node;\n  switch (localName) {\n    case 'input': {\n      if (!type || KEY_INPUT_EDIT.includes(type)) {\n        return true;\n      }\n      return false;\n    }\n    case 'textarea': {\n      return true;\n    }\n    default: {\n      return isContentEditable(node);\n    }\n  }\n};\n\n/**\n * is focusable area\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isFocusableArea = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  if (!node.isConnected) {\n    return false;\n  }\n  const window = node.ownerDocument.defaultView;\n  if (node instanceof window.HTMLElement) {\n    if (Number.isInteger(parseInt(node.getAttribute('tabindex')))) {\n      return true;\n    }\n    if (isContentEditable(node)) {\n      return true;\n    }\n    const { localName, parentNode } = node;\n    switch (localName) {\n      case 'a': {\n        if (node.href || node.hasAttribute('href')) {\n          return true;\n        }\n        return false;\n      }\n      case 'iframe': {\n        return true;\n      }\n      case 'input': {\n        if (node.disabled || node.hasAttribute('disabled') ||\n            node.hidden || node.hasAttribute('hidden')) {\n          return false;\n        }\n        return true;\n      }\n      case 'summary': {\n        if (parentNode.localName === 'details') {\n          let child = parentNode.firstElementChild;\n          let bool = false;\n          while (child) {\n            if (child.localName === 'summary') {\n              bool = child === node;\n              break;\n            }\n            child = child.nextElementSibling;\n          }\n          return bool;\n        }\n        return false;\n      }\n      default: {\n        const keys = ['button', 'select', 'textarea'];\n        if (keys.includes(localName) &&\n            !(node.disabled || node.hasAttribute('disabled'))) {\n          return true;\n        }\n      }\n    }\n  } else if (node instanceof window.SVGElement) {\n    if (Number.isInteger(parseInt(node.getAttributeNS(null, 'tabindex')))) {\n      const keys = [\n        'clipPath', 'defs', 'desc', 'linearGradient', 'marker', 'mask',\n        'metadata', 'pattern', 'radialGradient', 'script', 'style', 'symbol',\n        'title'\n      ];\n      const ns = 'http://www.w3.org/2000/svg';\n      let bool;\n      let refNode = node;\n      while (refNode.namespaceURI === ns) {\n        bool = keys.includes(refNode.localName);\n        if (bool) {\n          break;\n        }\n        if (refNode?.parentNode?.namespaceURI === ns) {\n          refNode = refNode.parentNode;\n        } else {\n          break;\n        }\n      }\n      if (bool) {\n        return false;\n      }\n      return true;\n    }\n    if (node.localName === 'a' &&\n        (node.href || node.hasAttributeNS(null, 'href'))) {\n      return true;\n    }\n  }\n  return false;\n};\n\n/**\n * is focusable\n * NOTE: not applied, need fix in jsdom itself\n * @see https://github.com/whatwg/html/pull/8392\n * @see https://phabricator.services.mozilla.com/D156219\n * @see https://github.com/jsdom/jsdom/issues/3029\n * @see https://github.com/jsdom/jsdom/issues/3464\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isFocusable = node => {\n  if (node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const window = node.ownerDocument.defaultView;\n  let refNode = node;\n  let res = true;\n  while (refNode) {\n    if (refNode.disabled || refNode.hasAttribute('disabled')) {\n      res = false;\n      break;\n    }\n    if (refNode.hidden || refNode.hasAttribute('hidden')) {\n      res = false;\n    }\n    const {\n      contentVisibility, display, visibility\n    } = window.getComputedStyle(refNode);\n    if (display === 'none' || visibility !== 'visible' ||\n        (contentVisibility === 'hidden' && refNode !== node)) {\n      res = false;\n    } else {\n      res = true;\n    }\n    if (res && refNode?.parentNode?.nodeType === ELEMENT_NODE) {\n      refNode = refNode.parentNode;\n    } else {\n      break;\n    }\n  }\n  return res;\n};\n\n/**\n * get namespace URI\n * @param {string} ns - namespace prefix\n * @param {Array} node - Element node\n * @returns {?string} - namespace URI\n */\nexport const getNamespaceURI = (ns, node) => {\n  if (typeof ns !== 'string') {\n    throw new TypeError(`Unexpected type ${getType(ns)}`);\n  } else if (!node?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(node)}`);\n  }\n  if (!ns || node.nodeType !== ELEMENT_NODE) {\n    return null;\n  }\n  const { attributes } = node;\n  let res;\n  for (const attr of attributes) {\n    const { name, namespaceURI, prefix, value } = attr;\n    if (name === `xmlns:${ns}`) {\n      res = value;\n    } else if (prefix === ns) {\n      res = namespaceURI;\n    }\n    if (res) {\n      break;\n    }\n  }\n  return res ?? null;\n};\n\n/**\n * is namespace declared\n * @param {string} ns - namespace\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const isNamespaceDeclared = (ns = '', node = {}) => {\n  if (!ns || typeof ns !== 'string' || node?.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  if (node.lookupNamespaceURI(ns)) {\n    return true;\n  }\n  const root = node.ownerDocument.documentElement;\n  let parent = node;\n  let res;\n  while (parent) {\n    res = getNamespaceURI(ns, parent);\n    if (res || parent === root) {\n      break;\n    }\n    parent = parent.parentNode;\n  }\n  return !!res;\n};\n\n/**\n * is preceding - nodeA precedes and/or contains nodeB\n * @param {object} nodeA - Element node\n * @param {object} nodeB - Element node\n * @returns {boolean} - result\n */\nexport const isPreceding = (nodeA, nodeB) => {\n  if (!nodeA?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(nodeA)}`);\n  } else if (!nodeB?.nodeType) {\n    throw new TypeError(`Unexpected type ${getType(nodeB)}`);\n  }\n  if (nodeA.nodeType !== ELEMENT_NODE || nodeB.nodeType !== ELEMENT_NODE) {\n    return false;\n  }\n  const posBit = nodeB.compareDocumentPosition(nodeA);\n  const res = posBit & DOCUMENT_POSITION_PRECEDING ||\n              posBit & DOCUMENT_POSITION_CONTAINS;\n  return !!res;\n};\n\n/**\n * sort nodes\n * @param {Array.<object>|Set.<object>} nodes - collection of nodes\n * @returns {Array.<object>} - collection of sorted nodes\n */\nexport const sortNodes = (nodes = []) => {\n  const arr = [...nodes];\n  if (arr.length > 1) {\n    arr.sort((a, b) => {\n      let res;\n      if (isPreceding(b, a)) {\n        res = 1;\n      } else {\n        res = -1;\n      }\n      return res;\n    });\n  }\n  return arr;\n};\n\n/**\n * concat array of nested selectors into equivalent selector\n * @param {Array.<Array.<string>>} selectors - [parents, children, ...]\n * @returns {string} - selector\n */\nexport const concatNestedSelectors = selectors => {\n  if (!Array.isArray(selectors)) {\n    throw new TypeError(`Unexpected type ${getType(selectors)}`);\n  }\n  let selector = '';\n  if (selectors.length) {\n    selectors = selectors.reverse();\n    let child = verifyArray(selectors.shift(), 'String');\n    if (child.length === 1) {\n      [child] = child;\n    }\n    while (selectors.length) {\n      const parentArr = verifyArray(selectors.shift(), 'String');\n      if (!parentArr.length) {\n        continue;\n      }\n      let parent;\n      if (parentArr.length === 1) {\n        [parent] = parentArr;\n        if (!/^[>~+]/.test(parent) && /[\\s>~+]/.test(parent)) {\n          parent = `:is(${parent})`;\n        }\n      } else {\n        parent = `:is(${parentArr.join(', ')})`;\n      }\n      if (selector.includes('\\x26')) {\n        selector = selector.replace(/\\x26/g, parent);\n      }\n      if (Array.isArray(child)) {\n        const items = [];\n        for (let item of child) {\n          if (item.includes('\\x26')) {\n            if (/^[>~+]/.test(item)) {\n              item = `${parent} ${item.replace(/\\x26/g, parent)} ${selector}`;\n            } else {\n              item = `${item.replace(/\\x26/g, parent)} ${selector}`;\n            }\n          } else {\n            item = `${parent} ${item} ${selector}`;\n          }\n          items.push(item.trim());\n        }\n        selector = items.join(', ');\n      } else if (selectors.length) {\n        selector = `${child} ${selector}`;\n      } else {\n        if (child.includes('\\x26')) {\n          if (/^[>~+]/.test(child)) {\n            selector =\n              `${parent} ${child.replace(/\\x26/g, parent)} ${selector}`;\n          } else {\n            selector = `${child.replace(/\\x26/g, parent)} ${selector}`;\n          }\n        } else {\n          selector = `${parent} ${child} ${selector}`;\n        }\n      }\n      selector = selector.trim();\n      if (selectors.length) {\n        child = parentArr.length > 1 ? parentArr : parent;\n      } else {\n        break;\n      }\n    }\n    selector = selector.replace(/\\x26/g, ':scope').trim();\n  }\n  return selector;\n};\n\n/**\n * extract nested selectors from CSSRule.cssText\n * @param {string} css - CSSRule.cssText\n * @returns {Array.<Array.<string>>} - array of nested selectors\n */\nexport const extractNestedSelectors = css => {\n  const ast = parse(css, {\n    context: 'rule'\n  });\n  const selectors = [];\n  let isScoped = false;\n  walk(ast, {\n    enter: node => {\n      switch (node.type) {\n        case ATRULE: {\n          if (node.name === 'scope') {\n            isScoped = true;\n          }\n          break;\n        }\n        case SCOPE: {\n          const { children, type } = node.root;\n          const arr = [];\n          if (type === SELECTOR_LIST) {\n            for (const child of children) {\n              const selector = generate(child);\n              arr.push(selector);\n            }\n            selectors.push(arr);\n          }\n          break;\n        }\n        case RULE: {\n          const { children, type } = node.prelude;\n          const arr = [];\n          if (type === SELECTOR_LIST) {\n            let hasAmp = false;\n            for (const child of children) {\n              const selector = generate(child);\n              if (isScoped && !hasAmp) {\n                hasAmp = /\\x26/.test(selector);\n              }\n              arr.push(selector);\n            }\n            if (isScoped) {\n              if (hasAmp) {\n                selectors.push(arr);\n              /* FIXME:\n              } else {\n                selectors = arr;\n                isScoped = false;\n              */\n              }\n            } else {\n              selectors.push(arr);\n            }\n          }\n        }\n      }\n    },\n    leave: node => {\n      if (node.type === ATRULE) {\n        if (node.name === 'scope') {\n          isScoped = false;\n        }\n      }\n    }\n  });\n  return selectors;\n};\n\n/**\n * init nwsapi\n * @param {object} window - Window\n * @param {object} document - Document\n * @returns {object} - nwsapi\n */\nexport const initNwsapi = (window, document) => {\n  if (!window?.DOMException) {\n    throw new TypeError(`Unexpected global object ${getType(window)}`);\n  }\n  if (document?.nodeType !== DOCUMENT_NODE) {\n    document = window.document;\n  }\n  const nw = nwsapi({\n    document,\n    DOMException: window.DOMException\n  });\n  nw.configure({\n    LOGERRORS: false\n  });\n  return nw;\n};\n\n/**\n * filter selector (for nwsapi)\n * @param {string} selector - selector\n * @param {object} [opt] - options\n * @returns {boolean} - result\n */\nexport const filterSelector = (selector, opt = {}) => {\n  if (!selector || typeof selector !== 'string' || /null|undefined/.test(selector)) {\n    return false;\n  }\n  const { complex, compound, descend, simple, target } = opt;\n  // exclude simple selector and compound selector\n  if (simple || compound) {\n    return false;\n  }\n  // exclude missing close square bracket\n  if (selector.includes('[')) {\n    const index = selector.lastIndexOf('[');\n    const sel = selector.substring(index);\n    if (sel.indexOf(']') < 0) {\n      return false;\n    }\n  }\n  // exclude '/'\n  if (selector.includes('/')) {\n    return false;\n  }\n  // exclude namespaced selectors, escaped selectors, pseudo-element selectors,\n  // selectors containing non-ASCII or control character other than whitespace,\n  // attribute selectors with case flag, e.g. [attr i], or with unclosed quotes,\n  // and empty :is() or :where()\n  if (/[|\\\\]|::|[^\\u0021-\\u007F\\s]|\\[\\s*[\\w$*=^|~-]+(?:(?:\"[\\w$*=^|~\\s'-]+\"|'[\\w$*=^|~\\s\"-]+')?(?:\\s+[\\w$*=^|~-]+)+|\"[^\"\\]]{1,255}|'[^'\\]]{1,255})\\s*\\]|:(?:is|where)\\(\\s*\\)/.test(selector)) {\n    return false;\n  }\n  // include pseudo-classes that are known to work correctly\n  if (selector.includes(':')) {\n    if (descend) {\n      return false;\n    } else if ((target === TARGET_SELF || target === TARGET_LINEAL) &&\n               /:has\\(/.test(selector)) {\n      if (!complex || REG_LOGIC_HAS_COMPOUND.test(selector)) {\n        return false;\n      }\n      return REG_END_WITH_HAS.test(selector);\n    } else if (/:(?:is|not)\\(/.test(selector)) {\n      if (complex) {\n        return !REG_LOGIC_COMPLEX.test(selector);\n      } else {\n        return !REG_LOGIC_COMPOUND.test(selector);\n      }\n    } else {\n      return !REG_WO_LOGICAL.test(selector);\n    }\n  }\n  return true;\n};\n", "/**\n * constant.js\n */\n\n/* string */\nexport const ATRULE = 'Atrule';\nexport const ATTR_SELECTOR = 'AttributeSelector';\nexport const CLASS_SELECTOR = 'ClassSelector';\nexport const COMBINATOR = 'Combinator';\nexport const IDENT = 'Identifier';\nexport const ID_SELECTOR = 'IdSelector';\nexport const NOT_SUPPORTED_ERR = 'NotSupportedError';\nexport const NTH = 'Nth';\nexport const OPERATOR = 'Operator';\nexport const PS_CLASS_SELECTOR = 'PseudoClassSelector';\nexport const PS_ELEMENT_SELECTOR = 'PseudoElementSelector';\nexport const RULE = 'Rule';\nexport const SCOPE = 'Scope';\nexport const SELECTOR = 'Selector';\nexport const SELECTOR_LIST = 'SelectorList';\nexport const STRING = 'String';\nexport const SYNTAX_ERR = 'SyntaxError';\nexport const TARGET_ALL = 'all';\nexport const TARGET_FIRST = 'first';\nexport const TARGET_LINEAL = 'lineal';\nexport const TARGET_SELF = 'self';\nexport const TYPE_SELECTOR = 'TypeSelector';\n\n/* numeric */\nexport const BIT_01 = 1;\nexport const BIT_02 = 2;\nexport const BIT_04 = 4;\nexport const BIT_08 = 8;\nexport const BIT_16 = 0x10;\nexport const BIT_32 = 0x20;\nexport const BIT_FFFF = 0xFFFF;\nexport const DUO = 2;\nexport const HEX = 16;\nexport const TYPE_FROM = 8;\nexport const TYPE_TO = -1;\n\n/* Node */\nexport const ELEMENT_NODE = 1;\nexport const TEXT_NODE = 3;\nexport const DOCUMENT_NODE = 9;\nexport const DOCUMENT_FRAGMENT_NODE = 11;\nexport const DOCUMENT_POSITION_PRECEDING = 2;\nexport const DOCUMENT_POSITION_CONTAINS = 8;\nexport const DOCUMENT_POSITION_CONTAINED_BY = 0x10;\n\n/* NodeFilter */\nexport const SHOW_ALL = 0xFFFFFFFF;\nexport const SHOW_CONTAINER = 0x501;\nexport const SHOW_DOCUMENT = 0x100;\nexport const SHOW_DOCUMENT_FRAGMENT = 0x400;\nexport const SHOW_ELEMENT = 1;\n\n/* selectors */\nexport const ALPHA_NUM = '[A-Z\\\\d]+';\nexport const CHILD_IDX = '(?:first|last|only)-(?:child|of-type)';\nexport const DIGIT = '(?:0|[1-9]\\\\d*)';\nexport const LANG_PART = `(?:-${ALPHA_NUM})*`;\nexport const PSEUDO_CLASS = `(?:any-)?link|${CHILD_IDX}|checked|empty|indeterminate|read-(?:only|write)|target`;\nexport const ANB =\n  `[+-]?(?:${DIGIT}n?|n)|(?:[+-]?${DIGIT})?n\\\\s*[+-]\\\\s*${DIGIT}`;\n// N_TH: excludes An+B with selector list, e.g. :nth-child(2n+1 of .foo)\nexport const N_TH =\n  `nth-(?:last-)?(?:child|of-type)\\\\(\\\\s*(?:even|odd|${ANB})\\\\s*\\\\)`;\n// SUB_TYPE: attr, id, class, pseudo-class, note that [foo|=bar] is excluded\nexport const SUB_TYPE = '\\\\[[^|\\\\]]+\\\\]|[#.:][\\\\w-]+';\nexport const SUB_TYPE_WO_PSEUDO = '\\\\[[^|\\\\]]+\\\\]|[#.][\\\\w-]+';\n// TAG_TYPE: *, tag\nexport const TAG_ID_CLASS = '(?:[A-Za-z][\\\\w-]*|[#.][\\\\w-]+)';\nexport const TAG_TYPE = '\\\\*|[A-Za-z][\\\\w-]*';\nexport const TAG_TYPE_I = '\\\\*|[A-Z][\\\\w-]*';\nexport const COMPOUND = `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE})+)`;\nexport const COMPOUND_WO_PSEUDO =\n  `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE_WO_PSEUDO})+)`;\nexport const COMBO = '\\\\s?[\\\\s>~+]\\\\s?';\nexport const COMPLEX = `${COMPOUND}(?:${COMBO}${COMPOUND})*`;\nexport const DESCEND = '\\\\s?[\\\\s>]\\\\s?';\nexport const SIBLING = '\\\\s?[+~]\\\\s?';\nexport const NESTED_LOGIC_A =\n  `:is\\\\(\\\\s*${COMPOUND}(?:\\\\s*,\\\\s*${COMPOUND})*\\\\s*\\\\)`;\nexport const NESTED_LOGIC_B =\n  `:is\\\\(\\\\s*${COMPLEX}(?:\\\\s*,\\\\s*${COMPLEX})*\\\\s*\\\\)`;\nexport const COMPOUND_A =\n  `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE}|${NESTED_LOGIC_A})+)`;\nexport const COMPOUND_B =\n  `(?:${TAG_TYPE}|(?:${TAG_TYPE})?(?:${SUB_TYPE}|${NESTED_LOGIC_B})+)`;\nexport const COMPOUND_I =\n  `(?:${TAG_TYPE_I}|(?:${TAG_TYPE_I})?(?:${SUB_TYPE})+)`;\nexport const COMPLEX_L = `${COMPOUND_B}(?:${COMBO}${COMPOUND_B})*`;\nexport const LOGIC_COMPLEX =\n  `(?:is|not)\\\\(\\\\s*${COMPLEX_L}(?:\\\\s*,\\\\s*${COMPLEX_L})*\\\\s*\\\\)`;\nexport const LOGIC_COMPOUND =\n  `(?:is|not)\\\\(\\\\s*${COMPOUND_A}(?:\\\\s*,\\\\s*${COMPOUND_A})*\\\\s*\\\\)`;\nexport const HAS_COMPOUND = `has\\\\([\\\\s>]?\\\\s*${COMPOUND_WO_PSEUDO}\\\\s*\\\\)`;\n\n/* array */\nexport const KEY_FORM_FOCUS =\n  Object.freeze(['button', 'input', 'select', 'textarea']);\nexport const KEY_INPUT_BUTTON = Object.freeze(['button', 'reset', 'submit']);\nexport const KEY_INPUT_DATE =\n  Object.freeze(['date', 'datetime-local', 'month', 'time', 'week']);\nexport const KEY_INPUT_TEXT =\n  Object.freeze(['email', 'password', 'search', 'tel', 'text', 'url']);\nexport const KEY_INPUT_EDIT =\n  Object.freeze([...KEY_INPUT_DATE, ...KEY_INPUT_TEXT, 'number']);\nexport const KEY_INPUT_LTR = Object.freeze([\n  'checkbox', 'color', 'date', 'image', 'number', 'range', 'radio', 'time'\n]);\nexport const KEY_LOGICAL = Object.freeze(['has', 'is', 'not', 'where']);\nexport const KEY_MODIFIER = Object.freeze([\n  'Alt', 'AltGraph', 'CapsLock', 'Control', 'Fn', 'FnLock', 'Hyper', 'Meta',\n  'NumLock', 'ScrollLock', 'Shift', 'Super', 'Symbol', 'SymbolLock'\n]);\nexport const KEY_PS_STATE = Object.freeze([\n  'enabled', 'disabled', 'valid', 'invalid', 'in-range', 'out-of-range',\n  'checked', 'indeterminate', 'read-only', 'read-write', 'open', 'closed',\n  'placeholder-shown'\n]);\nexport const KEY_SHADOW_HOST = Object.freeze(['host', 'host-context']);\n", "/**\n * matcher.js\n */\n\n/* import */\nimport { generateCSS, parseAstName, unescapeSelector } from './parser.js';\nimport { getDirectionality, getType, isNamespaceDeclared } from './utility.js';\n\n/* constants */\nimport {\n  ALPHA_NUM, ELEMENT_NODE, IDENT, LANG_PART, NOT_SUPPORTED_ERR,\n  PS_ELEMENT_SELECTOR, STRING, SYNTAX_ERR\n} from './constant.js';\n\n/**\n * match pseudo-element selector\n * @param {string} astName - AST name\n * @param {string} astType - AST type\n * @param {object} [opt] - options\n * @param {boolean} [opt.forgive] - forgive unknown pseudo-element\n * @param {boolean} [opt.warn] - warn unsupported pseudo-element\n * @throws {DOMException}\n * @returns {void}\n */\nexport const matchPseudoElementSelector = (astName, astType, opt = {}) => {\n  const { forgive, warn } = opt;\n  if (astType === PS_ELEMENT_SELECTOR) {\n    switch (astName) {\n      case 'after':\n      case 'backdrop':\n      case 'before':\n      case 'cue':\n      case 'cue-region':\n      case 'first-letter':\n      case 'first-line':\n      case 'file-selector-button':\n      case 'marker':\n      case 'placeholder':\n      case 'selection':\n      case 'target-text': {\n        if (warn) {\n          throw new DOMException(`Unsupported pseudo-element ::${astName}`,\n            NOT_SUPPORTED_ERR);\n        }\n        break;\n      }\n      case 'part':\n      case 'slotted': {\n        if (warn) {\n          throw new DOMException(`Unsupported pseudo-element ::${astName}()`,\n            NOT_SUPPORTED_ERR);\n        }\n        break;\n      }\n      default: {\n        if (astName.startsWith('-webkit-')) {\n          if (warn) {\n            throw new DOMException(`Unsupported pseudo-element ::${astName}`,\n              NOT_SUPPORTED_ERR);\n          }\n        } else if (!forgive) {\n          throw new DOMException(`Unknown pseudo-element ::${astName}`,\n            SYNTAX_ERR);\n        }\n      }\n    }\n  } else {\n    throw new TypeError(`Unexpected ast type ${getType(astType)}`);\n  }\n};\n\n/**\n * match directionality pseudo-class - :dir()\n * @param {object} ast - AST\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const matchDirectionPseudoClass = (ast, node) => {\n  const { name } = ast;\n  if (!name) {\n    let type;\n    if (name === '') {\n      type = '(empty String)';\n    } else {\n      type = getType(name);\n    }\n    throw new TypeError(`Unexpected ast type ${type}`);\n  }\n  const dir = getDirectionality(node);\n  return name === dir;\n};\n\n/**\n * match language pseudo-class - :lang()\n * @see https://datatracker.ietf.org/doc/html/rfc4647#section-3.3.1\n * @param {object} ast - AST\n * @param {object} node - Element node\n * @returns {boolean} - result\n */\nexport const matchLanguagePseudoClass = (ast, node) => {\n  const { name, type, value } = ast;\n  let astName;\n  if (type === STRING && value) {\n    astName = value;\n  } else if (type === IDENT && name) {\n    astName = unescapeSelector(name);\n  }\n  const { contentType } = node.ownerDocument;\n  const html = /^(?:application\\/xhtml\\+x|text\\/ht)ml$/.test(contentType);\n  const xml = /^(?:application\\/(?:[\\w\\-.]+\\+)?|image\\/[\\w\\-.]+\\+|text\\/)xml$/.test(contentType);\n  if (astName === '*') {\n    if ((html && node.hasAttribute('lang')) ||\n        (xml && node.hasAttribute('xml:lang'))) {\n      if ((html && node.getAttribute('lang')) ||\n        (xml && node.getAttribute('xml:lang'))) {\n        return true;\n      }\n    } else {\n      let parent = node.parentNode;\n      let res;\n      while (parent) {\n        if (parent.nodeType === ELEMENT_NODE) {\n          if ((html && parent.hasAttribute('lang')) ||\n              (xml && parent.hasAttribute('xml:lang'))) {\n            if ((html && parent.hasAttribute('lang')) ||\n                (xml && parent.hasAttribute('xml:lang'))) {\n              res = true;\n            }\n            break;\n          }\n          parent = parent.parentNode;\n        } else {\n          break;\n        }\n      }\n      return !!res;\n    }\n  } else if (astName) {\n    const reg = new RegExp(`^(?:\\\\*-)?${ALPHA_NUM}${LANG_PART}$`, 'i');\n    if (reg.test(astName)) {\n      let regExtendedLang;\n      if (astName.indexOf('-') > -1) {\n        const [langMain, langSub, ...langRest] = astName.split('-');\n        let extendedMain;\n        if (langMain === '*') {\n          extendedMain = `${ALPHA_NUM}${LANG_PART}`;\n        } else {\n          extendedMain = `${langMain}${LANG_PART}`;\n        }\n        const extendedSub = `-${langSub}${LANG_PART}`;\n        const len = langRest.length;\n        let extendedRest = '';\n        if (len) {\n          for (let i = 0; i < len; i++) {\n            extendedRest += `-${langRest[i]}${LANG_PART}`;\n          }\n        }\n        regExtendedLang =\n          new RegExp(`^${extendedMain}${extendedSub}${extendedRest}$`, 'i');\n      } else {\n        regExtendedLang = new RegExp(`^${astName}${LANG_PART}$`, 'i');\n      }\n      if ((html && node.hasAttribute('lang')) ||\n          (xml && node.hasAttribute('xml:lang'))) {\n        const attr = (html && node.getAttribute('lang')) ||\n                     (xml && node.getAttribute('xml:lang')) || '';\n        return regExtendedLang.test(attr);\n      } else {\n        let parent = node.parentNode;\n        let res;\n        while (parent) {\n          if (parent.nodeType === ELEMENT_NODE) {\n            if ((html && parent.hasAttribute('lang')) ||\n                (xml && parent.hasAttribute('xml:lang'))) {\n              const attr = (html && parent.getAttribute('lang')) ||\n                           (xml && parent.getAttribute('xml:lang')) || '';\n              res = regExtendedLang.test(attr);\n              break;\n            }\n            parent = parent.parentNode;\n          } else {\n            break;\n          }\n        }\n        return !!res;\n      }\n    }\n  }\n  return false;\n};\n\n/**\n * match attribute selector\n * @param {object} ast - AST\n * @param {object} node - Element node\n * @param {object} [opt] - options\n * @param {boolean} [opt.check] - running in internal check()\n * @param {boolean} [opt.forgive] - forgive unknown pseudo-element\n * @returns {boolean} - result\n */\nexport const matchAttributeSelector = (ast, node, opt = {}) => {\n  const {\n    flags: astFlags, matcher: astMatcher, name: astName, value: astValue\n  } = ast;\n  const { check, forgive } = opt;\n  if (typeof astFlags === 'string' && !/^[is]$/i.test(astFlags) && !forgive) {\n    const css = generateCSS(ast);\n    throw new DOMException(`Invalid selector ${css}`, SYNTAX_ERR);\n  }\n  const { attributes } = node;\n  if (attributes?.length) {\n    const contentType = node.ownerDocument.contentType;\n    let caseInsensitive;\n    if (contentType === 'text/html') {\n      if (typeof astFlags === 'string' && /^s$/i.test(astFlags)) {\n        caseInsensitive = false;\n      } else {\n        caseInsensitive = true;\n      }\n    } else if (typeof astFlags === 'string' && /^i$/i.test(astFlags)) {\n      caseInsensitive = true;\n    } else {\n      caseInsensitive = false;\n    }\n    let astAttrName = unescapeSelector(astName.name);\n    if (caseInsensitive) {\n      astAttrName = astAttrName.toLowerCase();\n    }\n    const attrValues = new Set();\n    // namespaced\n    if (astAttrName.indexOf('|') > -1) {\n      const {\n        prefix: astPrefix, localName: astLocalName\n      } = parseAstName(astAttrName);\n      for (const item of attributes) {\n        let { name: itemName, value: itemValue } = item;\n        if (caseInsensitive) {\n          itemName = itemName.toLowerCase();\n          itemValue = itemValue.toLowerCase();\n        }\n        switch (astPrefix) {\n          case '': {\n            if (astLocalName === itemName) {\n              attrValues.add(itemValue);\n            }\n            break;\n          }\n          case '*': {\n            if (itemName.indexOf(':') > -1) {\n              if (itemName.endsWith(`:${astLocalName}`)) {\n                attrValues.add(itemValue);\n              }\n            } else if (astLocalName === itemName) {\n              attrValues.add(itemValue);\n            }\n            break;\n          }\n          default: {\n            if (!check) {\n              if (forgive) {\n                return false;\n              }\n              const css = generateCSS(ast);\n              throw new DOMException(`Invalid selector ${css}`, SYNTAX_ERR);\n            }\n            if (itemName.indexOf(':') > -1) {\n              const [itemPrefix, itemLocalName] = itemName.split(':');\n              // ignore xml:lang\n              if (itemPrefix === 'xml' && itemLocalName === 'lang') {\n                continue;\n              } else if (astPrefix === itemPrefix &&\n                           astLocalName === itemLocalName) {\n                const namespaceDeclared =\n                    isNamespaceDeclared(astPrefix, node);\n                if (namespaceDeclared) {\n                  attrValues.add(itemValue);\n                }\n              }\n            }\n          }\n        }\n      }\n    } else {\n      for (let { name: itemName, value: itemValue } of attributes) {\n        if (caseInsensitive) {\n          itemName = itemName.toLowerCase();\n          itemValue = itemValue.toLowerCase();\n        }\n        if (itemName.indexOf(':') > -1) {\n          const [itemPrefix, itemLocalName] = itemName.split(':');\n          // ignore xml:lang\n          if (itemPrefix === 'xml' && itemLocalName === 'lang') {\n            continue;\n          } else if (astAttrName === itemLocalName) {\n            attrValues.add(itemValue);\n          }\n        } else if (astAttrName === itemName) {\n          attrValues.add(itemValue);\n        }\n      }\n    }\n    if (attrValues.size) {\n      const { name: astIdentValue, value: astStringValue } = astValue ?? {};\n      let attrValue;\n      if (astIdentValue) {\n        if (caseInsensitive) {\n          attrValue = astIdentValue.toLowerCase();\n        } else {\n          attrValue = astIdentValue;\n        }\n      } else if (astStringValue) {\n        if (caseInsensitive) {\n          attrValue = astStringValue.toLowerCase();\n        } else {\n          attrValue = astStringValue;\n        }\n      } else if (astStringValue === '') {\n        attrValue = astStringValue;\n      }\n      switch (astMatcher) {\n        case '=': {\n          return typeof attrValue === 'string' && attrValues.has(attrValue);\n        }\n        case '~=': {\n          if (attrValue && typeof attrValue === 'string') {\n            let res;\n            for (const value of attrValues) {\n              const item = new Set(value.split(/\\s+/));\n              if (item.has(attrValue)) {\n                res = true;\n                break;\n              }\n            }\n            return !!res;\n          }\n          return false;\n        }\n        case '|=': {\n          if (attrValue && typeof attrValue === 'string') {\n            let item;\n            for (const value of attrValues) {\n              if (value === attrValue || value.startsWith(`${attrValue}-`)) {\n                item = value;\n                break;\n              }\n            }\n            if (item) {\n              return true;\n            }\n            return false;\n          }\n          return false;\n        }\n        case '^=': {\n          if (attrValue && typeof attrValue === 'string') {\n            let item;\n            for (const value of attrValues) {\n              if (value.startsWith(`${attrValue}`)) {\n                item = value;\n                break;\n              }\n            }\n            if (item) {\n              return true;\n            }\n            return false;\n          }\n          return false;\n        }\n        case '$=': {\n          if (attrValue && typeof attrValue === 'string') {\n            let item;\n            for (const value of attrValues) {\n              if (value.endsWith(`${attrValue}`)) {\n                item = value;\n                break;\n              }\n            }\n            if (item) {\n              return true;\n            }\n            return false;\n          }\n          return false;\n        }\n        case '*=': {\n          if (attrValue && typeof attrValue === 'string') {\n            let item;\n            for (const value of attrValues) {\n              if (value.includes(`${attrValue}`)) {\n                item = value;\n                break;\n              }\n            }\n            if (item) {\n              return true;\n            }\n            return false;\n          }\n          return false;\n        }\n        case null:\n        default: {\n          return true;\n        }\n      }\n    }\n  }\n  return false;\n};\n\n/**\n * match type selector\n * @param {object} ast - AST\n * @param {object} node - Element node\n * @param {object} [opt] - options\n * @param {boolean} [opt.check] - running in internal check()\n * @param {boolean} [opt.forgive] - forgive undeclared namespace\n * @returns {boolean} - result\n */\nexport const matchTypeSelector = (ast, node, opt = {}) => {\n  const astName = unescapeSelector(ast.name);\n  const { localName, namespaceURI, prefix } = node;\n  const { check, forgive } = opt;\n  let {\n    prefix: astPrefix, localName: astLocalName\n  } = parseAstName(astName, node);\n  if (node.ownerDocument.contentType === 'text/html' &&\n      /[A-Z][\\\\w-]*/i.test(localName)) {\n    astPrefix = astPrefix.toLowerCase();\n    astLocalName = astLocalName.toLowerCase();\n  }\n  let nodePrefix;\n  let nodeLocalName;\n  // just in case that the namespaced content is parsed as text/html\n  if (localName.indexOf(':') > -1) {\n    [nodePrefix, nodeLocalName] = localName.split(':');\n  } else {\n    nodePrefix = prefix || '';\n    nodeLocalName = localName;\n  }\n  switch (astPrefix) {\n    case '': {\n      if (!nodePrefix && !namespaceURI &&\n          (astLocalName === '*' || astLocalName === nodeLocalName)) {\n        return true;\n      }\n      return false;\n    }\n    case '*': {\n      if (astLocalName === '*' || astLocalName === nodeLocalName) {\n        return true;\n      }\n      return false;\n    }\n    default: {\n      if (!check) {\n        if (forgive) {\n          return false;\n        }\n        const css = generateCSS(ast);\n        throw new DOMException(`Invalid selector ${css}`, SYNTAX_ERR);\n      }\n      const astNS = node.lookupNamespaceURI(astPrefix);\n      const nodeNS = node.lookupNamespaceURI(nodePrefix);\n      if (astNS === nodeNS && astPrefix === nodePrefix) {\n        if (astLocalName === '*' || astLocalName === nodeLocalName) {\n          return true;\n        }\n        return false;\n      } else if (!forgive && !astNS) {\n        throw new DOMException(`Undeclared namespace ${astPrefix}`, SYNTAX_ERR);\n      }\n      return false;\n    }\n  }\n};\n", "/**\n * finder.js\n */\n\n/* import */\nimport {\n  matchAttributeSelector, matchDirectionPseudoClass, matchLanguagePseudoClass,\n  matchPseudoElementSelector, matchTypeSelector\n} from './matcher.js';\nimport {\n  findAST, generateCSS, parseSelector, sortAST, unescapeSelector, walkAST\n} from './parser.js';\nimport {\n  isContentEditable, isCustomElement, isFocusVisible, isFocusableArea,\n  isVisible, resolveContent, sortNodes, traverseNode\n} from './utility.js';\n\n/* constants */\nimport {\n  ATTR_SELECTOR, BIT_01, CLASS_SELECTOR, COMBINATOR, DOCUMENT_FRAGMENT_NODE,\n  ELEMENT_NODE, ID_SELECTOR, KEY_FORM_FOCUS, KEY_INPUT_DATE, KEY_INPUT_EDIT,\n  KEY_INPUT_TEXT, KEY_LOGICAL, KEY_MODIFIER, NOT_SUPPORTED_ERR,\n  PS_CLASS_SELECTOR, PS_ELEMENT_SELECTOR, SHOW_ALL, SHOW_CONTAINER, SYNTAX_ERR,\n  TARGET_ALL, TARGET_FIRST, TARGET_LINEAL, TARGET_SELF, TEXT_NODE, TYPE_SELECTOR\n} from './constant.js';\nconst DIR_NEXT = 'next';\nconst DIR_PREV = 'prev';\n\n/**\n * Finder\n * NOTE: #ast[i] corresponds to #nodes[i]\n * #ast: Array.<Ast>\n * #nodes: Array.<Nodes>\n * Ast: {\n *   branch: Array.<Branch | undefined>,\n *   dir: string | null,\n *   filtered: boolean,\n *   find: boolean\n * }\n * Branch: Array.<Twig>\n * Twig: {\n *   combo: Leaf | null,\n *   leaves: Array<Leaf>\n * }\n * Leaf: {\n *   children: Array.<Leaf> | null,\n *   loc: null,\n *   type: string\n * }\n * Nodes: Array.<HTMLElement>\n */\nexport class Finder {\n  /* private fields */\n  #ast;\n  #astCache;\n  #check;\n  #descendant;\n  #document;\n  #documentCache;\n  #domSymbolTree;\n  #event;\n  #focus;\n  #invalidate;\n  #invalidateResults;\n  #lastFocusVisible;\n  #node;\n  #nodeWalker;\n  #nodes;\n  #noexcept;\n  #pseudoElement;\n  #results;\n  #root;\n  #rootWalker;\n  #selector;\n  #shadow;\n  #verifyShadowHost;\n  #walkers;\n  #warn;\n  #window;\n\n  /**\n   * construct\n   * @param {object} window - window\n   */\n  constructor(window) {\n    this.#window = window;\n    this.#astCache = new WeakMap();\n    this.#documentCache = new WeakMap();\n    this.#invalidateResults = new WeakMap();\n    this.#results = new WeakMap();\n    this.#event = null;\n    this.#focus = null;\n    this.#lastFocusVisible = null;\n    this._registerEventListeners();\n  }\n\n  /**\n   * handle error\n   * @param {Error} e - Error\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.noexcept] - no exception\n   * @throws {Error} - Error\n   * @returns {void}\n   */\n  onError(e, opt = {}) {\n    const noexcept = opt.noexcept ?? this.#noexcept;\n    if (!noexcept) {\n      if (e instanceof DOMException ||\n          e instanceof this.#window.DOMException) {\n        if (e.name === NOT_SUPPORTED_ERR) {\n          if (this.#warn) {\n            console.warn(e.message);\n          }\n        } else {\n          throw new this.#window.DOMException(e.message, e.name);\n        }\n      } else if (e.name in this.#window) {\n        throw new this.#window[e.name](e.message, { cause: e });\n      } else {\n        throw e;\n      }\n    }\n  }\n\n  /**\n   * setup finder\n   * @param {string} selector - CSS selector\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.check] - running in internal check()\n   * @param {object} [opt.domSymbolTree] - domSymbolTree\n   * @param {boolean} [opt.noexcept] - no exception\n   * @param {boolean} [opt.warn] - console warn\n   * @returns {object} - finder\n   */\n  setup(selector, node, opt = {}) {\n    const { check, domSymbolTree, noexcept, warn } = opt;\n    this.#check = !!check;\n    this.#domSymbolTree = domSymbolTree;\n    this.#noexcept = !!noexcept;\n    this.#warn = !!warn;\n    [\n      this.#document,\n      this.#root,\n      this.#shadow\n    ] = resolveContent(node);\n    this.#node = node;\n    this.#selector = selector;\n    [\n      this.#ast,\n      this.#nodes\n    ] = this._correspond(selector);\n    this.#invalidateResults = new WeakMap();\n    this.#pseudoElement = [];\n    this.#walkers = new WeakMap();\n    this.#verifyShadowHost = null;\n    return this;\n  }\n\n  /**\n   * register event listeners\n   * @private\n   * @returns {Array.<void>} - results\n   */\n  _registerEventListeners() {\n    const opt = {\n      capture: true,\n      passive: true\n    };\n    const func = [];\n    const focusKeys = ['focus', 'focusin'];\n    for (const key of focusKeys) {\n      func.push(this.#window.addEventListener(key, evt => {\n        this.#focus = evt;\n      }, opt));\n    }\n    const keyboardKeys = ['keydown', 'keyup'];\n    for (const key of keyboardKeys) {\n      func.push(this.#window.addEventListener(key, evt => {\n        const { key } = evt;\n        if (!KEY_MODIFIER.includes(key)) {\n          this.#event = evt;\n        }\n      }, opt));\n    }\n    const mouseKeys = [\n      'mouseover', 'mousedown', 'mouseup', 'click', 'mouseout'\n    ];\n    for (const key of mouseKeys) {\n      func.push(this.#window.addEventListener(key, evt => {\n        this.#event = evt;\n      }, opt));\n    }\n    return func;\n  }\n\n  /**\n   * correspond ast and nodes\n   * @private\n   * @param {string} selector - CSS selector\n   * @returns {Array.<Array.<object>>} - array of ast and nodes\n   */\n  _correspond(selector) {\n    const nodes = [];\n    this.#descendant = false;\n    this.#invalidate = false;\n    let ast;\n    if (this.#documentCache.has(this.#document)) {\n      const cachedItem = this.#documentCache.get(this.#document);\n      if (cachedItem && cachedItem.has(`${selector}`)) {\n        const item = cachedItem.get(`${selector}`);\n        ast = item.ast;\n        this.#descendant = item.descendant;\n        this.#invalidate = item.invalidate;\n      }\n    }\n    if (ast) {\n      const l = ast.length;\n      for (let i = 0; i < l; i++) {\n        ast[i].dir = null;\n        ast[i].filtered = false;\n        ast[i].find = false;\n        nodes[i] = [];\n      }\n    } else {\n      let cssAst;\n      try {\n        cssAst = parseSelector(selector);\n      } catch (e) {\n        return this.onError(e);\n      }\n      const { branches, info } = walkAST(cssAst);\n      const {\n        hasHasPseudoFunc, hasLogicalPseudoFunc, hasNthChildOfSelector,\n        hasStatePseudoClass\n      } = info;\n      let invalidate = hasHasPseudoFunc || hasStatePseudoClass ||\n        !!(hasLogicalPseudoFunc && hasNthChildOfSelector);\n      let descendant = false;\n      let i = 0;\n      ast = [];\n      for (const [...items] of branches) {\n        const branch = [];\n        let item = items.shift();\n        if (item && item.type !== COMBINATOR) {\n          const leaves = new Set();\n          while (item) {\n            let itemName = item.name;\n            if (item.type === COMBINATOR) {\n              const [nextItem] = items;\n              if (nextItem.type === COMBINATOR) {\n                return this.onError(new this.#window.DOMException(\n                  `Invalid selector ${selector}`,\n                  SYNTAX_ERR\n                ));\n              }\n              if (itemName === '+' || itemName === '~') {\n                invalidate = true;\n              } else {\n                descendant = true;\n              }\n              branch.push({\n                combo: item,\n                leaves: sortAST(leaves)\n              });\n              leaves.clear();\n            } else if (item) {\n              if (itemName && typeof itemName === 'string') {\n                itemName = unescapeSelector(itemName);\n                if (typeof itemName === 'string' && itemName !== item.name) {\n                  item.name = itemName;\n                }\n                if (/[|:]/.test(itemName)) {\n                  item.namespace = true;\n                }\n              }\n              leaves.add(item);\n            }\n            if (items.length) {\n              item = items.shift();\n            } else {\n              branch.push({\n                combo: null,\n                leaves: sortAST(leaves)\n              });\n              leaves.clear();\n              break;\n            }\n          }\n        }\n        ast.push({\n          branch,\n          dir: null,\n          filtered: false,\n          find: false\n        });\n        nodes[i] = [];\n        i++;\n      }\n      let cachedItem;\n      if (this.#documentCache.has(this.#document)) {\n        cachedItem = this.#documentCache.get(this.#document);\n      } else {\n        cachedItem = new Map();\n      }\n      cachedItem.set(`${selector}`, {\n        ast,\n        descendant,\n        invalidate\n      });\n      this.#documentCache.set(this.#document, cachedItem);\n      this.#descendant = descendant;\n      this.#invalidate = invalidate;\n    }\n    return [\n      ast,\n      nodes\n    ];\n  }\n\n  /**\n   * create tree walker\n   * @private\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.force] - force new tree walker\n   * @param {number} [opt.whatToShow] - NodeFilter whatToShow\n   * @returns {object} - tree walker\n   */\n  _createTreeWalker(node, opt = {}) {\n    const { force = false, whatToShow = SHOW_CONTAINER } = opt;\n    let walker;\n    if (force) {\n      walker = this.#document.createTreeWalker(node, whatToShow);\n    } else if (this.#walkers.has(node)) {\n      walker = this.#walkers.get(node);\n    } else {\n      walker = this.#document.createTreeWalker(node, whatToShow);\n      this.#walkers.set(node, walker);\n    }\n    return walker;\n  }\n\n  /**\n   * prepare querySelector walker\n   * @private\n   * @returns {object} - tree walker\n   */\n  _prepareQuerySelectorWalker() {\n    this.#nodeWalker = this._createTreeWalker(this.#node);\n    this.#rootWalker = null;\n    return this.#nodeWalker;\n  }\n\n  /**\n   * collect nth child\n   * @private\n   * @param {object} anb - An+B options\n   * @param {number} anb.a - a\n   * @param {number} anb.b - b\n   * @param {boolean} [anb.reverse] - reverse order\n   * @param {object} [anb.selector] - AST\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _collectNthChild(anb, node, opt = {}) {\n    const { a, b, reverse, selector } = anb;\n    const { parentNode } = node;\n    const matched = new Set();\n    let selectorBranches;\n    if (selector) {\n      if (this.#astCache.has(selector)) {\n        selectorBranches = this.#astCache.get(selector);\n      } else {\n        const { branches } = walkAST(selector);\n        selectorBranches = branches;\n        if (!this.#invalidate) {\n          this.#astCache.set(selector, selectorBranches);\n        }\n      }\n      const { branches } = walkAST(selector);\n      selectorBranches = branches;\n    }\n    if (parentNode) {\n      const walker = this._createTreeWalker(parentNode, {\n        force: true\n      });\n      let refNode = walker.firstChild();\n      const selectorNodes = new Set();\n      let l = 0;\n      if (selectorBranches) {\n        while (refNode) {\n          if (isVisible(refNode)) {\n            let bool;\n            for (const leaves of selectorBranches) {\n              bool = this._matchLeaves(leaves, refNode, opt);\n              if (!bool) {\n                break;\n              }\n            }\n            if (bool) {\n              selectorNodes.add(refNode);\n            }\n          }\n          l++;\n          refNode = walker.nextSibling();\n        }\n      } else {\n        while (refNode) {\n          l++;\n          refNode = walker.nextSibling();\n        }\n      }\n      // :first-child, :last-child, :nth-child(b of S), :nth-last-child(b of S)\n      if (a === 0) {\n        if (b > 0 && b <= l) {\n          if (selectorNodes.size) {\n            refNode = traverseNode(parentNode, walker);\n            if (reverse) {\n              refNode = walker.lastChild();\n            } else {\n              refNode = walker.firstChild();\n            }\n            let i = 0;\n            while (refNode) {\n              if (selectorNodes.has(refNode)) {\n                if (i === b - 1) {\n                  matched.add(refNode);\n                  break;\n                }\n                i++;\n              }\n              if (reverse) {\n                refNode = walker.previousSibling();\n              } else {\n                refNode = walker.nextSibling();\n              }\n            }\n          } else if (!selector) {\n            refNode = traverseNode(parentNode, walker);\n            if (reverse) {\n              refNode = walker.lastChild();\n            } else {\n              refNode = walker.firstChild();\n            }\n            let i = 0;\n            while (refNode) {\n              if (i === b - 1) {\n                matched.add(refNode);\n                break;\n              }\n              if (reverse) {\n                refNode = walker.previousSibling();\n              } else {\n                refNode = walker.nextSibling();\n              }\n              i++;\n            }\n          }\n        }\n      // :nth-child()\n      } else {\n        let nth = b - 1;\n        if (a > 0) {\n          while (nth < 0) {\n            nth += a;\n          }\n        }\n        if (nth >= 0 && nth < l) {\n          refNode = traverseNode(parentNode, walker);\n          if (reverse) {\n            refNode = walker.lastChild();\n          } else {\n            refNode = walker.firstChild();\n          }\n          let i = 0;\n          let j = a > 0 ? 0 : b - 1;\n          while (refNode) {\n            if (refNode && nth >= 0 && nth < l) {\n              if (selectorNodes.size) {\n                if (selectorNodes.has(refNode)) {\n                  if (j === nth) {\n                    matched.add(refNode);\n                    nth += a;\n                  }\n                  if (a > 0) {\n                    j++;\n                  } else {\n                    j--;\n                  }\n                }\n              } else if (i === nth) {\n                if (!selector) {\n                  matched.add(refNode);\n                }\n                nth += a;\n              }\n              if (reverse) {\n                refNode = walker.previousSibling();\n              } else {\n                refNode = walker.nextSibling();\n              }\n              i++;\n            } else {\n              break;\n            }\n          }\n        }\n      }\n      if (reverse && matched.size > 1) {\n        const m = [...matched];\n        return new Set(m.reverse());\n      }\n    } else if (node === this.#root && (a + b) === 1) {\n      if (selectorBranches) {\n        let bool;\n        for (const leaves of selectorBranches) {\n          bool = this._matchLeaves(leaves, node, opt);\n          if (bool) {\n            break;\n          }\n        }\n        if (bool) {\n          matched.add(node);\n        }\n      } else {\n        matched.add(node);\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * collect nth of type\n   * @private\n   * @param {object} anb - An+B options\n   * @param {number} anb.a - a\n   * @param {number} anb.b - b\n   * @param {boolean} [anb.reverse] - reverse order\n   * @param {object} node - Element node\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _collectNthOfType(anb, node) {\n    const { a, b, reverse } = anb;\n    const { localName, namespaceURI, parentNode, prefix } = node;\n    const matched = new Set();\n    if (parentNode) {\n      const walker = this._createTreeWalker(parentNode, {\n        force: true\n      });\n      let refNode = traverseNode(parentNode, walker);\n      refNode = walker.firstChild();\n      let l = 0;\n      while (refNode) {\n        l++;\n        refNode = walker.nextSibling();\n      }\n      // :first-of-type, :last-of-type\n      if (a === 0) {\n        if (b > 0 && b <= l) {\n          refNode = traverseNode(parentNode, walker);\n          if (reverse) {\n            refNode = walker.lastChild();\n          } else {\n            refNode = walker.firstChild();\n          }\n          let j = 0;\n          while (refNode) {\n            const {\n              localName: itemLocalName, namespaceURI: itemNamespaceURI,\n              prefix: itemPrefix\n            } = refNode;\n            if (itemLocalName === localName && itemPrefix === prefix &&\n                itemNamespaceURI === namespaceURI) {\n              if (j === b - 1) {\n                matched.add(refNode);\n                break;\n              }\n              j++;\n            }\n            if (reverse) {\n              refNode = walker.previousSibling();\n            } else {\n              refNode = walker.nextSibling();\n            }\n          }\n        }\n      // :nth-of-type()\n      } else {\n        let nth = b - 1;\n        if (a > 0) {\n          while (nth < 0) {\n            nth += a;\n          }\n        }\n        if (nth >= 0 && nth < l) {\n          refNode = traverseNode(parentNode, walker);\n          if (reverse) {\n            refNode = walker.lastChild();\n          } else {\n            refNode = walker.firstChild();\n          }\n          let j = a > 0 ? 0 : b - 1;\n          while (refNode) {\n            const {\n              localName: itemLocalName, namespaceURI: itemNamespaceURI,\n              prefix: itemPrefix\n            } = refNode;\n            if (itemLocalName === localName && itemPrefix === prefix &&\n                itemNamespaceURI === namespaceURI) {\n              if (j === nth) {\n                matched.add(refNode);\n                nth += a;\n              }\n              if (nth < 0 || nth >= l) {\n                break;\n              } else if (a > 0) {\n                j++;\n              } else {\n                j--;\n              }\n            }\n            if (reverse) {\n              refNode = walker.previousSibling();\n            } else {\n              refNode = walker.nextSibling();\n            }\n          }\n        }\n      }\n      if (reverse && matched.size > 1) {\n        const m = [...matched];\n        return new Set(m.reverse());\n      }\n    } else if (node === this.#root && (a + b) === 1) {\n      matched.add(node);\n    }\n    return matched;\n  }\n\n  /**\n   * match An+B\n   * @private\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @param {string} nthName - nth pseudo-class name\n   * @param {object} [opt] - options\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchAnPlusB(ast, node, nthName, opt = {}) {\n    const {\n      nth: {\n        a,\n        b,\n        name: nthIdentName\n      },\n      selector\n    } = ast;\n    const anbMap = new Map();\n    if (nthIdentName) {\n      if (nthIdentName === 'even') {\n        anbMap.set('a', 2);\n        anbMap.set('b', 0);\n      } else if (nthIdentName === 'odd') {\n        anbMap.set('a', 2);\n        anbMap.set('b', 1);\n      }\n      if (nthName.indexOf('last') > -1) {\n        anbMap.set('reverse', true);\n      }\n    } else {\n      if (typeof a === 'string' && /-?\\d+/.test(a)) {\n        anbMap.set('a', a * 1);\n      } else {\n        anbMap.set('a', 0);\n      }\n      if (typeof b === 'string' && /-?\\d+/.test(b)) {\n        anbMap.set('b', b * 1);\n      } else {\n        anbMap.set('b', 0);\n      }\n      if (nthName.indexOf('last') > -1) {\n        anbMap.set('reverse', true);\n      }\n    }\n    if (nthName === 'nth-child' || nthName === 'nth-last-child') {\n      if (selector) {\n        anbMap.set('selector', selector);\n      }\n      const anb = Object.fromEntries(anbMap);\n      const nodes = this._collectNthChild(anb, node, opt);\n      return nodes;\n    } else if (nthName === 'nth-of-type' || nthName === 'nth-last-of-type') {\n      const anb = Object.fromEntries(anbMap);\n      const nodes = this._collectNthOfType(anb, node);\n      return nodes;\n    }\n    return new Set();\n  }\n\n  /**\n   * match :has() pseudo-class function\n   * @private\n   * @param {Array.<object>} astLeaves - AST leaves\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {boolean} - result\n   */\n  _matchHasPseudoFunc(astLeaves, node, opt = {}) {\n    if (Array.isArray(astLeaves) && astLeaves.length) {\n      const leaves = [...astLeaves];\n      const [leaf] = leaves;\n      const { type: leafType } = leaf;\n      let combo;\n      if (leafType === COMBINATOR) {\n        combo = leaves.shift();\n      } else {\n        combo = {\n          name: ' ',\n          type: COMBINATOR\n        };\n      }\n      const twigLeaves = [];\n      while (leaves.length) {\n        const [item] = leaves;\n        const { type: itemType } = item;\n        if (itemType === COMBINATOR) {\n          break;\n        } else {\n          twigLeaves.push(leaves.shift());\n        }\n      }\n      const twig = {\n        combo,\n        leaves: twigLeaves\n      };\n      opt.dir = DIR_NEXT;\n      const nodes = this._matchCombinator(twig, node, opt);\n      if (nodes.size) {\n        if (leaves.length) {\n          let bool = false;\n          for (const nextNode of nodes) {\n            bool = this._matchHasPseudoFunc(leaves, nextNode, opt);\n            if (bool) {\n              break;\n            }\n          }\n          return bool;\n        }\n        return true;\n      }\n    }\n    return false;\n  }\n\n  /**\n   * match logical pseudo-class functions - :has(), :is(), :not(), :where()\n   * @private\n   * @param {object} astData - AST data\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {?object} - matched node\n   */\n  _matchLogicalPseudoFunc(astData, node, opt = {}) {\n    const { astName, branches, twigBranches } = astData;\n    const isShadowRoot = (opt.isShadowRoot || this.#shadow) &&\n      node.nodeType === DOCUMENT_FRAGMENT_NODE;\n    if (astName === 'has') {\n      let bool;\n      for (const leaves of branches) {\n        bool = this._matchHasPseudoFunc(leaves, node, opt);\n        if (bool) {\n          break;\n        }\n      }\n      if (bool) {\n        if (isShadowRoot) {\n          if (this.#verifyShadowHost) {\n            return node;\n          }\n        } else {\n          return node;\n        }\n      }\n    } else {\n      // check for invalid shadow root\n      if (isShadowRoot) {\n        let invalid;\n        for (const branch of branches) {\n          if (branch.length > 1) {\n            invalid = true;\n            break;\n          } else if (astName === 'not') {\n            const [{ type: childAstType }] = branch;\n            if (childAstType !== PS_CLASS_SELECTOR) {\n              invalid = true;\n              break;\n            }\n          }\n        }\n        if (invalid) {\n          return null;\n        }\n      }\n      opt.forgive = astName === 'is' || astName === 'where';\n      const l = twigBranches.length;\n      let bool;\n      for (let i = 0; i < l; i++) {\n        const branch = twigBranches[i];\n        const lastIndex = branch.length - 1;\n        const { leaves } = branch[lastIndex];\n        bool = this._matchLeaves(leaves, node, opt);\n        if (bool && lastIndex > 0) {\n          let nextNodes = new Set([node]);\n          for (let j = lastIndex - 1; j >= 0; j--) {\n            const twig = branch[j];\n            const arr = [];\n            opt.dir = DIR_PREV;\n            for (const nextNode of nextNodes) {\n              const m = this._matchCombinator(twig, nextNode, opt);\n              if (m.size) {\n                arr.push(...m);\n              }\n            }\n            if (arr.length) {\n              if (j === 0) {\n                bool = true;\n              } else {\n                nextNodes = new Set(arr);\n              }\n            } else {\n              bool = false;\n              break;\n            }\n          }\n        }\n        if (bool) {\n          break;\n        }\n      }\n      if (astName === 'not') {\n        if (bool) {\n          return null;\n        }\n        return node;\n      } else if (bool) {\n        return node;\n      }\n    }\n    return null;\n  }\n\n  /**\n   * match pseudo-class selector\n   * @private\n   * @see https://html.spec.whatwg.org/#pseudo-classes\n   * @param {object} ast - AST\n   * @param {object} node - Element node\n   * @param {object} [opt] - options\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchPseudoClassSelector(ast, node, opt = {}) {\n    const { children: astChildren, name: astName } = ast;\n    const { localName, parentNode } = node;\n    const {\n      forgive,\n      warn = this.#warn\n    } = opt;\n    const matched = new Set();\n    // :has(), :is(), :not(), :where()\n    if (Array.isArray(astChildren) && KEY_LOGICAL.includes(astName)) {\n      if (!astChildren.length && astName !== 'is' && astName !== 'where') {\n        const css = generateCSS(ast);\n        return this.onError(new this.#window.DOMException(\n          `Invalid selector ${css}`,\n          SYNTAX_ERR\n        ));\n      }\n      let astData;\n      if (this.#astCache.has(ast)) {\n        astData = this.#astCache.get(ast);\n      } else {\n        const { branches } = walkAST(ast);\n        if (astName === 'has') {\n          // check for nested :has()\n          let forgiven;\n          for (const child of astChildren) {\n            const item = findAST(child, leaf => {\n              if (KEY_LOGICAL.includes(leaf.name) &&\n                  findAST(leaf, nestedLeaf => nestedLeaf.name === 'has')) {\n                return leaf;\n              }\n              return null;\n            });\n            if (item) {\n              const itemName = item.name;\n              if (itemName === 'is' || itemName === 'where') {\n                forgiven = true;\n                break;\n              } else {\n                const css = generateCSS(ast);\n                return this.onError(new this.#window.DOMException(\n                  `Invalid selector ${css}`,\n                  SYNTAX_ERR\n                ));\n              }\n            }\n          }\n          if (forgiven) {\n            return matched;\n          }\n          astData = {\n            astName,\n            branches\n          };\n        } else {\n          const twigBranches = [];\n          for (const [...leaves] of branches) {\n            const branch = [];\n            const leavesSet = new Set();\n            let item = leaves.shift();\n            while (item) {\n              if (item.type === COMBINATOR) {\n                branch.push({\n                  combo: item,\n                  leaves: [...leavesSet]\n                });\n                leavesSet.clear();\n              } else if (item) {\n                leavesSet.add(item);\n              }\n              if (leaves.length) {\n                item = leaves.shift();\n              } else {\n                branch.push({\n                  combo: null,\n                  leaves: [...leavesSet]\n                });\n                leavesSet.clear();\n                break;\n              }\n            }\n            twigBranches.push(branch);\n          }\n          astData = {\n            astName,\n            branches,\n            twigBranches\n          };\n          if (!this.#invalidate) {\n            this.#astCache.set(ast, astData);\n          }\n        }\n      }\n      const res = this._matchLogicalPseudoFunc(astData, node, opt);\n      if (res) {\n        matched.add(res);\n      }\n    } else if (Array.isArray(astChildren)) {\n      // :nth-child(), :nth-last-child(), nth-of-type(), :nth-last-of-type()\n      if (/^nth-(?:last-)?(?:child|of-type)$/.test(astName)) {\n        if (astChildren.length !== 1) {\n          const css = generateCSS(ast);\n          return this.onError(new this.#window.DOMException(\n            `Invalid selector ${css}`,\n            SYNTAX_ERR\n          ));\n        }\n        const [branch] = astChildren;\n        const nodes = this._matchAnPlusB(branch, node, astName, opt);\n        return nodes;\n      } else {\n        switch (astName) {\n          // :dir()\n          case 'dir': {\n            if (astChildren.length !== 1) {\n              const css = generateCSS(ast);\n              return this.onError(new this.#window.DOMException(\n                `Invalid selector ${css}`,\n                SYNTAX_ERR\n              ));\n            }\n            const [astChild] = astChildren;\n            const res = matchDirectionPseudoClass(astChild, node);\n            if (res) {\n              matched.add(node);\n            }\n            break;\n          }\n          // :lang()\n          case 'lang': {\n            if (!astChildren.length) {\n              const css = generateCSS(ast);\n              return this.onError(new this.#window.DOMException(\n                `Invalid selector ${css}`,\n                SYNTAX_ERR\n              ));\n            }\n            let bool;\n            for (const astChild of astChildren) {\n              bool = matchLanguagePseudoClass(astChild, node);\n              if (bool) {\n                break;\n              }\n            }\n            if (bool) {\n              matched.add(node);\n            }\n            break;\n          }\n          // :state()\n          case 'state': {\n            if (isCustomElement(node)) {\n              const [{ value: stateValue }] = astChildren;\n              if (stateValue) {\n                if (node[stateValue]) {\n                  matched.add(node);\n                } else {\n                  for (const i in node) {\n                    const prop = node[i];\n                    if (prop instanceof this.#window.ElementInternals) {\n                      if (prop?.states?.has(stateValue)) {\n                        matched.add(node);\n                      }\n                      break;\n                    }\n                  }\n                }\n              }\n            }\n            break;\n          }\n          case 'current':\n          case 'nth-col':\n          case 'nth-last-col': {\n            if (warn) {\n              this.onError(new this.#window.DOMException(\n                `Unsupported pseudo-class :${astName}()`,\n                NOT_SUPPORTED_ERR\n              ));\n            }\n            break;\n          }\n          case 'host':\n          case 'host-context': {\n            // ignore\n            break;\n          }\n          // dropped from CSS Selectors 3\n          case 'contains': {\n            if (warn) {\n              this.onError(new this.#window.DOMException(\n                `Unknown pseudo-class :${astName}()`,\n                NOT_SUPPORTED_ERR\n              ));\n            }\n            break;\n          }\n          default: {\n            if (!forgive) {\n              this.onError(new this.#window.DOMException(\n                `Unknown pseudo-class :${astName}()`,\n                SYNTAX_ERR\n              ));\n            }\n          }\n        }\n      }\n    } else {\n      switch (astName) {\n        case 'any-link':\n        case 'link': {\n          if ((localName === 'a' || localName === 'area') &&\n              node.hasAttribute('href')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'local-link': {\n          if ((localName === 'a' || localName === 'area') &&\n              node.hasAttribute('href')) {\n            const { href, origin, pathname } = new URL(this.#document.URL);\n            const attrURL = new URL(node.getAttribute('href'), href);\n            if (attrURL.origin === origin && attrURL.pathname === pathname) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'visited': {\n          // prevent fingerprinting\n          break;\n        }\n        case 'hover': {\n          const { target, type } = this.#event ?? {};\n          if (/^(?:click|mouse(?:down|over|up))$/.test(type) &&\n              node.contains(target)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'active': {\n          const { buttons, target, type } = this.#event ?? {};\n          if (type === 'mousedown' && buttons & BIT_01 &&\n              node.contains(target)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'target': {\n          const { hash } = new URL(this.#document.URL);\n          if (node.id && hash === `#${node.id}` &&\n              this.#document.contains(node)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'target-within': {\n          const { hash } = new URL(this.#document.URL);\n          if (hash) {\n            const id = hash.replace(/^#/, '');\n            let current = this.#document.getElementById(id);\n            while (current) {\n              if (current === node) {\n                matched.add(node);\n                break;\n              }\n              current = current.parentNode;\n            }\n          }\n          break;\n        }\n        case 'scope': {\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            if (!this.#shadow && node === this.#node) {\n              matched.add(node);\n            }\n          } else if (node === this.#document.documentElement) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'focus': {\n          if (node === this.#document.activeElement && isFocusableArea(node)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'focus-visible': {\n          if (node === this.#document.activeElement && isFocusableArea(node)) {\n            let bool;\n            if (isFocusVisible(node)) {\n              bool = true;\n            } else if (this.#focus) {\n              const { relatedTarget, target: focusTarget } = this.#focus;\n              if (focusTarget === node) {\n                if (isFocusVisible(relatedTarget)) {\n                  bool = true;\n                } else if (this.#event) {\n                  const {\n                    key: eventKey, target: eventTarget, type: eventType\n                  } = this.#event;\n                  // this.#event is irrelevant if eventTarget === relatedTarget\n                  if (eventTarget === relatedTarget) {\n                    if (this.#lastFocusVisible === null) {\n                      bool = true;\n                    } else if (focusTarget === this.#lastFocusVisible) {\n                      bool = true;\n                    }\n                  } else if (eventKey === 'Tab') {\n                    if ((eventType === 'keydown' && eventTarget !== node) ||\n                        (eventType === 'keyup' && eventTarget === node)) {\n                      if (eventTarget === focusTarget) {\n                        if (this.#lastFocusVisible === null) {\n                          bool = true;\n                        } else if (eventTarget === this.#lastFocusVisible &&\n                                   relatedTarget === null) {\n                          bool = true;\n                        }\n                      } else {\n                        bool = true;\n                      }\n                    }\n                  } else if (eventKey) {\n                    if ((eventType === 'keydown' || eventType === 'keyup') &&\n                        eventTarget === node) {\n                      bool = true;\n                    }\n                  }\n                } else if (relatedTarget === null ||\n                           relatedTarget === this.#lastFocusVisible) {\n                  bool = true;\n                }\n              }\n            }\n            if (bool) {\n              this.#lastFocusVisible = node;\n              matched.add(node);\n            } else if (this.#lastFocusVisible === node) {\n              this.#lastFocusVisible = null;\n            }\n          }\n          break;\n        }\n        case 'focus-within': {\n          let bool;\n          let current = this.#document.activeElement;\n          if (isFocusableArea(current)) {\n            while (current) {\n              if (current === node) {\n                bool = true;\n                break;\n              }\n              current = current.parentNode;\n            }\n          }\n          if (bool) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'open':\n        case 'closed': {\n          if (localName === 'details' || localName === 'dialog') {\n            if (node.hasAttribute('open')) {\n              if (astName === 'open') {\n                matched.add(node);\n              }\n            } else if (astName === 'closed') {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'disabled':\n        case 'enabled': {\n          const keys = [...KEY_FORM_FOCUS, 'fieldset', 'optgroup', 'option'];\n          if (keys.includes(localName) ||\n              isCustomElement(node, { formAssociated: true })) {\n            let disabled;\n            if (node.disabled || node.hasAttribute('disabled')) {\n              disabled = true;\n            } else if (node.localName === 'option') {\n              if (parentNode.localName === 'optgroup' &&\n                  (parentNode.disabled ||\n                   parentNode.hasAttribute('disabled'))) {\n                disabled = true;\n              }\n            } else if (node.localName !== 'optgroup') {\n              let parent = parentNode;\n              while (parent) {\n                if (parent.localName === 'fieldset' &&\n                    (parent.disabled || parent.hasAttribute('disabled'))) {\n                  let refNode = parent.firstElementChild;\n                  while (refNode) {\n                    if (refNode.localName === 'legend') {\n                      break;\n                    }\n                    refNode = refNode.nextElementSibling;\n                  }\n                  if (refNode) {\n                    if (!refNode.contains(node)) {\n                      disabled = true;\n                    }\n                  } else {\n                    disabled = true;\n                  }\n                  break;\n                } else if (parent.localName === 'form') {\n                  break;\n                } else if (parent.parentNode?.nodeType === ELEMENT_NODE) {\n                  if (parent.parentNode.localName === 'form') {\n                    break;\n                  } else {\n                    parent = parent.parentNode;\n                  }\n                } else {\n                  break;\n                }\n              }\n            }\n            if (disabled) {\n              if (astName === 'disabled') {\n                matched.add(node);\n              }\n            } else if (astName === 'enabled') {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'read-only':\n        case 'read-write': {\n          let readonly;\n          let writable;\n          switch (localName) {\n            case 'textarea': {\n              if (node.readOnly || node.hasAttribute('readonly') ||\n                  node.disabled || node.hasAttribute('disabled')) {\n                readonly = true;\n              } else {\n                writable = true;\n              }\n              break;\n            }\n            case 'input': {\n              if (!node.type || KEY_INPUT_EDIT.includes(node.type)) {\n                if (node.readOnly || node.hasAttribute('readonly') ||\n                    node.disabled || node.hasAttribute('disabled')) {\n                  readonly = true;\n                } else {\n                  writable = true;\n                }\n              } else {\n                readonly = true;\n              }\n              break;\n            }\n            default: {\n              if (isContentEditable(node)) {\n                writable = true;\n              } else {\n                readonly = true;\n              }\n            }\n          }\n          if (readonly) {\n            if (astName === 'read-only') {\n              matched.add(node);\n            }\n          } else if (astName === 'read-write' && writable) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'placeholder-shown': {\n          let placeholder;\n          if (node.placeholder) {\n            placeholder = node.placeholder;\n          } else if (node.hasAttribute('placeholder')) {\n            placeholder = node.getAttribute('placeholder');\n          }\n          if (typeof placeholder === 'string' && !/[\\r\\n]/.test(placeholder)) {\n            let targetNode;\n            if (localName === 'textarea') {\n              targetNode = node;\n            } else if (localName === 'input') {\n              if (node.hasAttribute('type')) {\n                const keys = [...KEY_INPUT_TEXT, 'number'];\n                if (keys.includes(node.getAttribute('type'))) {\n                  targetNode = node;\n                }\n              } else {\n                targetNode = node;\n              }\n            }\n            if (targetNode && node.value === '') {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'checked': {\n          const attrType = node.getAttribute('type');\n          if ((node.checked && localName === 'input' &&\n               (attrType === 'checkbox' || attrType === 'radio')) ||\n              (node.selected && localName === 'option')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'indeterminate': {\n          if ((node.indeterminate && localName === 'input' &&\n               node.type === 'checkbox') ||\n              (localName === 'progress' && !node.hasAttribute('value'))) {\n            matched.add(node);\n          } else if (localName === 'input' && node.type === 'radio' &&\n                     !node.hasAttribute('checked')) {\n            const nodeName = node.name;\n            let parent = node.parentNode;\n            while (parent) {\n              if (parent.localName === 'form') {\n                break;\n              }\n              parent = parent.parentNode;\n            }\n            if (!parent) {\n              parent = this.#document.documentElement;\n            }\n            const walker = this._createTreeWalker(parent);\n            let refNode = traverseNode(parent, walker);\n            refNode = walker.firstChild();\n            let checked;\n            while (refNode) {\n              if (refNode.localName === 'input' &&\n                  refNode.getAttribute('type') === 'radio') {\n                if (refNode.hasAttribute('name')) {\n                  if (refNode.getAttribute('name') === nodeName) {\n                    checked = !!refNode.checked;\n                  }\n                } else {\n                  checked = !!refNode.checked;\n                }\n                if (checked) {\n                  break;\n                }\n              }\n              refNode = walker.nextNode();\n            }\n            if (!checked) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'default': {\n          // button[type=\"submit\"], input[type=\"submit\"], input[type=\"image\"]\n          const chekcKeys = ['checkbox', 'radio'];\n          const resetKeys = ['button', 'reset'];\n          const submitKeys = ['image', 'submit'];\n          const attrType = node.getAttribute('type');\n          if ((localName === 'button' &&\n               !(node.hasAttribute('type') && resetKeys.includes(attrType))) ||\n              (localName === 'input' && node.hasAttribute('type') &&\n               submitKeys.includes(attrType))) {\n            let form = node.parentNode;\n            while (form) {\n              if (form.localName === 'form') {\n                break;\n              }\n              form = form.parentNode;\n            }\n            if (form) {\n              const walker = this._createTreeWalker(form);\n              let refNode = traverseNode(form, walker);\n              refNode = walker.firstChild();\n              while (refNode) {\n                const nodeName = refNode.localName;\n                const nodeAttrType = refNode.getAttribute('type');\n                let m;\n                if (nodeName === 'button') {\n                  m = !(refNode.hasAttribute('type') &&\n                    resetKeys.includes(nodeAttrType));\n                } else if (nodeName === 'input') {\n                  m = refNode.hasAttribute('type') &&\n                    submitKeys.includes(nodeAttrType);\n                }\n                if (m) {\n                  if (refNode === node) {\n                    matched.add(node);\n                  }\n                  break;\n                }\n                refNode = walker.nextNode();\n              }\n            }\n          // input[type=\"checkbox\"], input[type=\"radio\"]\n          } else if (localName === 'input' && node.hasAttribute('type') &&\n                     chekcKeys.includes(attrType) &&\n                     node.hasAttribute('checked')) {\n            matched.add(node);\n          // option\n          } else if (localName === 'option' && node.hasAttribute('selected')) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'valid':\n        case 'invalid': {\n          const keys = [...KEY_FORM_FOCUS, 'form'];\n          if (keys.includes(localName)) {\n            let valid;\n            if (node.checkValidity()) {\n              if (node.maxLength >= 0) {\n                if (node.maxLength >= node.value.length) {\n                  valid = true;\n                }\n              } else {\n                valid = true;\n              }\n            }\n            if (valid) {\n              if (astName === 'valid') {\n                matched.add(node);\n              }\n            } else if (astName === 'invalid') {\n              matched.add(node);\n            }\n          } else if (localName === 'fieldset') {\n            const walker = this._createTreeWalker(node);\n            let refNode = traverseNode(node, walker);\n            refNode = walker.firstChild();\n            let valid;\n            if (!refNode) {\n              valid = true;\n            } else {\n              while (refNode) {\n                if (keys.includes(refNode.localName)) {\n                  if (refNode.checkValidity()) {\n                    if (refNode.maxLength >= 0) {\n                      valid = refNode.maxLength >= refNode.value.length;\n                    } else {\n                      valid = true;\n                    }\n                  } else {\n                    valid = false;\n                  }\n                  if (!valid) {\n                    break;\n                  }\n                }\n                refNode = walker.nextNode();\n              }\n            }\n            if (valid) {\n              if (astName === 'valid') {\n                matched.add(node);\n              }\n            } else if (astName === 'invalid') {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'in-range':\n        case 'out-of-range': {\n          const keys = [...KEY_INPUT_DATE, 'number', 'range'];\n          const attrType = node.getAttribute('type');\n          if (localName === 'input' &&\n              !(node.readonly || node.hasAttribute('readonly')) &&\n              !(node.disabled || node.hasAttribute('disabled')) &&\n              keys.includes(attrType)) {\n            const flowed =\n              node.validity.rangeUnderflow || node.validity.rangeOverflow;\n            if (astName === 'out-of-range' && flowed) {\n              matched.add(node);\n            } else if (astName === 'in-range' && !flowed &&\n                       (node.hasAttribute('min') || node.hasAttribute('max') ||\n                       attrType === 'range')) {\n              matched.add(node);\n            }\n          }\n          break;\n        }\n        case 'required':\n        case 'optional': {\n          let required;\n          let optional;\n          if (localName === 'select' || localName === 'textarea') {\n            if (node.required || node.hasAttribute('required')) {\n              required = true;\n            } else {\n              optional = true;\n            }\n          } else if (localName === 'input') {\n            if (node.hasAttribute('type')) {\n              const keys = [...KEY_INPUT_EDIT, 'checkbox', 'file', 'radio'];\n              const attrType = node.getAttribute('type');\n              if (keys.includes(attrType)) {\n                if (node.required || node.hasAttribute('required')) {\n                  required = true;\n                } else {\n                  optional = true;\n                }\n              } else {\n                optional = true;\n              }\n            } else if (node.required || node.hasAttribute('required')) {\n              required = true;\n            } else {\n              optional = true;\n            }\n          }\n          if (astName === 'required' && required) {\n            matched.add(node);\n          } else if (astName === 'optional' && optional) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'root': {\n          if (node === this.#document.documentElement) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'empty': {\n          if (node.hasChildNodes()) {\n            const walker = this._createTreeWalker(node, {\n              force: true,\n              whatToShow: SHOW_ALL\n            });\n            let refNode = walker.firstChild();\n            let bool;\n            while (refNode) {\n              bool = refNode.nodeType !== ELEMENT_NODE &&\n                refNode.nodeType !== TEXT_NODE;\n              if (!bool) {\n                break;\n              }\n              refNode = walker.nextSibling();\n            }\n            if (bool) {\n              matched.add(node);\n            }\n          } else {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'first-child': {\n          if ((parentNode && node === parentNode.firstElementChild) ||\n              node === this.#root) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'last-child': {\n          if ((parentNode && node === parentNode.lastElementChild) ||\n              node === this.#root) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'only-child': {\n          if ((parentNode && node === parentNode.firstElementChild &&\n               node === parentNode.lastElementChild) || node === this.#root) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'first-of-type': {\n          if (parentNode) {\n            const [node1] = this._collectNthOfType({\n              a: 0,\n              b: 1\n            }, node);\n            if (node1) {\n              matched.add(node1);\n            }\n          } else if (node === this.#root) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'last-of-type': {\n          if (parentNode) {\n            const [node1] = this._collectNthOfType({\n              a: 0,\n              b: 1,\n              reverse: true\n            }, node);\n            if (node1) {\n              matched.add(node1);\n            }\n          } else if (node === this.#root) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'only-of-type': {\n          if (parentNode) {\n            const [node1] = this._collectNthOfType({\n              a: 0,\n              b: 1\n            }, node);\n            if (node1 === node) {\n              const [node2] = this._collectNthOfType({\n                a: 0,\n                b: 1,\n                reverse: true\n              }, node);\n              if (node2 === node) {\n                matched.add(node);\n              }\n            }\n          } else if (node === this.#root) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'defined': {\n          if (node.hasAttribute('is') || localName.includes('-')) {\n            if (isCustomElement(node)) {\n              matched.add(node);\n            }\n          // NOTE: MathMLElement not implemented in jsdom\n          } else if (node instanceof this.#window.HTMLElement ||\n                     node instanceof this.#window.SVGElement) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'popover-open': {\n          if (node.popover && isVisible(node)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case 'host':\n        case 'host-context': {\n          // ignore\n          break;\n        }\n        // legacy pseudo-elements\n        case 'after':\n        case 'before':\n        case 'first-letter':\n        case 'first-line': {\n          if (warn) {\n            this.onError(new this.#window.DOMException(\n              `Unsupported pseudo-element ::${astName}`,\n              NOT_SUPPORTED_ERR\n            ));\n          }\n          break;\n        }\n        // not supported\n        case 'autofill':\n        case 'blank':\n        case 'buffering':\n        case 'current':\n        case 'fullscreen':\n        case 'future':\n        case 'has-slotted':\n        case 'modal':\n        case 'muted':\n        case 'past':\n        case 'paused':\n        case 'picture-in-picture':\n        case 'playing':\n        case 'seeking':\n        case 'stalled':\n        case 'user-invalid':\n        case 'user-valid':\n        case 'volume-locked':\n        case '-webkit-autofill': {\n          if (warn) {\n            this.onError(new this.#window.DOMException(\n              `Unsupported pseudo-class :${astName}`,\n              NOT_SUPPORTED_ERR\n            ));\n          }\n          break;\n        }\n        default: {\n          if (astName.startsWith('-webkit-')) {\n            if (warn) {\n              this.onError(new this.#window.DOMException(\n                `Unsupported pseudo-class :${astName}`,\n                NOT_SUPPORTED_ERR\n              ));\n            }\n          } else if (!forgive) {\n            this.onError(new this.#window.DOMException(\n              `Unknown pseudo-class :${astName}`,\n              SYNTAX_ERR\n            ));\n          }\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * match shadow host pseudo class\n   * @private\n   * @param {object} ast - AST\n   * @param {object} node - DocumentFragment node\n   * @returns {?object} - matched node\n   */\n  _matchShadowHostPseudoClass(ast, node) {\n    const { children: astChildren, name: astName } = ast;\n    if (Array.isArray(astChildren)) {\n      if (astChildren.length !== 1) {\n        const css = generateCSS(ast);\n        return this.onError(new this.#window.DOMException(\n          `Invalid selector ${css}`,\n          SYNTAX_ERR\n        ));\n      }\n      const { branches } = walkAST(astChildren[0]);\n      const [branch] = branches;\n      const [...leaves] = branch;\n      const { host } = node;\n      if (astName === 'host') {\n        let bool;\n        for (const leaf of leaves) {\n          const { type: leafType } = leaf;\n          if (leafType === COMBINATOR) {\n            const css = generateCSS(ast);\n            return this.onError(new this.#window.DOMException(\n              `Invalid selector ${css}`,\n              SYNTAX_ERR\n            ));\n          }\n          bool = this._matchSelector(leaf, host).has(host);\n          if (!bool) {\n            break;\n          }\n        }\n        if (bool) {\n          return node;\n        }\n        return null;\n      } else if (astName === 'host-context') {\n        let parent = host;\n        let bool;\n        while (parent) {\n          for (const leaf of leaves) {\n            const { type: leafType } = leaf;\n            if (leafType === COMBINATOR) {\n              const css = generateCSS(ast);\n              return this.onError(new this.#window.DOMException(\n                `Invalid selector ${css}`,\n                SYNTAX_ERR\n              ));\n            }\n            bool = this._matchSelector(leaf, parent).has(parent);\n            if (!bool) {\n              break;\n            }\n          }\n          if (bool) {\n            break;\n          } else {\n            parent = parent.parentNode;\n          }\n        }\n        if (bool) {\n          return node;\n        }\n        return null;\n      }\n    } else if (astName === 'host') {\n      return node;\n    }\n    return this.onError(new this.#window.DOMException(\n      `Invalid selector :${astName}`,\n      SYNTAX_ERR\n    ));\n  }\n\n  /**\n   * match selector\n   * @private\n   * @param {object} ast - AST\n   * @param {object} node - Document, DocumentFragment, Element node\n   * @param {object} [opt] - options\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchSelector(ast, node, opt = {}) {\n    const { type: astType } = ast;\n    const astName = unescapeSelector(ast.name);\n    const matched = new Set();\n    if (node.nodeType === ELEMENT_NODE) {\n      switch (astType) {\n        case ATTR_SELECTOR: {\n          const res = matchAttributeSelector(ast, node, opt);\n          if (res) {\n            matched.add(node);\n          }\n          break;\n        }\n        case ID_SELECTOR: {\n          if (node.id === astName) {\n            matched.add(node);\n          }\n          break;\n        }\n        case CLASS_SELECTOR: {\n          if (node.classList.contains(astName)) {\n            matched.add(node);\n          }\n          break;\n        }\n        case PS_CLASS_SELECTOR: {\n          const nodes = this._matchPseudoClassSelector(ast, node, opt);\n          return nodes;\n        }\n        case TYPE_SELECTOR: {\n          const res = matchTypeSelector(ast, node, opt);\n          if (res) {\n            matched.add(node);\n          }\n          break;\n        }\n        case PS_ELEMENT_SELECTOR:\n        default: {\n          try {\n            const { check } = opt;\n            if (check) {\n              const css = generateCSS(ast);\n              this.#pseudoElement.push(css);\n              matched.add(node);\n            } else {\n              matchPseudoElementSelector(astName, astType, opt);\n            }\n          } catch (e) {\n            this.onError(e);\n          }\n        }\n      }\n    } else if (this.#shadow && astType === PS_CLASS_SELECTOR &&\n               node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n      if (KEY_LOGICAL.includes(astName)) {\n        opt.isShadowRoot = true;\n        const nodes = this._matchPseudoClassSelector(ast, node, opt);\n        return nodes;\n      } else if (astName === 'host' || astName === 'host-context') {\n        const res = this._matchShadowHostPseudoClass(ast, node, opt);\n        if (res) {\n          this.#verifyShadowHost = true;\n          matched.add(res);\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * match leaves\n   * @private\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} node - node\n   * @param {object} [opt] - options\n   * @returns {boolean} - result\n   */\n  _matchLeaves(leaves, node, opt = {}) {\n    let result;\n    if (this.#invalidate) {\n      result = this.#invalidateResults.get(leaves);\n    } else {\n      result = this.#results.get(leaves);\n    }\n    if (result && result.has(node)) {\n      const { matched } = result.get(node);\n      return matched;\n    } else {\n      let cacheable = true;\n      const formKeys = [...KEY_FORM_FOCUS, 'fieldset', 'form'];\n      const pseudoKeys = ['any-link', 'defined', 'dir', 'link', 'scope'];\n      if (node.nodeType === ELEMENT_NODE && formKeys.includes(node.localName)) {\n        cacheable = false;\n      }\n      let bool;\n      for (const leaf of leaves) {\n        switch (leaf.type) {\n          case ATTR_SELECTOR:\n          case ID_SELECTOR: {\n            cacheable = false;\n            break;\n          }\n          case PS_CLASS_SELECTOR: {\n            if (pseudoKeys.includes(leaf.name)) {\n              cacheable = false;\n            }\n            break;\n          }\n          default:\n        }\n        bool = this._matchSelector(leaf, node, opt).has(node);\n        if (!bool) {\n          break;\n        }\n      }\n      if (cacheable) {\n        if (!result) {\n          result = new WeakMap();\n        }\n        result.set(node, {\n          matched: bool\n        });\n        if (this.#invalidate) {\n          this.#invalidateResults.set(leaves, result);\n        } else {\n          this.#results.set(leaves, result);\n        }\n      }\n      return bool;\n    }\n  }\n\n  /**\n   * find descendant nodes\n   * @private\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} baseNode - base Element node or Element.shadowRoot\n   * @param {object} [opt] - options\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _findDescendantNodes(leaves, baseNode, opt = {}) {\n    const [leaf, ...filterLeaves] = leaves;\n    const compound = filterLeaves.length > 0;\n    const { type: leafType } = leaf;\n    const leafName = unescapeSelector(leaf.name);\n    const nodes = new Set();\n    let pending = false;\n    if (this.#shadow || baseNode.nodeType !== ELEMENT_NODE) {\n      pending = true;\n    } else {\n      switch (leafType) {\n        case PS_ELEMENT_SELECTOR: {\n          matchPseudoElementSelector(leafName, leafType, opt);\n          break;\n        }\n        case ID_SELECTOR: {\n          if (this.#root.nodeType === ELEMENT_NODE) {\n            pending = true;\n          } else {\n            const node = this.#root.getElementById(leafName);\n            if (node && node !== baseNode && baseNode.contains(node)) {\n              if (compound) {\n                const bool = this._matchLeaves(filterLeaves, node, opt);\n                if (bool) {\n                  nodes.add(node);\n                }\n              } else {\n                nodes.add(node);\n              }\n            }\n          }\n          break;\n        }\n        default: {\n          pending = true;\n        }\n      }\n    }\n    if (pending) {\n      const walker = this._createTreeWalker(baseNode);\n      let refNode = traverseNode(baseNode, walker);\n      refNode = walker.firstChild();\n      while (refNode) {\n        const bool = this._matchLeaves(leaves, refNode, opt);\n        if (bool) {\n          nodes.add(refNode);\n        }\n        refNode = walker.nextNode();\n      }\n    }\n    return nodes;\n  }\n\n  /**\n   * match combinator\n   * @private\n   * @param {object} twig - twig\n   * @param {object} node - Element node\n   * @param {object} [opt] - option\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _matchCombinator(twig, node, opt = {}) {\n    const { combo, leaves } = twig;\n    const { name: comboName } = combo;\n    const { parentNode } = node;\n    const { dir } = opt;\n    const matched = new Set();\n    if (dir === DIR_NEXT) {\n      switch (comboName) {\n        case '+': {\n          const refNode = node.nextElementSibling;\n          if (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, opt);\n            if (bool) {\n              matched.add(refNode);\n            }\n          }\n          break;\n        }\n        case '~': {\n          if (parentNode) {\n            let refNode = node.nextElementSibling;\n            while (refNode) {\n              const bool = this._matchLeaves(leaves, refNode, opt);\n              if (bool) {\n                matched.add(refNode);\n              }\n              refNode = refNode.nextElementSibling;\n            }\n          }\n          break;\n        }\n        case '>': {\n          let refNode = node.firstElementChild;\n          while (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, opt);\n            if (bool) {\n              matched.add(refNode);\n            }\n            refNode = refNode.nextElementSibling;\n          }\n          break;\n        }\n        case ' ':\n        default: {\n          const nodes = this._findDescendantNodes(leaves, node, opt);\n          if (nodes.size) {\n            return nodes;\n          }\n        }\n      }\n    } else {\n      switch (comboName) {\n        case '+': {\n          const refNode = node.previousElementSibling;\n          if (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, opt);\n            if (bool) {\n              matched.add(refNode);\n            }\n          }\n          break;\n        }\n        case '~': {\n          if (parentNode) {\n            let refNode = parentNode.firstElementChild;\n            while (refNode) {\n              if (refNode === node) {\n                break;\n              } else {\n                const bool = this._matchLeaves(leaves, refNode, opt);\n                if (bool) {\n                  matched.add(refNode);\n                }\n              }\n              refNode = refNode.nextElementSibling;\n            }\n          }\n          break;\n        }\n        case '>': {\n          if (parentNode) {\n            const bool = this._matchLeaves(leaves, parentNode, opt);\n            if (bool) {\n              matched.add(parentNode);\n            }\n          }\n          break;\n        }\n        case ' ':\n        default: {\n          const arr = [];\n          let refNode = parentNode;\n          while (refNode) {\n            const bool = this._matchLeaves(leaves, refNode, opt);\n            if (bool) {\n              arr.push(refNode);\n            }\n            refNode = refNode.parentNode;\n          }\n          if (arr.length) {\n            return new Set(arr.reverse());\n          }\n        }\n      }\n    }\n    return matched;\n  }\n\n  /**\n   * find matched node(s) preceding this.#node\n   * @private\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} node - node to start from\n   * @param {object} opt - options\n   * @param {boolean} [opt.force] - traverse only to next node\n   * @param {string} [opt.targetType] - target type\n   * @returns {Array.<object>} - collection of matched nodes\n   */\n  _findPrecede(leaves, node, opt = {}) {\n    const { force, targetType } = opt;\n    if (!this.#rootWalker) {\n      this.#rootWalker = this._createTreeWalker(this.#root);\n    }\n    const walker = this.#rootWalker;\n    const nodes = [];\n    let refNode = traverseNode(node, walker, !!force);\n    if (refNode && refNode !== this.#node) {\n      if (refNode.nodeType !== ELEMENT_NODE) {\n        refNode = walker.nextNode();\n      } else if (refNode === node) {\n        if (refNode !== this.#root) {\n          refNode = walker.nextNode();\n        }\n      }\n      while (refNode) {\n        if (refNode === this.#node) {\n          break;\n        }\n        const matched = this._matchLeaves(leaves, refNode, {\n          warn: this.#warn\n        });\n        if (matched) {\n          nodes.push(refNode);\n          if (targetType !== TARGET_ALL) {\n            break;\n          }\n        }\n        refNode = walker.nextNode();\n      }\n    }\n    return nodes;\n  }\n\n  /**\n   * find matched node(s) in #nodeWalker\n   * @private\n   * @param {Array.<object>} leaves - AST leaves\n   * @param {object} node - node to start from\n   * @param {object} opt - options\n   * @param {boolean} [opt.precede] - find precede\n   * @param {boolean} [opt.force] - traverse only to next node\n   * @param {string} [opt.targetType] - target type\n   * @returns {Array.<object>} - collection of matched nodes\n   */\n  _findNodeWalker(leaves, node, opt = {}) {\n    const { force, precede, targetType } = opt;\n    const walker = this.#nodeWalker;\n    if (precede) {\n      const precedeNodes = this._findPrecede(leaves, this.#root, opt);\n      if (precedeNodes.length) {\n        return precedeNodes;\n      }\n    }\n    const nodes = [];\n    let refNode = traverseNode(node, walker, !!force);\n    if (refNode) {\n      if (refNode.nodeType !== ELEMENT_NODE) {\n        refNode = walker.nextNode();\n      } else if (refNode === node) {\n        if (refNode !== this.#root) {\n          refNode = walker.nextNode();\n        }\n      }\n      while (refNode) {\n        const matched = this._matchLeaves(leaves, refNode, {\n          warn: this.#warn\n        });\n        if (matched) {\n          nodes.push(refNode);\n          if (targetType !== TARGET_ALL) {\n            break;\n          }\n        }\n        refNode = walker.nextNode();\n      }\n    }\n    return nodes;\n  }\n\n  /**\n   * match self\n   * @private\n   * @param {Array} leaves - AST leaves\n   * @param {boolean} check - running in internal check()\n   * @returns {Array} - [nodes, filtered]\n   */\n  _matchSelf(leaves, check = false) {\n    const nodes = [];\n    let filtered = false;\n    const bool = this._matchLeaves(leaves, this.#node, {\n      check,\n      warn: this.#warn\n    });\n    if (bool) {\n      nodes.push(this.#node);\n      filtered = true;\n    }\n    return [nodes, filtered, this.#pseudoElement];\n  }\n\n  /**\n   * find lineal\n   * @private\n   * @param {Array} leaves - AST leaves\n   * @param {object} opt - options\n   * @returns {Array} - [nodes, filtered]\n   */\n  _findLineal(leaves, opt) {\n    const { complex } = opt;\n    const nodes = [];\n    let filtered = false;\n    let bool = this._matchLeaves(leaves, this.#node, {\n      warn: this.#warn\n    });\n    if (bool) {\n      nodes.push(this.#node);\n      filtered = true;\n    }\n    if (!bool || complex) {\n      let refNode = this.#node.parentNode;\n      while (refNode) {\n        bool = this._matchLeaves(leaves, refNode, {\n          warn: this.#warn\n        });\n        if (bool) {\n          nodes.push(refNode);\n          filtered = true;\n        }\n        if (refNode.parentNode) {\n          refNode = refNode.parentNode;\n        } else {\n          break;\n        }\n      }\n    }\n    return [nodes, filtered];\n  }\n\n  /**\n   * find entry nodes\n   * @private\n   * @param {object} twig - twig\n   * @param {string} targetType - target type\n   * @param {object} [opt] - options\n   * @param {boolean} [opt.complex] - complex selector\n   * @param {string} [opt.dir] - find direction\n   * @returns {object} - nodes and info about it's state.\n   */\n  _findEntryNodes(twig, targetType, opt = {}) {\n    const { leaves } = twig;\n    const [leaf, ...filterLeaves] = leaves;\n    const compound = filterLeaves.length > 0;\n    const { name: leafName, type: leafType } = leaf;\n    const { complex = false, dir = DIR_PREV } = opt;\n    const precede = dir === DIR_NEXT && this.#node.nodeType === ELEMENT_NODE &&\n      this.#node !== this.#root;\n    let nodes = [];\n    let filtered = false;\n    let pending = false;\n    switch (leafType) {\n      case PS_ELEMENT_SELECTOR: {\n        if (targetType === TARGET_SELF && this.#check) {\n          const css = generateCSS(leaf);\n          this.#pseudoElement.push(css);\n          if (filterLeaves.length) {\n            [nodes, filtered] = this._matchSelf(filterLeaves, this.#check);\n          } else {\n            nodes.push(this.#node);\n            filtered = true;\n          }\n        } else {\n          matchPseudoElementSelector(leafName, leafType, {\n            warn: this.#warn\n          });\n        }\n        break;\n      }\n      case ID_SELECTOR: {\n        if (targetType === TARGET_SELF) {\n          [nodes, filtered] = this._matchSelf(leaves);\n        } else if (targetType === TARGET_LINEAL) {\n          [nodes, filtered] = this._findLineal(leaves, {\n            complex\n          });\n        } else if (targetType === TARGET_FIRST &&\n                   this.#root.nodeType !== ELEMENT_NODE) {\n          const node = this.#root.getElementById(leafName);\n          if (node) {\n            if (compound) {\n              const bool = this._matchLeaves(filterLeaves, node, {\n                warn: this.#warn\n              });\n              if (bool) {\n                nodes.push(node);\n                filtered = true;\n              }\n            } else {\n              nodes.push(node);\n              filtered = true;\n            }\n          }\n        } else {\n          nodes = this._findNodeWalker(leaves, this.#node, {\n            precede,\n            targetType,\n          });\n          if (nodes.length) {\n            filtered = true;\n          }\n        }\n        break;\n      }\n      case CLASS_SELECTOR: {\n        if (targetType === TARGET_SELF) {\n          [nodes, filtered] = this._matchSelf(leaves);\n        } else if (targetType === TARGET_LINEAL) {\n          [nodes, filtered] = this._findLineal(leaves, {\n            complex\n          });\n        } else {\n          nodes = this._findNodeWalker(leaves, this.#node, {\n            precede,\n            targetType\n          });\n          if (nodes.length) {\n            filtered = true;\n          }\n        }\n        break;\n      }\n      case TYPE_SELECTOR: {\n        if (targetType === TARGET_SELF) {\n          [nodes, filtered] = this._matchSelf(leaves);\n        } else if (targetType === TARGET_LINEAL) {\n          [nodes, filtered] = this._findLineal(leaves, {\n            complex\n          });\n        } else {\n          nodes = this._findNodeWalker(leaves, this.#node, {\n            precede,\n            targetType,\n          });\n          if (nodes.length) {\n            filtered = true;\n          }\n        }\n        break;\n      }\n      default: {\n        if (targetType !== TARGET_LINEAL &&\n            (leafName === 'host' || leafName === 'host-context')) {\n          let shadowRoot;\n          if (this.#shadow &&\n              this.#node.nodeType === DOCUMENT_FRAGMENT_NODE) {\n            shadowRoot = this._matchShadowHostPseudoClass(leaf, this.#node);\n          } else if (compound && this.#node.nodeType === ELEMENT_NODE) {\n            shadowRoot =\n              this._matchShadowHostPseudoClass(leaf, this.#node.shadowRoot);\n          }\n          if (shadowRoot) {\n            let bool;\n            if (compound) {\n              for (const item of filterLeaves) {\n                if (/^host(?:-context)?$/.test(item.name)) {\n                  const node =\n                    this._matchShadowHostPseudoClass(item, shadowRoot);\n                  bool = node === shadowRoot;\n                } else if (item.name === 'has') {\n                  bool = this._matchPseudoClassSelector(item, shadowRoot, {})\n                    .has(shadowRoot);\n                } else {\n                  bool = false;\n                }\n                if (!bool) {\n                  break;\n                }\n              }\n            } else {\n              bool = true;\n            }\n            if (bool) {\n              nodes.push(shadowRoot);\n              filtered = true;\n            }\n          }\n        } else if (targetType === TARGET_SELF) {\n          [nodes, filtered] = this._matchSelf(leaves);\n        } else if (targetType === TARGET_LINEAL) {\n          [nodes, filtered] = this._findLineal(leaves, {\n            complex\n          });\n        } else if (targetType === TARGET_FIRST) {\n          nodes = this._findNodeWalker(leaves, this.#node, {\n            precede,\n            targetType,\n          });\n          if (nodes.length) {\n            filtered = true;\n          }\n        } else {\n          pending = true;\n        }\n      }\n    }\n    return {\n      compound,\n      filtered,\n      nodes,\n      pending\n    };\n  }\n\n  /**\n   * collect nodes\n   * @private\n   * @param {string} targetType - target type\n   * @returns {Array.<Array.<object>>} - #ast and #nodes\n   */\n  _collectNodes(targetType) {\n    const ast = this.#ast.values();\n    if (targetType === TARGET_ALL || targetType === TARGET_FIRST) {\n      const pendingItems = new Set();\n      let i = 0;\n      for (const { branch } of ast) {\n        const branchLen = branch.length;\n        const complex = branchLen > 1;\n        const firstTwig = branch[0];\n        let dir;\n        let twig;\n        if (complex) {\n          const {\n            combo: firstCombo,\n            leaves: [{\n              name: firstName,\n              type: firstType\n            }]\n          } = firstTwig;\n          const lastTwig = branch[branchLen - 1];\n          const {\n            leaves: [{\n              name: lastName,\n              type: lastType\n            }]\n          } = lastTwig;\n          dir = DIR_NEXT;\n          twig = firstTwig;\n          if (this.#selector.includes(':scope') ||\n              lastType === PS_ELEMENT_SELECTOR || lastType === ID_SELECTOR) {\n            dir = DIR_PREV;\n            twig = lastTwig;\n          } else if (firstType === ID_SELECTOR) {\n            dir = DIR_NEXT;\n            twig = firstTwig;\n          } else if (firstName === '*' && firstType === TYPE_SELECTOR) {\n            dir = DIR_PREV;\n            twig = lastTwig;\n          } else if (lastName === '*' && lastType === TYPE_SELECTOR) {\n            dir = DIR_NEXT;\n            twig = firstTwig;\n          } else if (branchLen === 2) {\n            if (targetType === TARGET_FIRST) {\n              dir = DIR_PREV;\n              twig = lastTwig;\n            } else {\n              const { name: comboName } = firstCombo;\n              if (comboName === '+' || comboName === '~') {\n                dir = DIR_PREV;\n                twig = lastTwig;\n              }\n            }\n          }\n        } else {\n          dir = DIR_PREV;\n          twig = firstTwig;\n        }\n        const {\n          compound, filtered, nodes, pending\n        } = this._findEntryNodes(twig, targetType, { complex, dir });\n        if (nodes.length) {\n          this.#ast[i].find = true;\n          this.#nodes[i] = nodes;\n        } else if (pending) {\n          pendingItems.add(new Map([\n            ['index', i],\n            ['twig', twig]\n          ]));\n        }\n        this.#ast[i].dir = dir;\n        this.#ast[i].filtered = filtered || !compound;\n        i++;\n      }\n      if (pendingItems.size) {\n        let node;\n        let walker;\n        if (this.#node !== this.#root && this.#node.nodeType === ELEMENT_NODE) {\n          node = this.#node;\n          walker = this.#nodeWalker;\n        } else {\n          if (!this.#rootWalker) {\n            this.#rootWalker = this._createTreeWalker(this.#root);\n          }\n          node = this.#root;\n          walker = this.#rootWalker;\n        }\n        let nextNode = traverseNode(node, walker);\n        while (nextNode) {\n          let bool = false;\n          if (this.#node.nodeType === ELEMENT_NODE) {\n            if (nextNode === this.#node) {\n              bool = true;\n            } else {\n              bool = this.#node.contains(nextNode);\n            }\n          } else {\n            bool = true;\n          }\n          if (bool) {\n            for (const pendingItem of pendingItems) {\n              const { leaves } = pendingItem.get('twig');\n              const matched = this._matchLeaves(leaves, nextNode, {\n                warn: this.#warn\n              });\n              if (matched) {\n                const index = pendingItem.get('index');\n                this.#ast[index].filtered = true;\n                this.#ast[index].find = true;\n                this.#nodes[index].push(nextNode);\n              }\n            }\n          }\n          if (nextNode !== walker.currentNode) {\n            nextNode = traverseNode(nextNode, walker);\n          }\n          nextNode = walker.nextNode();\n        }\n      }\n    } else {\n      let i = 0;\n      for (const { branch } of ast) {\n        const twig = branch[branch.length - 1];\n        const complex = branch.length > 1;\n        const dir = DIR_PREV;\n        const {\n          compound, filtered, nodes\n        } = this._findEntryNodes(twig, targetType, { complex, dir });\n        if (nodes.length) {\n          this.#ast[i].find = true;\n          this.#nodes[i] = nodes;\n        }\n        this.#ast[i].dir = dir;\n        this.#ast[i].filtered = filtered || !compound;\n        i++;\n      }\n    }\n    return [\n      this.#ast,\n      this.#nodes\n    ];\n  }\n\n  /**\n   * get combined nodes\n   * @private\n   * @param {object} twig - twig\n   * @param {object} nodes - collection of nodes\n   * @param {string} dir - direction\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  _getCombinedNodes(twig, nodes, dir) {\n    const arr = [];\n    for (const node of nodes) {\n      const matched = this._matchCombinator(twig, node, {\n        dir,\n        warn: this.#warn\n      });\n      if (matched.size) {\n        arr.push(...matched);\n      }\n    }\n    if (arr.length) {\n      return new Set(arr);\n    }\n    return new Set();\n  }\n\n  /**\n   * match node to next direction\n   * @private\n   * @param {Array} branch - branch\n   * @param {Set.<object>} nodes - collection of Element node\n   * @param {object} opt - option\n   * @param {object} opt.combo - combo\n   * @param {number} opt.index - index\n   * @returns {?object} - matched node\n   */\n  _matchNodeNext(branch, nodes, opt) {\n    const { combo, index } = opt;\n    const { combo: nextCombo, leaves } = branch[index];\n    const twig = {\n      combo,\n      leaves\n    };\n    const nextNodes = this._getCombinedNodes(twig, nodes, DIR_NEXT);\n    if (nextNodes.size) {\n      if (index === branch.length - 1) {\n        const [nextNode] = sortNodes(nextNodes);\n        return nextNode;\n      } else {\n        return this._matchNodeNext(branch, nextNodes, {\n          combo: nextCombo,\n          index: index + 1\n        });\n      }\n    }\n    return null;\n  }\n\n  /**\n   * match node to previous direction\n   * @private\n   * @param {Array} branch - branch\n   * @param {object} node - Element node\n   * @param {object} opt - option\n   * @param {number} opt.index - index\n   * @returns {?object} - node\n   */\n  _matchNodePrev(branch, node, opt) {\n    const { index } = opt;\n    const twig = branch[index];\n    const nodes = new Set([node]);\n    const nextNodes = this._getCombinedNodes(twig, nodes, DIR_PREV);\n    if (nextNodes.size) {\n      if (index === 0) {\n        return node;\n      } else {\n        let matched;\n        for (const nextNode of nextNodes) {\n          matched = this._matchNodePrev(branch, nextNode, {\n            index: index - 1\n          });\n          if (matched) {\n            break;\n          }\n        }\n        if (matched) {\n          return node;\n        }\n      }\n    }\n    return null;\n  }\n\n  /**\n   * find matched nodes\n   * @param {string} targetType - target type\n   * @returns {Set.<object>} - collection of matched nodes\n   */\n  find(targetType) {\n    if (targetType === TARGET_ALL || targetType === TARGET_FIRST) {\n      this._prepareQuerySelectorWalker();\n    }\n    const [[...branches], collectedNodes] = this._collectNodes(targetType);\n    const l = branches.length;\n    let sort;\n    let nodes = new Set();\n    for (let i = 0; i < l; i++) {\n      const { branch, dir, find } = branches[i];\n      const branchLen = branch.length;\n      if (branchLen && find) {\n        const entryNodes = collectedNodes[i];\n        const entryNodesLen = entryNodes.length;\n        const lastIndex = branchLen - 1;\n        if (lastIndex === 0) {\n          if ((targetType === TARGET_ALL || targetType === TARGET_FIRST) &&\n              this.#node.nodeType === ELEMENT_NODE) {\n            for (let j = 0; j < entryNodesLen; j++) {\n              const node = entryNodes[j];\n              if (node !== this.#node && this.#node.contains(node)) {\n                nodes.add(node);\n                if (targetType === TARGET_FIRST) {\n                  break;\n                }\n              }\n            }\n          } else if (targetType === TARGET_ALL) {\n            if (nodes.size) {\n              const n = [...nodes];\n              nodes = new Set([...n, ...entryNodes]);\n              sort = true;\n            } else {\n              nodes = new Set(entryNodes);\n            }\n          } else {\n            const [node] = entryNodes;\n            nodes.add(node);\n          }\n        } else if (targetType === TARGET_ALL) {\n          if (dir === DIR_NEXT) {\n            const { combo: firstCombo } = branch[0];\n            let combo = firstCombo;\n            for (const node of entryNodes) {\n              let nextNodes = new Set([node]);\n              for (let j = 1; j < branchLen; j++) {\n                const { combo: nextCombo, leaves } = branch[j];\n                const twig = {\n                  combo,\n                  leaves\n                };\n                nextNodes = this._getCombinedNodes(twig, nextNodes, dir);\n                if (nextNodes.size) {\n                  if (j === lastIndex) {\n                    if (nodes.size) {\n                      const n = [...nodes];\n                      nodes = new Set([...n, ...nextNodes]);\n                      sort = true;\n                      combo = firstCombo;\n                    } else {\n                      nodes = nextNodes;\n                      combo = firstCombo;\n                    }\n                  } else {\n                    combo = nextCombo;\n                  }\n                } else {\n                  break;\n                }\n              }\n            }\n          } else {\n            for (const node of entryNodes) {\n              let nextNodes = new Set([node]);\n              for (let j = lastIndex - 1; j >= 0; j--) {\n                const twig = branch[j];\n                nextNodes = this._getCombinedNodes(twig, nextNodes, dir);\n                if (nextNodes.size) {\n                  if (j === 0) {\n                    nodes.add(node);\n                    if (branchLen > 1 && nodes.size > 1) {\n                      sort = true;\n                    }\n                  }\n                } else {\n                  break;\n                }\n              }\n            }\n          }\n        } else if (targetType === TARGET_FIRST && dir === DIR_NEXT) {\n          const { combo: entryCombo } = branch[0];\n          let matched;\n          for (const node of entryNodes) {\n            const matchedNode = this._matchNodeNext(branch, new Set([node]), {\n              combo: entryCombo,\n              index: 1\n            });\n            if (matchedNode) {\n              if (this.#node.nodeType === ELEMENT_NODE) {\n                if (matchedNode !== this.#node &&\n                    this.#node.contains(matchedNode)) {\n                  nodes.add(matchedNode);\n                  matched = true;\n                  break;\n                }\n              } else {\n                nodes.add(matchedNode);\n                matched = true;\n                break;\n              }\n            }\n          }\n          if (!matched) {\n            const { leaves: entryLeaves } = branch[0];\n            const [entryNode] = entryNodes;\n            if (this.#node.contains(entryNode)) {\n              let [refNode] = this._findNodeWalker(entryLeaves, entryNode, {\n                targetType\n              });\n              while (refNode) {\n                const matchedNode =\n                  this._matchNodeNext(branch, new Set([refNode]), {\n                    combo: entryCombo,\n                    index: 1\n                  });\n                if (matchedNode) {\n                  if (this.#node.nodeType === ELEMENT_NODE) {\n                    if (matchedNode !== this.#node &&\n                        this.#node.contains(matchedNode)) {\n                      nodes.add(matchedNode);\n                      break;\n                    }\n                  } else {\n                    nodes.add(matchedNode);\n                    break;\n                  }\n                }\n                [refNode] = this._findNodeWalker(entryLeaves, refNode, {\n                  targetType,\n                  force: true\n                });\n              }\n            } else {\n              const { combo: firstCombo } = branch[0];\n              let combo = firstCombo;\n              let nextNodes = new Set([entryNode]);\n              for (let j = 1; j < branchLen; j++) {\n                const { combo: nextCombo, leaves } = branch[j];\n                const twig = {\n                  combo,\n                  leaves\n                };\n                nextNodes = this._getCombinedNodes(twig, nextNodes, dir);\n                if (nextNodes.size) {\n                  if (j === lastIndex) {\n                    for (const nextNode of nextNodes) {\n                      if (this.#node.contains(nextNode)) {\n                        nodes.add(nextNode);\n                        break;\n                      }\n                    }\n                  } else {\n                    combo = nextCombo;\n                  }\n                } else {\n                  break;\n                }\n              }\n            }\n          }\n        } else {\n          let matched;\n          for (const node of entryNodes) {\n            const matchedNode = this._matchNodePrev(branch, node, {\n              index: lastIndex - 1\n            });\n            if (matchedNode) {\n              nodes.add(node);\n              matched = true;\n              break;\n            }\n          }\n          if (!matched && targetType === TARGET_FIRST) {\n            const { leaves: entryLeaves } = branch[lastIndex];\n            const [entryNode] = entryNodes;\n            let [refNode] = this._findNodeWalker(entryLeaves, entryNode, {\n              targetType\n            });\n            while (refNode) {\n              const matchedNode = this._matchNodePrev(branch, refNode, {\n                index: lastIndex - 1\n              });\n              if (matchedNode) {\n                nodes.add(refNode);\n                break;\n              }\n              [refNode] = this._findNodeWalker(entryLeaves, refNode, {\n                targetType,\n                force: true\n              });\n            }\n          }\n        }\n      }\n    }\n    if (this.#check) {\n      const match = !!nodes.size;\n      let pseudoElement;\n      if (this.#pseudoElement.length) {\n        pseudoElement = this.#pseudoElement.join('');\n      } else {\n        pseudoElement = null;\n      }\n      return {\n        match,\n        pseudoElement\n      };\n    }\n    if (targetType === TARGET_FIRST) {\n      nodes.delete(this.#node);\n      if (nodes.size > 1) {\n        nodes = new Set(sortNodes(nodes));\n      }\n    } else if (targetType === TARGET_ALL) {\n      nodes.delete(this.#node);\n      if (sort && nodes.size > 1) {\n        nodes = new Set(sortNodes(nodes));\n      }\n    }\n    return nodes;\n  }\n}\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACKA,IAAAA,mBAAoD;;;ACApD,oBAAmB;AACnB,qBAAwB;AACxB,sBAAsC;AACtC,8CAAgC;;;ACFzB,IAAM,gBAAgB;AACtB,IAAM,iBAAiB;AACvB,IAAM,aAAa;AACnB,IAAM,QAAQ;AACd,IAAM,cAAc;AACpB,IAAM,oBAAoB;AAC1B,IAAM,MAAM;AAEZ,IAAM,oBAAoB;AAC1B,IAAM,sBAAsB;AAG5B,IAAM,WAAW;AAEjB,IAAM,SAAS;AACf,IAAM,aAAa;AACnB,IAAM,aAAa;AACnB,IAAM,eAAe;AACrB,IAAM,gBAAgB;AACtB,IAAM,cAAc;AACpB,IAAM,gBAAgB;AAGtB,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,SAAS;AACf,IAAM,WAAW;AACjB,IAAM,MAAM;AACZ,IAAM,MAAM;AACZ,IAAM,YAAY;AAClB,IAAM,UAAU;AAGhB,IAAM,eAAe;AACrB,IAAM,YAAY;AAClB,IAAM,gBAAgB;AACtB,IAAM,yBAAyB;AAC/B,IAAM,8BAA8B;AACpC,IAAM,6BAA6B;AAInC,IAAM,WAAW;AACjB,IAAM,iBAAiB;AAMvB,IAAM,YAAY;AAClB,IAAM,YAAY;AAClB,IAAM,QAAQ;AACd,IAAM,YAAY,OAAO,SAAS;AAClC,IAAM,eAAe,iBAAiB,SAAS;AAC/C,IAAM,MACX,WAAW,KAAK,iBAAiB,KAAK,kBAAkB,KAAK;AAExD,IAAM,OACX,qDAAqD,GAAG;AAEnD,IAAM,WAAW;AACjB,IAAM,qBAAqB;AAE3B,IAAM,eAAe;AACrB,IAAM,WAAW;AACjB,IAAM,aAAa;AACnB,IAAM,WAAW,MAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ;AAC9D,IAAM,qBACX,MAAM,QAAQ,OAAO,QAAQ,QAAQ,kBAAkB;AAClD,IAAM,QAAQ;AACd,IAAM,UAAU,GAAG,QAAQ,MAAM,KAAK,GAAG,QAAQ;AACjD,IAAM,UAAU;AAChB,IAAM,UAAU;AAChB,IAAM,iBACX,aAAa,QAAQ,eAAe,QAAQ;AACvC,IAAM,iBACX,aAAa,OAAO,eAAe,OAAO;AACrC,IAAM,aACX,MAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,IAAI,cAAc;AAC1D,IAAM,aACX,MAAM,QAAQ,OAAO,QAAQ,QAAQ,QAAQ,IAAI,cAAc;AAC1D,IAAM,aACX,MAAM,UAAU,OAAO,UAAU,QAAQ,QAAQ;AAC5C,IAAM,YAAY,GAAG,UAAU,MAAM,KAAK,GAAG,UAAU;AACvD,IAAM,gBACX,oBAAoB,SAAS,eAAe,SAAS;AAChD,IAAM,iBACX,oBAAoB,UAAU,eAAe,UAAU;AAClD,IAAM,eAAe,oBAAoB,kBAAkB;AAG3D,IAAM,iBACX,OAAO,OAAO,CAAC,UAAU,SAAS,UAAU,UAAU,CAAC;AAClD,IAAM,mBAAmB,OAAO,OAAO,CAAC,UAAU,SAAS,QAAQ,CAAC;AACpE,IAAM,iBACX,OAAO,OAAO,CAAC,QAAQ,kBAAkB,SAAS,QAAQ,MAAM,CAAC;AAC5D,IAAM,iBACX,OAAO,OAAO,CAAC,SAAS,YAAY,UAAU,OAAO,QAAQ,KAAK,CAAC;AAC9D,IAAM,iBACX,OAAO,OAAO,CAAC,GAAG,gBAAgB,GAAG,gBAAgB,QAAQ,CAAC;AACzD,IAAM,gBAAgB,OAAO,OAAO;AAAA,EACzC;AAAA,EAAY;AAAA,EAAS;AAAA,EAAQ;AAAA,EAAS;AAAA,EAAU;AAAA,EAAS;AAAA,EAAS;AACpE,CAAC;AACM,IAAM,cAAc,OAAO,OAAO,CAAC,OAAO,MAAM,OAAO,OAAO,CAAC;AAC/D,IAAM,eAAe,OAAO,OAAO;AAAA,EACxC;AAAA,EAAO;AAAA,EAAY;AAAA,EAAY;AAAA,EAAW;AAAA,EAAM;AAAA,EAAU;AAAA,EAAS;AAAA,EACnE;AAAA,EAAW;AAAA,EAAc;AAAA,EAAS;AAAA,EAAS;AAAA,EAAU;AACvD,CAAC;AACM,IAAM,eAAe,OAAO,OAAO;AAAA,EACxC;AAAA,EAAW;AAAA,EAAY;AAAA,EAAS;AAAA,EAAW;AAAA,EAAY;AAAA,EACvD;AAAA,EAAW;AAAA,EAAiB;AAAA,EAAa;AAAA,EAAc;AAAA,EAAQ;AAAA,EAC/D;AACF,CAAC;AACM,IAAM,kBAAkB,OAAO,OAAO,CAAC,QAAQ,cAAc,CAAC;;;ADxGrE,IAAM,oBACJ,IAAI,OAAO,OAAO,YAAY,IAAI,IAAI,IAAI,aAAa,GAAG;AAC5D,IAAM,qBACJ,IAAI,OAAO,OAAO,YAAY,IAAI,IAAI,IAAI,cAAc,GAAG;AAC7D,IAAM,yBACJ,IAAI,OAAO,OAAO,YAAY,IAAI,IAAI,IAAI,cAAc,IAAI,YAAY,GAAG;AAC7E,IAAM,mBAAmB,IAAI,OAAO,IAAI,YAAY,GAAG;AACvD,IAAM,iBAAiB,IAAI,OAAO,OAAO,YAAY,IAAI,IAAI,GAAG;AAOzD,IAAM,UAAU,OACrB,OAAO,UAAU,SAAS,KAAK,CAAC,EAAE,MAAM,WAAW,OAAO;AA8BrD,IAAM,iBAAiB,UAAQ;AACpC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI;AACJ,MAAI;AACJ,MAAI;AACJ,UAAQ,KAAK,UAAU;AAAA,IACrB,KAAK,eAAe;AAClB,iBAAW;AACX,aAAO;AACP;AAAA,IACF;AAAA,IACA,KAAK,wBAAwB;AAC3B,YAAM,EAAE,MAAM,MAAM,cAAc,IAAI;AACtC,iBAAW;AACX,aAAO;AACP,eAAS,SAAS,SAAS,WAAW,SAAS;AAC/C;AAAA,IACF;AAAA,IACA,KAAK,cAAc;AACjB,iBAAW,KAAK;AAChB,UAAI,UAAU;AACd,aAAO,SAAS;AACd,cAAM,EAAE,MAAM,MAAM,UAAU,WAAW,IAAI;AAC7C,YAAI,aAAa,wBAAwB;AACvC,mBAAS,SAAS,SAAS,WAAW,SAAS;AAC/C;AAAA,QACF,WAAW,YAAY;AACrB,oBAAU;AAAA,QACZ,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,aAAO;AACP;AAAA,IACF;AAAA,IACA,SAAU;AACR,YAAM,IAAI,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AAAA,IACxD;AAAA,EACF;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,IACA,CAAC,CAAC;AAAA,EACJ;AACF;AASO,IAAM,eAAe,CAAC,MAAM,QAAQ,QAAQ,UAAU;AAC3D,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,QAAQ;AACX,WAAO;AAAA,EACT;AACA,MAAI,UAAU,OAAO;AACrB,MAAI,YAAY,MAAM;AACpB,WAAO;AAAA,EACT,WAAW,SAAS,QAAQ,SAAS,IAAI,GAAG;AAC1C,cAAU,OAAO,SAAS;AAC1B,WAAO,SAAS;AACd,UAAI,YAAY,MAAM;AACpB;AAAA,MACF;AACA,gBAAU,OAAO,SAAS;AAAA,IAC5B;AACA,WAAO;AAAA,EACT,OAAO;AACL,QAAI,YAAY,OAAO,MAAM;AAC3B,UAAI;AACJ,aAAO,SAAS;AACd,YAAI,YAAY,MAAM;AACpB,iBAAO;AACP;AAAA,QACF,WAAW,YAAY,OAAO,QAAQ,QAAQ,SAAS,IAAI,GAAG;AAC5D;AAAA,QACF;AACA,kBAAU,OAAO,WAAW;AAAA,MAC9B;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AAAA,IACF;AACA,QAAI,KAAK,aAAa,cAAc;AAClC,UAAI;AACJ,aAAO,SAAS;AACd,YAAI,YAAY,MAAM;AACpB,iBAAO;AACP;AAAA,QACF;AACA,kBAAU,OAAO,SAAS;AAAA,MAC5B;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,kBAAkB,CAAC,MAAM,MAAM,CAAC,MAAM;AACjD,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,cAAc,IAAI;AACrC,QAAM,EAAE,eAAe,IAAI;AAC3B,QAAM,SAAS,cAAc;AAC7B,MAAI;AACJ,QAAM,OAAO,KAAK,aAAa,IAAI;AACnC,MAAI,MAAM;AACR,yBACE,wCAAAC,SAAoB,IAAI,KAAK,OAAO,eAAe,IAAI,IAAI;AAAA,EAC/D,OAAO;AACL,yBACE,wCAAAA,SAAoB,SAAS,KAAK,OAAO,eAAe,IAAI,SAAS;AAAA,EACzE;AACA,MAAI,gBAAgB;AAClB,QAAI,gBAAgB;AAClB,aAAO,CAAC,CAAC,eAAe;AAAA,IAC1B;AACA,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOO,IAAM,wBAAwB,UAAQ;AAC3C,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,OAAO,KAAK,kBAAkB,YAAY;AAC5C,WAAO;AAAA,EACT;AACA,QAAM,QAAQ,KAAK,cAAc;AACjC,MAAI,MAAM,QAAQ;AAChB,QAAI;AACJ,eAAW,QAAQ,OAAO;AACxB,aAAO,KAAK,YAAY,KAAK;AAC7B,UAAI,MAAM;AACR;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACA,SAAO,KAAK,YAAY,KAAK;AAC/B;AAQO,IAAM,oBAAoB,UAAQ;AACvC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,KAAK,SAAS,WAAW,WAAW,IAAI;AAChD,QAAM,EAAE,mBAAmB,QAAI,eAAAC,SAAY;AAC3C,MAAI,YAAY,SAAS,YAAY,OAAO;AAC1C,WAAO;AAAA,EACT,WAAW,YAAY,QAAQ;AAC7B,QAAI;AACJ,YAAQ,WAAW;AAAA,MACjB,KAAK,SAAS;AACZ,cAAM,YAAY,CAAC,GAAG,kBAAkB,GAAG,gBAAgB,QAAQ;AACnE,YAAI,CAAC,KAAK,QAAQ,UAAU,SAAS,KAAK,IAAI,GAAG;AAC/C,iBAAO,KAAK;AAAA,QACd,OAAO;AACL,gBAAM,UAAU;AAAA,YACd;AAAA,YAAY;AAAA,YAAS;AAAA,YAAQ;AAAA,YAAS;AAAA,YAAU;AAAA,YAAS;AAAA,YACzD;AAAA,UACF;AACA,cAAI,QAAQ,SAAS,KAAK,IAAI,GAAG;AAC/B,mBAAO;AAAA,UACT;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,QAAQ;AACX,eAAO,sBAAsB,IAAI;AACjC;AAAA,MACF;AAAA,MACA,KAAK,YAAY;AACf,eAAO,KAAK;AACZ;AAAA,MACF;AAAA,MACA,SAAS;AACP,cAAM,QAAQ,CAAC,EAAE,MAAM,KAAK,KAAK,UAAU;AAC3C,mBAAW,QAAQ,OAAO;AACxB,gBAAM;AAAA,YACJ,KAAK;AAAA,YAAS,WAAW;AAAA,YAAe,UAAU;AAAA,YAClD,aAAa;AAAA,UACf,IAAI;AACJ,cAAI,iBAAiB,WAAW;AAC9B,mBAAO,gBAAgB,KAAK;AAAA,UAC9B,WAAW,iBAAiB,cAAc;AACxC,kBAAM,OAAO,CAAC,OAAO,UAAU,SAAS,UAAU;AAClD,gBAAI,CAAC,KAAK,SAAS,aAAa,MAC3B,CAAC,WAAY,YAAY,SAAS,YAAY,QAAS;AAC1D,kBAAI,kBAAkB,QAAQ;AAC5B,uBAAO,sBAAsB,IAAI;AAAA,cACnC,OAAO;AACL,uBAAO,gBAAgB,KAAK;AAAA,cAC9B;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM;AACR;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM;AACR,YAAM,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,mBAAmB,IAAI;AAC3D,UAAI,QAAQ,MAAM,GAAG;AACnB,eAAO;AAAA,MACT;AAAA,IACF,WAAW,YAAY;AACrB,YAAM,EAAE,UAAU,eAAe,IAAI;AACrC,UAAI,mBAAmB,cAAc;AACnC,eAAO,kBAAkB,UAAU;AAAA,MACrC;AAAA,IACF;AAAA,EACF,WAAW,cAAc,WAAW,KAAK,SAAS,OAAO;AACvD,WAAO;AAAA,EACT,WAAW,cAAc,OAAO;AAC9B,UAAM,OAAO,KAAK,YAAY,KAAK;AACnC,QAAI,MAAM;AACR,YAAM,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,mBAAmB,IAAI;AAC3D,UAAI,QAAQ,MAAM,GAAG;AACnB,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF,WAAW,YAAY;AACrB,QAAI,cAAc,QAAQ;AACxB,YAAM,OAAO,sBAAsB,IAAI;AACvC,UAAI,MAAM;AACR,cAAM,EAAE,YAAY,CAAC,EAAE,MAAM,CAAC,EAAE,IAAI,mBAAmB,IAAI;AAC3D,YAAI,QAAQ,MAAM,GAAG;AACnB,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,EAAE,UAAU,eAAe,IAAI;AACrC,QAAI,mBAAmB,cAAc;AACnC,aAAO,kBAAkB,UAAU;AAAA,IACrC;AAAA,EACF;AACA,SAAO;AACT;AAQO,IAAM,oBAAoB,UAAQ;AACvC,MAAI,CAAC,MAAM,UAAU;AACnB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,KAAK,aAAa,cAAc;AAClC,WAAO;AAAA,EACT;AACA,MAAI,OAAO,KAAK,sBAAsB,WAAW;AAC/C,WAAO,KAAK;AAAA,EACd,WAAW,KAAK,cAAc,eAAe,MAAM;AACjD,WAAO;AAAA,EACT,OAAO;AACL,QAAI;AACJ,QAAI,KAAK,aAAa,iBAAiB,GAAG;AACxC,aAAO,KAAK,aAAa,iBAAiB;AAAA,IAC5C,OAAO;AACL,aAAO;AAAA,IACT;AACA,YAAQ,MAAM;AAAA,MACZ,KAAK;AAAA,MACL,KAAK,QAAQ;AACX,eAAO;AAAA,MACT;AAAA,MACA,KAAK,kBAAkB;AAIrB,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,YAAI,MAAM,YAAY,aAAa,cAAc;AAC/C,iBAAO,kBAAkB,KAAK,UAAU;AAAA,QAC1C;AACA,eAAO;AAAA,MACT;AAAA,IACF;AAAA,EACF;AACF;AAOO,IAAM,YAAY,UAAQ;AAC/B,MAAI,MAAM,aAAa,cAAc;AACnC,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,cAAc;AAClC,QAAM,EAAE,SAAS,WAAW,IAAI,OAAO,iBAAiB,IAAI;AAC5D,MAAI,YAAY,UAAU,eAAe,WAAW;AAClD,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAOO,IAAM,iBAAiB,UAAQ;AACpC,MAAI,MAAM,aAAa,cAAc;AACnC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,KAAK,IAAI;AAC5B,UAAQ,WAAW;AAAA,IACjB,KAAK,SAAS;AACZ,UAAI,CAAC,QAAQ,eAAe,SAAS,IAAI,GAAG;AAC1C,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,YAAY;AACf,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,aAAO,kBAAkB,IAAI;AAAA,IAC/B;AAAA,EACF;AACF;AAOO,IAAM,kBAAkB,UAAQ;AACrC,MAAI,MAAM,aAAa,cAAc;AACnC,WAAO;AAAA,EACT;AACA,MAAI,CAAC,KAAK,aAAa;AACrB,WAAO;AAAA,EACT;AACA,QAAM,SAAS,KAAK,cAAc;AAClC,MAAI,gBAAgB,OAAO,aAAa;AACtC,QAAI,OAAO,UAAU,SAAS,KAAK,aAAa,UAAU,CAAC,CAAC,GAAG;AAC7D,aAAO;AAAA,IACT;AACA,QAAI,kBAAkB,IAAI,GAAG;AAC3B,aAAO;AAAA,IACT;AACA,UAAM,EAAE,WAAW,WAAW,IAAI;AAClC,YAAQ,WAAW;AAAA,MACjB,KAAK,KAAK;AACR,YAAI,KAAK,QAAQ,KAAK,aAAa,MAAM,GAAG;AAC1C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,UAAU;AACb,eAAO;AAAA,MACT;AAAA,MACA,KAAK,SAAS;AACZ,YAAI,KAAK,YAAY,KAAK,aAAa,UAAU,KAC7C,KAAK,UAAU,KAAK,aAAa,QAAQ,GAAG;AAC9C,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,KAAK,WAAW;AACd,YAAI,WAAW,cAAc,WAAW;AACtC,cAAI,QAAQ,WAAW;AACvB,cAAI,OAAO;AACX,iBAAO,OAAO;AACZ,gBAAI,MAAM,cAAc,WAAW;AACjC,qBAAO,UAAU;AACjB;AAAA,YACF;AACA,oBAAQ,MAAM;AAAA,UAChB;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,MACA,SAAS;AACP,cAAM,OAAO,CAAC,UAAU,UAAU,UAAU;AAC5C,YAAI,KAAK,SAAS,SAAS,KACvB,EAAE,KAAK,YAAY,KAAK,aAAa,UAAU,IAAI;AACrD,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF,WAAW,gBAAgB,OAAO,YAAY;AAC5C,QAAI,OAAO,UAAU,SAAS,KAAK,eAAe,MAAM,UAAU,CAAC,CAAC,GAAG;AACrE,YAAM,OAAO;AAAA,QACX;AAAA,QAAY;AAAA,QAAQ;AAAA,QAAQ;AAAA,QAAkB;AAAA,QAAU;AAAA,QACxD;AAAA,QAAY;AAAA,QAAW;AAAA,QAAkB;AAAA,QAAU;AAAA,QAAS;AAAA,QAC5D;AAAA,MACF;AACA,YAAM,KAAK;AACX,UAAI;AACJ,UAAI,UAAU;AACd,aAAO,QAAQ,iBAAiB,IAAI;AAClC,eAAO,KAAK,SAAS,QAAQ,SAAS;AACtC,YAAI,MAAM;AACR;AAAA,QACF;AACA,YAAI,SAAS,YAAY,iBAAiB,IAAI;AAC5C,oBAAU,QAAQ;AAAA,QACpB,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM;AACR,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AACA,QAAI,KAAK,cAAc,QAClB,KAAK,QAAQ,KAAK,eAAe,MAAM,MAAM,IAAI;AACpD,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAmDO,IAAM,kBAAkB,CAAC,IAAI,SAAS;AAC3C,MAAI,OAAO,OAAO,UAAU;AAC1B,UAAM,IAAI,UAAU,mBAAmB,QAAQ,EAAE,CAAC,EAAE;AAAA,EACtD,WAAW,CAAC,MAAM,UAAU;AAC1B,UAAM,IAAI,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AAAA,EACxD;AACA,MAAI,CAAC,MAAM,KAAK,aAAa,cAAc;AACzC,WAAO;AAAA,EACT;AACA,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI;AACJ,aAAW,QAAQ,YAAY;AAC7B,UAAM,EAAE,MAAM,cAAc,QAAQ,MAAM,IAAI;AAC9C,QAAI,SAAS,SAAS,EAAE,IAAI;AAC1B,YAAM;AAAA,IACR,WAAW,WAAW,IAAI;AACxB,YAAM;AAAA,IACR;AACA,QAAI,KAAK;AACP;AAAA,IACF;AAAA,EACF;AACA,SAAO,OAAO;AAChB;AAQO,IAAM,sBAAsB,CAAC,KAAK,IAAI,OAAO,CAAC,MAAM;AACzD,MAAI,CAAC,MAAM,OAAO,OAAO,YAAY,MAAM,aAAa,cAAc;AACpE,WAAO;AAAA,EACT;AACA,MAAI,KAAK,mBAAmB,EAAE,GAAG;AAC/B,WAAO;AAAA,EACT;AACA,QAAM,OAAO,KAAK,cAAc;AAChC,MAAI,SAAS;AACb,MAAI;AACJ,SAAO,QAAQ;AACb,UAAM,gBAAgB,IAAI,MAAM;AAChC,QAAI,OAAO,WAAW,MAAM;AAC1B;AAAA,IACF;AACA,aAAS,OAAO;AAAA,EAClB;AACA,SAAO,CAAC,CAAC;AACX;AAQO,IAAM,cAAc,CAAC,OAAO,UAAU;AAC3C,MAAI,CAAC,OAAO,UAAU;AACpB,UAAM,IAAI,UAAU,mBAAmB,QAAQ,KAAK,CAAC,EAAE;AAAA,EACzD,WAAW,CAAC,OAAO,UAAU;AAC3B,UAAM,IAAI,UAAU,mBAAmB,QAAQ,KAAK,CAAC,EAAE;AAAA,EACzD;AACA,MAAI,MAAM,aAAa,gBAAgB,MAAM,aAAa,cAAc;AACtE,WAAO;AAAA,EACT;AACA,QAAM,SAAS,MAAM,wBAAwB,KAAK;AAClD,QAAM,MAAM,SAAS,+BACT,SAAS;AACrB,SAAO,CAAC,CAAC;AACX;AAOO,IAAM,YAAY,CAAC,QAAQ,CAAC,MAAM;AACvC,QAAM,MAAM,CAAC,GAAG,KAAK;AACrB,MAAI,IAAI,SAAS,GAAG;AAClB,QAAI,KAAK,CAAC,GAAG,MAAM;AACjB,UAAI;AACJ,UAAI,YAAY,GAAG,CAAC,GAAG;AACrB,cAAM;AAAA,MACR,OAAO;AACL,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAyJO,IAAM,aAAa,CAAC,QAAQ,aAAa;AAC9C,MAAI,CAAC,QAAQ,cAAc;AACzB,UAAM,IAAI,UAAU,4BAA4B,QAAQ,MAAM,CAAC,EAAE;AAAA,EACnE;AACA,MAAI,UAAU,aAAa,eAAe;AACxC,eAAW,OAAO;AAAA,EACpB;AACA,QAAM,SAAK,cAAAC,SAAO;AAAA,IAChB;AAAA,IACA,cAAc,OAAO;AAAA,EACvB,CAAC;AACD,KAAG,UAAU;AAAA,IACX,WAAW;AAAA,EACb,CAAC;AACD,SAAO;AACT;AAQO,IAAM,iBAAiB,CAAC,UAAU,MAAM,CAAC,MAAM;AACpD,MAAI,CAAC,YAAY,OAAO,aAAa,YAAY,iBAAiB,KAAK,QAAQ,GAAG;AAChF,WAAO;AAAA,EACT;AACA,QAAM,EAAE,SAAS,UAAU,SAAS,QAAQ,OAAO,IAAI;AAEvD,MAAI,UAAU,UAAU;AACtB,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,GAAG,GAAG;AAC1B,UAAM,QAAQ,SAAS,YAAY,GAAG;AACtC,UAAM,MAAM,SAAS,UAAU,KAAK;AACpC,QAAI,IAAI,QAAQ,GAAG,IAAI,GAAG;AACxB,aAAO;AAAA,IACT;AAAA,EACF;AAEA,MAAI,SAAS,SAAS,GAAG,GAAG;AAC1B,WAAO;AAAA,EACT;AAKA,MAAI,wKAAwK,KAAK,QAAQ,GAAG;AAC1L,WAAO;AAAA,EACT;AAEA,MAAI,SAAS,SAAS,GAAG,GAAG;AAC1B,QAAI,SAAS;AACX,aAAO;AAAA,IACT,YAAY,WAAW,eAAe,WAAW,kBACtC,SAAS,KAAK,QAAQ,GAAG;AAClC,UAAI,CAAC,WAAW,uBAAuB,KAAK,QAAQ,GAAG;AACrD,eAAO;AAAA,MACT;AACA,aAAO,iBAAiB,KAAK,QAAQ;AAAA,IACvC,WAAW,gBAAgB,KAAK,QAAQ,GAAG;AACzC,UAAI,SAAS;AACX,eAAO,CAAC,kBAAkB,KAAK,QAAQ;AAAA,MACzC,OAAO;AACL,eAAO,CAAC,mBAAmB,KAAK,QAAQ;AAAA,MAC1C;AAAA,IACF,OAAO;AACL,aAAO,CAAC,eAAe,KAAK,QAAQ;AAAA,IACtC;AAAA,EACF;AACA,SAAO;AACT;;;AD9gBA,IAAAC,mBAAyD;AA7VzD,IAAM,oBAAoB;AAC1B,IAAM,wBAAwB;AAC9B,IAAM,SAAS;AAOR,IAAM,mBAAmB,CAAC,WAAW,OAAO;AACjD,MAAI,OAAO,aAAa,YAAY,SAAS,QAAQ,MAAM,CAAC,KAAK,GAAG;AAClE,UAAM,MAAM,SAAS,MAAM,IAAI;AAC/B,UAAM,IAAI,IAAI;AACd,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAI,OAAO,IAAI,CAAC;AAChB,UAAI,SAAS,MAAM,MAAM,IAAI,GAAG;AAC9B,eAAO;AAAA,MACT,OAAO;AACL,cAAM,YAAY,sBAAsB,KAAK,IAAI;AACjD,YAAI,WAAW;AACb,gBAAM,CAAC,EAAE,GAAG,IAAI;AAChB,cAAI;AACJ,cAAI;AACF,kBAAM,MAAM,SAAS,QAAQ,GAAG;AAChC,kBAAM,OAAO,SAAS,QAAQ,GAAG;AACjC,kBAAM,OAAO,SAAS,KAAK,GAAG;AAC9B,gBAAI,SAAS,KAAM,QAAQ,OAAO,QAAQ,MAAO;AAC/C,oBAAM;AAAA,YACR,OAAO;AACL,oBAAM,OAAO,cAAc,IAAI;AAAA,YACjC;AAAA,UACF,SAAS,GAAG;AACV,kBAAM;AAAA,UACR;AACA,cAAI,UAAU;AACd,cAAI,KAAK,SAAS,IAAI,QAAQ;AAC5B,sBAAU,KAAK,UAAU,IAAI,MAAM;AAAA,UACrC;AACA,iBAAO,GAAG,GAAG,GAAG,OAAO;AAAA,QAEzB,WAAW,YAAY,KAAK,IAAI,GAAG;AACjC,iBAAO,OAAO;AAAA,QAChB;AAAA,MACF;AACA,UAAI,CAAC,IAAI;AAAA,IACX;AACA,eAAW,IAAI,KAAK,EAAE;AAAA,EACxB;AACA,SAAO;AACT;AAQO,IAAM,aAAa,IAAI,SAAS;AACrC,MAAI,CAAC,KAAK,QAAQ;AAChB,UAAM,IAAI,UAAU,0CAA0C;AAAA,EAChE;AACA,MAAI,CAAC,QAAQ,IAAI;AACjB,MAAI,OAAO,aAAa,UAAU;AAChC,QAAI,QAAQ;AACZ,WAAO,SAAS,GAAG;AAEjB,cAAQ,SAAS,QAAQ,KAAK,KAAK;AACnC,UAAI,QAAQ,GAAG;AACb;AAAA,MACF;AACA,YAAM,UAAU,SAAS,UAAU,GAAG,QAAQ,CAAC;AAC/C,UAAI,WAAW,SAAS,UAAU,QAAQ,CAAC;AAC3C,YAAM,YAAY,SAAS,YAAY,CAAC;AACxC,UAAI,YAAY,UAAU;AACxB,cAAM,MAAM,KAAK,UAAU,SAAS,GAAG,CAAC;AACxC,YAAI,SAAS,WAAW,KAAK;AAC3B,qBAAW;AAAA,QACb,OAAO;AACL,qBAAW,GAAG,GAAG,GAAG,SAAS,UAAU,GAAG,CAAC;AAAA,QAC7C;AAAA,MACF;AACA,iBAAW,GAAG,OAAO,GAAG,QAAQ;AAChC;AAAA,IACF;AACA,eAAW,SAAS,QAAQ,aAAa,IAAI,EAC1C,QAAQ,0BAA0B,MAAM;AAAA,EAC7C,WAAW,aAAa,UAAa,aAAa,MAAM;AACtD,eAAW,QAAQ,QAAQ,EAAE,YAAY;AAAA,EAC3C,WAAW,MAAM,QAAQ,QAAQ,GAAG;AAClC,eAAW,SAAS,KAAK,GAAG;AAAA,EAC9B,WAAW,OAAO,OAAO,UAAU,UAAU,GAAG;AAC9C,eAAW,SAAS,SAAS;AAAA,EAC/B,OAAO;AACL,UAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,EACnE;AACA,SAAO,SAAS,QAAQ,SAAS,QAAQ;AAC3C;AAOO,IAAM,gBAAgB,cAAY;AACvC,aAAW,WAAW,QAAQ;AAE9B,MAAI,iBAAiB,KAAK,QAAQ,GAAG;AACnC,UAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,EACnE;AACA,MAAI;AACJ,MAAI;AACF,UAAM,UAAM,wBAAM,UAAU;AAAA,MAC1B,SAAS;AAAA,MACT,qBAAqB;AAAA,IACvB,CAAC;AACD,cAAM,gCAAc,GAAG;AAAA,EACzB,SAAS,GAAG;AACV,UAAM,EAAE,QAAQ,IAAI;AACpB,QAAI,2DAA2D,KAAK,OAAO,KACvE,CAAC,SAAS,SAAS,GAAG,GAAG;AAC3B,YAAM,QAAQ,SAAS,YAAY,GAAG;AACtC,YAAM,MAAM,SAAS,UAAU,KAAK;AACpC,UAAI,IAAI,SAAS,GAAG,GAAG;AACrB,cAAM,SAAS,IAAI,MAAM,IAAI,EAAE;AAC/B,YAAI,SAAS,GAAG;AACd,gBAAM,cAAc,GAAG,QAAQ,IAAI;AAAA,QACrC,OAAO;AACL,gBAAM,cAAc,GAAG,QAAQ,GAAG;AAAA,QACpC;AAAA,MACF,OAAO;AACL,cAAM,cAAc,GAAG,QAAQ,GAAG;AAAA,MACpC;AAAA,IACF,WAAW,YAAY,mBAAmB;AAExC,UAAI,kBAAkB,KAAK,QAAQ,GAAG;AACpC,cAAM,cAAc,GAAG,SAAS,WAAW,mBAAmB,IAAI,CAAC,EAAE;AAAA,MACvE,WAAW,CAAC,SAAS,SAAS,GAAG,GAAG;AAClC,cAAM,cAAc,GAAG,QAAQ,GAAG;AAAA,MACpC,OAAO;AACL,cAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,MACnE;AAAA,IACF,OAAO;AACL,YAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,IACnE;AAAA,EACF;AACA,SAAO;AACT;AAOO,IAAM,UAAU,CAAC,MAAM,CAAC,MAAM;AACnC,QAAM,WAAW,oBAAI,IAAI;AACzB,QAAM,OAAO,oBAAI,IAAI;AACrB,QAAM,MAAM;AAAA,IACV,OAAO,UAAQ;AACb,cAAQ,KAAK,MAAM;AAAA,QACjB,KAAK,gBAAgB;AACnB,cAAI,QAAQ,KAAK,KAAK,IAAI,GAAG;AAC3B,kBAAM,IAAI;AAAA,cAAa,qBAAqB,KAAK,IAAI;AAAA,cACnD;AAAA,YAAU;AAAA,UACd;AACA;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,cAAI,QAAQ,KAAK,KAAK,IAAI,GAAG;AAC3B,kBAAM,IAAI;AAAA,cAAa,qBAAqB,KAAK,IAAI;AAAA,cACnD;AAAA,YAAU;AAAA,UACd;AACA;AAAA,QACF;AAAA,QACA,KAAK,mBAAmB;AACtB,cAAI,YAAY,SAAS,KAAK,IAAI,GAAG;AACnC,iBAAK,IAAI,qBAAqB,IAAI;AAClC,iBAAK,IAAI,wBAAwB,IAAI;AACrC,gBAAI,KAAK,SAAS,OAAO;AACvB,mBAAK,IAAI,oBAAoB,IAAI;AAAA,YACnC;AAAA,UACF,WAAW,aAAa,SAAS,KAAK,IAAI,GAAG;AAC3C,iBAAK,IAAI,uBAAuB,IAAI;AAAA,UACtC,WAAW,gBAAgB,SAAS,KAAK,IAAI,KAClC,MAAM,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,QAAQ;AAC/D,iBAAK,IAAI,qBAAqB,IAAI;AAAA,UACpC;AACA;AAAA,QACF;AAAA,QACA,KAAK,qBAAqB;AACxB,cAAI,sBAAsB,KAAK,KAAK,IAAI,GAAG;AACzC,iBAAK,IAAI,qBAAqB,IAAI;AAAA,UACpC;AACA;AAAA,QACF;AAAA,QACA,KAAK,KAAK;AACR,cAAI,KAAK,UAAU;AACjB,iBAAK,IAAI,qBAAqB,IAAI;AAClC,iBAAK,IAAI,yBAAyB,IAAI;AAAA,UACxC;AACA;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,mBAAS,IAAI,KAAK,QAAQ;AAC1B;AAAA,QACF;AAAA,QACA;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,6BAAK,KAAK,GAAG;AACb,MAAI,KAAK,IAAI,mBAAmB,GAAG;AACjC,kCAAQ,KAAK,CAAC,MAAM,MAAM,SAAS;AACjC,UAAI,MAAM;AACR,YAAI,KAAK,SAAS,qBACd,YAAY,SAAS,KAAK,IAAI,GAAG;AACnC,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,MAAM,KAAK,IAAI;AACvB,mBAAO,SAAS,qBAAqB,YAAY,SAAS,IAAI;AAAA,UAChE,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AAEnC,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAElD,yBAAW,EAAE,UAAU,mBAAmB,KAAK,eAAe;AAC5D,oBAAI,SAAS,IAAI,kBAAkB,GAAG;AACpC,2BAAS,OAAO,kBAAkB;AAAA,gBACpC;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,KAAK,SAAS,qBACd,gBAAgB,SAAS,KAAK,IAAI,KAClC,MAAM,QAAQ,KAAK,QAAQ,KAAK,KAAK,SAAS,QAAQ;AAC/D,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,UAAU,MAAM,KAAK,IAAI;AACjC,kBAAM,MACJ,SAAS,qBAAqB,gBAAgB,SAAS,IAAI,KAC3D,MAAM,QAAQ,QAAQ,KAAK,SAAS;AACtC,mBAAO;AAAA,UACT,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AAEnC,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAClD,kBAAI,SAAS,IAAI,aAAa,GAAG;AAC/B,yBAAS,OAAO,aAAa;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,KAAK,SAAS,uBACd,sBAAsB,KAAK,KAAK,IAAI,GAAG;AAChD,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,MAAM,KAAK,IAAI;AACvB,kBAAM,MACJ,SAAS,uBAAuB,sBAAsB,KAAK,IAAI;AACjE,mBAAO;AAAA,UACT,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AAEnC,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAClD,kBAAI,SAAS,IAAI,aAAa,GAAG;AAC/B,yBAAS,OAAO,aAAa;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,KAAK,SAAS,OAAO,KAAK,UAAU;AAC7C,gBAAM,WAAW,KAAK,OAAO,OAAK;AAChC,kBAAM,EAAE,UAAU,KAAK,IAAI;AAC3B,kBAAM,MAAM,SAAS,OAAO;AAC5B,mBAAO;AAAA,UACT,CAAC;AACD,qBAAW,EAAE,SAAS,KAAK,UAAU;AACnC,kBAAM,EAAE,SAAS,IAAI;AAErB,uBAAW,EAAE,UAAU,cAAc,KAAK,UAAU;AAClD,kBAAI,SAAS,IAAI,aAAa,GAAG;AAC/B,yBAAS,OAAO,aAAa;AAAA,cAC/B;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,CAAC;AAAA,EACH;AACA,SAAO;AAAA,IACL,UAAU,CAAC,GAAG,QAAQ;AAAA,IACtB,MAAM,OAAO,YAAY,IAAI;AAAA,EAC/B;AACF;AAOO,IAAM,UAAU,UAAQ;AAC7B,QAAM,MAAM,CAAC,GAAG,IAAI;AACpB,MAAI,IAAI,SAAS,GAAG;AAClB,UAAM,QAAQ,oBAAI,IAAI;AAAA,MACpB,CAAC,qBAAqB,MAAM;AAAA,MAC5B,CAAC,aAAa,MAAM;AAAA,MACpB,CAAC,gBAAgB,MAAM;AAAA,MACvB,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,eAAe,MAAM;AAAA,MACtB,CAAC,mBAAmB,MAAM;AAAA,IAC5B,CAAC;AACD,QAAI,KAAK,CAAC,GAAG,MAAM;AACjB,YAAM,EAAE,MAAM,MAAM,IAAI;AACxB,YAAM,EAAE,MAAM,MAAM,IAAI;AACxB,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,YAAM,OAAO,MAAM,IAAI,KAAK;AAC5B,UAAI;AACJ,UAAI,SAAS,MAAM;AACjB,cAAM;AAAA,MACR,WAAW,OAAO,MAAM;AACtB,cAAM;AAAA,MACR,OAAO;AACL,cAAM;AAAA,MACR;AACA,aAAO;AAAA,IACT,CAAC;AAAA,EACH;AACA,SAAO;AACT;AAOO,IAAM,eAAe,cAAY;AACtC,MAAI;AACJ,MAAI;AACJ,MAAI,YAAY,OAAO,aAAa,UAAU;AAC5C,QAAI,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC9B,OAAC,QAAQ,SAAS,IAAI,SAAS,MAAM,GAAG;AAAA,IAC1C,OAAO;AACL,eAAS;AACT,kBAAY;AAAA,IACd;AAAA,EACF,OAAO;AACL,UAAM,IAAI,aAAa,oBAAoB,QAAQ,IAAI,UAAU;AAAA,EACnE;AACA,SAAO;AAAA,IACL;AAAA,IACA;AAAA,EACF;AACF;;;AGjVO,IAAM,6BAA6B,CAAC,SAAS,SAAS,MAAM,CAAC,MAAM;AACxE,QAAM,EAAE,SAAS,KAAK,IAAI;AAC1B,MAAI,YAAY,qBAAqB;AACnC,YAAQ,SAAS;AAAA,MACf,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK,eAAe;AAClB,YAAI,MAAM;AACR,gBAAM,IAAI;AAAA,YAAa,gCAAgC,OAAO;AAAA,YAC5D;AAAA,UAAiB;AAAA,QACrB;AACA;AAAA,MACF;AAAA,MACA,KAAK;AAAA,MACL,KAAK,WAAW;AACd,YAAI,MAAM;AACR,gBAAM,IAAI;AAAA,YAAa,gCAAgC,OAAO;AAAA,YAC5D;AAAA,UAAiB;AAAA,QACrB;AACA;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,QAAQ,WAAW,UAAU,GAAG;AAClC,cAAI,MAAM;AACR,kBAAM,IAAI;AAAA,cAAa,gCAAgC,OAAO;AAAA,cAC5D;AAAA,YAAiB;AAAA,UACrB;AAAA,QACF,WAAW,CAAC,SAAS;AACnB,gBAAM,IAAI;AAAA,YAAa,4BAA4B,OAAO;AAAA,YACxD;AAAA,UAAU;AAAA,QACd;AAAA,MACF;AAAA,IACF;AAAA,EACF,OAAO;AACL,UAAM,IAAI,UAAU,uBAAuB,QAAQ,OAAO,CAAC,EAAE;AAAA,EAC/D;AACF;AAQO,IAAM,4BAA4B,CAAC,KAAK,SAAS;AACtD,QAAM,EAAE,KAAK,IAAI;AACjB,MAAI,CAAC,MAAM;AACT,QAAI;AACJ,QAAI,SAAS,IAAI;AACf,aAAO;AAAA,IACT,OAAO;AACL,aAAO,QAAQ,IAAI;AAAA,IACrB;AACA,UAAM,IAAI,UAAU,uBAAuB,IAAI,EAAE;AAAA,EACnD;AACA,QAAM,MAAM,kBAAkB,IAAI;AAClC,SAAO,SAAS;AAClB;AASO,IAAM,2BAA2B,CAAC,KAAK,SAAS;AACrD,QAAM,EAAE,MAAM,MAAM,MAAM,IAAI;AAC9B,MAAI;AACJ,MAAI,SAAS,UAAU,OAAO;AAC5B,cAAU;AAAA,EACZ,WAAW,SAAS,SAAS,MAAM;AACjC,cAAU,iBAAiB,IAAI;AAAA,EACjC;AACA,QAAM,EAAE,YAAY,IAAI,KAAK;AAC7B,QAAM,OAAO,yCAAyC,KAAK,WAAW;AACtE,QAAM,MAAM,iEAAiE,KAAK,WAAW;AAC7F,MAAI,YAAY,KAAK;AACnB,QAAK,QAAQ,KAAK,aAAa,MAAM,KAChC,OAAO,KAAK,aAAa,UAAU,GAAI;AAC1C,UAAK,QAAQ,KAAK,aAAa,MAAM,KAClC,OAAO,KAAK,aAAa,UAAU,GAAI;AACxC,eAAO;AAAA,MACT;AAAA,IACF,OAAO;AACL,UAAI,SAAS,KAAK;AAClB,UAAI;AACJ,aAAO,QAAQ;AACb,YAAI,OAAO,aAAa,cAAc;AACpC,cAAK,QAAQ,OAAO,aAAa,MAAM,KAClC,OAAO,OAAO,aAAa,UAAU,GAAI;AAC5C,gBAAK,QAAQ,OAAO,aAAa,MAAM,KAClC,OAAO,OAAO,aAAa,UAAU,GAAI;AAC5C,oBAAM;AAAA,YACR;AACA;AAAA,UACF;AACA,mBAAS,OAAO;AAAA,QAClB,OAAO;AACL;AAAA,QACF;AAAA,MACF;AACA,aAAO,CAAC,CAAC;AAAA,IACX;AAAA,EACF,WAAW,SAAS;AAClB,UAAM,MAAM,IAAI,OAAO,aAAa,SAAS,GAAG,SAAS,KAAK,GAAG;AACjE,QAAI,IAAI,KAAK,OAAO,GAAG;AACrB,UAAI;AACJ,UAAI,QAAQ,QAAQ,GAAG,IAAI,IAAI;AAC7B,cAAM,CAAC,UAAU,SAAS,GAAG,QAAQ,IAAI,QAAQ,MAAM,GAAG;AAC1D,YAAI;AACJ,YAAI,aAAa,KAAK;AACpB,yBAAe,GAAG,SAAS,GAAG,SAAS;AAAA,QACzC,OAAO;AACL,yBAAe,GAAG,QAAQ,GAAG,SAAS;AAAA,QACxC;AACA,cAAM,cAAc,IAAI,OAAO,GAAG,SAAS;AAC3C,cAAM,MAAM,SAAS;AACrB,YAAI,eAAe;AACnB,YAAI,KAAK;AACP,mBAAS,IAAI,GAAG,IAAI,KAAK,KAAK;AAC5B,4BAAgB,IAAI,SAAS,CAAC,CAAC,GAAG,SAAS;AAAA,UAC7C;AAAA,QACF;AACA,0BACE,IAAI,OAAO,IAAI,YAAY,GAAG,WAAW,GAAG,YAAY,KAAK,GAAG;AAAA,MACpE,OAAO;AACL,0BAAkB,IAAI,OAAO,IAAI,OAAO,GAAG,SAAS,KAAK,GAAG;AAAA,MAC9D;AACA,UAAK,QAAQ,KAAK,aAAa,MAAM,KAChC,OAAO,KAAK,aAAa,UAAU,GAAI;AAC1C,cAAM,OAAQ,QAAQ,KAAK,aAAa,MAAM,KAChC,OAAO,KAAK,aAAa,UAAU,KAAM;AACvD,eAAO,gBAAgB,KAAK,IAAI;AAAA,MAClC,OAAO;AACL,YAAI,SAAS,KAAK;AAClB,YAAI;AACJ,eAAO,QAAQ;AACb,cAAI,OAAO,aAAa,cAAc;AACpC,gBAAK,QAAQ,OAAO,aAAa,MAAM,KAClC,OAAO,OAAO,aAAa,UAAU,GAAI;AAC5C,oBAAM,OAAQ,QAAQ,OAAO,aAAa,MAAM,KAClC,OAAO,OAAO,aAAa,UAAU,KAAM;AACzD,oBAAM,gBAAgB,KAAK,IAAI;AAC/B;AAAA,YACF;AACA,qBAAS,OAAO;AAAA,UAClB,OAAO;AACL;AAAA,UACF;AAAA,QACF;AACA,eAAO,CAAC,CAAC;AAAA,MACX;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAWO,IAAM,yBAAyB,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AAC7D,QAAM;AAAA,IACJ,OAAO;AAAA,IAAU,SAAS;AAAA,IAAY,MAAM;AAAA,IAAS,OAAO;AAAA,EAC9D,IAAI;AACJ,QAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,MAAI,OAAO,aAAa,YAAY,CAAC,UAAU,KAAK,QAAQ,KAAK,CAAC,SAAS;AACzE,UAAM,UAAM,2BAAY,GAAG;AAC3B,UAAM,IAAI,aAAa,oBAAoB,GAAG,IAAI,UAAU;AAAA,EAC9D;AACA,QAAM,EAAE,WAAW,IAAI;AACvB,MAAI,YAAY,QAAQ;AACtB,UAAM,cAAc,KAAK,cAAc;AACvC,QAAI;AACJ,QAAI,gBAAgB,aAAa;AAC/B,UAAI,OAAO,aAAa,YAAY,OAAO,KAAK,QAAQ,GAAG;AACzD,0BAAkB;AAAA,MACpB,OAAO;AACL,0BAAkB;AAAA,MACpB;AAAA,IACF,WAAW,OAAO,aAAa,YAAY,OAAO,KAAK,QAAQ,GAAG;AAChE,wBAAkB;AAAA,IACpB,OAAO;AACL,wBAAkB;AAAA,IACpB;AACA,QAAI,cAAc,iBAAiB,QAAQ,IAAI;AAC/C,QAAI,iBAAiB;AACnB,oBAAc,YAAY,YAAY;AAAA,IACxC;AACA,UAAM,aAAa,oBAAI,IAAI;AAE3B,QAAI,YAAY,QAAQ,GAAG,IAAI,IAAI;AACjC,YAAM;AAAA,QACJ,QAAQ;AAAA,QAAW,WAAW;AAAA,MAChC,IAAI,aAAa,WAAW;AAC5B,iBAAW,QAAQ,YAAY;AAC7B,YAAI,EAAE,MAAM,UAAU,OAAO,UAAU,IAAI;AAC3C,YAAI,iBAAiB;AACnB,qBAAW,SAAS,YAAY;AAChC,sBAAY,UAAU,YAAY;AAAA,QACpC;AACA,gBAAQ,WAAW;AAAA,UACjB,KAAK,IAAI;AACP,gBAAI,iBAAiB,UAAU;AAC7B,yBAAW,IAAI,SAAS;AAAA,YAC1B;AACA;AAAA,UACF;AAAA,UACA,KAAK,KAAK;AACR,gBAAI,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC9B,kBAAI,SAAS,SAAS,IAAI,YAAY,EAAE,GAAG;AACzC,2BAAW,IAAI,SAAS;AAAA,cAC1B;AAAA,YACF,WAAW,iBAAiB,UAAU;AACpC,yBAAW,IAAI,SAAS;AAAA,YAC1B;AACA;AAAA,UACF;AAAA,UACA,SAAS;AACP,gBAAI,CAAC,OAAO;AACV,kBAAI,SAAS;AACX,uBAAO;AAAA,cACT;AACA,oBAAM,UAAM,2BAAY,GAAG;AAC3B,oBAAM,IAAI,aAAa,oBAAoB,GAAG,IAAI,UAAU;AAAA,YAC9D;AACA,gBAAI,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC9B,oBAAM,CAAC,YAAY,aAAa,IAAI,SAAS,MAAM,GAAG;AAEtD,kBAAI,eAAe,SAAS,kBAAkB,QAAQ;AACpD;AAAA,cACF,WAAW,cAAc,cACZ,iBAAiB,eAAe;AAC3C,sBAAM,oBACF,oBAAoB,WAAW,IAAI;AACvC,oBAAI,mBAAmB;AACrB,6BAAW,IAAI,SAAS;AAAA,gBAC1B;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,eAAS,EAAE,MAAM,UAAU,OAAO,UAAU,KAAK,YAAY;AAC3D,YAAI,iBAAiB;AACnB,qBAAW,SAAS,YAAY;AAChC,sBAAY,UAAU,YAAY;AAAA,QACpC;AACA,YAAI,SAAS,QAAQ,GAAG,IAAI,IAAI;AAC9B,gBAAM,CAAC,YAAY,aAAa,IAAI,SAAS,MAAM,GAAG;AAEtD,cAAI,eAAe,SAAS,kBAAkB,QAAQ;AACpD;AAAA,UACF,WAAW,gBAAgB,eAAe;AACxC,uBAAW,IAAI,SAAS;AAAA,UAC1B;AAAA,QACF,WAAW,gBAAgB,UAAU;AACnC,qBAAW,IAAI,SAAS;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,QAAI,WAAW,MAAM;AACnB,YAAM,EAAE,MAAM,eAAe,OAAO,eAAe,IAAI,YAAY,CAAC;AACpE,UAAI;AACJ,UAAI,eAAe;AACjB,YAAI,iBAAiB;AACnB,sBAAY,cAAc,YAAY;AAAA,QACxC,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF,WAAW,gBAAgB;AACzB,YAAI,iBAAiB;AACnB,sBAAY,eAAe,YAAY;AAAA,QACzC,OAAO;AACL,sBAAY;AAAA,QACd;AAAA,MACF,WAAW,mBAAmB,IAAI;AAChC,oBAAY;AAAA,MACd;AACA,cAAQ,YAAY;AAAA,QAClB,KAAK,KAAK;AACR,iBAAO,OAAO,cAAc,YAAY,WAAW,IAAI,SAAS;AAAA,QAClE;AAAA,QACA,KAAK,MAAM;AACT,cAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,gBAAI;AACJ,uBAAW,SAAS,YAAY;AAC9B,oBAAM,OAAO,IAAI,IAAI,MAAM,MAAM,KAAK,CAAC;AACvC,kBAAI,KAAK,IAAI,SAAS,GAAG;AACvB,sBAAM;AACN;AAAA,cACF;AAAA,YACF;AACA,mBAAO,CAAC,CAAC;AAAA,UACX;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,MAAM;AACT,cAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,gBAAI;AACJ,uBAAW,SAAS,YAAY;AAC9B,kBAAI,UAAU,aAAa,MAAM,WAAW,GAAG,SAAS,GAAG,GAAG;AAC5D,uBAAO;AACP;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,MAAM;AACT,cAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,gBAAI;AACJ,uBAAW,SAAS,YAAY;AAC9B,kBAAI,MAAM,WAAW,GAAG,SAAS,EAAE,GAAG;AACpC,uBAAO;AACP;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,MAAM;AACT,cAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,gBAAI;AACJ,uBAAW,SAAS,YAAY;AAC9B,kBAAI,MAAM,SAAS,GAAG,SAAS,EAAE,GAAG;AAClC,uBAAO;AACP;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,MAAM;AACT,cAAI,aAAa,OAAO,cAAc,UAAU;AAC9C,gBAAI;AACJ,uBAAW,SAAS,YAAY;AAC9B,kBAAI,MAAM,SAAS,GAAG,SAAS,EAAE,GAAG;AAClC,uBAAO;AACP;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,qBAAO;AAAA,YACT;AACA,mBAAO;AAAA,UACT;AACA,iBAAO;AAAA,QACT;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AAAA,EACF;AACA,SAAO;AACT;AAWO,IAAM,oBAAoB,CAAC,KAAK,MAAM,MAAM,CAAC,MAAM;AACxD,QAAM,UAAU,iBAAiB,IAAI,IAAI;AACzC,QAAM,EAAE,WAAW,cAAc,OAAO,IAAI;AAC5C,QAAM,EAAE,OAAO,QAAQ,IAAI;AAC3B,MAAI;AAAA,IACF,QAAQ;AAAA,IAAW,WAAW;AAAA,EAChC,IAAI,aAAa,SAAS,IAAI;AAC9B,MAAI,KAAK,cAAc,gBAAgB,eACnC,gBAAgB,KAAK,SAAS,GAAG;AACnC,gBAAY,UAAU,YAAY;AAClC,mBAAe,aAAa,YAAY;AAAA,EAC1C;AACA,MAAI;AACJ,MAAI;AAEJ,MAAI,UAAU,QAAQ,GAAG,IAAI,IAAI;AAC/B,KAAC,YAAY,aAAa,IAAI,UAAU,MAAM,GAAG;AAAA,EACnD,OAAO;AACL,iBAAa,UAAU;AACvB,oBAAgB;AAAA,EAClB;AACA,UAAQ,WAAW;AAAA,IACjB,KAAK,IAAI;AACP,UAAI,CAAC,cAAc,CAAC,iBACf,iBAAiB,OAAO,iBAAiB,gBAAgB;AAC5D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,KAAK,KAAK;AACR,UAAI,iBAAiB,OAAO,iBAAiB,eAAe;AAC1D,eAAO;AAAA,MACT;AACA,aAAO;AAAA,IACT;AAAA,IACA,SAAS;AACP,UAAI,CAAC,OAAO;AACV,YAAI,SAAS;AACX,iBAAO;AAAA,QACT;AACA,cAAM,UAAM,2BAAY,GAAG;AAC3B,cAAM,IAAI,aAAa,oBAAoB,GAAG,IAAI,UAAU;AAAA,MAC9D;AACA,YAAM,QAAQ,KAAK,mBAAmB,SAAS;AAC/C,YAAM,SAAS,KAAK,mBAAmB,UAAU;AACjD,UAAI,UAAU,UAAU,cAAc,YAAY;AAChD,YAAI,iBAAiB,OAAO,iBAAiB,eAAe;AAC1D,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,WAAW,CAAC,WAAW,CAAC,OAAO;AAC7B,cAAM,IAAI,aAAa,wBAAwB,SAAS,IAAI,UAAU;AAAA,MACxE;AACA,aAAO;AAAA,IACT;AAAA,EACF;AACF;;;ACncA,IAAM,WAAW;AACjB,IAAM,WAAW;AAyBV,IAAM,SAAN,MAAa;AAAA;AAAA,EAElB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,YAAY,QAAQ;AAClB,SAAK,UAAU;AACf,SAAK,YAAY,oBAAI,QAAQ;AAC7B,SAAK,iBAAiB,oBAAI,QAAQ;AAClC,SAAK,qBAAqB,oBAAI,QAAQ;AACtC,SAAK,WAAW,oBAAI,QAAQ;AAC5B,SAAK,SAAS;AACd,SAAK,SAAS;AACd,SAAK,oBAAoB;AACzB,SAAK,wBAAwB;AAAA,EAC/B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,QAAQ,GAAG,MAAM,CAAC,GAAG;AACnB,UAAM,WAAW,IAAI,YAAY,KAAK;AACtC,QAAI,CAAC,UAAU;AACb,UAAI,aAAa,gBACb,aAAa,KAAK,QAAQ,cAAc;AAC1C,YAAI,EAAE,SAAS,mBAAmB;AAChC,cAAI,KAAK,OAAO;AACd,oBAAQ,KAAK,EAAE,OAAO;AAAA,UACxB;AAAA,QACF,OAAO;AACL,gBAAM,IAAI,KAAK,QAAQ,aAAa,EAAE,SAAS,EAAE,IAAI;AAAA,QACvD;AAAA,MACF,WAAW,EAAE,QAAQ,KAAK,SAAS;AACjC,cAAM,IAAI,KAAK,QAAQ,EAAE,IAAI,EAAE,EAAE,SAAS,EAAE,OAAO,EAAE,CAAC;AAAA,MACxD,OAAO;AACL,cAAM;AAAA,MACR;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,MAAM,UAAU,MAAM,MAAM,CAAC,GAAG;AAC9B,UAAM,EAAE,OAAO,eAAe,UAAU,KAAK,IAAI;AACjD,SAAK,SAAS,CAAC,CAAC;AAChB,SAAK,iBAAiB;AACtB,SAAK,YAAY,CAAC,CAAC;AACnB,SAAK,QAAQ,CAAC,CAAC;AACf;AAAA,MACE,KAAK;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP,IAAI,eAAe,IAAI;AACvB,SAAK,QAAQ;AACb,SAAK,YAAY;AACjB;AAAA,MACE,KAAK;AAAA,MACL,KAAK;AAAA,IACP,IAAI,KAAK,YAAY,QAAQ;AAC7B,SAAK,qBAAqB,oBAAI,QAAQ;AACtC,SAAK,iBAAiB,CAAC;AACvB,SAAK,WAAW,oBAAI,QAAQ;AAC5B,SAAK,oBAAoB;AACzB,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,0BAA0B;AACxB,UAAM,MAAM;AAAA,MACV,SAAS;AAAA,MACT,SAAS;AAAA,IACX;AACA,UAAM,OAAO,CAAC;AACd,UAAM,YAAY,CAAC,SAAS,SAAS;AACrC,eAAW,OAAO,WAAW;AAC3B,WAAK,KAAK,KAAK,QAAQ,iBAAiB,KAAK,SAAO;AAClD,aAAK,SAAS;AAAA,MAChB,GAAG,GAAG,CAAC;AAAA,IACT;AACA,UAAM,eAAe,CAAC,WAAW,OAAO;AACxC,eAAW,OAAO,cAAc;AAC9B,WAAK,KAAK,KAAK,QAAQ,iBAAiB,KAAK,SAAO;AAClD,cAAM,EAAE,KAAAC,KAAI,IAAI;AAChB,YAAI,CAAC,aAAa,SAASA,IAAG,GAAG;AAC/B,eAAK,SAAS;AAAA,QAChB;AAAA,MACF,GAAG,GAAG,CAAC;AAAA,IACT;AACA,UAAM,YAAY;AAAA,MAChB;AAAA,MAAa;AAAA,MAAa;AAAA,MAAW;AAAA,MAAS;AAAA,IAChD;AACA,eAAW,OAAO,WAAW;AAC3B,WAAK,KAAK,KAAK,QAAQ,iBAAiB,KAAK,SAAO;AAClD,aAAK,SAAS;AAAA,MAChB,GAAG,GAAG,CAAC;AAAA,IACT;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,UAAU;AACpB,UAAM,QAAQ,CAAC;AACf,SAAK,cAAc;AACnB,SAAK,cAAc;AACnB,QAAI;AACJ,QAAI,KAAK,eAAe,IAAI,KAAK,SAAS,GAAG;AAC3C,YAAM,aAAa,KAAK,eAAe,IAAI,KAAK,SAAS;AACzD,UAAI,cAAc,WAAW,IAAI,GAAG,QAAQ,EAAE,GAAG;AAC/C,cAAM,OAAO,WAAW,IAAI,GAAG,QAAQ,EAAE;AACzC,cAAM,KAAK;AACX,aAAK,cAAc,KAAK;AACxB,aAAK,cAAc,KAAK;AAAA,MAC1B;AAAA,IACF;AACA,QAAI,KAAK;AACP,YAAM,IAAI,IAAI;AACd,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAI,CAAC,EAAE,MAAM;AACb,YAAI,CAAC,EAAE,WAAW;AAClB,YAAI,CAAC,EAAE,OAAO;AACd,cAAM,CAAC,IAAI,CAAC;AAAA,MACd;AAAA,IACF,OAAO;AACL,UAAI;AACJ,UAAI;AACF,iBAAS,cAAc,QAAQ;AAAA,MACjC,SAAS,GAAG;AACV,eAAO,KAAK,QAAQ,CAAC;AAAA,MACvB;AACA,YAAM,EAAE,UAAU,KAAK,IAAI,QAAQ,MAAM;AACzC,YAAM;AAAA,QACJ;AAAA,QAAkB;AAAA,QAAsB;AAAA,QACxC;AAAA,MACF,IAAI;AACJ,UAAI,aAAa,oBAAoB,uBACnC,CAAC,EAAE,wBAAwB;AAC7B,UAAI,aAAa;AACjB,UAAI,IAAI;AACR,YAAM,CAAC;AACP,iBAAW,CAAC,GAAG,KAAK,KAAK,UAAU;AACjC,cAAM,SAAS,CAAC;AAChB,YAAI,OAAO,MAAM,MAAM;AACvB,YAAI,QAAQ,KAAK,SAAS,YAAY;AACpC,gBAAM,SAAS,oBAAI,IAAI;AACvB,iBAAO,MAAM;AACX,gBAAI,WAAW,KAAK;AACpB,gBAAI,KAAK,SAAS,YAAY;AAC5B,oBAAM,CAAC,QAAQ,IAAI;AACnB,kBAAI,SAAS,SAAS,YAAY;AAChC,uBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,kBACnC,oBAAoB,QAAQ;AAAA,kBAC5B;AAAA,gBACF,CAAC;AAAA,cACH;AACA,kBAAI,aAAa,OAAO,aAAa,KAAK;AACxC,6BAAa;AAAA,cACf,OAAO;AACL,6BAAa;AAAA,cACf;AACA,qBAAO,KAAK;AAAA,gBACV,OAAO;AAAA,gBACP,QAAQ,QAAQ,MAAM;AAAA,cACxB,CAAC;AACD,qBAAO,MAAM;AAAA,YACf,WAAW,MAAM;AACf,kBAAI,YAAY,OAAO,aAAa,UAAU;AAC5C,2BAAW,iBAAiB,QAAQ;AACpC,oBAAI,OAAO,aAAa,YAAY,aAAa,KAAK,MAAM;AAC1D,uBAAK,OAAO;AAAA,gBACd;AACA,oBAAI,OAAO,KAAK,QAAQ,GAAG;AACzB,uBAAK,YAAY;AAAA,gBACnB;AAAA,cACF;AACA,qBAAO,IAAI,IAAI;AAAA,YACjB;AACA,gBAAI,MAAM,QAAQ;AAChB,qBAAO,MAAM,MAAM;AAAA,YACrB,OAAO;AACL,qBAAO,KAAK;AAAA,gBACV,OAAO;AAAA,gBACP,QAAQ,QAAQ,MAAM;AAAA,cACxB,CAAC;AACD,qBAAO,MAAM;AACb;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,KAAK;AAAA,UACP;AAAA,UACA,KAAK;AAAA,UACL,UAAU;AAAA,UACV,MAAM;AAAA,QACR,CAAC;AACD,cAAM,CAAC,IAAI,CAAC;AACZ;AAAA,MACF;AACA,UAAI;AACJ,UAAI,KAAK,eAAe,IAAI,KAAK,SAAS,GAAG;AAC3C,qBAAa,KAAK,eAAe,IAAI,KAAK,SAAS;AAAA,MACrD,OAAO;AACL,qBAAa,oBAAI,IAAI;AAAA,MACvB;AACA,iBAAW,IAAI,GAAG,QAAQ,IAAI;AAAA,QAC5B;AAAA,QACA;AAAA,QACA;AAAA,MACF,CAAC;AACD,WAAK,eAAe,IAAI,KAAK,WAAW,UAAU;AAClD,WAAK,cAAc;AACnB,WAAK,cAAc;AAAA,IACrB;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,kBAAkB,MAAM,MAAM,CAAC,GAAG;AAChC,UAAM,EAAE,QAAQ,OAAO,aAAa,eAAe,IAAI;AACvD,QAAI;AACJ,QAAI,OAAO;AACT,eAAS,KAAK,UAAU,iBAAiB,MAAM,UAAU;AAAA,IAC3D,WAAW,KAAK,SAAS,IAAI,IAAI,GAAG;AAClC,eAAS,KAAK,SAAS,IAAI,IAAI;AAAA,IACjC,OAAO;AACL,eAAS,KAAK,UAAU,iBAAiB,MAAM,UAAU;AACzD,WAAK,SAAS,IAAI,MAAM,MAAM;AAAA,IAChC;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,8BAA8B;AAC5B,SAAK,cAAc,KAAK,kBAAkB,KAAK,KAAK;AACpD,SAAK,cAAc;AACnB,WAAO,KAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAcA,iBAAiB,KAAK,MAAM,MAAM,CAAC,GAAG;AACpC,UAAM,EAAE,GAAG,GAAG,SAAS,SAAS,IAAI;AACpC,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI;AACJ,QAAI,UAAU;AACZ,UAAI,KAAK,UAAU,IAAI,QAAQ,GAAG;AAChC,2BAAmB,KAAK,UAAU,IAAI,QAAQ;AAAA,MAChD,OAAO;AACL,cAAM,EAAE,UAAAC,UAAS,IAAI,QAAQ,QAAQ;AACrC,2BAAmBA;AACnB,YAAI,CAAC,KAAK,aAAa;AACrB,eAAK,UAAU,IAAI,UAAU,gBAAgB;AAAA,QAC/C;AAAA,MACF;AACA,YAAM,EAAE,SAAS,IAAI,QAAQ,QAAQ;AACrC,yBAAmB;AAAA,IACrB;AACA,QAAI,YAAY;AACd,YAAM,SAAS,KAAK,kBAAkB,YAAY;AAAA,QAChD,OAAO;AAAA,MACT,CAAC;AACD,UAAI,UAAU,OAAO,WAAW;AAChC,YAAM,gBAAgB,oBAAI,IAAI;AAC9B,UAAI,IAAI;AACR,UAAI,kBAAkB;AACpB,eAAO,SAAS;AACd,cAAI,UAAU,OAAO,GAAG;AACtB,gBAAI;AACJ,uBAAW,UAAU,kBAAkB;AACrC,qBAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AAC7C,kBAAI,CAAC,MAAM;AACT;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,4BAAc,IAAI,OAAO;AAAA,YAC3B;AAAA,UACF;AACA;AACA,oBAAU,OAAO,YAAY;AAAA,QAC/B;AAAA,MACF,OAAO;AACL,eAAO,SAAS;AACd;AACA,oBAAU,OAAO,YAAY;AAAA,QAC/B;AAAA,MACF;AAEA,UAAI,MAAM,GAAG;AACX,YAAI,IAAI,KAAK,KAAK,GAAG;AACnB,cAAI,cAAc,MAAM;AACtB,sBAAU,aAAa,YAAY,MAAM;AACzC,gBAAI,SAAS;AACX,wBAAU,OAAO,UAAU;AAAA,YAC7B,OAAO;AACL,wBAAU,OAAO,WAAW;AAAA,YAC9B;AACA,gBAAI,IAAI;AACR,mBAAO,SAAS;AACd,kBAAI,cAAc,IAAI,OAAO,GAAG;AAC9B,oBAAI,MAAM,IAAI,GAAG;AACf,0BAAQ,IAAI,OAAO;AACnB;AAAA,gBACF;AACA;AAAA,cACF;AACA,kBAAI,SAAS;AACX,0BAAU,OAAO,gBAAgB;AAAA,cACnC,OAAO;AACL,0BAAU,OAAO,YAAY;AAAA,cAC/B;AAAA,YACF;AAAA,UACF,WAAW,CAAC,UAAU;AACpB,sBAAU,aAAa,YAAY,MAAM;AACzC,gBAAI,SAAS;AACX,wBAAU,OAAO,UAAU;AAAA,YAC7B,OAAO;AACL,wBAAU,OAAO,WAAW;AAAA,YAC9B;AACA,gBAAI,IAAI;AACR,mBAAO,SAAS;AACd,kBAAI,MAAM,IAAI,GAAG;AACf,wBAAQ,IAAI,OAAO;AACnB;AAAA,cACF;AACA,kBAAI,SAAS;AACX,0BAAU,OAAO,gBAAgB;AAAA,cACnC,OAAO;AACL,0BAAU,OAAO,YAAY;AAAA,cAC/B;AACA;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MAEF,OAAO;AACL,YAAI,MAAM,IAAI;AACd,YAAI,IAAI,GAAG;AACT,iBAAO,MAAM,GAAG;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,OAAO,KAAK,MAAM,GAAG;AACvB,oBAAU,aAAa,YAAY,MAAM;AACzC,cAAI,SAAS;AACX,sBAAU,OAAO,UAAU;AAAA,UAC7B,OAAO;AACL,sBAAU,OAAO,WAAW;AAAA,UAC9B;AACA,cAAI,IAAI;AACR,cAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,iBAAO,SAAS;AACd,gBAAI,WAAW,OAAO,KAAK,MAAM,GAAG;AAClC,kBAAI,cAAc,MAAM;AACtB,oBAAI,cAAc,IAAI,OAAO,GAAG;AAC9B,sBAAI,MAAM,KAAK;AACb,4BAAQ,IAAI,OAAO;AACnB,2BAAO;AAAA,kBACT;AACA,sBAAI,IAAI,GAAG;AACT;AAAA,kBACF,OAAO;AACL;AAAA,kBACF;AAAA,gBACF;AAAA,cACF,WAAW,MAAM,KAAK;AACpB,oBAAI,CAAC,UAAU;AACb,0BAAQ,IAAI,OAAO;AAAA,gBACrB;AACA,uBAAO;AAAA,cACT;AACA,kBAAI,SAAS;AACX,0BAAU,OAAO,gBAAgB;AAAA,cACnC,OAAO;AACL,0BAAU,OAAO,YAAY;AAAA,cAC/B;AACA;AAAA,YACF,OAAO;AACL;AAAA,YACF;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,cAAM,IAAI,CAAC,GAAG,OAAO;AACrB,eAAO,IAAI,IAAI,EAAE,QAAQ,CAAC;AAAA,MAC5B;AAAA,IACF,WAAW,SAAS,KAAK,SAAU,IAAI,MAAO,GAAG;AAC/C,UAAI,kBAAkB;AACpB,YAAI;AACJ,mBAAW,UAAU,kBAAkB;AACrC,iBAAO,KAAK,aAAa,QAAQ,MAAM,GAAG;AAC1C,cAAI,MAAM;AACR;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AACR,kBAAQ,IAAI,IAAI;AAAA,QAClB;AAAA,MACF,OAAO;AACL,gBAAQ,IAAI,IAAI;AAAA,MAClB;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,kBAAkB,KAAK,MAAM;AAC3B,UAAM,EAAE,GAAG,GAAG,QAAQ,IAAI;AAC1B,UAAM,EAAE,WAAW,cAAc,YAAY,OAAO,IAAI;AACxD,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,YAAY;AACd,YAAM,SAAS,KAAK,kBAAkB,YAAY;AAAA,QAChD,OAAO;AAAA,MACT,CAAC;AACD,UAAI,UAAU,aAAa,YAAY,MAAM;AAC7C,gBAAU,OAAO,WAAW;AAC5B,UAAI,IAAI;AACR,aAAO,SAAS;AACd;AACA,kBAAU,OAAO,YAAY;AAAA,MAC/B;AAEA,UAAI,MAAM,GAAG;AACX,YAAI,IAAI,KAAK,KAAK,GAAG;AACnB,oBAAU,aAAa,YAAY,MAAM;AACzC,cAAI,SAAS;AACX,sBAAU,OAAO,UAAU;AAAA,UAC7B,OAAO;AACL,sBAAU,OAAO,WAAW;AAAA,UAC9B;AACA,cAAI,IAAI;AACR,iBAAO,SAAS;AACd,kBAAM;AAAA,cACJ,WAAW;AAAA,cAAe,cAAc;AAAA,cACxC,QAAQ;AAAA,YACV,IAAI;AACJ,gBAAI,kBAAkB,aAAa,eAAe,UAC9C,qBAAqB,cAAc;AACrC,kBAAI,MAAM,IAAI,GAAG;AACf,wBAAQ,IAAI,OAAO;AACnB;AAAA,cACF;AACA;AAAA,YACF;AACA,gBAAI,SAAS;AACX,wBAAU,OAAO,gBAAgB;AAAA,YACnC,OAAO;AACL,wBAAU,OAAO,YAAY;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAAA,MAEF,OAAO;AACL,YAAI,MAAM,IAAI;AACd,YAAI,IAAI,GAAG;AACT,iBAAO,MAAM,GAAG;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AACA,YAAI,OAAO,KAAK,MAAM,GAAG;AACvB,oBAAU,aAAa,YAAY,MAAM;AACzC,cAAI,SAAS;AACX,sBAAU,OAAO,UAAU;AAAA,UAC7B,OAAO;AACL,sBAAU,OAAO,WAAW;AAAA,UAC9B;AACA,cAAI,IAAI,IAAI,IAAI,IAAI,IAAI;AACxB,iBAAO,SAAS;AACd,kBAAM;AAAA,cACJ,WAAW;AAAA,cAAe,cAAc;AAAA,cACxC,QAAQ;AAAA,YACV,IAAI;AACJ,gBAAI,kBAAkB,aAAa,eAAe,UAC9C,qBAAqB,cAAc;AACrC,kBAAI,MAAM,KAAK;AACb,wBAAQ,IAAI,OAAO;AACnB,uBAAO;AAAA,cACT;AACA,kBAAI,MAAM,KAAK,OAAO,GAAG;AACvB;AAAA,cACF,WAAW,IAAI,GAAG;AAChB;AAAA,cACF,OAAO;AACL;AAAA,cACF;AAAA,YACF;AACA,gBAAI,SAAS;AACX,wBAAU,OAAO,gBAAgB;AAAA,YACnC,OAAO;AACL,wBAAU,OAAO,YAAY;AAAA,YAC/B;AAAA,UACF;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW,QAAQ,OAAO,GAAG;AAC/B,cAAM,IAAI,CAAC,GAAG,OAAO;AACrB,eAAO,IAAI,IAAI,EAAE,QAAQ,CAAC;AAAA,MAC5B;AAAA,IACF,WAAW,SAAS,KAAK,SAAU,IAAI,MAAO,GAAG;AAC/C,cAAQ,IAAI,IAAI;AAAA,IAClB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,cAAc,KAAK,MAAM,SAAS,MAAM,CAAC,GAAG;AAC1C,UAAM;AAAA,MACJ,KAAK;AAAA,QACH;AAAA,QACA;AAAA,QACA,MAAM;AAAA,MACR;AAAA,MACA;AAAA,IACF,IAAI;AACJ,UAAM,SAAS,oBAAI,IAAI;AACvB,QAAI,cAAc;AAChB,UAAI,iBAAiB,QAAQ;AAC3B,eAAO,IAAI,KAAK,CAAC;AACjB,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB,WAAW,iBAAiB,OAAO;AACjC,eAAO,IAAI,KAAK,CAAC;AACjB,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,QAAQ,MAAM,IAAI,IAAI;AAChC,eAAO,IAAI,WAAW,IAAI;AAAA,MAC5B;AAAA,IACF,OAAO;AACL,UAAI,OAAO,MAAM,YAAY,QAAQ,KAAK,CAAC,GAAG;AAC5C,eAAO,IAAI,KAAK,IAAI,CAAC;AAAA,MACvB,OAAO;AACL,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB;AACA,UAAI,OAAO,MAAM,YAAY,QAAQ,KAAK,CAAC,GAAG;AAC5C,eAAO,IAAI,KAAK,IAAI,CAAC;AAAA,MACvB,OAAO;AACL,eAAO,IAAI,KAAK,CAAC;AAAA,MACnB;AACA,UAAI,QAAQ,QAAQ,MAAM,IAAI,IAAI;AAChC,eAAO,IAAI,WAAW,IAAI;AAAA,MAC5B;AAAA,IACF;AACA,QAAI,YAAY,eAAe,YAAY,kBAAkB;AAC3D,UAAI,UAAU;AACZ,eAAO,IAAI,YAAY,QAAQ;AAAA,MACjC;AACA,YAAM,MAAM,OAAO,YAAY,MAAM;AACrC,YAAM,QAAQ,KAAK,iBAAiB,KAAK,MAAM,GAAG;AAClD,aAAO;AAAA,IACT,WAAW,YAAY,iBAAiB,YAAY,oBAAoB;AACtE,YAAM,MAAM,OAAO,YAAY,MAAM;AACrC,YAAM,QAAQ,KAAK,kBAAkB,KAAK,IAAI;AAC9C,aAAO;AAAA,IACT;AACA,WAAO,oBAAI,IAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,oBAAoB,WAAW,MAAM,MAAM,CAAC,GAAG;AAC7C,QAAI,MAAM,QAAQ,SAAS,KAAK,UAAU,QAAQ;AAChD,YAAM,SAAS,CAAC,GAAG,SAAS;AAC5B,YAAM,CAAC,IAAI,IAAI;AACf,YAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,UAAI;AACJ,UAAI,aAAa,YAAY;AAC3B,gBAAQ,OAAO,MAAM;AAAA,MACvB,OAAO;AACL,gBAAQ;AAAA,UACN,MAAM;AAAA,UACN,MAAM;AAAA,QACR;AAAA,MACF;AACA,YAAM,aAAa,CAAC;AACpB,aAAO,OAAO,QAAQ;AACpB,cAAM,CAAC,IAAI,IAAI;AACf,cAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,YAAI,aAAa,YAAY;AAC3B;AAAA,QACF,OAAO;AACL,qBAAW,KAAK,OAAO,MAAM,CAAC;AAAA,QAChC;AAAA,MACF;AACA,YAAM,OAAO;AAAA,QACX;AAAA,QACA,QAAQ;AAAA,MACV;AACA,UAAI,MAAM;AACV,YAAM,QAAQ,KAAK,iBAAiB,MAAM,MAAM,GAAG;AACnD,UAAI,MAAM,MAAM;AACd,YAAI,OAAO,QAAQ;AACjB,cAAI,OAAO;AACX,qBAAW,YAAY,OAAO;AAC5B,mBAAO,KAAK,oBAAoB,QAAQ,UAAU,GAAG;AACrD,gBAAI,MAAM;AACR;AAAA,YACF;AAAA,UACF;AACA,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,wBAAwB,SAAS,MAAM,MAAM,CAAC,GAAG;AAC/C,UAAM,EAAE,SAAS,UAAU,aAAa,IAAI;AAC5C,UAAM,gBAAgB,IAAI,gBAAgB,KAAK,YAC7C,KAAK,aAAa;AACpB,QAAI,YAAY,OAAO;AACrB,UAAI;AACJ,iBAAW,UAAU,UAAU;AAC7B,eAAO,KAAK,oBAAoB,QAAQ,MAAM,GAAG;AACjD,YAAI,MAAM;AACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,MAAM;AACR,YAAI,cAAc;AAChB,cAAI,KAAK,mBAAmB;AAC1B,mBAAO;AAAA,UACT;AAAA,QACF,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,OAAO;AAEL,UAAI,cAAc;AAChB,YAAI;AACJ,mBAAW,UAAU,UAAU;AAC7B,cAAI,OAAO,SAAS,GAAG;AACrB,sBAAU;AACV;AAAA,UACF,WAAW,YAAY,OAAO;AAC5B,kBAAM,CAAC,EAAE,MAAM,aAAa,CAAC,IAAI;AACjC,gBAAI,iBAAiB,mBAAmB;AACtC,wBAAU;AACV;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,SAAS;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AACA,UAAI,UAAU,YAAY,QAAQ,YAAY;AAC9C,YAAM,IAAI,aAAa;AACvB,UAAI;AACJ,eAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,cAAM,SAAS,aAAa,CAAC;AAC7B,cAAM,YAAY,OAAO,SAAS;AAClC,cAAM,EAAE,OAAO,IAAI,OAAO,SAAS;AACnC,eAAO,KAAK,aAAa,QAAQ,MAAM,GAAG;AAC1C,YAAI,QAAQ,YAAY,GAAG;AACzB,cAAI,YAAY,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,mBAAS,IAAI,YAAY,GAAG,KAAK,GAAG,KAAK;AACvC,kBAAM,OAAO,OAAO,CAAC;AACrB,kBAAM,MAAM,CAAC;AACb,gBAAI,MAAM;AACV,uBAAW,YAAY,WAAW;AAChC,oBAAM,IAAI,KAAK,iBAAiB,MAAM,UAAU,GAAG;AACnD,kBAAI,EAAE,MAAM;AACV,oBAAI,KAAK,GAAG,CAAC;AAAA,cACf;AAAA,YACF;AACA,gBAAI,IAAI,QAAQ;AACd,kBAAI,MAAM,GAAG;AACX,uBAAO;AAAA,cACT,OAAO;AACL,4BAAY,IAAI,IAAI,GAAG;AAAA,cACzB;AAAA,YACF,OAAO;AACL,qBAAO;AACP;AAAA,YACF;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AACR;AAAA,QACF;AAAA,MACF;AACA,UAAI,YAAY,OAAO;AACrB,YAAI,MAAM;AACR,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,WAAW,MAAM;AACf,eAAO;AAAA,MACT;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,0BAA0B,KAAK,MAAM,MAAM,CAAC,GAAG;AAC7C,UAAM,EAAE,UAAU,aAAa,MAAM,QAAQ,IAAI;AACjD,UAAM,EAAE,WAAW,WAAW,IAAI;AAClC,UAAM;AAAA,MACJ;AAAA,MACA,OAAO,KAAK;AAAA,IACd,IAAI;AACJ,UAAM,UAAU,oBAAI,IAAI;AAExB,QAAI,MAAM,QAAQ,WAAW,KAAK,YAAY,SAAS,OAAO,GAAG;AAC/D,UAAI,CAAC,YAAY,UAAU,YAAY,QAAQ,YAAY,SAAS;AAClE,cAAM,UAAM,2BAAY,GAAG;AAC3B,eAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,UACnC,oBAAoB,GAAG;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AACA,UAAI;AACJ,UAAI,KAAK,UAAU,IAAI,GAAG,GAAG;AAC3B,kBAAU,KAAK,UAAU,IAAI,GAAG;AAAA,MAClC,OAAO;AACL,cAAM,EAAE,SAAS,IAAI,QAAQ,GAAG;AAChC,YAAI,YAAY,OAAO;AAErB,cAAI;AACJ,qBAAW,SAAS,aAAa;AAC/B,kBAAM,WAAO,uBAAQ,OAAO,UAAQ;AAClC,kBAAI,YAAY,SAAS,KAAK,IAAI,SAC9B,uBAAQ,MAAM,gBAAc,WAAW,SAAS,KAAK,GAAG;AAC1D,uBAAO;AAAA,cACT;AACA,qBAAO;AAAA,YACT,CAAC;AACD,gBAAI,MAAM;AACR,oBAAM,WAAW,KAAK;AACtB,kBAAI,aAAa,QAAQ,aAAa,SAAS;AAC7C,2BAAW;AACX;AAAA,cACF,OAAO;AACL,sBAAM,UAAM,2BAAY,GAAG;AAC3B,uBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,kBACnC,oBAAoB,GAAG;AAAA,kBACvB;AAAA,gBACF,CAAC;AAAA,cACH;AAAA,YACF;AAAA,UACF;AACA,cAAI,UAAU;AACZ,mBAAO;AAAA,UACT;AACA,oBAAU;AAAA,YACR;AAAA,YACA;AAAA,UACF;AAAA,QACF,OAAO;AACL,gBAAM,eAAe,CAAC;AACtB,qBAAW,CAAC,GAAG,MAAM,KAAK,UAAU;AAClC,kBAAM,SAAS,CAAC;AAChB,kBAAM,YAAY,oBAAI,IAAI;AAC1B,gBAAI,OAAO,OAAO,MAAM;AACxB,mBAAO,MAAM;AACX,kBAAI,KAAK,SAAS,YAAY;AAC5B,uBAAO,KAAK;AAAA,kBACV,OAAO;AAAA,kBACP,QAAQ,CAAC,GAAG,SAAS;AAAA,gBACvB,CAAC;AACD,0BAAU,MAAM;AAAA,cAClB,WAAW,MAAM;AACf,0BAAU,IAAI,IAAI;AAAA,cACpB;AACA,kBAAI,OAAO,QAAQ;AACjB,uBAAO,OAAO,MAAM;AAAA,cACtB,OAAO;AACL,uBAAO,KAAK;AAAA,kBACV,OAAO;AAAA,kBACP,QAAQ,CAAC,GAAG,SAAS;AAAA,gBACvB,CAAC;AACD,0BAAU,MAAM;AAChB;AAAA,cACF;AAAA,YACF;AACA,yBAAa,KAAK,MAAM;AAAA,UAC1B;AACA,oBAAU;AAAA,YACR;AAAA,YACA;AAAA,YACA;AAAA,UACF;AACA,cAAI,CAAC,KAAK,aAAa;AACrB,iBAAK,UAAU,IAAI,KAAK,OAAO;AAAA,UACjC;AAAA,QACF;AAAA,MACF;AACA,YAAM,MAAM,KAAK,wBAAwB,SAAS,MAAM,GAAG;AAC3D,UAAI,KAAK;AACP,gBAAQ,IAAI,GAAG;AAAA,MACjB;AAAA,IACF,WAAW,MAAM,QAAQ,WAAW,GAAG;AAErC,UAAI,oCAAoC,KAAK,OAAO,GAAG;AACrD,YAAI,YAAY,WAAW,GAAG;AAC5B,gBAAM,UAAM,2BAAY,GAAG;AAC3B,iBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,YACnC,oBAAoB,GAAG;AAAA,YACvB;AAAA,UACF,CAAC;AAAA,QACH;AACA,cAAM,CAAC,MAAM,IAAI;AACjB,cAAM,QAAQ,KAAK,cAAc,QAAQ,MAAM,SAAS,GAAG;AAC3D,eAAO;AAAA,MACT,OAAO;AACL,gBAAQ,SAAS;AAAA;AAAA,UAEf,KAAK,OAAO;AACV,gBAAI,YAAY,WAAW,GAAG;AAC5B,oBAAM,UAAM,2BAAY,GAAG;AAC3B,qBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBACnC,oBAAoB,GAAG;AAAA,gBACvB;AAAA,cACF,CAAC;AAAA,YACH;AACA,kBAAM,CAAC,QAAQ,IAAI;AACnB,kBAAM,MAAM,0BAA0B,UAAU,IAAI;AACpD,gBAAI,KAAK;AACP,sBAAQ,IAAI,IAAI;AAAA,YAClB;AACA;AAAA,UACF;AAAA;AAAA,UAEA,KAAK,QAAQ;AACX,gBAAI,CAAC,YAAY,QAAQ;AACvB,oBAAM,UAAM,2BAAY,GAAG;AAC3B,qBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBACnC,oBAAoB,GAAG;AAAA,gBACvB;AAAA,cACF,CAAC;AAAA,YACH;AACA,gBAAI;AACJ,uBAAW,YAAY,aAAa;AAClC,qBAAO,yBAAyB,UAAU,IAAI;AAC9C,kBAAI,MAAM;AACR;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,sBAAQ,IAAI,IAAI;AAAA,YAClB;AACA;AAAA,UACF;AAAA;AAAA,UAEA,KAAK,SAAS;AACZ,gBAAI,gBAAgB,IAAI,GAAG;AACzB,oBAAM,CAAC,EAAE,OAAO,WAAW,CAAC,IAAI;AAChC,kBAAI,YAAY;AACd,oBAAI,KAAK,UAAU,GAAG;AACpB,0BAAQ,IAAI,IAAI;AAAA,gBAClB,OAAO;AACL,6BAAW,KAAK,MAAM;AACpB,0BAAM,OAAO,KAAK,CAAC;AACnB,wBAAI,gBAAgB,KAAK,QAAQ,kBAAkB;AACjD,0BAAI,MAAM,QAAQ,IAAI,UAAU,GAAG;AACjC,gCAAQ,IAAI,IAAI;AAAA,sBAClB;AACA;AAAA,oBACF;AAAA,kBACF;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK;AAAA,UACL,KAAK,gBAAgB;AACnB,gBAAI,MAAM;AACR,mBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBAC5B,6BAA6B,OAAO;AAAA,gBACpC;AAAA,cACF,CAAC;AAAA,YACH;AACA;AAAA,UACF;AAAA,UACA,KAAK;AAAA,UACL,KAAK,gBAAgB;AAEnB;AAAA,UACF;AAAA;AAAA,UAEA,KAAK,YAAY;AACf,gBAAI,MAAM;AACR,mBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBAC5B,yBAAyB,OAAO;AAAA,gBAChC;AAAA,cACF,CAAC;AAAA,YACH;AACA;AAAA,UACF;AAAA,UACA,SAAS;AACP,gBAAI,CAAC,SAAS;AACZ,mBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBAC5B,yBAAyB,OAAO;AAAA,gBAChC;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ,SAAS;AAAA,QACf,KAAK;AAAA,QACL,KAAK,QAAQ;AACX,eAAK,cAAc,OAAO,cAAc,WACpC,KAAK,aAAa,MAAM,GAAG;AAC7B,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,cAAc;AACjB,eAAK,cAAc,OAAO,cAAc,WACpC,KAAK,aAAa,MAAM,GAAG;AAC7B,kBAAM,EAAE,MAAM,QAAQ,SAAS,IAAI,IAAI,IAAI,KAAK,UAAU,GAAG;AAC7D,kBAAM,UAAU,IAAI,IAAI,KAAK,aAAa,MAAM,GAAG,IAAI;AACvD,gBAAI,QAAQ,WAAW,UAAU,QAAQ,aAAa,UAAU;AAC9D,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AAEd;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,gBAAM,EAAE,QAAQ,KAAK,IAAI,KAAK,UAAU,CAAC;AACzC,cAAI,oCAAoC,KAAK,IAAI,KAC7C,KAAK,SAAS,MAAM,GAAG;AACzB,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,gBAAM,EAAE,SAAS,QAAQ,KAAK,IAAI,KAAK,UAAU,CAAC;AAClD,cAAI,SAAS,eAAe,UAAU,UAClC,KAAK,SAAS,MAAM,GAAG;AACzB,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,UAAU;AACb,gBAAM,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,UAAU,GAAG;AAC3C,cAAI,KAAK,MAAM,SAAS,IAAI,KAAK,EAAE,MAC/B,KAAK,UAAU,SAAS,IAAI,GAAG;AACjC,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,iBAAiB;AACpB,gBAAM,EAAE,KAAK,IAAI,IAAI,IAAI,KAAK,UAAU,GAAG;AAC3C,cAAI,MAAM;AACR,kBAAM,KAAK,KAAK,QAAQ,MAAM,EAAE;AAChC,gBAAI,UAAU,KAAK,UAAU,eAAe,EAAE;AAC9C,mBAAO,SAAS;AACd,kBAAI,YAAY,MAAM;AACpB,wBAAQ,IAAI,IAAI;AAChB;AAAA,cACF;AACA,wBAAU,QAAQ;AAAA,YACpB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,KAAK,MAAM,aAAa,cAAc;AACxC,gBAAI,CAAC,KAAK,WAAW,SAAS,KAAK,OAAO;AACxC,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF,WAAW,SAAS,KAAK,UAAU,iBAAiB;AAClD,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,SAAS,KAAK,UAAU,iBAAiB,gBAAgB,IAAI,GAAG;AAClE,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,iBAAiB;AACpB,cAAI,SAAS,KAAK,UAAU,iBAAiB,gBAAgB,IAAI,GAAG;AAClE,gBAAI;AACJ,gBAAI,eAAe,IAAI,GAAG;AACxB,qBAAO;AAAA,YACT,WAAW,KAAK,QAAQ;AACtB,oBAAM,EAAE,eAAe,QAAQ,YAAY,IAAI,KAAK;AACpD,kBAAI,gBAAgB,MAAM;AACxB,oBAAI,eAAe,aAAa,GAAG;AACjC,yBAAO;AAAA,gBACT,WAAW,KAAK,QAAQ;AACtB,wBAAM;AAAA,oBACJ,KAAK;AAAA,oBAAU,QAAQ;AAAA,oBAAa,MAAM;AAAA,kBAC5C,IAAI,KAAK;AAET,sBAAI,gBAAgB,eAAe;AACjC,wBAAI,KAAK,sBAAsB,MAAM;AACnC,6BAAO;AAAA,oBACT,WAAW,gBAAgB,KAAK,mBAAmB;AACjD,6BAAO;AAAA,oBACT;AAAA,kBACF,WAAW,aAAa,OAAO;AAC7B,wBAAK,cAAc,aAAa,gBAAgB,QAC3C,cAAc,WAAW,gBAAgB,MAAO;AACnD,0BAAI,gBAAgB,aAAa;AAC/B,4BAAI,KAAK,sBAAsB,MAAM;AACnC,iCAAO;AAAA,wBACT,WAAW,gBAAgB,KAAK,qBACrB,kBAAkB,MAAM;AACjC,iCAAO;AAAA,wBACT;AAAA,sBACF,OAAO;AACL,+BAAO;AAAA,sBACT;AAAA,oBACF;AAAA,kBACF,WAAW,UAAU;AACnB,yBAAK,cAAc,aAAa,cAAc,YAC1C,gBAAgB,MAAM;AACxB,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF,WAAW,kBAAkB,QAClB,kBAAkB,KAAK,mBAAmB;AACnD,yBAAO;AAAA,gBACT;AAAA,cACF;AAAA,YACF;AACA,gBAAI,MAAM;AACR,mBAAK,oBAAoB;AACzB,sBAAQ,IAAI,IAAI;AAAA,YAClB,WAAW,KAAK,sBAAsB,MAAM;AAC1C,mBAAK,oBAAoB;AAAA,YAC3B;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,cAAI;AACJ,cAAI,UAAU,KAAK,UAAU;AAC7B,cAAI,gBAAgB,OAAO,GAAG;AAC5B,mBAAO,SAAS;AACd,kBAAI,YAAY,MAAM;AACpB,uBAAO;AACP;AAAA,cACF;AACA,wBAAU,QAAQ;AAAA,YACpB;AAAA,UACF;AACA,cAAI,MAAM;AACR,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,UAAU;AACb,cAAI,cAAc,aAAa,cAAc,UAAU;AACrD,gBAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,kBAAI,YAAY,QAAQ;AACtB,wBAAQ,IAAI,IAAI;AAAA,cAClB;AAAA,YACF,WAAW,YAAY,UAAU;AAC/B,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,WAAW;AACd,gBAAM,OAAO,CAAC,GAAG,gBAAgB,YAAY,YAAY,QAAQ;AACjE,cAAI,KAAK,SAAS,SAAS,KACvB,gBAAgB,MAAM,EAAE,gBAAgB,KAAK,CAAC,GAAG;AACnD,gBAAI;AACJ,gBAAI,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,yBAAW;AAAA,YACb,WAAW,KAAK,cAAc,UAAU;AACtC,kBAAI,WAAW,cAAc,eACxB,WAAW,YACX,WAAW,aAAa,UAAU,IAAI;AACzC,2BAAW;AAAA,cACb;AAAA,YACF,WAAW,KAAK,cAAc,YAAY;AACxC,kBAAI,SAAS;AACb,qBAAO,QAAQ;AACb,oBAAI,OAAO,cAAc,eACpB,OAAO,YAAY,OAAO,aAAa,UAAU,IAAI;AACxD,sBAAI,UAAU,OAAO;AACrB,yBAAO,SAAS;AACd,wBAAI,QAAQ,cAAc,UAAU;AAClC;AAAA,oBACF;AACA,8BAAU,QAAQ;AAAA,kBACpB;AACA,sBAAI,SAAS;AACX,wBAAI,CAAC,QAAQ,SAAS,IAAI,GAAG;AAC3B,iCAAW;AAAA,oBACb;AAAA,kBACF,OAAO;AACL,+BAAW;AAAA,kBACb;AACA;AAAA,gBACF,WAAW,OAAO,cAAc,QAAQ;AACtC;AAAA,gBACF,WAAW,OAAO,YAAY,aAAa,cAAc;AACvD,sBAAI,OAAO,WAAW,cAAc,QAAQ;AAC1C;AAAA,kBACF,OAAO;AACL,6BAAS,OAAO;AAAA,kBAClB;AAAA,gBACF,OAAO;AACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AACA,gBAAI,UAAU;AACZ,kBAAI,YAAY,YAAY;AAC1B,wBAAQ,IAAI,IAAI;AAAA,cAClB;AAAA,YACF,WAAW,YAAY,WAAW;AAChC,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,cAAc;AACjB,cAAI;AACJ,cAAI;AACJ,kBAAQ,WAAW;AAAA,YACjB,KAAK,YAAY;AACf,kBAAI,KAAK,YAAY,KAAK,aAAa,UAAU,KAC7C,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,2BAAW;AAAA,cACb,OAAO;AACL,2BAAW;AAAA,cACb;AACA;AAAA,YACF;AAAA,YACA,KAAK,SAAS;AACZ,kBAAI,CAAC,KAAK,QAAQ,eAAe,SAAS,KAAK,IAAI,GAAG;AACpD,oBAAI,KAAK,YAAY,KAAK,aAAa,UAAU,KAC7C,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,6BAAW;AAAA,gBACb,OAAO;AACL,6BAAW;AAAA,gBACb;AAAA,cACF,OAAO;AACL,2BAAW;AAAA,cACb;AACA;AAAA,YACF;AAAA,YACA,SAAS;AACP,kBAAI,kBAAkB,IAAI,GAAG;AAC3B,2BAAW;AAAA,cACb,OAAO;AACL,2BAAW;AAAA,cACb;AAAA,YACF;AAAA,UACF;AACA,cAAI,UAAU;AACZ,gBAAI,YAAY,aAAa;AAC3B,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF,WAAW,YAAY,gBAAgB,UAAU;AAC/C,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,qBAAqB;AACxB,cAAI;AACJ,cAAI,KAAK,aAAa;AACpB,0BAAc,KAAK;AAAA,UACrB,WAAW,KAAK,aAAa,aAAa,GAAG;AAC3C,0BAAc,KAAK,aAAa,aAAa;AAAA,UAC/C;AACA,cAAI,OAAO,gBAAgB,YAAY,CAAC,SAAS,KAAK,WAAW,GAAG;AAClE,gBAAI;AACJ,gBAAI,cAAc,YAAY;AAC5B,2BAAa;AAAA,YACf,WAAW,cAAc,SAAS;AAChC,kBAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,sBAAM,OAAO,CAAC,GAAG,gBAAgB,QAAQ;AACzC,oBAAI,KAAK,SAAS,KAAK,aAAa,MAAM,CAAC,GAAG;AAC5C,+BAAa;AAAA,gBACf;AAAA,cACF,OAAO;AACL,6BAAa;AAAA,cACf;AAAA,YACF;AACA,gBAAI,cAAc,KAAK,UAAU,IAAI;AACnC,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AACd,gBAAM,WAAW,KAAK,aAAa,MAAM;AACzC,cAAK,KAAK,WAAW,cAAc,YAC7B,aAAa,cAAc,aAAa,YACzC,KAAK,YAAY,cAAc,UAAW;AAC7C,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,iBAAiB;AACpB,cAAK,KAAK,iBAAiB,cAAc,WACpC,KAAK,SAAS,cACd,cAAc,cAAc,CAAC,KAAK,aAAa,OAAO,GAAI;AAC7D,oBAAQ,IAAI,IAAI;AAAA,UAClB,WAAW,cAAc,WAAW,KAAK,SAAS,WACvC,CAAC,KAAK,aAAa,SAAS,GAAG;AACxC,kBAAM,WAAW,KAAK;AACtB,gBAAI,SAAS,KAAK;AAClB,mBAAO,QAAQ;AACb,kBAAI,OAAO,cAAc,QAAQ;AAC/B;AAAA,cACF;AACA,uBAAS,OAAO;AAAA,YAClB;AACA,gBAAI,CAAC,QAAQ;AACX,uBAAS,KAAK,UAAU;AAAA,YAC1B;AACA,kBAAM,SAAS,KAAK,kBAAkB,MAAM;AAC5C,gBAAI,UAAU,aAAa,QAAQ,MAAM;AACzC,sBAAU,OAAO,WAAW;AAC5B,gBAAI;AACJ,mBAAO,SAAS;AACd,kBAAI,QAAQ,cAAc,WACtB,QAAQ,aAAa,MAAM,MAAM,SAAS;AAC5C,oBAAI,QAAQ,aAAa,MAAM,GAAG;AAChC,sBAAI,QAAQ,aAAa,MAAM,MAAM,UAAU;AAC7C,8BAAU,CAAC,CAAC,QAAQ;AAAA,kBACtB;AAAA,gBACF,OAAO;AACL,4BAAU,CAAC,CAAC,QAAQ;AAAA,gBACtB;AACA,oBAAI,SAAS;AACX;AAAA,gBACF;AAAA,cACF;AACA,wBAAU,OAAO,SAAS;AAAA,YAC5B;AACA,gBAAI,CAAC,SAAS;AACZ,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AAEd,gBAAM,YAAY,CAAC,YAAY,OAAO;AACtC,gBAAM,YAAY,CAAC,UAAU,OAAO;AACpC,gBAAM,aAAa,CAAC,SAAS,QAAQ;AACrC,gBAAM,WAAW,KAAK,aAAa,MAAM;AACzC,cAAK,cAAc,YACd,EAAE,KAAK,aAAa,MAAM,KAAK,UAAU,SAAS,QAAQ,MAC1D,cAAc,WAAW,KAAK,aAAa,MAAM,KACjD,WAAW,SAAS,QAAQ,GAAI;AACnC,gBAAI,OAAO,KAAK;AAChB,mBAAO,MAAM;AACX,kBAAI,KAAK,cAAc,QAAQ;AAC7B;AAAA,cACF;AACA,qBAAO,KAAK;AAAA,YACd;AACA,gBAAI,MAAM;AACR,oBAAM,SAAS,KAAK,kBAAkB,IAAI;AAC1C,kBAAI,UAAU,aAAa,MAAM,MAAM;AACvC,wBAAU,OAAO,WAAW;AAC5B,qBAAO,SAAS;AACd,sBAAM,WAAW,QAAQ;AACzB,sBAAM,eAAe,QAAQ,aAAa,MAAM;AAChD,oBAAI;AACJ,oBAAI,aAAa,UAAU;AACzB,sBAAI,EAAE,QAAQ,aAAa,MAAM,KAC/B,UAAU,SAAS,YAAY;AAAA,gBACnC,WAAW,aAAa,SAAS;AAC/B,sBAAI,QAAQ,aAAa,MAAM,KAC7B,WAAW,SAAS,YAAY;AAAA,gBACpC;AACA,oBAAI,GAAG;AACL,sBAAI,YAAY,MAAM;AACpB,4BAAQ,IAAI,IAAI;AAAA,kBAClB;AACA;AAAA,gBACF;AACA,0BAAU,OAAO,SAAS;AAAA,cAC5B;AAAA,YACF;AAAA,UAEF,WAAW,cAAc,WAAW,KAAK,aAAa,MAAM,KACjD,UAAU,SAAS,QAAQ,KAC3B,KAAK,aAAa,SAAS,GAAG;AACvC,oBAAQ,IAAI,IAAI;AAAA,UAElB,WAAW,cAAc,YAAY,KAAK,aAAa,UAAU,GAAG;AAClE,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,WAAW;AACd,gBAAM,OAAO,CAAC,GAAG,gBAAgB,MAAM;AACvC,cAAI,KAAK,SAAS,SAAS,GAAG;AAC5B,gBAAI;AACJ,gBAAI,KAAK,cAAc,GAAG;AACxB,kBAAI,KAAK,aAAa,GAAG;AACvB,oBAAI,KAAK,aAAa,KAAK,MAAM,QAAQ;AACvC,0BAAQ;AAAA,gBACV;AAAA,cACF,OAAO;AACL,wBAAQ;AAAA,cACV;AAAA,YACF;AACA,gBAAI,OAAO;AACT,kBAAI,YAAY,SAAS;AACvB,wBAAQ,IAAI,IAAI;AAAA,cAClB;AAAA,YACF,WAAW,YAAY,WAAW;AAChC,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF,WAAW,cAAc,YAAY;AACnC,kBAAM,SAAS,KAAK,kBAAkB,IAAI;AAC1C,gBAAI,UAAU,aAAa,MAAM,MAAM;AACvC,sBAAU,OAAO,WAAW;AAC5B,gBAAI;AACJ,gBAAI,CAAC,SAAS;AACZ,sBAAQ;AAAA,YACV,OAAO;AACL,qBAAO,SAAS;AACd,oBAAI,KAAK,SAAS,QAAQ,SAAS,GAAG;AACpC,sBAAI,QAAQ,cAAc,GAAG;AAC3B,wBAAI,QAAQ,aAAa,GAAG;AAC1B,8BAAQ,QAAQ,aAAa,QAAQ,MAAM;AAAA,oBAC7C,OAAO;AACL,8BAAQ;AAAA,oBACV;AAAA,kBACF,OAAO;AACL,4BAAQ;AAAA,kBACV;AACA,sBAAI,CAAC,OAAO;AACV;AAAA,kBACF;AAAA,gBACF;AACA,0BAAU,OAAO,SAAS;AAAA,cAC5B;AAAA,YACF;AACA,gBAAI,OAAO;AACT,kBAAI,YAAY,SAAS;AACvB,wBAAQ,IAAI,IAAI;AAAA,cAClB;AAAA,YACF,WAAW,YAAY,WAAW;AAChC,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,gBAAgB;AACnB,gBAAM,OAAO,CAAC,GAAG,gBAAgB,UAAU,OAAO;AAClD,gBAAM,WAAW,KAAK,aAAa,MAAM;AACzC,cAAI,cAAc,WACd,EAAE,KAAK,YAAY,KAAK,aAAa,UAAU,MAC/C,EAAE,KAAK,YAAY,KAAK,aAAa,UAAU,MAC/C,KAAK,SAAS,QAAQ,GAAG;AAC3B,kBAAM,SACJ,KAAK,SAAS,kBAAkB,KAAK,SAAS;AAChD,gBAAI,YAAY,kBAAkB,QAAQ;AACxC,sBAAQ,IAAI,IAAI;AAAA,YAClB,WAAW,YAAY,cAAc,CAAC,WAC1B,KAAK,aAAa,KAAK,KAAK,KAAK,aAAa,KAAK,KACpD,aAAa,UAAU;AAChC,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,YAAY;AACf,cAAI;AACJ,cAAI;AACJ,cAAI,cAAc,YAAY,cAAc,YAAY;AACtD,gBAAI,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,yBAAW;AAAA,YACb,OAAO;AACL,yBAAW;AAAA,YACb;AAAA,UACF,WAAW,cAAc,SAAS;AAChC,gBAAI,KAAK,aAAa,MAAM,GAAG;AAC7B,oBAAM,OAAO,CAAC,GAAG,gBAAgB,YAAY,QAAQ,OAAO;AAC5D,oBAAM,WAAW,KAAK,aAAa,MAAM;AACzC,kBAAI,KAAK,SAAS,QAAQ,GAAG;AAC3B,oBAAI,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AAClD,6BAAW;AAAA,gBACb,OAAO;AACL,6BAAW;AAAA,gBACb;AAAA,cACF,OAAO;AACL,2BAAW;AAAA,cACb;AAAA,YACF,WAAW,KAAK,YAAY,KAAK,aAAa,UAAU,GAAG;AACzD,yBAAW;AAAA,YACb,OAAO;AACL,yBAAW;AAAA,YACb;AAAA,UACF;AACA,cAAI,YAAY,cAAc,UAAU;AACtC,oBAAQ,IAAI,IAAI;AAAA,UAClB,WAAW,YAAY,cAAc,UAAU;AAC7C,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,QAAQ;AACX,cAAI,SAAS,KAAK,UAAU,iBAAiB;AAC3C,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,KAAK,cAAc,GAAG;AACxB,kBAAM,SAAS,KAAK,kBAAkB,MAAM;AAAA,cAC1C,OAAO;AAAA,cACP,YAAY;AAAA,YACd,CAAC;AACD,gBAAI,UAAU,OAAO,WAAW;AAChC,gBAAI;AACJ,mBAAO,SAAS;AACd,qBAAO,QAAQ,aAAa,gBAC1B,QAAQ,aAAa;AACvB,kBAAI,CAAC,MAAM;AACT;AAAA,cACF;AACA,wBAAU,OAAO,YAAY;AAAA,YAC/B;AACA,gBAAI,MAAM;AACR,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UACF,OAAO;AACL,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,eAAe;AAClB,cAAK,cAAc,SAAS,WAAW,qBACnC,SAAS,KAAK,OAAO;AACvB,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,cAAc;AACjB,cAAK,cAAc,SAAS,WAAW,oBACnC,SAAS,KAAK,OAAO;AACvB,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,cAAc;AACjB,cAAK,cAAc,SAAS,WAAW,qBAClC,SAAS,WAAW,oBAAqB,SAAS,KAAK,OAAO;AACjE,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,iBAAiB;AACpB,cAAI,YAAY;AACd,kBAAM,CAAC,KAAK,IAAI,KAAK,kBAAkB;AAAA,cACrC,GAAG;AAAA,cACH,GAAG;AAAA,YACL,GAAG,IAAI;AACP,gBAAI,OAAO;AACT,sBAAQ,IAAI,KAAK;AAAA,YACnB;AAAA,UACF,WAAW,SAAS,KAAK,OAAO;AAC9B,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,cAAI,YAAY;AACd,kBAAM,CAAC,KAAK,IAAI,KAAK,kBAAkB;AAAA,cACrC,GAAG;AAAA,cACH,GAAG;AAAA,cACH,SAAS;AAAA,YACX,GAAG,IAAI;AACP,gBAAI,OAAO;AACT,sBAAQ,IAAI,KAAK;AAAA,YACnB;AAAA,UACF,WAAW,SAAS,KAAK,OAAO;AAC9B,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,cAAI,YAAY;AACd,kBAAM,CAAC,KAAK,IAAI,KAAK,kBAAkB;AAAA,cACrC,GAAG;AAAA,cACH,GAAG;AAAA,YACL,GAAG,IAAI;AACP,gBAAI,UAAU,MAAM;AAClB,oBAAM,CAAC,KAAK,IAAI,KAAK,kBAAkB;AAAA,gBACrC,GAAG;AAAA,gBACH,GAAG;AAAA,gBACH,SAAS;AAAA,cACX,GAAG,IAAI;AACP,kBAAI,UAAU,MAAM;AAClB,wBAAQ,IAAI,IAAI;AAAA,cAClB;AAAA,YACF;AAAA,UACF,WAAW,SAAS,KAAK,OAAO;AAC9B,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,WAAW;AACd,cAAI,KAAK,aAAa,IAAI,KAAK,UAAU,SAAS,GAAG,GAAG;AACtD,gBAAI,gBAAgB,IAAI,GAAG;AACzB,sBAAQ,IAAI,IAAI;AAAA,YAClB;AAAA,UAEF,WAAW,gBAAgB,KAAK,QAAQ,eAC7B,gBAAgB,KAAK,QAAQ,YAAY;AAClD,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,cAAI,KAAK,WAAW,UAAU,IAAI,GAAG;AACnC,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,KAAK,gBAAgB;AAEnB;AAAA,QACF;AAAA;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,cAAc;AACjB,cAAI,MAAM;AACR,iBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,cAC5B,gCAAgC,OAAO;AAAA,cACvC;AAAA,YACF,CAAC;AAAA,UACH;AACA;AAAA,QACF;AAAA;AAAA,QAEA,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK;AAAA,QACL,KAAK,oBAAoB;AACvB,cAAI,MAAM;AACR,iBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,cAC5B,6BAA6B,OAAO;AAAA,cACpC;AAAA,YACF,CAAC;AAAA,UACH;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,cAAI,QAAQ,WAAW,UAAU,GAAG;AAClC,gBAAI,MAAM;AACR,mBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBAC5B,6BAA6B,OAAO;AAAA,gBACpC;AAAA,cACF,CAAC;AAAA,YACH;AAAA,UACF,WAAW,CAAC,SAAS;AACnB,iBAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,cAC5B,yBAAyB,OAAO;AAAA,cAChC;AAAA,YACF,CAAC;AAAA,UACH;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,4BAA4B,KAAK,MAAM;AACrC,UAAM,EAAE,UAAU,aAAa,MAAM,QAAQ,IAAI;AACjD,QAAI,MAAM,QAAQ,WAAW,GAAG;AAC9B,UAAI,YAAY,WAAW,GAAG;AAC5B,cAAM,UAAM,2BAAY,GAAG;AAC3B,eAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,UACnC,oBAAoB,GAAG;AAAA,UACvB;AAAA,QACF,CAAC;AAAA,MACH;AACA,YAAM,EAAE,SAAS,IAAI,QAAQ,YAAY,CAAC,CAAC;AAC3C,YAAM,CAAC,MAAM,IAAI;AACjB,YAAM,CAAC,GAAG,MAAM,IAAI;AACpB,YAAM,EAAE,KAAK,IAAI;AACjB,UAAI,YAAY,QAAQ;AACtB,YAAI;AACJ,mBAAW,QAAQ,QAAQ;AACzB,gBAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,cAAI,aAAa,YAAY;AAC3B,kBAAM,UAAM,2BAAY,GAAG;AAC3B,mBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,cACnC,oBAAoB,GAAG;AAAA,cACvB;AAAA,YACF,CAAC;AAAA,UACH;AACA,iBAAO,KAAK,eAAe,MAAM,IAAI,EAAE,IAAI,IAAI;AAC/C,cAAI,CAAC,MAAM;AACT;AAAA,UACF;AAAA,QACF;AACA,YAAI,MAAM;AACR,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT,WAAW,YAAY,gBAAgB;AACrC,YAAI,SAAS;AACb,YAAI;AACJ,eAAO,QAAQ;AACb,qBAAW,QAAQ,QAAQ;AACzB,kBAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,gBAAI,aAAa,YAAY;AAC3B,oBAAM,UAAM,2BAAY,GAAG;AAC3B,qBAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,gBACnC,oBAAoB,GAAG;AAAA,gBACvB;AAAA,cACF,CAAC;AAAA,YACH;AACA,mBAAO,KAAK,eAAe,MAAM,MAAM,EAAE,IAAI,MAAM;AACnD,gBAAI,CAAC,MAAM;AACT;AAAA,YACF;AAAA,UACF;AACA,cAAI,MAAM;AACR;AAAA,UACF,OAAO;AACL,qBAAS,OAAO;AAAA,UAClB;AAAA,QACF;AACA,YAAI,MAAM;AACR,iBAAO;AAAA,QACT;AACA,eAAO;AAAA,MACT;AAAA,IACF,WAAW,YAAY,QAAQ;AAC7B,aAAO;AAAA,IACT;AACA,WAAO,KAAK,QAAQ,IAAI,KAAK,QAAQ;AAAA,MACnC,qBAAqB,OAAO;AAAA,MAC5B;AAAA,IACF,CAAC;AAAA,EACH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,eAAe,KAAK,MAAM,MAAM,CAAC,GAAG;AAClC,UAAM,EAAE,MAAM,QAAQ,IAAI;AAC1B,UAAM,UAAU,iBAAiB,IAAI,IAAI;AACzC,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,KAAK,aAAa,cAAc;AAClC,cAAQ,SAAS;AAAA,QACf,KAAK,eAAe;AAClB,gBAAM,MAAM,uBAAuB,KAAK,MAAM,GAAG;AACjD,cAAI,KAAK;AACP,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,cAAI,KAAK,OAAO,SAAS;AACvB,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,gBAAgB;AACnB,cAAI,KAAK,UAAU,SAAS,OAAO,GAAG;AACpC,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK,mBAAmB;AACtB,gBAAM,QAAQ,KAAK,0BAA0B,KAAK,MAAM,GAAG;AAC3D,iBAAO;AAAA,QACT;AAAA,QACA,KAAK,eAAe;AAClB,gBAAM,MAAM,kBAAkB,KAAK,MAAM,GAAG;AAC5C,cAAI,KAAK;AACP,oBAAQ,IAAI,IAAI;AAAA,UAClB;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,cAAI;AACF,kBAAM,EAAE,MAAM,IAAI;AAClB,gBAAI,OAAO;AACT,oBAAM,UAAM,2BAAY,GAAG;AAC3B,mBAAK,eAAe,KAAK,GAAG;AAC5B,sBAAQ,IAAI,IAAI;AAAA,YAClB,OAAO;AACL,yCAA2B,SAAS,SAAS,GAAG;AAAA,YAClD;AAAA,UACF,SAAS,GAAG;AACV,iBAAK,QAAQ,CAAC;AAAA,UAChB;AAAA,QACF;AAAA,MACF;AAAA,IACF,WAAW,KAAK,WAAW,YAAY,qBAC5B,KAAK,aAAa,wBAAwB;AACnD,UAAI,YAAY,SAAS,OAAO,GAAG;AACjC,YAAI,eAAe;AACnB,cAAM,QAAQ,KAAK,0BAA0B,KAAK,MAAM,GAAG;AAC3D,eAAO;AAAA,MACT,WAAW,YAAY,UAAU,YAAY,gBAAgB;AAC3D,cAAM,MAAM,KAAK,4BAA4B,KAAK,MAAM,GAAG;AAC3D,YAAI,KAAK;AACP,eAAK,oBAAoB;AACzB,kBAAQ,IAAI,GAAG;AAAA,QACjB;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,aAAa,QAAQ,MAAM,MAAM,CAAC,GAAG;AACnC,QAAI;AACJ,QAAI,KAAK,aAAa;AACpB,eAAS,KAAK,mBAAmB,IAAI,MAAM;AAAA,IAC7C,OAAO;AACL,eAAS,KAAK,SAAS,IAAI,MAAM;AAAA,IACnC;AACA,QAAI,UAAU,OAAO,IAAI,IAAI,GAAG;AAC9B,YAAM,EAAE,QAAQ,IAAI,OAAO,IAAI,IAAI;AACnC,aAAO;AAAA,IACT,OAAO;AACL,UAAI,YAAY;AAChB,YAAM,WAAW,CAAC,GAAG,gBAAgB,YAAY,MAAM;AACvD,YAAM,aAAa,CAAC,YAAY,WAAW,OAAO,QAAQ,OAAO;AACjE,UAAI,KAAK,aAAa,gBAAgB,SAAS,SAAS,KAAK,SAAS,GAAG;AACvE,oBAAY;AAAA,MACd;AACA,UAAI;AACJ,iBAAW,QAAQ,QAAQ;AACzB,gBAAQ,KAAK,MAAM;AAAA,UACjB,KAAK;AAAA,UACL,KAAK,aAAa;AAChB,wBAAY;AACZ;AAAA,UACF;AAAA,UACA,KAAK,mBAAmB;AACtB,gBAAI,WAAW,SAAS,KAAK,IAAI,GAAG;AAClC,0BAAY;AAAA,YACd;AACA;AAAA,UACF;AAAA,UACA;AAAA,QACF;AACA,eAAO,KAAK,eAAe,MAAM,MAAM,GAAG,EAAE,IAAI,IAAI;AACpD,YAAI,CAAC,MAAM;AACT;AAAA,QACF;AAAA,MACF;AACA,UAAI,WAAW;AACb,YAAI,CAAC,QAAQ;AACX,mBAAS,oBAAI,QAAQ;AAAA,QACvB;AACA,eAAO,IAAI,MAAM;AAAA,UACf,SAAS;AAAA,QACX,CAAC;AACD,YAAI,KAAK,aAAa;AACpB,eAAK,mBAAmB,IAAI,QAAQ,MAAM;AAAA,QAC5C,OAAO;AACL,eAAK,SAAS,IAAI,QAAQ,MAAM;AAAA,QAClC;AAAA,MACF;AACA,aAAO;AAAA,IACT;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,qBAAqB,QAAQ,UAAU,MAAM,CAAC,GAAG;AAC/C,UAAM,CAAC,MAAM,GAAG,YAAY,IAAI;AAChC,UAAM,WAAW,aAAa,SAAS;AACvC,UAAM,EAAE,MAAM,SAAS,IAAI;AAC3B,UAAM,WAAW,iBAAiB,KAAK,IAAI;AAC3C,UAAM,QAAQ,oBAAI,IAAI;AACtB,QAAI,UAAU;AACd,QAAI,KAAK,WAAW,SAAS,aAAa,cAAc;AACtD,gBAAU;AAAA,IACZ,OAAO;AACL,cAAQ,UAAU;AAAA,QAChB,KAAK,qBAAqB;AACxB,qCAA2B,UAAU,UAAU,GAAG;AAClD;AAAA,QACF;AAAA,QACA,KAAK,aAAa;AAChB,cAAI,KAAK,MAAM,aAAa,cAAc;AACxC,sBAAU;AAAA,UACZ,OAAO;AACL,kBAAM,OAAO,KAAK,MAAM,eAAe,QAAQ;AAC/C,gBAAI,QAAQ,SAAS,YAAY,SAAS,SAAS,IAAI,GAAG;AACxD,kBAAI,UAAU;AACZ,sBAAM,OAAO,KAAK,aAAa,cAAc,MAAM,GAAG;AACtD,oBAAI,MAAM;AACR,wBAAM,IAAI,IAAI;AAAA,gBAChB;AAAA,cACF,OAAO;AACL,sBAAM,IAAI,IAAI;AAAA,cAChB;AAAA,YACF;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,QAAI,SAAS;AACX,YAAM,SAAS,KAAK,kBAAkB,QAAQ;AAC9C,UAAI,UAAU,aAAa,UAAU,MAAM;AAC3C,gBAAU,OAAO,WAAW;AAC5B,aAAO,SAAS;AACd,cAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,YAAI,MAAM;AACR,gBAAM,IAAI,OAAO;AAAA,QACnB;AACA,kBAAU,OAAO,SAAS;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,MAAM,MAAM,MAAM,CAAC,GAAG;AACrC,UAAM,EAAE,OAAO,OAAO,IAAI;AAC1B,UAAM,EAAE,MAAM,UAAU,IAAI;AAC5B,UAAM,EAAE,WAAW,IAAI;AACvB,UAAM,EAAE,IAAI,IAAI;AAChB,UAAM,UAAU,oBAAI,IAAI;AACxB,QAAI,QAAQ,UAAU;AACpB,cAAQ,WAAW;AAAA,QACjB,KAAK,KAAK;AACR,gBAAM,UAAU,KAAK;AACrB,cAAI,SAAS;AACX,kBAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,gBAAI,MAAM;AACR,sBAAQ,IAAI,OAAO;AAAA,YACrB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,KAAK;AACR,cAAI,YAAY;AACd,gBAAI,UAAU,KAAK;AACnB,mBAAO,SAAS;AACd,oBAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,kBAAI,MAAM;AACR,wBAAQ,IAAI,OAAO;AAAA,cACrB;AACA,wBAAU,QAAQ;AAAA,YACpB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,KAAK;AACR,cAAI,UAAU,KAAK;AACnB,iBAAO,SAAS;AACd,kBAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,gBAAI,MAAM;AACR,sBAAQ,IAAI,OAAO;AAAA,YACrB;AACA,sBAAU,QAAQ;AAAA,UACpB;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,gBAAM,QAAQ,KAAK,qBAAqB,QAAQ,MAAM,GAAG;AACzD,cAAI,MAAM,MAAM;AACd,mBAAO;AAAA,UACT;AAAA,QACF;AAAA,MACF;AAAA,IACF,OAAO;AACL,cAAQ,WAAW;AAAA,QACjB,KAAK,KAAK;AACR,gBAAM,UAAU,KAAK;AACrB,cAAI,SAAS;AACX,kBAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,gBAAI,MAAM;AACR,sBAAQ,IAAI,OAAO;AAAA,YACrB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,KAAK;AACR,cAAI,YAAY;AACd,gBAAI,UAAU,WAAW;AACzB,mBAAO,SAAS;AACd,kBAAI,YAAY,MAAM;AACpB;AAAA,cACF,OAAO;AACL,sBAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,oBAAI,MAAM;AACR,0BAAQ,IAAI,OAAO;AAAA,gBACrB;AAAA,cACF;AACA,wBAAU,QAAQ;AAAA,YACpB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK,KAAK;AACR,cAAI,YAAY;AACd,kBAAM,OAAO,KAAK,aAAa,QAAQ,YAAY,GAAG;AACtD,gBAAI,MAAM;AACR,sBAAQ,IAAI,UAAU;AAAA,YACxB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,gBAAM,MAAM,CAAC;AACb,cAAI,UAAU;AACd,iBAAO,SAAS;AACd,kBAAM,OAAO,KAAK,aAAa,QAAQ,SAAS,GAAG;AACnD,gBAAI,MAAM;AACR,kBAAI,KAAK,OAAO;AAAA,YAClB;AACA,sBAAU,QAAQ;AAAA,UACpB;AACA,cAAI,IAAI,QAAQ;AACd,mBAAO,IAAI,IAAI,IAAI,QAAQ,CAAC;AAAA,UAC9B;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,aAAa,QAAQ,MAAM,MAAM,CAAC,GAAG;AACnC,UAAM,EAAE,OAAO,WAAW,IAAI;AAC9B,QAAI,CAAC,KAAK,aAAa;AACrB,WAAK,cAAc,KAAK,kBAAkB,KAAK,KAAK;AAAA,IACtD;AACA,UAAM,SAAS,KAAK;AACpB,UAAM,QAAQ,CAAC;AACf,QAAI,UAAU,aAAa,MAAM,QAAQ,CAAC,CAAC,KAAK;AAChD,QAAI,WAAW,YAAY,KAAK,OAAO;AACrC,UAAI,QAAQ,aAAa,cAAc;AACrC,kBAAU,OAAO,SAAS;AAAA,MAC5B,WAAW,YAAY,MAAM;AAC3B,YAAI,YAAY,KAAK,OAAO;AAC1B,oBAAU,OAAO,SAAS;AAAA,QAC5B;AAAA,MACF;AACA,aAAO,SAAS;AACd,YAAI,YAAY,KAAK,OAAO;AAC1B;AAAA,QACF;AACA,cAAM,UAAU,KAAK,aAAa,QAAQ,SAAS;AAAA,UACjD,MAAM,KAAK;AAAA,QACb,CAAC;AACD,YAAI,SAAS;AACX,gBAAM,KAAK,OAAO;AAClB,cAAI,eAAe,YAAY;AAC7B;AAAA,UACF;AAAA,QACF;AACA,kBAAU,OAAO,SAAS;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAaA,gBAAgB,QAAQ,MAAM,MAAM,CAAC,GAAG;AACtC,UAAM,EAAE,OAAO,SAAS,WAAW,IAAI;AACvC,UAAM,SAAS,KAAK;AACpB,QAAI,SAAS;AACX,YAAM,eAAe,KAAK,aAAa,QAAQ,KAAK,OAAO,GAAG;AAC9D,UAAI,aAAa,QAAQ;AACvB,eAAO;AAAA,MACT;AAAA,IACF;AACA,UAAM,QAAQ,CAAC;AACf,QAAI,UAAU,aAAa,MAAM,QAAQ,CAAC,CAAC,KAAK;AAChD,QAAI,SAAS;AACX,UAAI,QAAQ,aAAa,cAAc;AACrC,kBAAU,OAAO,SAAS;AAAA,MAC5B,WAAW,YAAY,MAAM;AAC3B,YAAI,YAAY,KAAK,OAAO;AAC1B,oBAAU,OAAO,SAAS;AAAA,QAC5B;AAAA,MACF;AACA,aAAO,SAAS;AACd,cAAM,UAAU,KAAK,aAAa,QAAQ,SAAS;AAAA,UACjD,MAAM,KAAK;AAAA,QACb,CAAC;AACD,YAAI,SAAS;AACX,gBAAM,KAAK,OAAO;AAClB,cAAI,eAAe,YAAY;AAC7B;AAAA,UACF;AAAA,QACF;AACA,kBAAU,OAAO,SAAS;AAAA,MAC5B;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,WAAW,QAAQ,QAAQ,OAAO;AAChC,UAAM,QAAQ,CAAC;AACf,QAAI,WAAW;AACf,UAAM,OAAO,KAAK,aAAa,QAAQ,KAAK,OAAO;AAAA,MACjD;AAAA,MACA,MAAM,KAAK;AAAA,IACb,CAAC;AACD,QAAI,MAAM;AACR,YAAM,KAAK,KAAK,KAAK;AACrB,iBAAW;AAAA,IACb;AACA,WAAO,CAAC,OAAO,UAAU,KAAK,cAAc;AAAA,EAC9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,YAAY,QAAQ,KAAK;AACvB,UAAM,EAAE,QAAQ,IAAI;AACpB,UAAM,QAAQ,CAAC;AACf,QAAI,WAAW;AACf,QAAI,OAAO,KAAK,aAAa,QAAQ,KAAK,OAAO;AAAA,MAC/C,MAAM,KAAK;AAAA,IACb,CAAC;AACD,QAAI,MAAM;AACR,YAAM,KAAK,KAAK,KAAK;AACrB,iBAAW;AAAA,IACb;AACA,QAAI,CAAC,QAAQ,SAAS;AACpB,UAAI,UAAU,KAAK,MAAM;AACzB,aAAO,SAAS;AACd,eAAO,KAAK,aAAa,QAAQ,SAAS;AAAA,UACxC,MAAM,KAAK;AAAA,QACb,CAAC;AACD,YAAI,MAAM;AACR,gBAAM,KAAK,OAAO;AAClB,qBAAW;AAAA,QACb;AACA,YAAI,QAAQ,YAAY;AACtB,oBAAU,QAAQ;AAAA,QACpB,OAAO;AACL;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,WAAO,CAAC,OAAO,QAAQ;AAAA,EACzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,gBAAgB,MAAM,YAAY,MAAM,CAAC,GAAG;AAC1C,UAAM,EAAE,OAAO,IAAI;AACnB,UAAM,CAAC,MAAM,GAAG,YAAY,IAAI;AAChC,UAAM,WAAW,aAAa,SAAS;AACvC,UAAM,EAAE,MAAM,UAAU,MAAM,SAAS,IAAI;AAC3C,UAAM,EAAE,UAAU,OAAO,MAAM,SAAS,IAAI;AAC5C,UAAM,UAAU,QAAQ,YAAY,KAAK,MAAM,aAAa,gBAC1D,KAAK,UAAU,KAAK;AACtB,QAAI,QAAQ,CAAC;AACb,QAAI,WAAW;AACf,QAAI,UAAU;AACd,YAAQ,UAAU;AAAA,MAChB,KAAK,qBAAqB;AACxB,YAAI,eAAe,eAAe,KAAK,QAAQ;AAC7C,gBAAM,UAAM,2BAAY,IAAI;AAC5B,eAAK,eAAe,KAAK,GAAG;AAC5B,cAAI,aAAa,QAAQ;AACvB,aAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,cAAc,KAAK,MAAM;AAAA,UAC/D,OAAO;AACL,kBAAM,KAAK,KAAK,KAAK;AACrB,uBAAW;AAAA,UACb;AAAA,QACF,OAAO;AACL,qCAA2B,UAAU,UAAU;AAAA,YAC7C,MAAM,KAAK;AAAA,UACb,CAAC;AAAA,QACH;AACA;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,YAAI,eAAe,aAAa;AAC9B,WAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,QAC5C,WAAW,eAAe,eAAe;AACvC,WAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ;AAAA,YAC3C;AAAA,UACF,CAAC;AAAA,QACH,WAAW,eAAe,gBACf,KAAK,MAAM,aAAa,cAAc;AAC/C,gBAAM,OAAO,KAAK,MAAM,eAAe,QAAQ;AAC/C,cAAI,MAAM;AACR,gBAAI,UAAU;AACZ,oBAAM,OAAO,KAAK,aAAa,cAAc,MAAM;AAAA,gBACjD,MAAM,KAAK;AAAA,cACb,CAAC;AACD,kBAAI,MAAM;AACR,sBAAM,KAAK,IAAI;AACf,2BAAW;AAAA,cACb;AAAA,YACF,OAAO;AACL,oBAAM,KAAK,IAAI;AACf,yBAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF,OAAO;AACL,kBAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO;AAAA,YAC/C;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,MAAM,QAAQ;AAChB,uBAAW;AAAA,UACb;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,gBAAgB;AACnB,YAAI,eAAe,aAAa;AAC9B,WAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,QAC5C,WAAW,eAAe,eAAe;AACvC,WAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ;AAAA,YAC3C;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,kBAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO;AAAA,YAC/C;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,MAAM,QAAQ;AAChB,uBAAW;AAAA,UACb;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK,eAAe;AAClB,YAAI,eAAe,aAAa;AAC9B,WAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,QAC5C,WAAW,eAAe,eAAe;AACvC,WAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ;AAAA,YAC3C;AAAA,UACF,CAAC;AAAA,QACH,OAAO;AACL,kBAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO;AAAA,YAC/C;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,MAAM,QAAQ;AAChB,uBAAW;AAAA,UACb;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,eAAe,kBACd,aAAa,UAAU,aAAa,iBAAiB;AACxD,cAAI;AACJ,cAAI,KAAK,WACL,KAAK,MAAM,aAAa,wBAAwB;AAClD,yBAAa,KAAK,4BAA4B,MAAM,KAAK,KAAK;AAAA,UAChE,WAAW,YAAY,KAAK,MAAM,aAAa,cAAc;AAC3D,yBACE,KAAK,4BAA4B,MAAM,KAAK,MAAM,UAAU;AAAA,UAChE;AACA,cAAI,YAAY;AACd,gBAAI;AACJ,gBAAI,UAAU;AACZ,yBAAW,QAAQ,cAAc;AAC/B,oBAAI,sBAAsB,KAAK,KAAK,IAAI,GAAG;AACzC,wBAAM,OACJ,KAAK,4BAA4B,MAAM,UAAU;AACnD,yBAAO,SAAS;AAAA,gBAClB,WAAW,KAAK,SAAS,OAAO;AAC9B,yBAAO,KAAK,0BAA0B,MAAM,YAAY,CAAC,CAAC,EACvD,IAAI,UAAU;AAAA,gBACnB,OAAO;AACL,yBAAO;AAAA,gBACT;AACA,oBAAI,CAAC,MAAM;AACT;AAAA,gBACF;AAAA,cACF;AAAA,YACF,OAAO;AACL,qBAAO;AAAA,YACT;AACA,gBAAI,MAAM;AACR,oBAAM,KAAK,UAAU;AACrB,yBAAW;AAAA,YACb;AAAA,UACF;AAAA,QACF,WAAW,eAAe,aAAa;AACrC,WAAC,OAAO,QAAQ,IAAI,KAAK,WAAW,MAAM;AAAA,QAC5C,WAAW,eAAe,eAAe;AACvC,WAAC,OAAO,QAAQ,IAAI,KAAK,YAAY,QAAQ;AAAA,YAC3C;AAAA,UACF,CAAC;AAAA,QACH,WAAW,eAAe,cAAc;AACtC,kBAAQ,KAAK,gBAAgB,QAAQ,KAAK,OAAO;AAAA,YAC/C;AAAA,YACA;AAAA,UACF,CAAC;AACD,cAAI,MAAM,QAAQ;AAChB,uBAAW;AAAA,UACb;AAAA,QACF,OAAO;AACL,oBAAU;AAAA,QACZ;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL;AAAA,MACA;AAAA,MACA;AAAA,MACA;AAAA,IACF;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,cAAc,YAAY;AACxB,UAAM,MAAM,KAAK,KAAK,OAAO;AAC7B,QAAI,eAAe,cAAc,eAAe,cAAc;AAC5D,YAAM,eAAe,oBAAI,IAAI;AAC7B,UAAI,IAAI;AACR,iBAAW,EAAE,OAAO,KAAK,KAAK;AAC5B,cAAM,YAAY,OAAO;AACzB,cAAM,UAAU,YAAY;AAC5B,cAAM,YAAY,OAAO,CAAC;AAC1B,YAAI;AACJ,YAAI;AACJ,YAAI,SAAS;AACX,gBAAM;AAAA,YACJ,OAAO;AAAA,YACP,QAAQ,CAAC;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,YACR,CAAC;AAAA,UACH,IAAI;AACJ,gBAAM,WAAW,OAAO,YAAY,CAAC;AACrC,gBAAM;AAAA,YACJ,QAAQ,CAAC;AAAA,cACP,MAAM;AAAA,cACN,MAAM;AAAA,YACR,CAAC;AAAA,UACH,IAAI;AACJ,gBAAM;AACN,iBAAO;AACP,cAAI,KAAK,UAAU,SAAS,QAAQ,KAChC,aAAa,uBAAuB,aAAa,aAAa;AAChE,kBAAM;AACN,mBAAO;AAAA,UACT,WAAW,cAAc,aAAa;AACpC,kBAAM;AACN,mBAAO;AAAA,UACT,WAAW,cAAc,OAAO,cAAc,eAAe;AAC3D,kBAAM;AACN,mBAAO;AAAA,UACT,WAAW,aAAa,OAAO,aAAa,eAAe;AACzD,kBAAM;AACN,mBAAO;AAAA,UACT,WAAW,cAAc,GAAG;AAC1B,gBAAI,eAAe,cAAc;AAC/B,oBAAM;AACN,qBAAO;AAAA,YACT,OAAO;AACL,oBAAM,EAAE,MAAM,UAAU,IAAI;AAC5B,kBAAI,cAAc,OAAO,cAAc,KAAK;AAC1C,sBAAM;AACN,uBAAO;AAAA,cACT;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,gBAAM;AACN,iBAAO;AAAA,QACT;AACA,cAAM;AAAA,UACJ;AAAA,UAAU;AAAA,UAAU;AAAA,UAAO;AAAA,QAC7B,IAAI,KAAK,gBAAgB,MAAM,YAAY,EAAE,SAAS,IAAI,CAAC;AAC3D,YAAI,MAAM,QAAQ;AAChB,eAAK,KAAK,CAAC,EAAE,OAAO;AACpB,eAAK,OAAO,CAAC,IAAI;AAAA,QACnB,WAAW,SAAS;AAClB,uBAAa,IAAI,oBAAI,IAAI;AAAA,YACvB,CAAC,SAAS,CAAC;AAAA,YACX,CAAC,QAAQ,IAAI;AAAA,UACf,CAAC,CAAC;AAAA,QACJ;AACA,aAAK,KAAK,CAAC,EAAE,MAAM;AACnB,aAAK,KAAK,CAAC,EAAE,WAAW,YAAY,CAAC;AACrC;AAAA,MACF;AACA,UAAI,aAAa,MAAM;AACrB,YAAI;AACJ,YAAI;AACJ,YAAI,KAAK,UAAU,KAAK,SAAS,KAAK,MAAM,aAAa,cAAc;AACrE,iBAAO,KAAK;AACZ,mBAAS,KAAK;AAAA,QAChB,OAAO;AACL,cAAI,CAAC,KAAK,aAAa;AACrB,iBAAK,cAAc,KAAK,kBAAkB,KAAK,KAAK;AAAA,UACtD;AACA,iBAAO,KAAK;AACZ,mBAAS,KAAK;AAAA,QAChB;AACA,YAAI,WAAW,aAAa,MAAM,MAAM;AACxC,eAAO,UAAU;AACf,cAAI,OAAO;AACX,cAAI,KAAK,MAAM,aAAa,cAAc;AACxC,gBAAI,aAAa,KAAK,OAAO;AAC3B,qBAAO;AAAA,YACT,OAAO;AACL,qBAAO,KAAK,MAAM,SAAS,QAAQ;AAAA,YACrC;AAAA,UACF,OAAO;AACL,mBAAO;AAAA,UACT;AACA,cAAI,MAAM;AACR,uBAAW,eAAe,cAAc;AACtC,oBAAM,EAAE,OAAO,IAAI,YAAY,IAAI,MAAM;AACzC,oBAAM,UAAU,KAAK,aAAa,QAAQ,UAAU;AAAA,gBAClD,MAAM,KAAK;AAAA,cACb,CAAC;AACD,kBAAI,SAAS;AACX,sBAAM,QAAQ,YAAY,IAAI,OAAO;AACrC,qBAAK,KAAK,KAAK,EAAE,WAAW;AAC5B,qBAAK,KAAK,KAAK,EAAE,OAAO;AACxB,qBAAK,OAAO,KAAK,EAAE,KAAK,QAAQ;AAAA,cAClC;AAAA,YACF;AAAA,UACF;AACA,cAAI,aAAa,OAAO,aAAa;AACnC,uBAAW,aAAa,UAAU,MAAM;AAAA,UAC1C;AACA,qBAAW,OAAO,SAAS;AAAA,QAC7B;AAAA,MACF;AAAA,IACF,OAAO;AACL,UAAI,IAAI;AACR,iBAAW,EAAE,OAAO,KAAK,KAAK;AAC5B,cAAM,OAAO,OAAO,OAAO,SAAS,CAAC;AACrC,cAAM,UAAU,OAAO,SAAS;AAChC,cAAM,MAAM;AACZ,cAAM;AAAA,UACJ;AAAA,UAAU;AAAA,UAAU;AAAA,QACtB,IAAI,KAAK,gBAAgB,MAAM,YAAY,EAAE,SAAS,IAAI,CAAC;AAC3D,YAAI,MAAM,QAAQ;AAChB,eAAK,KAAK,CAAC,EAAE,OAAO;AACpB,eAAK,OAAO,CAAC,IAAI;AAAA,QACnB;AACA,aAAK,KAAK,CAAC,EAAE,MAAM;AACnB,aAAK,KAAK,CAAC,EAAE,WAAW,YAAY,CAAC;AACrC;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,MACL,KAAK;AAAA,MACL,KAAK;AAAA,IACP;AAAA,EACF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,kBAAkB,MAAM,OAAO,KAAK;AAClC,UAAM,MAAM,CAAC;AACb,eAAW,QAAQ,OAAO;AACxB,YAAM,UAAU,KAAK,iBAAiB,MAAM,MAAM;AAAA,QAChD;AAAA,QACA,MAAM,KAAK;AAAA,MACb,CAAC;AACD,UAAI,QAAQ,MAAM;AAChB,YAAI,KAAK,GAAG,OAAO;AAAA,MACrB;AAAA,IACF;AACA,QAAI,IAAI,QAAQ;AACd,aAAO,IAAI,IAAI,GAAG;AAAA,IACpB;AACA,WAAO,oBAAI,IAAI;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAYA,eAAe,QAAQ,OAAO,KAAK;AACjC,UAAM,EAAE,OAAO,MAAM,IAAI;AACzB,UAAM,EAAE,OAAO,WAAW,OAAO,IAAI,OAAO,KAAK;AACjD,UAAM,OAAO;AAAA,MACX;AAAA,MACA;AAAA,IACF;AACA,UAAM,YAAY,KAAK,kBAAkB,MAAM,OAAO,QAAQ;AAC9D,QAAI,UAAU,MAAM;AAClB,UAAI,UAAU,OAAO,SAAS,GAAG;AAC/B,cAAM,CAAC,QAAQ,IAAI,UAAU,SAAS;AACtC,eAAO;AAAA,MACT,OAAO;AACL,eAAO,KAAK,eAAe,QAAQ,WAAW;AAAA,UAC5C,OAAO;AAAA,UACP,OAAO,QAAQ;AAAA,QACjB,CAAC;AAAA,MACH;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAWA,eAAe,QAAQ,MAAM,KAAK;AAChC,UAAM,EAAE,MAAM,IAAI;AAClB,UAAM,OAAO,OAAO,KAAK;AACzB,UAAM,QAAQ,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC5B,UAAM,YAAY,KAAK,kBAAkB,MAAM,OAAO,QAAQ;AAC9D,QAAI,UAAU,MAAM;AAClB,UAAI,UAAU,GAAG;AACf,eAAO;AAAA,MACT,OAAO;AACL,YAAI;AACJ,mBAAW,YAAY,WAAW;AAChC,oBAAU,KAAK,eAAe,QAAQ,UAAU;AAAA,YAC9C,OAAO,QAAQ;AAAA,UACjB,CAAC;AACD,cAAI,SAAS;AACX;AAAA,UACF;AAAA,QACF;AACA,YAAI,SAAS;AACX,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,YAAY;AACf,QAAI,eAAe,cAAc,eAAe,cAAc;AAC5D,WAAK,4BAA4B;AAAA,IACnC;AACA,UAAM,CAAC,CAAC,GAAG,QAAQ,GAAG,cAAc,IAAI,KAAK,cAAc,UAAU;AACrE,UAAM,IAAI,SAAS;AACnB,QAAI;AACJ,QAAI,QAAQ,oBAAI,IAAI;AACpB,aAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,YAAM,EAAE,QAAQ,KAAK,MAAAC,MAAK,IAAI,SAAS,CAAC;AACxC,YAAM,YAAY,OAAO;AACzB,UAAI,aAAaA,OAAM;AACrB,cAAM,aAAa,eAAe,CAAC;AACnC,cAAM,gBAAgB,WAAW;AACjC,cAAM,YAAY,YAAY;AAC9B,YAAI,cAAc,GAAG;AACnB,eAAK,eAAe,cAAc,eAAe,iBAC7C,KAAK,MAAM,aAAa,cAAc;AACxC,qBAAS,IAAI,GAAG,IAAI,eAAe,KAAK;AACtC,oBAAM,OAAO,WAAW,CAAC;AACzB,kBAAI,SAAS,KAAK,SAAS,KAAK,MAAM,SAAS,IAAI,GAAG;AACpD,sBAAM,IAAI,IAAI;AACd,oBAAI,eAAe,cAAc;AAC/B;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,WAAW,eAAe,YAAY;AACpC,gBAAI,MAAM,MAAM;AACd,oBAAM,IAAI,CAAC,GAAG,KAAK;AACnB,sBAAQ,oBAAI,IAAI,CAAC,GAAG,GAAG,GAAG,UAAU,CAAC;AACrC,qBAAO;AAAA,YACT,OAAO;AACL,sBAAQ,IAAI,IAAI,UAAU;AAAA,YAC5B;AAAA,UACF,OAAO;AACL,kBAAM,CAAC,IAAI,IAAI;AACf,kBAAM,IAAI,IAAI;AAAA,UAChB;AAAA,QACF,WAAW,eAAe,YAAY;AACpC,cAAI,QAAQ,UAAU;AACpB,kBAAM,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACtC,gBAAI,QAAQ;AACZ,uBAAW,QAAQ,YAAY;AAC7B,kBAAI,YAAY,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,uBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,sBAAM,EAAE,OAAO,WAAW,OAAO,IAAI,OAAO,CAAC;AAC7C,sBAAM,OAAO;AAAA,kBACX;AAAA,kBACA;AAAA,gBACF;AACA,4BAAY,KAAK,kBAAkB,MAAM,WAAW,GAAG;AACvD,oBAAI,UAAU,MAAM;AAClB,sBAAI,MAAM,WAAW;AACnB,wBAAI,MAAM,MAAM;AACd,4BAAM,IAAI,CAAC,GAAG,KAAK;AACnB,8BAAQ,oBAAI,IAAI,CAAC,GAAG,GAAG,GAAG,SAAS,CAAC;AACpC,6BAAO;AACP,8BAAQ;AAAA,oBACV,OAAO;AACL,8BAAQ;AACR,8BAAQ;AAAA,oBACV;AAAA,kBACF,OAAO;AACL,4BAAQ;AAAA,kBACV;AAAA,gBACF,OAAO;AACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF,OAAO;AACL,uBAAW,QAAQ,YAAY;AAC7B,kBAAI,YAAY,oBAAI,IAAI,CAAC,IAAI,CAAC;AAC9B,uBAAS,IAAI,YAAY,GAAG,KAAK,GAAG,KAAK;AACvC,sBAAM,OAAO,OAAO,CAAC;AACrB,4BAAY,KAAK,kBAAkB,MAAM,WAAW,GAAG;AACvD,oBAAI,UAAU,MAAM;AAClB,sBAAI,MAAM,GAAG;AACX,0BAAM,IAAI,IAAI;AACd,wBAAI,YAAY,KAAK,MAAM,OAAO,GAAG;AACnC,6BAAO;AAAA,oBACT;AAAA,kBACF;AAAA,gBACF,OAAO;AACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,WAAW,eAAe,gBAAgB,QAAQ,UAAU;AAC1D,gBAAM,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACtC,cAAI;AACJ,qBAAW,QAAQ,YAAY;AAC7B,kBAAM,cAAc,KAAK,eAAe,QAAQ,oBAAI,IAAI,CAAC,IAAI,CAAC,GAAG;AAAA,cAC/D,OAAO;AAAA,cACP,OAAO;AAAA,YACT,CAAC;AACD,gBAAI,aAAa;AACf,kBAAI,KAAK,MAAM,aAAa,cAAc;AACxC,oBAAI,gBAAgB,KAAK,SACrB,KAAK,MAAM,SAAS,WAAW,GAAG;AACpC,wBAAM,IAAI,WAAW;AACrB,4BAAU;AACV;AAAA,gBACF;AAAA,cACF,OAAO;AACL,sBAAM,IAAI,WAAW;AACrB,0BAAU;AACV;AAAA,cACF;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,SAAS;AACZ,kBAAM,EAAE,QAAQ,YAAY,IAAI,OAAO,CAAC;AACxC,kBAAM,CAAC,SAAS,IAAI;AACpB,gBAAI,KAAK,MAAM,SAAS,SAAS,GAAG;AAClC,kBAAI,CAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,WAAW;AAAA,gBAC3D;AAAA,cACF,CAAC;AACD,qBAAO,SAAS;AACd,sBAAM,cACJ,KAAK,eAAe,QAAQ,oBAAI,IAAI,CAAC,OAAO,CAAC,GAAG;AAAA,kBAC9C,OAAO;AAAA,kBACP,OAAO;AAAA,gBACT,CAAC;AACH,oBAAI,aAAa;AACf,sBAAI,KAAK,MAAM,aAAa,cAAc;AACxC,wBAAI,gBAAgB,KAAK,SACrB,KAAK,MAAM,SAAS,WAAW,GAAG;AACpC,4BAAM,IAAI,WAAW;AACrB;AAAA,oBACF;AAAA,kBACF,OAAO;AACL,0BAAM,IAAI,WAAW;AACrB;AAAA,kBACF;AAAA,gBACF;AACA,iBAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,SAAS;AAAA,kBACrD;AAAA,kBACA,OAAO;AAAA,gBACT,CAAC;AAAA,cACH;AAAA,YACF,OAAO;AACL,oBAAM,EAAE,OAAO,WAAW,IAAI,OAAO,CAAC;AACtC,kBAAI,QAAQ;AACZ,kBAAI,YAAY,oBAAI,IAAI,CAAC,SAAS,CAAC;AACnC,uBAAS,IAAI,GAAG,IAAI,WAAW,KAAK;AAClC,sBAAM,EAAE,OAAO,WAAW,OAAO,IAAI,OAAO,CAAC;AAC7C,sBAAM,OAAO;AAAA,kBACX;AAAA,kBACA;AAAA,gBACF;AACA,4BAAY,KAAK,kBAAkB,MAAM,WAAW,GAAG;AACvD,oBAAI,UAAU,MAAM;AAClB,sBAAI,MAAM,WAAW;AACnB,+BAAW,YAAY,WAAW;AAChC,0BAAI,KAAK,MAAM,SAAS,QAAQ,GAAG;AACjC,8BAAM,IAAI,QAAQ;AAClB;AAAA,sBACF;AAAA,oBACF;AAAA,kBACF,OAAO;AACL,4BAAQ;AAAA,kBACV;AAAA,gBACF,OAAO;AACL;AAAA,gBACF;AAAA,cACF;AAAA,YACF;AAAA,UACF;AAAA,QACF,OAAO;AACL,cAAI;AACJ,qBAAW,QAAQ,YAAY;AAC7B,kBAAM,cAAc,KAAK,eAAe,QAAQ,MAAM;AAAA,cACpD,OAAO,YAAY;AAAA,YACrB,CAAC;AACD,gBAAI,aAAa;AACf,oBAAM,IAAI,IAAI;AACd,wBAAU;AACV;AAAA,YACF;AAAA,UACF;AACA,cAAI,CAAC,WAAW,eAAe,cAAc;AAC3C,kBAAM,EAAE,QAAQ,YAAY,IAAI,OAAO,SAAS;AAChD,kBAAM,CAAC,SAAS,IAAI;AACpB,gBAAI,CAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,WAAW;AAAA,cAC3D;AAAA,YACF,CAAC;AACD,mBAAO,SAAS;AACd,oBAAM,cAAc,KAAK,eAAe,QAAQ,SAAS;AAAA,gBACvD,OAAO,YAAY;AAAA,cACrB,CAAC;AACD,kBAAI,aAAa;AACf,sBAAM,IAAI,OAAO;AACjB;AAAA,cACF;AACA,eAAC,OAAO,IAAI,KAAK,gBAAgB,aAAa,SAAS;AAAA,gBACrD;AAAA,gBACA,OAAO;AAAA,cACT,CAAC;AAAA,YACH;AAAA,UACF;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,KAAK,QAAQ;AACf,YAAM,QAAQ,CAAC,CAAC,MAAM;AACtB,UAAI;AACJ,UAAI,KAAK,eAAe,QAAQ;AAC9B,wBAAgB,KAAK,eAAe,KAAK,EAAE;AAAA,MAC7C,OAAO;AACL,wBAAgB;AAAA,MAClB;AACA,aAAO;AAAA,QACL;AAAA,QACA;AAAA,MACF;AAAA,IACF;AACA,QAAI,eAAe,cAAc;AAC/B,YAAM,OAAO,KAAK,KAAK;AACvB,UAAI,MAAM,OAAO,GAAG;AAClB,gBAAQ,IAAI,IAAI,UAAU,KAAK,CAAC;AAAA,MAClC;AAAA,IACF,WAAW,eAAe,YAAY;AACpC,YAAM,OAAO,KAAK,KAAK;AACvB,UAAI,QAAQ,MAAM,OAAO,GAAG;AAC1B,gBAAQ,IAAI,IAAI,UAAU,KAAK,CAAC;AAAA,MAClC;AAAA,IACF;AACA,WAAO;AAAA,EACT;AACF;;;ALn4FA,IAAM,cAAc,IAAI,OAAO,GAAG,UAAU,GAAG,KAAK,GAAG,UAAU,IAAI,GAAG;AACxE,IAAM,cAAc,IAAI,OAAO,GAAG,UAAU,GAAG,OAAO,GAAG,UAAU,IAAI,GAAG;AAC1E,IAAM,cAAc,IAAI,OAAO,GAAG,UAAU,GAAG,OAAO,GAAG,UAAU,IAAI,GAAG;AAC1E,IAAM,aAAa,IAAI,OAAO,IAAI,YAAY,GAAG;AAG1C,IAAM,cAAN,MAAkB;AAAA;AAAA,EAEvB;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA,EACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAQA,YAAY,QAAQ,UAAU,MAAM,CAAC,GAAG;AACtC,UAAM,EAAE,eAAe,SAAS,IAAI;AACpC,SAAK,UAAU;AACf,SAAK,YAAY,YAAY,OAAO;AACpC,SAAK,iBAAiB;AACtB,SAAK,UAAU,IAAI,OAAO,MAAM;AAChC,SAAK,YAAY;AACjB,SAAK,UAAU,WAAW,QAAQ,QAAQ;AAAA,EAC5C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAgBA,MAAM,UAAU,MAAM,MAAM,CAAC,GAAG;AAC9B,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B,WAAW,KAAK,aAAa,cAAc;AACzC,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,UAAM,WAAW,KAAK,iBAClB,KAAK,iBACL,KAAK;AACT,QAAI,aAAa,KAAK,aAAa,SAAS,gBAAgB,eACxD,SAAS,mBAAmB,KAAK,YAAY;AAC/C,YAAM,YAAY;AAAA,QAChB,SAAS,YAAY,KAAK,QAAQ;AAAA,QAClC,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AACA,UAAI,eAAe,UAAU,SAAS,GAAG;AACvC,YAAI;AACF,gBAAM,IAAI,KAAK,YAAY,KAAK,UAAU,eAAe,IAAI,IAAI;AACjE,gBAAM,QAAQ,KAAK,QAAQ,MAAM,UAAU,CAAC;AAC5C,iBAAO;AAAA,YACL;AAAA,YACA,eAAe;AAAA,UACjB;AAAA,QACF,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AAEF,UAAI,KAAK,WAAW;AAClB,eAAO,KAAK,UAAU,eAAe,IAAI;AAAA,MAC3C;AACA,UAAI,gBAAgB,KAAK;AACzB,UAAI,QAAQ;AACZ,UAAI,UAAU;AACd,UAAI,OAAO;AACX,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,KAAK,QAAQ,KAAK,WAAW;AAAA,IACrC,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU,MAAM,MAAM,CAAC,GAAG;AAChC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B,WAAW,KAAK,aAAa,cAAc;AACzC,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,UAAM,WAAW,KAAK,iBAClB,KAAK,iBACL,KAAK;AACT,QAAI,aAAa,KAAK,aAAa,SAAS,gBAAgB,eACxD,SAAS,mBAAmB,KAAK,YAAY;AAC/C,YAAM,YAAY;AAAA,QAChB,SAAS,YAAY,KAAK,QAAQ;AAAA,QAClC,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AACA,UAAI,eAAe,UAAU,SAAS,GAAG;AACvC,YAAI;AACF,gBAAM,IAAI,KAAK,YAAY,KAAK,UAAU,eAAe,IAAI,IAAI;AACjE,gBAAMC,OAAM,KAAK,QAAQ,MAAM,UAAU,CAAC;AAC1C,iBAAOA;AAAA,QACT,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AAEF,UAAI,KAAK,WAAW;AAClB,eAAO,KAAK,UAAU,eAAe,IAAI;AAAA,MAC3C;AACA,UAAI,gBAAgB,KAAK;AACzB,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,WAAW;AAC3C,YAAM,MAAM;AAAA,IACd,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,CAAC,CAAC;AAAA,EACX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,QAAQ,UAAU,MAAM,MAAM,CAAC,GAAG;AAChC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B,WAAW,KAAK,aAAa,cAAc;AACzC,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,KAAK,QAAQ,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,UAAM,WAAW,KAAK,iBAClB,KAAK,iBACL,KAAK;AACT,QAAI,aAAa,KAAK,aAAa,SAAS,gBAAgB,eACxD,SAAS,mBAAmB,KAAK,YAAY;AAC/C,YAAM,YAAY;AAAA,QAChB,SAAS,YAAY,KAAK,QAAQ;AAAA,QAClC,UAAU;AAAA,QACV,SAAS;AAAA,QACT,QAAQ;AAAA,QACR,QAAQ;AAAA,MACV;AACA,UAAI,eAAe,UAAU,SAAS,GAAG;AACvC,YAAI;AACF,gBAAM,IAAI,KAAK,YAAY,KAAK,UAAU,eAAe,IAAI,IAAI;AACjE,gBAAMA,OAAM,KAAK,QAAQ,QAAQ,UAAU,CAAC;AAC5C,iBAAOA;AAAA,QACT,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AAEF,UAAI,KAAK,WAAW;AAClB,eAAO,KAAK,UAAU,eAAe,IAAI;AAAA,MAC3C;AACA,UAAI,gBAAgB,KAAK;AACzB,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,aAAa;AAC7C,UAAI,MAAM,MAAM;AACd,YAAI,UAAU;AACd,eAAO,SAAS;AACd,cAAI,MAAM,IAAI,OAAO,GAAG;AACtB,kBAAM;AACN;AAAA,UACF;AACA,oBAAU,QAAQ;AAAA,QACpB;AAAA,MACF;AAAA,IACF,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EASA,cAAc,UAAU,MAAM,MAAM,CAAC,GAAG;AACtC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,QAAI;AACJ,QAAI;AAEF,UAAI,KAAK,WAAW;AAClB,eAAO,KAAK,UAAU,eAAe,IAAI;AAAA,MAC3C;AACA,UAAI,gBAAgB,KAAK;AACzB,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,YAAY;AAC5C,UAAI,MAAM,MAAM;AACd,SAAC,GAAG,IAAI,CAAC,GAAG,KAAK;AAAA,MACnB;AAAA,IACF,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,OAAO;AAAA,EAChB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAUA,iBAAiB,UAAU,MAAM,MAAM,CAAC,GAAG;AACzC,QAAI,CAAC,MAAM,UAAU;AACnB,YAAM,IAAI,IAAI,KAAK,QAAQ,UAAU,mBAAmB,QAAQ,IAAI,CAAC,EAAE;AACvE,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,QAAI;AACJ,QAAI,KAAK,gBAAgB;AACvB,iBAAW,KAAK;AAAA,IAClB,WAAW,KAAK,aAAa,eAAe;AAC1C,iBAAW;AAAA,IACb,OAAO;AACL,iBAAW,KAAK;AAAA,IAClB;AACA,QAAI,aAAa,KAAK,aAAa,SAAS,gBAAgB,eACxD,SAAS,iBAAiB;AAC5B,YAAM,YAAY;AAAA,QAChB,SAAS;AAAA,QACT,UAAU;AAAA,QACV,SAAS,YAAY,KAAK,QAAQ,KAAK,CAAC,YAAY,KAAK,QAAQ;AAAA,QACjE,QAAQ,WAAW,KAAK,QAAQ;AAAA,QAChC,QAAQ;AAAA,MACV;AACA,UAAI,eAAe,UAAU,SAAS,GAAG;AACvC,YAAI;AACF,gBAAM,IAAI,KAAK,YAAY,KAAK,UAAU,eAAe,IAAI,IAAI;AACjE,gBAAMA,OAAM,KAAK,QAAQ,OAAO,UAAU,CAAC;AAC3C,iBAAOA;AAAA,QACT,SAAS,GAAG;AAAA,QAEZ;AAAA,MACF;AAAA,IACF;AACA,QAAI;AACJ,QAAI;AAEF,UAAI,KAAK,WAAW;AAClB,eAAO,KAAK,UAAU,eAAe,IAAI;AAAA,MAC3C;AACA,UAAI,gBAAgB,KAAK;AACzB,WAAK,QAAQ,MAAM,UAAU,MAAM,GAAG;AACtC,YAAM,QAAQ,KAAK,QAAQ,KAAK,UAAU;AAC1C,UAAI,MAAM,MAAM;AACd,cAAM,CAAC,GAAG,KAAK;AAAA,MACjB;AAAA,IACF,SAAS,GAAG;AACV,WAAK,QAAQ,QAAQ,GAAG,GAAG;AAAA,IAC7B;AACA,WAAO,OAAO,CAAC;AAAA,EACjB;AACF;", "names": ["import_css_tree", "isCustomElementName", "bidiFactory", "nwsapi", "import_css_tree", "key", "branches", "find", "res"]}
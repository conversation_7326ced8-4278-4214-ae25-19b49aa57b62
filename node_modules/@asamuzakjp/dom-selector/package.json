{"name": "@asamuzakjp/dom-selector", "description": "A CSS selector engine.", "author": "asamuzaK", "license": "MIT", "homepage": "https://github.com/asamuzaK/domSelector#readme", "bugs": {"url": "https://github.com/asamuzaK/domSelector/issues"}, "repository": {"type": "git", "url": "git+https://github.com/asamuzaK/domSelector.git"}, "files": ["dist", "src", "types"], "type": "module", "exports": {"import": {"types": "./types/index.d.ts", "default": "./src/index.js"}, "require": {"types": "./dist/cjs/index.d.cts", "default": "./dist/cjs/index.cjs"}, "default": {"types": "./dist/cjs/types/index.d.cts", "default": "./dist/cjs/index.cjs"}}, "types": "types/index.d.ts", "dependencies": {"@asamuzakjp/nwsapi": "^2.3.9", "bidi-js": "^1.0.3", "css-tree": "^3.1.0", "is-potential-custom-element-name": "^1.0.1"}, "devDependencies": {"@types/css-tree": "^2.3.10", "benchmark": "^2.1.4", "c8": "^10.1.3", "chai": "^6.0.1", "commander": "^14.0.1", "esbuild": "^0.25.9", "eslint": "^9.35.0", "eslint-plugin-jsdoc": "^57.0.8", "eslint-plugin-regexp": "^2.10.0", "eslint-plugin-unicorn": "^61.0.2", "globals": "^16.4.0", "happy-dom": "^18.0.1", "jsdom": "^27.0.0-beta.2", "linkedom": "^0.18.12", "mocha": "^11.7.2", "neostandard": "^0.12.2", "sinon": "^21.0.0", "tsup": "^8.5.0", "typescript": "^5.9.2", "wpt-runner": "^6.0.0"}, "overrides": {"jsdom": "$jsdom"}, "scripts": {"bench": "node benchmark/bench.js", "bench:sizzle": "node benchmark/bench-sizzle.js", "build": "npm run tsc && npm run lint && npm test && npm run bundle && npm run test:cjs", "bundle": "tsup src/index.js --format=cjs --platform=node --outDir=dist/cjs/ --sourcemap --dts", "lint": "eslint --fix .", "test": "c8 --reporter=text mocha --exit test/**/*.test.js", "test:cjs": "mocha --exit test/index.test.cjs", "test:wpt": "node test/wpt/wpt-runner.js", "tsc": "node scripts/index clean --dir=types -i && npx tsc", "update:wpt": "git submodule update --init --recursive --remote"}, "version": "6.5.5"}
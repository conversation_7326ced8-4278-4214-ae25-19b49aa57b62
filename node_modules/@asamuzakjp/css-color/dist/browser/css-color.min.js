var ul=Object.defineProperty,so=t=>{throw TypeError(t)},hl=(t,e,n)=>e in t?ul(t,e,{enumerable:!0,configurable:!0,writable:!0,value:n}):t[e]=n,X=(t,e,n)=>hl(t,typeof e!="symbol"?e+"":e,n),qs=(t,e,n)=>e.has(t)||so("Cannot "+n),h=(t,e,n)=>(qs(t,e,"read from private field"),n?n.call(t):e.get(t)),U=(t,e,n)=>e.has(t)?so("Cannot add the same private member more than once"):e instanceof WeakSet?e.add(t):e.set(t,n),F=(t,e,n,r)=>(qs(t,e,"write to private field"),r?r.call(t,n):e.set(t,n),n),L=(t,e,n)=>(qs(t,e,"access private method"),n),Vs=(t,e,n,r)=>({set _(s){F(t,e,s,n)},get _(){return h(t,e,r)}});class wr extends Error{constructor(e,n,r,s){super(e),X(this,"sourceStart"),X(this,"sourceEnd"),X(this,"parserState"),this.name="ParseError",this.sourceStart=n,this.sourceEnd=r,this.parserState=s}}class cn extends wr{constructor(e,n,r,s,a){super(e,n,r,s),X(this,"token"),this.token=a}}const Xe={UnexpectedNewLineInString:"Unexpected newline while consuming a string token.",UnexpectedEOFInString:"Unexpected EOF while consuming a string token.",UnexpectedEOFInComment:"Unexpected EOF while consuming a comment.",UnexpectedEOFInURL:"Unexpected EOF while consuming a url token.",UnexpectedEOFInEscapedCodePoint:"Unexpected EOF while consuming an escaped code point.",UnexpectedCharacterInURL:"Unexpected character while consuming a url token.",InvalidEscapeSequenceInURL:"Invalid escape sequence while consuming a url token.",InvalidEscapeSequenceAfterBackslash:'Invalid escape sequence after "\\"'};function Ke(...t){let e="";for(let n=0;n<t.length;n++)e+=t[n][1];return e}const In=13,At=45,Ln=10,Tn=43,un=65533;function fl(t){return t.source.codePointAt(t.cursor)===60&&t.source.codePointAt(t.cursor+1)===33&&t.source.codePointAt(t.cursor+2)===At&&t.source.codePointAt(t.cursor+3)===At}function oe(t){return t>=48&&t<=57}function pl(t){return t>=65&&t<=90}function dl(t){return t>=97&&t<=122}function _n(t){return t>=48&&t<=57||t>=97&&t<=102||t>=65&&t<=70}function ml(t){return dl(t)||pl(t)}function Hn(t){return ml(t)||gl(t)||t===95}function Xs(t){return Hn(t)||oe(t)||t===At}function gl(t){return t===183||t===8204||t===8205||t===8255||t===8256||t===8204||192<=t&&t<=214||216<=t&&t<=246||248<=t&&t<=893||895<=t&&t<=8191||8304<=t&&t<=8591||11264<=t&&t<=12271||12289<=t&&t<=55295||63744<=t&&t<=64975||65008<=t&&t<=65533||t===0||!!Un(t)||t>=65536}function ts(t){return t===Ln||t===In||t===12}function Cn(t){return t===32||t===Ln||t===9||t===In||t===12}function Un(t){return t>=55296&&t<=57343}function zn(t){return t.source.codePointAt(t.cursor)===92&&!ts(t.source.codePointAt(t.cursor+1)??-1)}function es(t,e){return e.source.codePointAt(e.cursor)===At?e.source.codePointAt(e.cursor+1)===At||!!Hn(e.source.codePointAt(e.cursor+1)??-1)||e.source.codePointAt(e.cursor+1)===92&&!ts(e.source.codePointAt(e.cursor+2)??-1):!!Hn(e.source.codePointAt(e.cursor)??-1)||zn(e)}function ao(t){return t.source.codePointAt(t.cursor)===Tn||t.source.codePointAt(t.cursor)===At?!!oe(t.source.codePointAt(t.cursor+1)??-1)||t.source.codePointAt(t.cursor+1)===46&&oe(t.source.codePointAt(t.cursor+2)??-1):t.source.codePointAt(t.cursor)===46?oe(t.source.codePointAt(t.cursor+1)??-1):oe(t.source.codePointAt(t.cursor)??-1)}function vl(t){return t.source.codePointAt(t.cursor)===47&&t.source.codePointAt(t.cursor+1)===42}function bl(t){return t.source.codePointAt(t.cursor)===At&&t.source.codePointAt(t.cursor+1)===At&&t.source.codePointAt(t.cursor+2)===62}var v,S,ns;function wl(t){switch(t){case v.OpenParen:return v.CloseParen;case v.CloseParen:return v.OpenParen;case v.OpenCurly:return v.CloseCurly;case v.CloseCurly:return v.OpenCurly;case v.OpenSquare:return v.CloseSquare;case v.CloseSquare:return v.OpenSquare;default:return null}}function $l(t){switch(t[0]){case v.OpenParen:return[v.CloseParen,")",-1,-1,void 0];case v.CloseParen:return[v.OpenParen,"(",-1,-1,void 0];case v.OpenCurly:return[v.CloseCurly,"}",-1,-1,void 0];case v.CloseCurly:return[v.OpenCurly,"{",-1,-1,void 0];case v.OpenSquare:return[v.CloseSquare,"]",-1,-1,void 0];case v.CloseSquare:return[v.OpenSquare,"[",-1,-1,void 0];default:return null}}function yl(t,e){for(e.advanceCodePoint(2);;){const n=e.readCodePoint();if(n===void 0){const r=[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new cn(Xe.UnexpectedEOFInComment,e.representationStart,e.representationEnd,["4.3.2. Consume comments","Unexpected EOF"],r)),r}if(n===42&&e.source.codePointAt(e.cursor)!==void 0&&e.source.codePointAt(e.cursor)===47){e.advanceCodePoint();break}}return[v.Comment,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0]}function rs(t,e){const n=e.readCodePoint();if(n===void 0)return t.onParseError(new wr(Xe.UnexpectedEOFInEscapedCodePoint,e.representationStart,e.representationEnd,["4.3.7. Consume an escaped code point","Unexpected EOF"])),un;if(_n(n)){const r=[n];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&_n(s)&&r.length<6;)r.push(s),e.advanceCodePoint();Cn(e.source.codePointAt(e.cursor)??-1)&&(e.source.codePointAt(e.cursor)===In&&e.source.codePointAt(e.cursor+1)===Ln&&e.advanceCodePoint(),e.advanceCodePoint());const a=parseInt(String.fromCodePoint(...r),16);return a===0||Un(a)||a>1114111?un:a}return n===0||Un(n)?un:n}function ss(t,e){const n=[];for(;;){const r=e.source.codePointAt(e.cursor)??-1;if(r===0||Un(r))n.push(un),e.advanceCodePoint(+(r>65535)+1);else if(Xs(r))n.push(r),e.advanceCodePoint(+(r>65535)+1);else{if(!zn(e))return n;e.advanceCodePoint(),n.push(rs(t,e))}}}function Nl(t,e){e.advanceCodePoint();const n=e.source.codePointAt(e.cursor);if(n!==void 0&&(Xs(n)||zn(e))){let r=ns.Unrestricted;es(0,e)&&(r=ns.ID);const s=ss(t,e);return[v.Hash,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...s),type:r}]}return[v.Delim,"#",e.representationStart,e.representationEnd,{value:"#"}]}function El(t,e){let n=S.Integer;for(e.source.codePointAt(e.cursor)!==Tn&&e.source.codePointAt(e.cursor)!==At||e.advanceCodePoint();oe(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===46&&oe(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint(2),n=S.Number;oe(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===101||e.source.codePointAt(e.cursor)===69){if(oe(e.source.codePointAt(e.cursor+1)??-1))e.advanceCodePoint(2);else{if(e.source.codePointAt(e.cursor+1)!==At&&e.source.codePointAt(e.cursor+1)!==Tn||!oe(e.source.codePointAt(e.cursor+2)??-1))return n;e.advanceCodePoint(3)}for(n=S.Number;oe(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint()}return n}function Ks(t,e){let n;{const a=e.source.codePointAt(e.cursor);a===At?n="-":a===Tn&&(n="+")}const r=El(0,e),s=parseFloat(e.source.slice(e.representationStart,e.representationEnd+1));if(es(0,e)){const a=ss(t,e);return[v.Dimension,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r,unit:String.fromCodePoint(...a)}]}return e.source.codePointAt(e.cursor)===37?(e.advanceCodePoint(),[v.Percentage,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n}]):[v.Number,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:s,signCharacter:n,type:r}]}function Cl(t){for(;Cn(t.source.codePointAt(t.cursor)??-1);)t.advanceCodePoint();return[v.Whitespace,t.source.slice(t.representationStart,t.representationEnd+1),t.representationStart,t.representationEnd,void 0]}(function(t){t.Comment="comment",t.AtKeyword="at-keyword-token",t.BadString="bad-string-token",t.BadURL="bad-url-token",t.CDC="CDC-token",t.CDO="CDO-token",t.Colon="colon-token",t.Comma="comma-token",t.Delim="delim-token",t.Dimension="dimension-token",t.EOF="EOF-token",t.Function="function-token",t.Hash="hash-token",t.Ident="ident-token",t.Number="number-token",t.Percentage="percentage-token",t.Semicolon="semicolon-token",t.String="string-token",t.URL="url-token",t.Whitespace="whitespace-token",t.OpenParen="(-token",t.CloseParen=")-token",t.OpenSquare="[-token",t.CloseSquare="]-token",t.OpenCurly="{-token",t.CloseCurly="}-token",t.UnicodeRange="unicode-range-token"})(v||(v={})),function(t){t.Integer="integer",t.Number="number"}(S||(S={})),function(t){t.Unrestricted="unrestricted",t.ID="id"}(ns||(ns={}));class kl{constructor(e){X(this,"cursor",0),X(this,"source",""),X(this,"representationStart",0),X(this,"representationEnd",-1),this.source=e}advanceCodePoint(e=1){this.cursor=this.cursor+e,this.representationEnd=this.cursor-1}readCodePoint(){const e=this.source.codePointAt(this.cursor);if(e!==void 0)return this.cursor=this.cursor+1,this.representationEnd=this.cursor-1,e}unreadCodePoint(e=1){this.cursor=this.cursor-e,this.representationEnd=this.cursor-1}resetRepresentation(){this.representationStart=this.cursor,this.representationEnd=-1}}function Fl(t,e){let n="";const r=e.readCodePoint();for(;;){const s=e.readCodePoint();if(s===void 0){const a=[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new cn(Xe.UnexpectedEOFInString,e.representationStart,e.representationEnd,["4.3.5. Consume a string token","Unexpected EOF"],a)),a}if(ts(s)){e.unreadCodePoint();const a=[v.BadString,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new cn(Xe.UnexpectedNewLineInString,e.representationStart,e.source.codePointAt(e.cursor)===In&&e.source.codePointAt(e.cursor+1)===Ln?e.representationEnd+2:e.representationEnd+1,["4.3.5. Consume a string token","Unexpected newline"],a)),a}if(s===r)return[v.String,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(s!==92)s===0||Un(s)?n+=String.fromCodePoint(un):n+=String.fromCodePoint(s);else{if(e.source.codePointAt(e.cursor)===void 0)continue;if(ts(e.source.codePointAt(e.cursor)??-1)){e.source.codePointAt(e.cursor)===In&&e.source.codePointAt(e.cursor+1)===Ln&&e.advanceCodePoint(),e.advanceCodePoint();continue}n+=String.fromCodePoint(rs(t,e))}}}function xl(t){return!(t.length!==3||t[0]!==117&&t[0]!==85||t[1]!==114&&t[1]!==82||t[2]!==108&&t[2]!==76)}function Ys(t,e){for(;;){const n=e.source.codePointAt(e.cursor);if(n===void 0)return;if(n===41)return void e.advanceCodePoint();zn(e)?(e.advanceCodePoint(),rs(t,e)):e.advanceCodePoint()}}function Sl(t,e){for(;Cn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();let n="";for(;;){if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new cn(Xe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Unexpected EOF"],a)),a}if(e.source.codePointAt(e.cursor)===41)return e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];if(Cn(e.source.codePointAt(e.cursor)??-1)){for(e.advanceCodePoint();Cn(e.source.codePointAt(e.cursor)??-1);)e.advanceCodePoint();if(e.source.codePointAt(e.cursor)===void 0){const a=[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}];return t.onParseError(new cn(Xe.UnexpectedEOFInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","Consume as much whitespace as possible","Unexpected EOF"],a)),a}return e.source.codePointAt(e.cursor)===41?(e.advanceCodePoint(),[v.URL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:n}]):(Ys(t,e),[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0])}const s=e.source.codePointAt(e.cursor);if(s===34||s===39||s===40||(r=s??-1)===11||r===127||0<=r&&r<=8||14<=r&&r<=31){Ys(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new cn(Xe.UnexpectedCharacterInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token",`Unexpected U+0022 QUOTATION MARK ("), U+0027 APOSTROPHE ('), U+0028 LEFT PARENTHESIS (() or non-printable code point`],a)),a}if(s===92){if(zn(e)){e.advanceCodePoint(),n+=String.fromCodePoint(rs(t,e));continue}Ys(t,e);const a=[v.BadURL,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,void 0];return t.onParseError(new cn(Xe.InvalidEscapeSequenceInURL,e.representationStart,e.representationEnd,["4.3.6. Consume a url token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],a)),a}e.source.codePointAt(e.cursor)===0||Un(e.source.codePointAt(e.cursor)??-1)?(n+=String.fromCodePoint(un),e.advanceCodePoint()):(n+=e.source[e.cursor],e.advanceCodePoint())}var r}function Zs(t,e){const n=ss(t,e);if(e.source.codePointAt(e.cursor)!==40)return[v.Ident,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];if(xl(n)){e.advanceCodePoint();let r=0;for(;;){const s=Cn(e.source.codePointAt(e.cursor)??-1),a=Cn(e.source.codePointAt(e.cursor+1)??-1);if(s&&a){r+=1,e.advanceCodePoint(1);continue}const o=s?e.source.codePointAt(e.cursor+1):e.source.codePointAt(e.cursor);if(o===34||o===39)return r>0&&e.unreadCodePoint(r),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}];break}return Sl(t,e)}return e.advanceCodePoint(),[v.Function,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{value:String.fromCodePoint(...n)}]}function Al(t){return!(t.source.codePointAt(t.cursor)!==117&&t.source.codePointAt(t.cursor)!==85||t.source.codePointAt(t.cursor+1)!==Tn||t.source.codePointAt(t.cursor+2)!==63&&!_n(t.source.codePointAt(t.cursor+2)??-1))}function Ml(t,e){e.advanceCodePoint(2);const n=[],r=[];let s;for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&_n(s);)n.push(s),e.advanceCodePoint();for(;(s=e.source.codePointAt(e.cursor))!==void 0&&n.length<6&&s===63;)r.length===0&&r.push(...n),n.push(48),r.push(70),e.advanceCodePoint();if(!r.length&&e.source.codePointAt(e.cursor)===At&&_n(e.source.codePointAt(e.cursor+1)??-1))for(e.advanceCodePoint();(s=e.source.codePointAt(e.cursor))!==void 0&&r.length<6&&_n(s);)r.push(s),e.advanceCodePoint();if(!r.length){const i=parseInt(String.fromCodePoint(...n),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:i,endOfRange:i}]}const a=parseInt(String.fromCodePoint(...n),16),o=parseInt(String.fromCodePoint(...r),16);return[v.UnicodeRange,e.source.slice(e.representationStart,e.representationEnd+1),e.representationStart,e.representationEnd,{startOfRange:a,endOfRange:o}]}function Ye(t,e){const n=oo(t),r=[];for(;!n.endOfFile();)r.push(n.nextToken());return r.push(n.nextToken()),r}function oo(t,e){const n=t.css.valueOf(),r=t.unicodeRangesAllowed??!1,s=new kl(n),a={onParseError:Pl};return{nextToken:function(){s.resetRepresentation();const o=s.source.codePointAt(s.cursor);if(o===void 0)return[v.EOF,"",-1,-1,void 0];if(o===47&&vl(s))return yl(a,s);if(r&&(o===117||o===85)&&Al(s))return Ml(0,s);if(Hn(o))return Zs(a,s);if(oe(o))return Ks(a,s);switch(o){case 44:return s.advanceCodePoint(),[v.Comma,",",s.representationStart,s.representationEnd,void 0];case 58:return s.advanceCodePoint(),[v.Colon,":",s.representationStart,s.representationEnd,void 0];case 59:return s.advanceCodePoint(),[v.Semicolon,";",s.representationStart,s.representationEnd,void 0];case 40:return s.advanceCodePoint(),[v.OpenParen,"(",s.representationStart,s.representationEnd,void 0];case 41:return s.advanceCodePoint(),[v.CloseParen,")",s.representationStart,s.representationEnd,void 0];case 91:return s.advanceCodePoint(),[v.OpenSquare,"[",s.representationStart,s.representationEnd,void 0];case 93:return s.advanceCodePoint(),[v.CloseSquare,"]",s.representationStart,s.representationEnd,void 0];case 123:return s.advanceCodePoint(),[v.OpenCurly,"{",s.representationStart,s.representationEnd,void 0];case 125:return s.advanceCodePoint(),[v.CloseCurly,"}",s.representationStart,s.representationEnd,void 0];case 39:case 34:return Fl(a,s);case 35:return Nl(a,s);case Tn:case 46:return ao(s)?Ks(a,s):(s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]);case Ln:case In:case 12:case 9:case 32:return Cl(s);case At:return ao(s)?Ks(a,s):bl(s)?(s.advanceCodePoint(3),[v.CDC,"-->",s.representationStart,s.representationEnd,void 0]):es(0,s)?Zs(a,s):(s.advanceCodePoint(),[v.Delim,"-",s.representationStart,s.representationEnd,{value:"-"}]);case 60:return fl(s)?(s.advanceCodePoint(4),[v.CDO,"<!--",s.representationStart,s.representationEnd,void 0]):(s.advanceCodePoint(),[v.Delim,"<",s.representationStart,s.representationEnd,{value:"<"}]);case 64:if(s.advanceCodePoint(),es(0,s)){const i=ss(a,s);return[v.AtKeyword,s.source.slice(s.representationStart,s.representationEnd+1),s.representationStart,s.representationEnd,{value:String.fromCodePoint(...i)}]}return[v.Delim,"@",s.representationStart,s.representationEnd,{value:"@"}];case 92:{if(zn(s))return Zs(a,s);s.advanceCodePoint();const i=[v.Delim,"\\",s.representationStart,s.representationEnd,{value:"\\"}];return a.onParseError(new cn(Xe.InvalidEscapeSequenceAfterBackslash,s.representationStart,s.representationEnd,["4.3.1. Consume a token","U+005C REVERSE SOLIDUS (\\)","The input stream does not start with a valid escape sequence"],i)),i}}return s.advanceCodePoint(),[v.Delim,s.source[s.representationStart],s.representationStart,s.representationEnd,{value:s.source[s.representationStart]}]},endOfFile:function(){return s.source.codePointAt(s.cursor)===void 0}}}function Pl(){}function io(t,e){const n=[];for(const i of e)n.push(i.codePointAt(0));const r=Dl(n);r[0]===101&&Js(r,0,r[0]);const s=String.fromCodePoint(...r),a=t[4].signCharacter==="+"?t[4].signCharacter:"",o=t[4].value.toString();t[1]=`${a}${o}${s}`,t[4].unit=e}function Dl(t){let e=0;if(t[0]===0)t.splice(0,1,un),e=1;else if(t[0]===At&&t[1]===At)e=2;else if(t[0]===At&&t[1])e=2,Hn(t[1])||(e+=Js(t,1,t[1]));else{if(t[0]===At&&!t[1])return[92,t[0]];Hn(t[0])?e=1:(e=1,e+=Js(t,0,t[0]))}for(let n=e;n<t.length;n++)t[n]!==0?Xs(t[n])||(n+=Bl(t,n,t[n])):(t.splice(n,1,un),n++);return t}function Bl(t,e,n){return t.splice(e,1,92,n),1}function Js(t,e,n){const r=n.toString(16),s=[];for(const a of r)s.push(a.codePointAt(0));return t.splice(e,1,92,...s,32),1+s.length}const Rl=Object.values(v);function Qs(t){return!!Array.isArray(t)&&!(t.length<4)&&!!Rl.includes(t[0])&&typeof t[1]=="string"&&typeof t[2]=="number"&&typeof t[3]=="number"}function mt(t){if(!t)return!1;switch(t[0]){case v.Dimension:case v.Number:case v.Percentage:return!0;default:return!1}}function lo(t){if(!t)return!1;switch(t[0]){case v.Whitespace:case v.Comment:return!0;default:return!1}}function we(t){return!!t&&t[0]===v.Comma}function co(t){return!!t&&t[0]===v.Comment}function as(t){return!!t&&t[0]===v.Delim}function st(t){return!!t&&t[0]===v.Dimension}function Se(t){return!!t&&t[0]===v.EOF}function Ol(t){return!!t&&t[0]===v.Function}function Wl(t){return!!t&&t[0]===v.Hash}function $t(t){return!!t&&t[0]===v.Ident}function H(t){return!!t&&t[0]===v.Number}function nt(t){return!!t&&t[0]===v.Percentage}function ta(t){return!!t&&t[0]===v.Whitespace}function uo(t){return!!t&&t[0]===v.OpenParen}function Il(t){return!!t&&t[0]===v.CloseParen}function Ll(t){return!!t&&t[0]===v.OpenSquare}function Tl(t){return!!t&&t[0]===v.OpenCurly}var ie;function ho(t){let e=t.slice();return(n,r,s)=>{let a=-1;for(let o=e.indexOf(r);o<e.length&&(a=n.indexOf(e[o]),a===-1||a<s);o++);return a===-1||a===s&&r===n[s]&&(a++,a>=n.length)?-1:(e=n.slice(),a)}}function os(t,e){const n=e[0];if(uo(n)||Tl(n)||Ll(n)){const r=Hl(t,e);return{advance:r.advance,node:r.node}}if(Ol(n)){const r=_l(t,e);return{advance:r.advance,node:r.node}}if(ta(n)){const r=po(t,e);return{advance:r.advance,node:r.node}}if(co(n)){const r=Ul(t,e);return{advance:r.advance,node:r.node}}return{advance:1,node:new G(n)}}(function(t){t.Function="function",t.SimpleBlock="simple-block",t.Whitespace="whitespace",t.Comment="comment",t.Token="token"})(ie||(ie={}));class fo{constructor(){X(this,"value",[])}indexOf(e){return this.value.indexOf(e)}at(e){if(typeof e=="number")return e<0&&(e=this.value.length+e),this.value[e]}forEach(e,n){if(this.value.length===0)return;const r=ho(this.value);let s=0;for(;s<this.value.length;){const a=this.value[s];let o;if(n&&(o={...n}),e({node:a,parent:this,state:o},s)===!1)return!1;if(s=r(this.value,a,s),s===-1)break}}walk(e,n){this.value.length!==0&&this.forEach((r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!this.value.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0,n)}}class qt extends fo{constructor(e,n,r){super(),X(this,"type",ie.Function),X(this,"name"),X(this,"endToken"),this.name=e,this.endToken=n,this.value=r}getName(){return this.name[4].value}normalize(){Se(this.endToken)&&(this.endToken=[v.CloseParen,")",-1,-1,void 0])}tokens(){return Se(this.endToken)?[this.name,...this.value.flatMap(e=>e.tokens())]:[this.name,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>Qs(n)?Ke(n):n.toString()).join("");return Ke(this.name)+e+Ke(this.endToken)}toJSON(){return{type:this.type,name:this.getName(),tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isFunctionNode(){return qt.isFunctionNode(this)}static isFunctionNode(e){return!!e&&e instanceof qt&&e.type===ie.Function}}function _l(t,e){const n=[];let r=1;for(;;){const s=e[r];if(!s||Se(s))return t.onParseError(new wr("Unexpected EOF while consuming a function.",e[0][2],e[e.length-1][3],["5.4.9. Consume a function","Unexpected EOF"])),{advance:e.length,node:new qt(e[0],s,n)};if(Il(s))return{advance:r+1,node:new qt(e[0],s,n)};if(lo(s)){const o=mo(t,e.slice(r));r+=o.advance,n.push(...o.nodes);continue}const a=os(t,e.slice(r));r+=a.advance,n.push(a.node)}}class vr extends fo{constructor(e,n,r){super(),X(this,"type",ie.SimpleBlock),X(this,"startToken"),X(this,"endToken"),this.startToken=e,this.endToken=n,this.value=r}normalize(){if(Se(this.endToken)){const e=$l(this.startToken);e&&(this.endToken=e)}}tokens(){return Se(this.endToken)?[this.startToken,...this.value.flatMap(e=>e.tokens())]:[this.startToken,...this.value.flatMap(e=>e.tokens()),this.endToken]}toString(){const e=this.value.map(n=>Qs(n)?Ke(n):n.toString()).join("");return Ke(this.startToken)+e+Ke(this.endToken)}toJSON(){return{type:this.type,startToken:this.startToken,tokens:this.tokens(),value:this.value.map(e=>e.toJSON())}}isSimpleBlockNode(){return vr.isSimpleBlockNode(this)}static isSimpleBlockNode(e){return!!e&&e instanceof vr&&e.type===ie.SimpleBlock}}function Hl(t,e){const n=wl(e[0][0]);if(!n)throw new Error("Failed to parse, a mirror variant must exist for all block open tokens.");const r=[];let s=1;for(;;){const a=e[s];if(!a||Se(a))return t.onParseError(new wr("Unexpected EOF while consuming a simple block.",e[0][2],e[e.length-1][3],["5.4.8. Consume a simple block","Unexpected EOF"])),{advance:e.length,node:new vr(e[0],a,r)};if(a[0]===n)return{advance:s+1,node:new vr(e[0],a,r)};if(lo(a)){const i=mo(t,e.slice(s));s+=i.advance,r.push(...i.nodes);continue}const o=os(t,e.slice(s));s+=o.advance,r.push(o.node)}}class ae{constructor(e){X(this,"type",ie.Whitespace),X(this,"value"),this.value=e}tokens(){return this.value}toString(){return Ke(...this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isWhitespaceNode(){return ae.isWhitespaceNode(this)}static isWhitespaceNode(e){return!!e&&e instanceof ae&&e.type===ie.Whitespace}}function po(t,e){let n=0;for(;;){const r=e[n];if(!ta(r))return{advance:n,node:new ae(e.slice(0,n))};n++}}class br{constructor(e){X(this,"type",ie.Comment),X(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return Ke(this.value)}toJSON(){return{type:this.type,tokens:this.tokens()}}isCommentNode(){return br.isCommentNode(this)}static isCommentNode(e){return!!e&&e instanceof br&&e.type===ie.Comment}}function Ul(t,e){return{advance:1,node:new br(e[0])}}function mo(t,e){const n=[];let r=0;for(;;)if(ta(e[r])){const s=po(0,e.slice(r));r+=s.advance,n.push(s.node)}else{if(!co(e[r]))return{advance:r,nodes:n};n.push(new br(e[r])),r++}}class G{constructor(e){X(this,"type",ie.Token),X(this,"value"),this.value=e}tokens(){return[this.value]}toString(){return this.value[1]}toJSON(){return{type:this.type,tokens:this.tokens()}}isTokenNode(){return G.isTokenNode(this)}static isTokenNode(e){return!!e&&e instanceof G&&e.type===ie.Token}}function zl(t,e){const n={onParseError:()=>{}},r=[...t];Se(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=os(n,r);if(Se(r[Math.min(s.advance,r.length-1)]))return s.node;n.onParseError(new wr("Expected EOF after parsing a component value.",t[0][2],t[t.length-1][3],["5.3.9. Parse a component value","Expected EOF"]))}function Gl(t,e){const n={onParseError:e?.onParseError??(()=>{})},r=[...t];if(t.length===0)return[];Se(r[r.length-1])&&r.push([v.EOF,"",r[r.length-1][2],r[r.length-1][3],void 0]);const s=[];let a=[],o=0;for(;;){if(!r[o]||Se(r[o]))return a.length&&s.push(a),s;if(we(r[o])){s.push(a),a=[],o++;continue}const i=os(n,t.slice(o));a.push(i.node),o+=i.advance}}function jl(t,e,n){if(t.length===0)return;const r=ho(t);let s=0;for(;s<t.length;){const a=t[s];if(e({node:a,parent:{value:t},state:void 0},s)===!1)return!1;if(s=r(t,a,s),s===-1)break}}function ql(t,e,n){t.length!==0&&jl(t,(r,s)=>e(r,s)!==!1&&(!("walk"in r.node)||!t.includes(r.node)||r.node.walk(e,r.state)!==!1)&&void 0)}function Vl(t,e){for(let n=0;n<t.length;n++)ql(t[n],(r,s)=>{if(typeof s!="number")return;const a=e(r.node);a&&(Array.isArray(a)?r.parent.value.splice(s,1,...a):r.parent.value.splice(s,1,a))});return t}function Xl(t){return vr.isSimpleBlockNode(t)}function le(t){return qt.isFunctionNode(t)}function Ae(t){return ae.isWhitespaceNode(t)}function Me(t){return br.isCommentNode(t)}function Ze(t){return Ae(t)||Me(t)}function T(t){return G.isTokenNode(t)}const Kl=/[A-Z]/g;function Vt(t){return t.replace(Kl,e=>String.fromCharCode(e.charCodeAt(0)+32))}const Yl={cm:"px",in:"px",mm:"px",pc:"px",pt:"px",px:"px",q:"px",deg:"deg",grad:"deg",rad:"deg",turn:"deg",ms:"s",s:"s",hz:"hz",khz:"hz"},Zl=new Map([["cm",t=>t],["mm",t=>10*t],["q",t=>40*t],["in",t=>t/2.54],["pc",t=>t/2.54*6],["pt",t=>t/2.54*72],["px",t=>t/2.54*96]]),is=new Map([["deg",t=>t],["grad",t=>t/.9],["rad",t=>t/180*Math.PI],["turn",t=>t/360]]),$r=new Map([["deg",t=>.9*t],["grad",t=>t],["rad",t=>.9*t/180*Math.PI],["turn",t=>.9*t/360]]),Jl=new Map([["hz",t=>t],["khz",t=>t/1e3]]),Ql=new Map([["cm",t=>2.54*t],["mm",t=>25.4*t],["q",t=>25.4*t*4],["in",t=>t],["pc",t=>6*t],["pt",t=>72*t],["px",t=>96*t]]),tc=new Map([["hz",t=>1e3*t],["khz",t=>t]]),ec=new Map([["cm",t=>t/10],["mm",t=>t],["q",t=>4*t],["in",t=>t/25.4],["pc",t=>t/25.4*6],["pt",t=>t/25.4*72],["px",t=>t/25.4*96]]),nc=new Map([["ms",t=>t],["s",t=>t/1e3]]),rc=new Map([["cm",t=>t/6*2.54],["mm",t=>t/6*25.4],["q",t=>t/6*25.4*4],["in",t=>t/6],["pc",t=>t],["pt",t=>t/6*72],["px",t=>t/6*96]]),sc=new Map([["cm",t=>t/72*2.54],["mm",t=>t/72*25.4],["q",t=>t/72*25.4*4],["in",t=>t/72],["pc",t=>t/72*6],["pt",t=>t],["px",t=>t/72*96]]),ac=new Map([["cm",t=>t/96*2.54],["mm",t=>t/96*25.4],["q",t=>t/96*25.4*4],["in",t=>t/96],["pc",t=>t/96*6],["pt",t=>t/96*72],["px",t=>t]]),oc=new Map([["cm",t=>t/4/10],["mm",t=>t/4],["q",t=>t],["in",t=>t/4/25.4],["pc",t=>t/4/25.4*6],["pt",t=>t/4/25.4*72],["px",t=>t/4/25.4*96]]),go=new Map([["deg",t=>180*t/Math.PI],["grad",t=>180*t/Math.PI/.9],["rad",t=>t],["turn",t=>180*t/Math.PI/360]]),ic=new Map([["ms",t=>1e3*t],["s",t=>t]]),yr=new Map([["deg",t=>360*t],["grad",t=>360*t/.9],["rad",t=>360*t/180*Math.PI],["turn",t=>t]]),vo=new Map([["cm",Zl],["mm",ec],["q",oc],["in",Ql],["pc",rc],["pt",sc],["px",ac],["ms",nc],["s",ic],["deg",is],["grad",$r],["rad",go],["turn",yr],["hz",Jl],["khz",tc]]);function ne(t,e){if(!st(t)||!st(e))return e;const n=Vt(t[4].unit),r=Vt(e[4].unit);if(n===r)return e;const s=vo.get(r);if(!s)return e;const a=s.get(n);if(!a)return e;const o=a(e[4].value),i=[v.Dimension,"",e[2],e[3],{...e[4],signCharacter:o<0?"-":void 0,type:Number.isInteger(o)?S.Integer:S.Number,value:o}];return io(i,t[4].unit),i}function lc(t){if(!st(t))return t;const e=Vt(t[4].unit),n=Yl[e];if(e===n)return t;const r=vo.get(e);if(!r)return t;const s=r.get(n);if(!s)return t;const a=s(t[4].value),o=[v.Dimension,"",t[2],t[3],{...t[4],signCharacter:a<0?"-":void 0,type:Number.isInteger(a)?S.Integer:S.Number,value:a}];return io(o,n),o}function cc(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(H(e)&&H(n)){const r=e[4].value+n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number}])}if(nt(e)&&nt(n)){const r=e[4].value+n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(st(e)&&st(n)&&(n=ne(e,n),Vt(e[4].unit)===Vt(n[4].unit))){const r=e[4].value+n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:e[4].unit}])}return-1}function uc(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(H(e)&&H(n)){const r=e[4].value/n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:Number.isInteger(r)?S.Integer:S.Number}])}if(nt(e)&&H(n)){const r=e[4].value/n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(st(e)&&H(n)){const r=e[4].value/n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:Number.isInteger(r)?S.Integer:S.Number,unit:e[4].unit}])}return-1}function kn(t){return!!t&&typeof t=="object"&&"inputs"in t&&Array.isArray(t.inputs)&&"operation"in t}function Xt(t){if(t===-1)return-1;const e=[];for(let n=0;n<t.inputs.length;n++){const r=t.inputs[n];if(T(r)){e.push(r);continue}const s=Xt(r);if(s===-1)return-1;e.push(s)}return t.operation(e)}function hc(t){if(t.length!==2)return-1;const e=t[0].value,n=t[1].value;if(H(e)&&H(n)){const r=e[4].value*n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number}])}if(nt(e)&&H(n)){const r=e[4].value*n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(H(e)&&nt(n)){const r=e[4].value*n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(st(e)&&H(n)){const r=e[4].value*n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:e[4].unit}])}if(H(e)&&st(n)){const r=e[4].value*n[4].value;return new G([v.Dimension,r.toString()+n[4].unit,e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:n[4].unit}])}return-1}function Gn(t,e){for(let n=0;n<t.length;n++){const r=t[n];if(!T(r))continue;const s=r.value;if(!$t(s))continue;const a=Vt(s[4].value);switch(a){case"e":t.splice(n,1,new G([v.Number,Math.E.toString(),s[2],s[3],{value:Math.E,type:S.Number}]));break;case"pi":t.splice(n,1,new G([v.Number,Math.PI.toString(),s[2],s[3],{value:Math.PI,type:S.Number}]));break;case"infinity":t.splice(n,1,new G([v.Number,"infinity",s[2],s[3],{value:1/0,type:S.Number}]));break;case"-infinity":t.splice(n,1,new G([v.Number,"-infinity",s[2],s[3],{value:-1/0,type:S.Number}]));break;case"nan":t.splice(n,1,new G([v.Number,"NaN",s[2],s[3],{value:Number.NaN,type:S.Number}]));break;default:if(e.has(a)){const o=e.get(a);t.splice(n,1,new G(o))}}}return t}function ls(t){if(t.length!==1)return-1;const e=t[0].value;return mt(e)?t[0]:-1}function ce(t,e,n){return st(e)?Nr(t,e[4].unit,n):nt(e)?fc(t,n):H(e)?Pe(t,n):-1}function Nr(t,e,n){const r=t.tokens();return{inputs:[new G([v.Dimension,n.toString()+e,r[0][2],r[r.length-1][3],{value:n,type:Number.isInteger(n)?S.Integer:S.Number,unit:e}])],operation:ls}}function fc(t,e){const n=t.tokens();return{inputs:[new G([v.Percentage,e.toString()+"%",n[0][2],n[n.length-1][3],{value:e}])],operation:ls}}function Pe(t,e){const n=t.tokens();return{inputs:[new G([v.Number,e.toString(),n[0][2],n[n.length-1][3],{value:e,type:Number.isInteger(e)?S.Integer:S.Number}])],operation:ls}}function pc(t,e){const n=e.value;return H(n)?Nr(t,"rad",Math.acos(n[4].value)):-1}function dc(t,e){const n=e.value;return H(n)?Nr(t,"rad",Math.asin(n[4].value)):-1}function mc(t,e){const n=e.value;return H(n)?Nr(t,"rad",Math.atan(n[4].value)):-1}function cs(t){return st(t)||H(t)}function ea(t){if(t.length===0)return!0;const e=t[0];if(!mt(e))return!1;if(t.length===1)return!0;if(st(e)){const n=Vt(e[4].unit);for(let r=1;r<t.length;r++){const s=t[r];if(e[0]!==s[0]||n!==Vt(s[4].unit))return!1}return!0}for(let n=1;n<t.length;n++){const r=t[n];if(e[0]!==r[0])return!1}return!0}function Je(t,e){return!!mt(t)&&(st(t)?t[0]===e[0]&&Vt(t[4].unit)===Vt(e[4].unit):t[0]===e[0])}function gc(t,e,n){const r=e.value;if(!cs(r))return-1;const s=ne(r,n.value);return Je(r,s)?Nr(t,"rad",Math.atan2(r[4].value,s[4].value)):-1}function vc(t,e,n){const r=e.value;return!mt(r)||!n.rawPercentages&&nt(r)?-1:ce(t,r,Math.abs(r[4].value))}function bc(t,e,n,r,s){if(!T(e)||!T(n)||!T(r))return-1;const a=e.value;if(!mt(a)||!s.rawPercentages&&nt(a))return-1;const o=ne(a,n.value);if(!Je(a,o))return-1;const i=ne(a,r.value);return Je(a,i)?ce(t,a,Math.max(a[4].value,Math.min(o[4].value,i[4].value))):-1}function wc(t,e){const n=e.value;if(!cs(n))return-1;let r=n[4].value;if(st(n))switch(n[4].unit.toLowerCase()){case"rad":break;case"deg":r=is.get("rad")(n[4].value);break;case"grad":r=$r.get("rad")(n[4].value);break;case"turn":r=yr.get("rad")(n[4].value);break;default:return-1}return r=Math.cos(r),Pe(t,r)}function $c(t,e){const n=e.value;return H(n)?Pe(t,Math.exp(n[4].value)):-1}function yc(t,e,n){if(!e.every(T))return-1;const r=e[0].value;if(!mt(r)||!n.rawPercentages&&nt(r))return-1;const s=e.map(i=>ne(r,i.value));if(!ea(s))return-1;const a=s.map(i=>i[4].value),o=Math.hypot(...a);return ce(t,r,o)}function bo(t,e,n){if(!e.every(T))return-1;const r=e[0].value;if(!mt(r)||!n.rawPercentages&&nt(r))return-1;const s=e.map(i=>ne(r,i.value));if(!ea(s))return-1;const a=s.map(i=>i[4].value),o=Math.max(...a);return ce(t,r,o)}function wo(t,e,n){if(!e.every(T))return-1;const r=e[0].value;if(!mt(r)||!n.rawPercentages&&nt(r))return-1;const s=e.map(i=>ne(r,i.value));if(!ea(s))return-1;const a=s.map(i=>i[4].value),o=Math.min(...a);return ce(t,r,o)}function Nc(t,e,n){const r=e.value;if(!mt(r))return-1;const s=ne(r,n.value);if(!Je(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)&&(Number.isFinite(s[4].value)||(s[4].value!==Number.POSITIVE_INFINITY||r[4].value!==Number.NEGATIVE_INFINITY&&!Object.is(0*r[4].value,-0))&&(s[4].value!==Number.NEGATIVE_INFINITY||r[4].value!==Number.POSITIVE_INFINITY&&!Object.is(0*r[4].value,0)))?Number.isFinite(s[4].value)?(r[4].value%s[4].value+s[4].value)%s[4].value:r[4].value:Number.NaN,ce(t,r,a)}function Ec(t,e,n){const r=e.value,s=n.value;return!H(r)||!Je(r,s)?-1:Pe(t,Math.pow(r[4].value,s[4].value))}function Cc(t,e,n){const r=e.value;if(!mt(r))return-1;const s=ne(r,n.value);if(!Je(r,s))return-1;let a;return a=s[4].value===0?Number.NaN:Number.isFinite(r[4].value)?Number.isFinite(s[4].value)?r[4].value%s[4].value:r[4].value:Number.NaN,ce(t,r,a)}function kc(t,e,n,r,s){const a=n.value;if(!mt(a)||!s.rawPercentages&&nt(a))return-1;const o=ne(a,r.value);if(!Je(a,o))return-1;let i;if(o[4].value===0)i=Number.NaN;else if(Number.isFinite(a[4].value)||Number.isFinite(o[4].value))if(!Number.isFinite(a[4].value)&&Number.isFinite(o[4].value))i=a[4].value;else if(Number.isFinite(a[4].value)&&!Number.isFinite(o[4].value))switch(e){case"down":i=a[4].value<0?-1/0:Object.is(-0,0*a[4].value)?-0:0;break;case"up":i=a[4].value>0?1/0:Object.is(0,0*a[4].value)?0:-0;break;default:i=Object.is(0,0*a[4].value)?0:-0}else if(Number.isFinite(o[4].value))switch(e){case"down":i=Math.floor(a[4].value/o[4].value)*o[4].value;break;case"up":i=Math.ceil(a[4].value/o[4].value)*o[4].value;break;case"to-zero":i=Math.trunc(a[4].value/o[4].value)*o[4].value;break;default:{let c=Math.floor(a[4].value/o[4].value)*o[4].value,l=Math.ceil(a[4].value/o[4].value)*o[4].value;if(c>l){const d=c;c=l,l=d}const u=Math.abs(a[4].value-c),f=Math.abs(a[4].value-l);i=u===f?l:u<f?c:l;break}}else i=a[4].value;else i=Number.NaN;return ce(t,a,i)}function Fc(t,e,n){const r=e.value;return!mt(r)||!n.rawPercentages&&nt(r)?-1:Pe(t,Math.sign(r[4].value))}function xc(t,e){const n=e.value;if(!cs(n))return-1;let r=n[4].value;if(st(n))switch(Vt(n[4].unit)){case"rad":break;case"deg":r=is.get("rad")(n[4].value);break;case"grad":r=$r.get("rad")(n[4].value);break;case"turn":r=yr.get("rad")(n[4].value);break;default:return-1}return r=Math.sin(r),Pe(t,r)}function Sc(t,e){const n=e.value;return H(n)?Pe(t,Math.sqrt(n[4].value)):-1}function Ac(t,e){const n=e.value;if(!cs(n))return-1;const r=n[4].value;let s=0,a=n[4].value;if(st(n))switch(Vt(n[4].unit)){case"rad":s=go.get("deg")(r);break;case"deg":s=r,a=is.get("rad")(r);break;case"grad":s=$r.get("deg")(r),a=$r.get("rad")(r);break;case"turn":s=yr.get("deg")(r),a=yr.get("rad")(r);break;default:return-1}const o=s/90;return a=s%90==0&&o%2!=0?o>0?1/0:-1/0:Math.tan(a),Pe(t,a)}function Mc(t){if(t.length!==2)return-1;const e=t[0].value;let n=t[1].value;if(H(e)&&H(n)){const r=e[4].value-n[4].value;return new G([v.Number,r.toString(),e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number}])}if(nt(e)&&nt(n)){const r=e[4].value-n[4].value;return new G([v.Percentage,r.toString()+"%",e[2],n[3],{value:r}])}if(st(e)&&st(n)&&(n=ne(e,n),Vt(e[4].unit)===Vt(n[4].unit))){const r=e[4].value-n[4].value;return new G([v.Dimension,r.toString()+e[4].unit,e[2],n[3],{value:r,type:e[4].type===S.Integer&&n[4].type===S.Integer?S.Integer:S.Number,unit:e[4].unit}])}return-1}function Pc(t,e){if(e.length===1){const n=e[0];if(!n||!T(n))return-1;const r=n.value;return H(r)?Pe(t,Math.log(r[4].value)):-1}if(e.length===2){const n=e[0];if(!n||!T(n))return-1;const r=n.value;if(!H(r))return-1;const s=e[1];if(!s||!T(s))return-1;const a=s.value;return H(a)?Pe(t,Math.log(r[4].value)/Math.log(a[4].value)):-1}return-1}const Dc=/^none$/i;function na(t){if(Array.isArray(t)){const n=t.filter(r=>!(Ae(r)&&Me(r)));return n.length===1&&na(n[0])}if(!T(t))return!1;const e=t.value;return!!$t(e)&&Dc.test(e[4].value)}const Bc=String.fromCodePoint(0);function Rc(t,e,n,r,s,a){var o;if(e.fixed===-1&&!a.randomCaching)return-1;a.randomCaching||(a.randomCaching={propertyName:"",propertyN:0,elementID:"",documentID:""}),a.randomCaching&&!a.randomCaching.propertyN&&(a.randomCaching.propertyN=0);const i=n.value;if(!mt(i))return-1;const c=ne(i,r.value);if(!Je(i,c))return-1;let l=null;if(s&&(l=ne(i,s.value),!Je(i,l)))return-1;if(!Number.isFinite(i[4].value)||!Number.isFinite(c[4].value)||!Number.isFinite(c[4].value-i[4].value))return ce(t,i,Number.NaN);if(l&&!Number.isFinite(l[4].value))return ce(t,i,i[4].value);const u=e.fixed===-1?Oc(Wc([e.dashedIdent?e.dashedIdent:`${(o=a.randomCaching)==null?void 0:o.propertyName} ${a.randomCaching.propertyN++}`,e.elementShared?"":a.randomCaching.elementID,a.randomCaching.documentID].join(Bc))):()=>e.fixed;let f=i[4].value,d=c[4].value;if(f>d&&([f,d]=[d,f]),l&&(l[4].value<=0||Math.abs(f-d)/l[4].value>1e10)&&(l=null),l){const p=Math.max(l[4].value/1e3,1e-9),m=[f];let $=0;for(;;){$+=l[4].value;const N=f+$;if(!(N+p<d)){m.push(d);break}if(m.push(N),N+l[4].value-p>d)break}const w=u();return ce(t,i,Number(m[Math.floor(m.length*w)].toFixed(5)))}const b=u();return ce(t,i,Number((b*(d-f)+f).toFixed(5)))}function Oc(t=.34944106645296036,e=.19228640875738723,n=.8784393832007205,r=.04850964319275053){return()=>{const s=((t|=0)+(e|=0)|0)+(r|=0)|0;return r=r+1|0,t=e^e>>>9,e=(n|=0)+(n<<3)|0,n=(n=n<<21|n>>>11)+s|0,(s>>>0)/4294967296}}function Wc(t){let e=0,n=0,r=0;e^=-1;for(let s=0,a=t.length;s<a;s++)r=255&(e^t.charCodeAt(s)),n=+("0x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substring(9*r,9*r+8)),e=e>>>8^n;return(-1^e)>>>0}const ra=new Map([["abs",function(t,e,n){return De(t,e,n,vc)}],["acos",function(t,e,n){return De(t,e,n,pc)}],["asin",function(t,e,n){return De(t,e,n,dc)}],["atan",function(t,e,n){return De(t,e,n,mc)}],["atan2",function(t,e,n){return us(t,e,n,gc)}],["calc",_t],["clamp",function(t,e,n){const r=Gn([...t.value.filter(p=>!Ze(p))],e),s=[],a=[],o=[];{let p=s;for(let m=0;m<r.length;m++){const $=r[m];if(T($)&&we($.value)){if(p===o)return-1;if(p===a){p=o;continue}if(p===s){p=a;continue}return-1}p.push($)}}const i=na(s),c=na(o);if(i&&c)return _t(re(a),e,n);const l=Xt(_t(re(a),e,n));if(l===-1)return-1;if(i){const p=Xt(_t(re(o),e,n));return p===-1?-1:wo((u=l,f=p,new qt([v.Function,"min(",-1,-1,{value:"min"}],[v.CloseParen,")",-1,-1,void 0],[u,new G([v.Comma,",",-1,-1,void 0]),f])),[l,p],n)}if(c){const p=Xt(_t(re(s),e,n));return p===-1?-1:bo(Hc(p,l),[p,l],n)}var u,f;const d=Xt(_t(re(s),e,n));if(d===-1)return-1;const b=Xt(_t(re(o),e,n));return b===-1?-1:bc(t,d,l,b,n)}],["cos",function(t,e,n){return De(t,e,n,wc)}],["exp",function(t,e,n){return De(t,e,n,$c)}],["hypot",function(t,e,n){return hs(t,t.value,e,n,yc)}],["log",function(t,e,n){return hs(t,t.value,e,n,Pc)}],["max",function(t,e,n){return hs(t,t.value,e,n,bo)}],["min",function(t,e,n){return hs(t,t.value,e,n,wo)}],["mod",function(t,e,n){return us(t,e,n,Nc)}],["pow",function(t,e,n){return us(t,e,n,Ec)}],["random",function(t,e,n){const r=_c(t.value.filter(u=>!Ze(u)),e,n);if(r===-1)return-1;const[s,a]=r,o=$o(a,e,n);if(o===-1)return-1;const[i,c,l]=o;return!i||!c?-1:Rc(t,s,i,c,l,n)}],["rem",function(t,e,n){return us(t,e,n,Cc)}],["round",function(t,e,n){const r=Gn([...t.value.filter(u=>!Ze(u))],e);let s="",a=!1;const o=[],i=[];{let u=o;for(let f=0;f<r.length;f++){const d=r[f];if(!s&&o.length===0&&i.length===0&&T(d)&&$t(d.value)){const b=d.value[4].value.toLowerCase();if(Tc.has(b)){s=b;continue}}if(T(d)&&we(d.value)){if(u===i)return-1;if(u===o&&s&&o.length===0)continue;if(u===o){a=!0,u=i;continue}return-1}u.push(d)}}const c=Xt(_t(re(o),e,n));if(c===-1)return-1;a||i.length!==0||i.push(new G([v.Number,"1",-1,-1,{value:1,type:S.Integer}]));const l=Xt(_t(re(i),e,n));return l===-1?-1:(s||(s="nearest"),kc(t,s,c,l,n))}],["sign",function(t,e,n){return De(t,e,n,Fc)}],["sin",function(t,e,n){return De(t,e,n,xc)}],["sqrt",function(t,e,n){return De(t,e,n,Sc)}],["tan",function(t,e,n){return De(t,e,n,Ac)}]]);function _t(t,e,n){const r=Gn([...t.value.filter(a=>!Ze(a))],e);if(r.length===1&&T(r[0]))return{inputs:[r[0]],operation:ls};let s=0;for(;s<r.length;){const a=r[s];if(Xl(a)&&uo(a.startToken)){const o=_t(a,e,n);if(o===-1)return-1;r.splice(s,1,o)}else if(le(a)){const o=ra.get(a.getName().toLowerCase());if(!o)return-1;const i=o(a,e,n);if(i===-1)return-1;r.splice(s,1,i)}else s++}if(s=0,r.length===1&&kn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!T(a)&&!kn(a)){s++;continue}const o=r[s+1];if(!o||!T(o)){s++;continue}const i=o.value;if(!as(i)||i[4].value!=="*"&&i[4].value!=="/"){s++;continue}const c=r[s+2];if(!c||!T(c)&&!kn(c))return-1;i[4].value!=="*"?i[4].value!=="/"?s++:r.splice(s,3,{inputs:[a,c],operation:uc}):r.splice(s,3,{inputs:[a,c],operation:hc})}if(s=0,r.length===1&&kn(r[0]))return r[0];for(;s<r.length;){const a=r[s];if(!a||!T(a)&&!kn(a)){s++;continue}const o=r[s+1];if(!o||!T(o)){s++;continue}const i=o.value;if(!as(i)||i[4].value!=="+"&&i[4].value!=="-"){s++;continue}const c=r[s+2];if(!c||!T(c)&&!kn(c))return-1;i[4].value!=="+"?i[4].value!=="-"?s++:r.splice(s,3,{inputs:[a,c],operation:Mc}):r.splice(s,3,{inputs:[a,c],operation:cc})}return r.length===1&&kn(r[0])?r[0]:-1}function De(t,e,n,r){const s=Ic(t.value,e,n);return s===-1?-1:r(t,s,n)}function Ic(t,e,n){const r=Xt(_t(re(Gn([...t.filter(s=>!Ze(s))],e)),e,n));return r===-1?-1:r}function us(t,e,n,r){const s=Lc(t.value,e,n);if(s===-1)return-1;const[a,o]=s;return r(t,a,o,n)}function Lc(t,e,n){const r=Gn([...t.filter(c=>!Ze(c))],e),s=[],a=[];{let c=s;for(let l=0;l<r.length;l++){const u=r[l];if(T(u)&&we(u.value)){if(c===a)return-1;if(c===s){c=a;continue}return-1}c.push(u)}}const o=Xt(_t(re(s),e,n));if(o===-1)return-1;const i=Xt(_t(re(a),e,n));return i===-1?-1:[o,i]}function hs(t,e,n,r,s){const a=$o(t.value,n,r);return a===-1?-1:s(t,a,r)}function $o(t,e,n){const r=Gn([...t.filter(a=>!Ze(a))],e),s=[];{const a=[];let o=[];for(let i=0;i<r.length;i++){const c=r[i];T(c)&&we(c.value)?(a.push(o),o=[]):o.push(c)}a.push(o);for(let i=0;i<a.length;i++){if(a[i].length===0)return-1;const c=Xt(_t(re(a[i]),e,n));if(c===-1)return-1;s.push(c)}}return s}const Tc=new Set(["nearest","up","down","to-zero"]);function _c(t,e,n){const r={isAuto:!1,dashedIdent:"",fixed:-1,elementShared:!1},s=t[0];if(!T(s)||!$t(s.value))return[r,t];for(let a=0;a<t.length;a++){const o=t[a];if(!T(o))return-1;if(we(o.value))return[r,t.slice(a+1)];if(!$t(o.value))return-1;const i=o.value[4].value.toLowerCase();if(i!=="element-shared")if(i!=="fixed")if(i!=="auto"){if(i.startsWith("--")){if(r.fixed!==-1||r.isAuto)return-1;r.dashedIdent=i}}else{if(r.fixed!==-1||r.dashedIdent)return-1;r.isAuto=!0}else{if(r.elementShared||r.dashedIdent||r.isAuto)return-1;a++;const c=t[a];if(!c)return-1;const l=Xt(_t(re([c]),e,n));if(l===-1||!H(l.value)||l.value[4].value<0||l.value[4].value>1)return-1;r.fixed=Math.max(0,Math.min(l.value[4].value,1-1e-9))}else{if(r.fixed!==-1)return-1;r.elementShared=!0}}return-1}function re(t){return new qt([v.Function,"calc(",-1,-1,{value:"calc"}],[v.CloseParen,")",-1,-1,void 0],t)}function Hc(t,e){return new qt([v.Function,"max(",-1,-1,{value:"max"}],[v.CloseParen,")",-1,-1,void 0],[t,new G([v.Comma,",",-1,-1,void 0]),e])}function Uc(t){if(t===-1)return-1;if(le(t))return t;const e=t.value;return mt(e)&&Number.isNaN(e[4].value)?H(e)?new qt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,"NaN",e[2],e[3],{value:"NaN"}])]):st(e)?new qt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:S.Integer,unit:e[4].unit}])]):nt(e)?new qt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,"NaN",e[2],e[3],{value:"NaN"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Percentage,"1%",e[2],e[3],{value:1}])]):-1:t}function zc(t){if(t===-1)return-1;if(le(t))return t;const e=t.value;if(!mt(e)||Number.isFinite(e[4].value)||Number.isNaN(e[4].value))return t;let n="";return Number.NEGATIVE_INFINITY===e[4].value&&(n="-"),H(e)?new qt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}])]):st(e)?new qt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Dimension,"1"+e[4].unit,e[2],e[3],{value:1,type:S.Integer,unit:e[4].unit}])]):new qt([v.Function,"calc(",e[2],e[3],{value:"calc"}],[v.CloseParen,")",e[2],e[3],void 0],[new G([v.Ident,n+"infinity",e[2],e[3],{value:n+"infinity"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Delim,"*",e[2],e[3],{value:"*"}]),new ae([[v.Whitespace," ",e[2],e[3],void 0]]),new G([v.Percentage,"1%",e[2],e[3],{value:1}])])}function Gc(t){if(t===-1)return-1;if(le(t))return t;const e=t.value;return mt(e)&&Object.is(-0,e[4].value)&&(e[1]==="-0"||(nt(e)?e[1]="-0%":st(e)?e[1]="-0"+e[4].unit:e[1]="-0")),t}function jc(t,e=13){if(t===-1)return-1;if(e<=0||le(t))return t;const n=t.value;if(!mt(n)||Number.isInteger(n[4].value))return t;const r=Number(n[4].value.toFixed(e)).toString();return H(n)?n[1]=r:nt(n)?n[1]=r+"%":st(n)&&(n[1]=r+n[4].unit),t}function qc(t){return t===-1?-1:(le(t)||st(t.value)&&(t.value=lc(t.value)),t)}function Vc(t,e){let n=t;return e!=null&&e.toCanonicalUnits&&(n=qc(n)),n=jc(n,e?.precision),n=Gc(n),e!=null&&e.censorIntoStandardRepresentableValues||(n=Uc(n),n=zc(n)),n}function Xc(t){const e=new Map;if(!t)return e;for(const[n,r]of t)if(Qs(r))e.set(n,r);else if(typeof r=="string"){const s=oo({css:r}),a=s.nextToken();if(s.nextToken(),!s.endOfFile()||!mt(a))continue;e.set(n,a)}return e}function Ht(t,e){return Er(Gl(Ye({css:t}),{}),e).map(n=>n.map(r=>Ke(...r.tokens())).join("")).join(",")}function Er(t,e){const n=Xc(e?.globals);return Vl(t,r=>{if(!le(r))return;const s=ra.get(r.getName().toLowerCase());if(!s)return;const a=Vc(Xt(s(r,n,e??{})),e);return a!==-1?a:void 0})}const fs=new Set(ra.keys()),jn=typeof performance=="object"&&performance&&typeof performance.now=="function"?performance:Date,yo=new Set,sa=typeof process=="object"&&process?process:{},No=(t,e,n,r)=>{typeof sa.emitWarning=="function"?sa.emitWarning(t,e,n,r):console.error(`[${n}] ${e}: ${t}`)};let ps=globalThis.AbortController,Eo=globalThis.AbortSignal;var Co;if(typeof ps>"u"){Eo=class{constructor(){X(this,"onabort"),X(this,"_onabort",[]),X(this,"reason"),X(this,"aborted",!1)}addEventListener(n,r){this._onabort.push(r)}},ps=class{constructor(){X(this,"signal",new Eo),e()}abort(n){var r,s;if(!this.signal.aborted){this.signal.reason=n,this.signal.aborted=!0;for(const a of this.signal._onabort)a(n);(s=(r=this.signal).onabort)==null||s.call(r,n)}}};let t=((Co=sa.env)==null?void 0:Co.LRU_CACHE_IGNORE_AC_WARNING)!=="1";const e=()=>{t&&(t=!1,No("AbortController is not defined. If using lru-cache in node 14, load an AbortController polyfill from the `node-abort-controller` package. A minimal polyfill is provided for use by LRUCache.fetch(), but it should not be relied upon in other contexts (eg, passing it to other APIs that use AbortController/AbortSignal might have undesirable effects). You may disable this with LRU_CACHE_IGNORE_AC_WARNING=1 in the env.","NO_ABORT_CONTROLLER","ENOTSUP",e))}}const Kc=t=>!yo.has(t),hn=t=>t&&t===Math.floor(t)&&t>0&&isFinite(t),ko=t=>hn(t)?t<=Math.pow(2,8)?Uint8Array:t<=Math.pow(2,16)?Uint16Array:t<=Math.pow(2,32)?Uint32Array:t<=Number.MAX_SAFE_INTEGER?ds:null:null;class ds extends Array{constructor(e){super(e),this.fill(0)}}var Cr;const Fo=class Qr{constructor(e,n){if(X(this,"heap"),X(this,"length"),!h(Qr,Cr))throw new TypeError("instantiate Stack using Stack.create(n)");this.heap=new n(e),this.length=0}static create(e){const n=ko(e);if(!n)return[];F(Qr,Cr,!0);const r=new Qr(e,n);return F(Qr,Cr,!1),r}push(e){this.heap[this.length++]=e}pop(){return this.heap[--this.length]}};Cr=new WeakMap,U(Fo,Cr,!1);let Yc=Fo;var xo,So,Be,ue,Re,qn,Oe,kr,Fr,xt,he,Et,ct,K,Kt,fe,Ut,Mt,We,Pt,Ie,Le,pe,Te,Fn,Yt,xr,W,aa,Vn,fn,ms,de,Ao,Xn,Sr,gs,pn,dn,oa,vs,bs,lt,ia,Ar,mn,la;const Zc=class cl{constructor(e){U(this,W),U(this,Be),U(this,ue),U(this,Re),U(this,qn),U(this,Oe),U(this,kr),U(this,Fr),X(this,"ttl"),X(this,"ttlResolution"),X(this,"ttlAutopurge"),X(this,"updateAgeOnGet"),X(this,"updateAgeOnHas"),X(this,"allowStale"),X(this,"noDisposeOnSet"),X(this,"noUpdateTTL"),X(this,"maxEntrySize"),X(this,"sizeCalculation"),X(this,"noDeleteOnFetchRejection"),X(this,"noDeleteOnStaleGet"),X(this,"allowStaleOnFetchAbort"),X(this,"allowStaleOnFetchRejection"),X(this,"ignoreFetchAbort"),U(this,xt),U(this,he),U(this,Et),U(this,ct),U(this,K),U(this,Kt),U(this,fe),U(this,Ut),U(this,Mt),U(this,We),U(this,Pt),U(this,Ie),U(this,Le),U(this,pe),U(this,Te),U(this,Fn),U(this,Yt),U(this,xr),U(this,Vn,()=>{}),U(this,fn,()=>{}),U(this,ms,()=>{}),U(this,de,()=>!1),U(this,Xn,O=>{}),U(this,Sr,(O,j,z)=>{}),U(this,gs,(O,j,z,Y)=>{if(z||Y)throw new TypeError("cannot set size without setting maxSize or maxEntrySize on cache");return 0}),X(this,xo,"LRUCache");const{max:n=0,ttl:r,ttlResolution:s=1,ttlAutopurge:a,updateAgeOnGet:o,updateAgeOnHas:i,allowStale:c,dispose:l,onInsert:u,disposeAfter:f,noDisposeOnSet:d,noUpdateTTL:b,maxSize:p=0,maxEntrySize:m=0,sizeCalculation:$,fetchMethod:w,memoMethod:N,noDeleteOnFetchRejection:C,noDeleteOnStaleGet:E,allowStaleOnFetchRejection:J,allowStaleOnFetchAbort:P,ignoreFetchAbort:D}=e;if(n!==0&&!hn(n))throw new TypeError("max option must be a nonnegative integer");const M=n?ko(n):Array;if(!M)throw new Error("invalid max value: "+n);if(F(this,Be,n),F(this,ue,p),this.maxEntrySize=m||h(this,ue),this.sizeCalculation=$,this.sizeCalculation){if(!h(this,ue)&&!this.maxEntrySize)throw new TypeError("cannot set sizeCalculation without setting maxSize or maxEntrySize");if(typeof this.sizeCalculation!="function")throw new TypeError("sizeCalculation set to non-function")}if(N!==void 0&&typeof N!="function")throw new TypeError("memoMethod must be a function if defined");if(F(this,Fr,N),w!==void 0&&typeof w!="function")throw new TypeError("fetchMethod must be a function if specified");if(F(this,kr,w),F(this,Fn,!!w),F(this,Et,new Map),F(this,ct,new Array(n).fill(void 0)),F(this,K,new Array(n).fill(void 0)),F(this,Kt,new M(n)),F(this,fe,new M(n)),F(this,Ut,0),F(this,Mt,0),F(this,We,Yc.create(n)),F(this,xt,0),F(this,he,0),typeof l=="function"&&F(this,Re,l),typeof u=="function"&&F(this,qn,u),typeof f=="function"?(F(this,Oe,f),F(this,Pt,[])):(F(this,Oe,void 0),F(this,Pt,void 0)),F(this,Te,!!h(this,Re)),F(this,xr,!!h(this,qn)),F(this,Yt,!!h(this,Oe)),this.noDisposeOnSet=!!d,this.noUpdateTTL=!!b,this.noDeleteOnFetchRejection=!!C,this.allowStaleOnFetchRejection=!!J,this.allowStaleOnFetchAbort=!!P,this.ignoreFetchAbort=!!D,this.maxEntrySize!==0){if(h(this,ue)!==0&&!hn(h(this,ue)))throw new TypeError("maxSize must be a positive integer if specified");if(!hn(this.maxEntrySize))throw new TypeError("maxEntrySize must be a positive integer if specified");L(this,W,Ao).call(this)}if(this.allowStale=!!c,this.noDeleteOnStaleGet=!!E,this.updateAgeOnGet=!!o,this.updateAgeOnHas=!!i,this.ttlResolution=hn(s)||s===0?s:1,this.ttlAutopurge=!!a,this.ttl=r||0,this.ttl){if(!hn(this.ttl))throw new TypeError("ttl must be a positive integer if specified");L(this,W,aa).call(this)}if(h(this,Be)===0&&this.ttl===0&&h(this,ue)===0)throw new TypeError("At least one of max, maxSize, or ttl is required");if(!this.ttlAutopurge&&!h(this,Be)&&!h(this,ue)){const O="LRU_CACHE_UNBOUNDED";Kc(O)&&(yo.add(O),No("TTL caching without ttlAutopurge, max, or maxSize can result in unbounded memory consumption.","UnboundedCacheWarning",O,cl))}}static unsafeExposeInternals(e){return{starts:h(e,Le),ttls:h(e,pe),sizes:h(e,Ie),keyMap:h(e,Et),keyList:h(e,ct),valList:h(e,K),next:h(e,Kt),prev:h(e,fe),get head(){return h(e,Ut)},get tail(){return h(e,Mt)},free:h(e,We),isBackgroundFetch:n=>{var r;return L(r=e,W,lt).call(r,n)},backgroundFetch:(n,r,s,a)=>{var o;return L(o=e,W,bs).call(o,n,r,s,a)},moveToTail:n=>{var r;return L(r=e,W,Ar).call(r,n)},indexes:n=>{var r;return L(r=e,W,pn).call(r,n)},rindexes:n=>{var r;return L(r=e,W,dn).call(r,n)},isStale:n=>{var r;return h(r=e,de).call(r,n)}}}get max(){return h(this,Be)}get maxSize(){return h(this,ue)}get calculatedSize(){return h(this,he)}get size(){return h(this,xt)}get fetchMethod(){return h(this,kr)}get memoMethod(){return h(this,Fr)}get dispose(){return h(this,Re)}get onInsert(){return h(this,qn)}get disposeAfter(){return h(this,Oe)}getRemainingTTL(e){return h(this,Et).has(e)?1/0:0}*entries(){for(const e of L(this,W,pn).call(this))h(this,K)[e]!==void 0&&h(this,ct)[e]!==void 0&&!L(this,W,lt).call(this,h(this,K)[e])&&(yield[h(this,ct)[e],h(this,K)[e]])}*rentries(){for(const e of L(this,W,dn).call(this))h(this,K)[e]!==void 0&&h(this,ct)[e]!==void 0&&!L(this,W,lt).call(this,h(this,K)[e])&&(yield[h(this,ct)[e],h(this,K)[e]])}*keys(){for(const e of L(this,W,pn).call(this)){const n=h(this,ct)[e];n!==void 0&&!L(this,W,lt).call(this,h(this,K)[e])&&(yield n)}}*rkeys(){for(const e of L(this,W,dn).call(this)){const n=h(this,ct)[e];n!==void 0&&!L(this,W,lt).call(this,h(this,K)[e])&&(yield n)}}*values(){for(const e of L(this,W,pn).call(this))h(this,K)[e]!==void 0&&!L(this,W,lt).call(this,h(this,K)[e])&&(yield h(this,K)[e])}*rvalues(){for(const e of L(this,W,dn).call(this))h(this,K)[e]!==void 0&&!L(this,W,lt).call(this,h(this,K)[e])&&(yield h(this,K)[e])}[(So=Symbol.iterator,xo=Symbol.toStringTag,So)](){return this.entries()}find(e,n={}){for(const r of L(this,W,pn).call(this)){const s=h(this,K)[r],a=L(this,W,lt).call(this,s)?s.__staleWhileFetching:s;if(a!==void 0&&e(a,h(this,ct)[r],this))return this.get(h(this,ct)[r],n)}}forEach(e,n=this){for(const r of L(this,W,pn).call(this)){const s=h(this,K)[r],a=L(this,W,lt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,h(this,ct)[r],this)}}rforEach(e,n=this){for(const r of L(this,W,dn).call(this)){const s=h(this,K)[r],a=L(this,W,lt).call(this,s)?s.__staleWhileFetching:s;a!==void 0&&e.call(n,a,h(this,ct)[r],this)}}purgeStale(){let e=!1;for(const n of L(this,W,dn).call(this,{allowStale:!0}))h(this,de).call(this,n)&&(L(this,W,mn).call(this,h(this,ct)[n],"expire"),e=!0);return e}info(e){const n=h(this,Et).get(e);if(n===void 0)return;const r=h(this,K)[n],s=L(this,W,lt).call(this,r)?r.__staleWhileFetching:r;if(s===void 0)return;const a={value:s};if(h(this,pe)&&h(this,Le)){const o=h(this,pe)[n],i=h(this,Le)[n];if(o&&i){const c=o-(jn.now()-i);a.ttl=c,a.start=Date.now()}}return h(this,Ie)&&(a.size=h(this,Ie)[n]),a}dump(){const e=[];for(const n of L(this,W,pn).call(this,{allowStale:!0})){const r=h(this,ct)[n],s=h(this,K)[n],a=L(this,W,lt).call(this,s)?s.__staleWhileFetching:s;if(a===void 0||r===void 0)continue;const o={value:a};if(h(this,pe)&&h(this,Le)){o.ttl=h(this,pe)[n];const i=jn.now()-h(this,Le)[n];o.start=Math.floor(Date.now()-i)}h(this,Ie)&&(o.size=h(this,Ie)[n]),e.unshift([r,o])}return e}load(e){this.clear();for(const[n,r]of e){if(r.start){const s=Date.now()-r.start;r.start=jn.now()-s}this.set(n,r.value,r)}}set(e,n,r={}){var s,a,o,i,c,l,u;if(n===void 0)return this.delete(e),this;const{ttl:f=this.ttl,start:d,noDisposeOnSet:b=this.noDisposeOnSet,sizeCalculation:p=this.sizeCalculation,status:m}=r;let{noUpdateTTL:$=this.noUpdateTTL}=r;const w=h(this,gs).call(this,e,n,r.size||0,p);if(this.maxEntrySize&&w>this.maxEntrySize)return m&&(m.set="miss",m.maxEntrySizeExceeded=!0),L(this,W,mn).call(this,e,"set"),this;let N=h(this,xt)===0?void 0:h(this,Et).get(e);if(N===void 0)N=h(this,xt)===0?h(this,Mt):h(this,We).length!==0?h(this,We).pop():h(this,xt)===h(this,Be)?L(this,W,vs).call(this,!1):h(this,xt),h(this,ct)[N]=e,h(this,K)[N]=n,h(this,Et).set(e,N),h(this,Kt)[h(this,Mt)]=N,h(this,fe)[N]=h(this,Mt),F(this,Mt,N),Vs(this,xt)._++,h(this,Sr).call(this,N,w,m),m&&(m.set="add"),$=!1,h(this,xr)&&((s=h(this,qn))==null||s.call(this,n,e,"add"));else{L(this,W,Ar).call(this,N);const C=h(this,K)[N];if(n!==C){if(h(this,Fn)&&L(this,W,lt).call(this,C)){C.__abortController.abort(new Error("replaced"));const{__staleWhileFetching:E}=C;E!==void 0&&!b&&(h(this,Te)&&((a=h(this,Re))==null||a.call(this,E,e,"set")),h(this,Yt)&&((o=h(this,Pt))==null||o.push([E,e,"set"])))}else b||(h(this,Te)&&((i=h(this,Re))==null||i.call(this,C,e,"set")),h(this,Yt)&&((c=h(this,Pt))==null||c.push([C,e,"set"])));if(h(this,Xn).call(this,N),h(this,Sr).call(this,N,w,m),h(this,K)[N]=n,m){m.set="replace";const E=C&&L(this,W,lt).call(this,C)?C.__staleWhileFetching:C;E!==void 0&&(m.oldValue=E)}}else m&&(m.set="update");h(this,xr)&&((l=this.onInsert)==null||l.call(this,n,e,n===C?"update":"replace"))}if(f!==0&&!h(this,pe)&&L(this,W,aa).call(this),h(this,pe)&&($||h(this,ms).call(this,N,f,d),m&&h(this,fn).call(this,m,N)),!b&&h(this,Yt)&&h(this,Pt)){const C=h(this,Pt);let E;for(;E=C?.shift();)(u=h(this,Oe))==null||u.call(this,...E)}return this}pop(){var e;try{for(;h(this,xt);){const n=h(this,K)[h(this,Ut)];if(L(this,W,vs).call(this,!0),L(this,W,lt).call(this,n)){if(n.__staleWhileFetching)return n.__staleWhileFetching}else if(n!==void 0)return n}}finally{if(h(this,Yt)&&h(this,Pt)){const n=h(this,Pt);let r;for(;r=n?.shift();)(e=h(this,Oe))==null||e.call(this,...r)}}}has(e,n={}){const{updateAgeOnHas:r=this.updateAgeOnHas,status:s}=n,a=h(this,Et).get(e);if(a!==void 0){const o=h(this,K)[a];if(L(this,W,lt).call(this,o)&&o.__staleWhileFetching===void 0)return!1;if(h(this,de).call(this,a))s&&(s.has="stale",h(this,fn).call(this,s,a));else return r&&h(this,Vn).call(this,a),s&&(s.has="hit",h(this,fn).call(this,s,a)),!0}else s&&(s.has="miss");return!1}peek(e,n={}){const{allowStale:r=this.allowStale}=n,s=h(this,Et).get(e);if(s===void 0||!r&&h(this,de).call(this,s))return;const a=h(this,K)[s];return L(this,W,lt).call(this,a)?a.__staleWhileFetching:a}async fetch(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,ttl:o=this.ttl,noDisposeOnSet:i=this.noDisposeOnSet,size:c=0,sizeCalculation:l=this.sizeCalculation,noUpdateTTL:u=this.noUpdateTTL,noDeleteOnFetchRejection:f=this.noDeleteOnFetchRejection,allowStaleOnFetchRejection:d=this.allowStaleOnFetchRejection,ignoreFetchAbort:b=this.ignoreFetchAbort,allowStaleOnFetchAbort:p=this.allowStaleOnFetchAbort,context:m,forceRefresh:$=!1,status:w,signal:N}=n;if(!h(this,Fn))return w&&(w.fetch="get"),this.get(e,{allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,status:w});const C={allowStale:r,updateAgeOnGet:s,noDeleteOnStaleGet:a,ttl:o,noDisposeOnSet:i,size:c,sizeCalculation:l,noUpdateTTL:u,noDeleteOnFetchRejection:f,allowStaleOnFetchRejection:d,allowStaleOnFetchAbort:p,ignoreFetchAbort:b,status:w,signal:N};let E=h(this,Et).get(e);if(E===void 0){w&&(w.fetch="miss");const J=L(this,W,bs).call(this,e,E,C,m);return J.__returned=J}else{const J=h(this,K)[E];if(L(this,W,lt).call(this,J)){const O=r&&J.__staleWhileFetching!==void 0;return w&&(w.fetch="inflight",O&&(w.returnedStale=!0)),O?J.__staleWhileFetching:J.__returned=J}const P=h(this,de).call(this,E);if(!$&&!P)return w&&(w.fetch="hit"),L(this,W,Ar).call(this,E),s&&h(this,Vn).call(this,E),w&&h(this,fn).call(this,w,E),J;const D=L(this,W,bs).call(this,e,E,C,m),M=D.__staleWhileFetching!==void 0&&r;return w&&(w.fetch=P?"stale":"refresh",M&&P&&(w.returnedStale=!0)),M?D.__staleWhileFetching:D.__returned=D}}async forceFetch(e,n={}){const r=await this.fetch(e,n);if(r===void 0)throw new Error("fetch() returned undefined");return r}memo(e,n={}){const r=h(this,Fr);if(!r)throw new Error("no memoMethod provided to constructor");const{context:s,forceRefresh:a,...o}=n,i=this.get(e,o);if(!a&&i!==void 0)return i;const c=r(e,i,{options:o,context:s});return this.set(e,c,o),c}get(e,n={}){const{allowStale:r=this.allowStale,updateAgeOnGet:s=this.updateAgeOnGet,noDeleteOnStaleGet:a=this.noDeleteOnStaleGet,status:o}=n,i=h(this,Et).get(e);if(i!==void 0){const c=h(this,K)[i],l=L(this,W,lt).call(this,c);return o&&h(this,fn).call(this,o,i),h(this,de).call(this,i)?(o&&(o.get="stale"),l?(o&&r&&c.__staleWhileFetching!==void 0&&(o.returnedStale=!0),r?c.__staleWhileFetching:void 0):(a||L(this,W,mn).call(this,e,"expire"),o&&r&&(o.returnedStale=!0),r?c:void 0)):(o&&(o.get="hit"),l?c.__staleWhileFetching:(L(this,W,Ar).call(this,i),s&&h(this,Vn).call(this,i),c))}else o&&(o.get="miss")}delete(e){return L(this,W,mn).call(this,e,"delete")}clear(){return L(this,W,la).call(this,"delete")}};Be=new WeakMap,ue=new WeakMap,Re=new WeakMap,qn=new WeakMap,Oe=new WeakMap,kr=new WeakMap,Fr=new WeakMap,xt=new WeakMap,he=new WeakMap,Et=new WeakMap,ct=new WeakMap,K=new WeakMap,Kt=new WeakMap,fe=new WeakMap,Ut=new WeakMap,Mt=new WeakMap,We=new WeakMap,Pt=new WeakMap,Ie=new WeakMap,Le=new WeakMap,pe=new WeakMap,Te=new WeakMap,Fn=new WeakMap,Yt=new WeakMap,xr=new WeakMap,W=new WeakSet,aa=function(){const t=new ds(h(this,Be)),e=new ds(h(this,Be));F(this,pe,t),F(this,Le,e),F(this,ms,(s,a,o=jn.now())=>{if(e[s]=a!==0?o:0,t[s]=a,a!==0&&this.ttlAutopurge){const i=setTimeout(()=>{h(this,de).call(this,s)&&L(this,W,mn).call(this,h(this,ct)[s],"expire")},a+1);i.unref&&i.unref()}}),F(this,Vn,s=>{e[s]=t[s]!==0?jn.now():0}),F(this,fn,(s,a)=>{if(t[a]){const o=t[a],i=e[a];if(!o||!i)return;s.ttl=o,s.start=i,s.now=n||r();const c=s.now-i;s.remainingTTL=o-c}});let n=0;const r=()=>{const s=jn.now();if(this.ttlResolution>0){n=s;const a=setTimeout(()=>n=0,this.ttlResolution);a.unref&&a.unref()}return s};this.getRemainingTTL=s=>{const a=h(this,Et).get(s);if(a===void 0)return 0;const o=t[a],i=e[a];if(!o||!i)return 1/0;const c=(n||r())-i;return o-c},F(this,de,s=>{const a=e[s],o=t[s];return!!o&&!!a&&(n||r())-a>o})},Vn=new WeakMap,fn=new WeakMap,ms=new WeakMap,de=new WeakMap,Ao=function(){const t=new ds(h(this,Be));F(this,he,0),F(this,Ie,t),F(this,Xn,e=>{F(this,he,h(this,he)-t[e]),t[e]=0}),F(this,gs,(e,n,r,s)=>{if(L(this,W,lt).call(this,n))return 0;if(!hn(r))if(s){if(typeof s!="function")throw new TypeError("sizeCalculation must be a function");if(r=s(n,e),!hn(r))throw new TypeError("sizeCalculation return invalid (expect positive integer)")}else throw new TypeError("invalid size value (must be positive integer). When maxSize or maxEntrySize is used, sizeCalculation or size must be set.");return r}),F(this,Sr,(e,n,r)=>{if(t[e]=n,h(this,ue)){const s=h(this,ue)-t[e];for(;h(this,he)>s;)L(this,W,vs).call(this,!0)}F(this,he,h(this,he)+t[e]),r&&(r.entrySize=n,r.totalCalculatedSize=h(this,he))})},Xn=new WeakMap,Sr=new WeakMap,gs=new WeakMap,pn=function*({allowStale:t=this.allowStale}={}){if(h(this,xt))for(let e=h(this,Mt);!(!L(this,W,oa).call(this,e)||((t||!h(this,de).call(this,e))&&(yield e),e===h(this,Ut)));)e=h(this,fe)[e]},dn=function*({allowStale:t=this.allowStale}={}){if(h(this,xt))for(let e=h(this,Ut);!(!L(this,W,oa).call(this,e)||((t||!h(this,de).call(this,e))&&(yield e),e===h(this,Mt)));)e=h(this,Kt)[e]},oa=function(t){return t!==void 0&&h(this,Et).get(h(this,ct)[t])===t},vs=function(t){var e,n;const r=h(this,Ut),s=h(this,ct)[r],a=h(this,K)[r];return h(this,Fn)&&L(this,W,lt).call(this,a)?a.__abortController.abort(new Error("evicted")):(h(this,Te)||h(this,Yt))&&(h(this,Te)&&((e=h(this,Re))==null||e.call(this,a,s,"evict")),h(this,Yt)&&((n=h(this,Pt))==null||n.push([a,s,"evict"]))),h(this,Xn).call(this,r),t&&(h(this,ct)[r]=void 0,h(this,K)[r]=void 0,h(this,We).push(r)),h(this,xt)===1?(F(this,Ut,F(this,Mt,0)),h(this,We).length=0):F(this,Ut,h(this,Kt)[r]),h(this,Et).delete(s),Vs(this,xt)._--,r},bs=function(t,e,n,r){const s=e===void 0?void 0:h(this,K)[e];if(L(this,W,lt).call(this,s))return s;const a=new ps,{signal:o}=n;o?.addEventListener("abort",()=>a.abort(o.reason),{signal:a.signal});const i={signal:a.signal,options:n,context:r},c=(p,m=!1)=>{const{aborted:$}=a.signal,w=n.ignoreFetchAbort&&p!==void 0;if(n.status&&($&&!m?(n.status.fetchAborted=!0,n.status.fetchError=a.signal.reason,w&&(n.status.fetchAbortIgnored=!0)):n.status.fetchResolved=!0),$&&!w&&!m)return u(a.signal.reason);const N=d;return h(this,K)[e]===d&&(p===void 0?N.__staleWhileFetching?h(this,K)[e]=N.__staleWhileFetching:L(this,W,mn).call(this,t,"fetch"):(n.status&&(n.status.fetchUpdated=!0),this.set(t,p,i.options))),p},l=p=>(n.status&&(n.status.fetchRejected=!0,n.status.fetchError=p),u(p)),u=p=>{const{aborted:m}=a.signal,$=m&&n.allowStaleOnFetchAbort,w=$||n.allowStaleOnFetchRejection,N=w||n.noDeleteOnFetchRejection,C=d;if(h(this,K)[e]===d&&(!N||C.__staleWhileFetching===void 0?L(this,W,mn).call(this,t,"fetch"):$||(h(this,K)[e]=C.__staleWhileFetching)),w)return n.status&&C.__staleWhileFetching!==void 0&&(n.status.returnedStale=!0),C.__staleWhileFetching;if(C.__returned===C)throw p},f=(p,m)=>{var $;const w=($=h(this,kr))==null?void 0:$.call(this,t,s,i);w&&w instanceof Promise&&w.then(N=>p(N===void 0?void 0:N),m),a.signal.addEventListener("abort",()=>{(!n.ignoreFetchAbort||n.allowStaleOnFetchAbort)&&(p(void 0),n.allowStaleOnFetchAbort&&(p=N=>c(N,!0)))})};n.status&&(n.status.fetchDispatched=!0);const d=new Promise(f).then(c,l),b=Object.assign(d,{__abortController:a,__staleWhileFetching:s,__returned:void 0});return e===void 0?(this.set(t,b,{...i.options,status:void 0}),e=h(this,Et).get(t)):h(this,K)[e]=b,b},lt=function(t){if(!h(this,Fn))return!1;const e=t;return!!e&&e instanceof Promise&&e.hasOwnProperty("__staleWhileFetching")&&e.__abortController instanceof ps},ia=function(t,e){h(this,fe)[e]=t,h(this,Kt)[t]=e},Ar=function(t){t!==h(this,Mt)&&(t===h(this,Ut)?F(this,Ut,h(this,Kt)[t]):L(this,W,ia).call(this,h(this,fe)[t],h(this,Kt)[t]),L(this,W,ia).call(this,h(this,Mt),t),F(this,Mt,t))},mn=function(t,e){var n,r,s,a;let o=!1;if(h(this,xt)!==0){const i=h(this,Et).get(t);if(i!==void 0)if(o=!0,h(this,xt)===1)L(this,W,la).call(this,e);else{h(this,Xn).call(this,i);const c=h(this,K)[i];if(L(this,W,lt).call(this,c)?c.__abortController.abort(new Error("deleted")):(h(this,Te)||h(this,Yt))&&(h(this,Te)&&((n=h(this,Re))==null||n.call(this,c,t,e)),h(this,Yt)&&((r=h(this,Pt))==null||r.push([c,t,e]))),h(this,Et).delete(t),h(this,ct)[i]=void 0,h(this,K)[i]=void 0,i===h(this,Mt))F(this,Mt,h(this,fe)[i]);else if(i===h(this,Ut))F(this,Ut,h(this,Kt)[i]);else{const l=h(this,fe)[i];h(this,Kt)[l]=h(this,Kt)[i];const u=h(this,Kt)[i];h(this,fe)[u]=h(this,fe)[i]}Vs(this,xt)._--,h(this,We).push(i)}}if(h(this,Yt)&&(s=h(this,Pt))!=null&&s.length){const i=h(this,Pt);let c;for(;c=i?.shift();)(a=h(this,Oe))==null||a.call(this,...c)}return o},la=function(t){var e,n,r;for(const s of L(this,W,dn).call(this,{allowStale:!0})){const a=h(this,K)[s];if(L(this,W,lt).call(this,a))a.__abortController.abort(new Error("deleted"));else{const o=h(this,ct)[s];h(this,Te)&&((e=h(this,Re))==null||e.call(this,a,o,t)),h(this,Yt)&&((n=h(this,Pt))==null||n.push([a,o,t]))}}if(h(this,Et).clear(),h(this,K).fill(void 0),h(this,ct).fill(void 0),h(this,pe)&&h(this,Le)&&(h(this,pe).fill(0),h(this,Le).fill(0)),h(this,Ie)&&h(this,Ie).fill(0),F(this,Ut,0),F(this,Mt,0),h(this,We).length=0,F(this,he,0),F(this,xt,0),h(this,Yt)&&h(this,Pt)){const s=h(this,Pt);let a;for(;a=s?.shift();)(r=h(this,Oe))==null||r.call(this,...a)}};let Jc=Zc;const x=t=>typeof t=="string"||t instanceof String,Kn=t=>x(t)||typeof t=="number",ws="(?:0|[1-9]\\d*)",Qc="clamp|max|min",tu="exp|hypot|log|pow|sqrt",eu="abs|sign",nu="mod|rem|round",ru="a?(?:cos|sin|tan)|atan2",Mo=`${Qc}|${tu}|${eu}|${nu}|${ru}`,ca=`calc|${Mo}`,su=`var|${ca}`,Yn="deg|g?rad|turn",Mr="[cm]m|[dls]?v(?:[bhiw]|max|min)|in|p[ctx]|q|r?(?:[cl]h|cap|e[mx]|ic)",zt=`[+-]?(?:${ws}(?:\\.\\d*)?|\\.\\d+)(?:e-?${ws})?`,Po=`\\+?(?:${ws}(?:\\.\\d*)?|\\.\\d+)(?:e-?${ws})?`,g="none",Gt=`${zt}%`,$s=`^(?:${ca})\\(|(?<=[*\\/\\s\\(])(?:${ca})\\(`,Do=`^(?:${Mo})\\($`,Pr="^var\\(|(?<=[*\\/\\s\\(])var\\(",au=`^(?:${su})\\(`,ys=`(?:\\s*\\/\\s*(?:${zt}|${Gt}|${g}))?`,Bo=`(?:\\s*,\\s*(?:${zt}|${Gt}))?`,Ro="(?:ok)?l(?:ab|ch)|color|hsla?|hwb|rgba?",ou="[a-z]+|#[\\da-f]{3}|#[\\da-f]{4}|#[\\da-f]{6}|#[\\da-f]{8}",Oo="(?:ok)?lch|hsl|hwb",Wo="(?:de|in)creasing|longer|shorter",iu=`${zt}(?:${Yn})?`,Io=`(?:${zt}(?:${Yn})?|${g})`,Dr=`(?:${zt}|${Gt}|${g})`,Lo=`(?:${Oo})(?:\\s(?:${Wo})\\shue)?`,lu=`(${Oo})(?:\\s(${Wo})\\shue)?`,To="(?:ok)?lab",cu="(?:ok)?lch",uu="srgb(?:-linear)?",ua=`(?:a98|prophoto)-rgb|display-p3|rec2020|${uu}`,ha="xyz(?:-d(?:50|65))?",_o=`${To}|${ua}|${ha}`,Ns=`${Lo}|${_o}`,ht="color(",Es="light-dark(",gn="color-mix(",Br=`(?:${Ro})\\(\\s*from\\s+`,hu=`(${Ro})\\(\\s*from\\s+`,fa="var(",Ho=`(?:${ua}|${ha})(?:\\s+${Dr}){3}${ys}`,fu="^light-dark\\(",Uo=`^${Br}|(?<=[\\s])${Br}`,pa=`${Io}(?:\\s+${Dr}){2}${ys}`,zo=`${iu}(?:\\s*,\\s*${Gt}){2}${Bo}`,da=`(?:${Dr}\\s+){2}${Io}${ys}`,Cs=`${Dr}(?:\\s+${Dr}){2}${ys}`,Go=`(?:${zt}(?:\\s*,\\s*${zt}){2}|${Gt}(?:\\s*,\\s*${Gt}){2})${Bo}`,Zn=`${ou}|hsla?\\(\\s*${zo}\\s*\\)|rgba?\\(\\s*${Go}\\s*\\)|(?:hsla?|hwb)\\(\\s*${pa}\\s*\\)|(?:(?:ok)?lab|rgba?)\\(\\s*${Cs}\\s*\\)|(?:ok)?lch\\(\\s*${da}\\s*\\)|color\\(\\s*${Ho}\\s*\\)`,Jn=`(?:${Zn})(?:\\s+${Gt})?`,ks=`color-mix\\(\\s*in\\s+(?:${Ns})\\s*,\\s*${Jn}\\s*,\\s*${Jn}\\s*\\)`,pu=`color-mix\\(\\s*in\\s+(${Ns})\\s*,\\s*(${Jn})\\s*,\\s*(${Jn})\\s*\\)`,at="computedValue",Z="mixValue",Q="specifiedValue",ma="color",ga=.001,Rr=.5,va=2,gt=3,_e=4,He=8,Or=10,Fs=12,I=16,du=60,Qn=180,me=360,q=100,_=255,xn=2,Wr=3,tr=2.4,er=12.92,ge=.055,Ir=116,jo=500,qo=200,ba=216/24389,xs=24389/27,Vo=[.3457/.3585,1,(1-.3457-.3585)/.3585],Lr=[[.955473421488075,-.02309845494876471,.06325924320057072],[-.0283697093338637,1.0099953980813041,.021041441191917323],[.012314014864481998,-.020507649298898964,1.330365926242124]],$e=[[1.0479297925449969,.022946870601609652,-.05019226628920524],[.02962780877005599,.9904344267538799,-.017073799063418826],[-.009243040646204504,.015055191490298152,.7518742814281371]],wa=[[506752/1228815,87881/245763,12673/70218],[87098/409605,175762/245763,12673/175545],[7918/409605,87881/737289,1001167/1053270]],Ss=[[12831/3959,-329/214,-1974/3959],[-851781/878810,1648619/878810,36519/878810],[705/12673,-2585/12673,705/667]],mu=[[.819022437996703,.3619062600528904,-.1288737815209879],[.0329836539323885,.9292868615863434,.0361446663506424],[.0481771893596242,.2642395317527308,.6335478284694309]],Xo=[[1.2268798758459243,-.5578149944602171,.2813910456659647],[-.0405757452148008,1.112286803280317,-.0717110580655164],[-.0763729366746601,-.4214933324022432,1.5869240198367816]],Ko=[[1,.3963377773761749,.2158037573099136],[1,-.1055613458156586,-.0638541728258133],[1,-.0894841775298119,-1.2914855480194092]],gu=[[.210454268309314,.7936177747023054,-.0040720430116193],[1.9779985324311684,-2.42859224204858,.450593709617411],[.0259040424655478,.7827717124575296,-.8086757549230774]],vu=[[608311/1250200,189793/714400,198249/1000160],[35783/156275,247089/357200,198249/2500400],[0/1,32229/714400,5220557/5000800]],bu=[[63426534/99577255,20160776/139408157,47086771/278816314],[26158966/99577255,472592308/697040785,8267143/139408157],[0/1,19567812/697040785,295819943/278816314]],wu=[[573536/994567,263643/1420810,187206/994567],[591459/1989134,6239551/9945670,374412/4972835],[53769/1989134,351524/4972835,4929758/4972835]],$u=[[.7977666449006423,.13518129740053308,.0313477341283922],[.2880748288194013,.711835234241873,8993693872564e-17],[0,0,.8251046025104602]],Yo=new RegExp(`^(?:${Zn})$`),nr=new RegExp(`^${lu}$`),yu=/^xyz(?:-d(?:50|65))?$/,yt=/^currentColor$/i,rr=new RegExp(`^color\\(\\s*(${Ho})\\s*\\)$`),$a=new RegExp(`^hsla?\\(\\s*(${pa}|${zo})\\s*\\)$`),ya=new RegExp(`^hwb\\(\\s*(${pa})\\s*\\)$`),Na=new RegExp(`^lab\\(\\s*(${Cs})\\s*\\)$`),Ea=new RegExp(`^lch\\(\\s*(${da})\\s*\\)$`),Zo=new RegExp(`^${ks}$`),Nu=new RegExp(`^${pu}$`),Jo=new RegExp(`${ks}`,"g"),Ca=new RegExp(`^oklab\\(\\s*(${Cs})\\s*\\)$`),ka=new RegExp(`^oklch\\(\\s*(${da})\\s*\\)$`),Zt=/^(?:specifi|comput)edValue$/,sr={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]},ye=(t,e,n=!1)=>{if(e===Q)return k(t,""),"";if(n)return k(t,null),new A;const r=["rgb",0,0,0,0];return k(t,r),r},Ne=(t,e=!1)=>{switch(t){case"hsl":case"hwb":case Z:return new A;case Q:return"";default:return e?new A:["rgb",0,0,0,0]}},ve=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{alpha:n=!1,minLength:r=gt,maxLength:s=_e,minRange:a=0,maxRange:o=1,validateRange:i=!0}=e;if(!Number.isFinite(r))throw new TypeError(`${r} is not a number.`);if(!Number.isFinite(s))throw new TypeError(`${s} is not a number.`);if(!Number.isFinite(a))throw new TypeError(`${a} is not a number.`);if(!Number.isFinite(o))throw new TypeError(`${o} is not a number.`);const c=t.length;if(c<r||c>s)throw new Error(`Unexpected array length ${c}.`);let l=0;for(;l<c;){const u=t[l];if(Number.isFinite(u)){if(l<gt&&i&&(u<a||u>o))throw new RangeError(`${u} is not between ${a} and ${o}.`);if(l===gt&&(u<0||u>1))throw new RangeError(`${u} is not between 0 and 1.`)}else throw new TypeError(`${u} is not a number.`);l++}return n&&c===gt&&t.push(1),t},et=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==gt)throw new Error(`Unexpected array length ${t.length}.`);if(!n)for(let N of t)N=ve(N,{maxLength:gt,validateRange:!1})}else throw new TypeError(`${t} is not an array.`);const[[r,s,a],[o,i,c],[l,u,f]]=t;let d,b,p;n?[d,b,p]=e:[d,b,p]=ve(e,{maxLength:gt,validateRange:!1});const m=r*d+s*b+a*p,$=o*d+i*b+c*p,w=l*d+u*b+f*p;return[m,$,w]},Tr=(t,e,n=!1)=>{if(Array.isArray(t)){if(t.length!==_e)throw new Error(`Unexpected array length ${t.length}.`)}else throw new TypeError(`${t} is not an array.`);if(Array.isArray(e)){if(e.length!==_e)throw new Error(`Unexpected array length ${e.length}.`)}else throw new TypeError(`${e} is not an array.`);let r=0;for(;r<_e;)t[r]===g&&e[r]===g?(t[r]=0,e[r]=0):t[r]===g?t[r]=e[r]:e[r]===g&&(e[r]=t[r]),r++;if(n)return[t,e];const s=ve(t,{minLength:_e,validateRange:!1}),a=ve(e,{minLength:_e,validateRange:!1});return[s,a]},_r=t=>{if(Number.isFinite(t)){if(t=Math.round(t),t<0||t>_)throw new RangeError(`${t} is not between 0 and ${_}.`)}else throw new TypeError(`${t} is not a number.`);let e=t.toString(I);return e.length===1&&(e=`0${e}`),e},As=t=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=me/400,n=me/(Math.PI*va),r=new RegExp(`^(${zt})(${Yn})?$`);if(!r.test(t))throw new SyntaxError(`Invalid property value: ${t}`);const[,s,a]=t.match(r);let o;switch(a){case"grad":o=parseFloat(s)*e;break;case"rad":o=parseFloat(s)*n;break;case"turn":o=parseFloat(s)*me;break;default:o=parseFloat(s)}return o%=me,o<0?o+=me:Object.is(o,-0)&&(o=0),o},Qe=(t="")=>{if(x(t))if(t=t.trim(),!t)t="1";else if(t===g)t="0";else{let e;if(t.endsWith("%")?e=parseFloat(t)/q:e=parseFloat(t),!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);e<ga?t="0":e>1?t="1":t=e.toFixed(gt)}else t="1";return parseFloat(t)},Qo=t=>{if(x(t)){if(t==="")throw new SyntaxError("Invalid property value: (empty string)");t=t.trim()}else throw new TypeError(`${t} is not a string.`);let e=parseInt(t,I);if(e<=0)return 0;if(e>=_)return 1;const n=new Map;for(let r=1;r<q;r++)n.set(Math.round(r*_/q),r);return n.has(e)?e=n.get(e)/q:e=Math.round(e/_/ga)*ga,parseFloat(e.toFixed(gt))},Fa=(t,e=!1)=>{let n,r,s;e?[n,r,s]=t:[n,r,s]=ve(t,{maxLength:gt,maxRange:_});let a=n/_,o=r/_,i=s/_;const c=.04045;return a>c?a=Math.pow((a+ge)/(1+ge),tr):a/=er,o>c?o=Math.pow((o+ge)/(1+ge),tr):o/=er,i>c?i=Math.pow((i+ge)/(1+ge),tr):i/=er,[a,o,i]},xa=(t,e=!1)=>(e||(t=ve(t,{maxLength:gt,maxRange:_})),t=Fa(t,!0),et(wa,t,!0)),ti=(t,e=!1)=>{let[n,r,s]=ve(t,{maxLength:gt});const a=809/258400;return n>a?n=Math.pow(n,1/tr)*(1+ge)-ge:n*=er,n*=_,r>a?r=Math.pow(r,1/tr)*(1+ge)-ge:r*=er,r*=_,s>a?s=Math.pow(s,1/tr)*(1+ge)-ge:s*=er,s*=_,[e?Math.round(n):n,e?Math.round(r):r,e?Math.round(s):s]},ar=(t,e=!1)=>{e||(t=ve(t,{maxLength:gt,validateRange:!1}));let[n,r,s]=et(Ss,t,!0);return[n,r,s]=ti([Math.min(Math.max(n,0),1),Math.min(Math.max(r,0),1),Math.min(Math.max(s,0),1)],!0),[n,r,s]},ei=(t,e=!1)=>{const[n,r,s]=ar(t,e),a=n/_,o=r/_,i=s/_,c=Math.max(a,o,i),l=Math.min(a,o,i),u=c-l,f=(c+l)*Rr*q;let d,b;if(Math.round(f)===0||Math.round(f)===q)d=0,b=0;else if(b=u/(1-Math.abs(c+l-1))*q,b===0)d=0;else{switch(c){case a:d=(o-i)/u;break;case o:d=(i-a)/u+va;break;case i:default:d=(a-o)/u+_e;break}d=d*du%me,d<0&&(d+=me)}return[d,b,f]},Eu=(t,e=!1)=>{const[n,r,s]=ar(t,e),a=Math.min(n,r,s)/_,o=1-Math.max(n,r,s)/_;let i;return a+o===1?i=0:[i]=ei(t),[i,a*q,o*q]},ni=(t,e=!1)=>{e||(t=ve(t,{maxLength:gt,validateRange:!1}));const n=et(mu,t,!0).map(i=>Math.cbrt(i));let[r,s,a]=et(gu,n,!0);r=Math.min(Math.max(r,0),1);const o=Math.round(parseFloat(r.toFixed(_e))*q);return(o===0||o===q)&&(s=0,a=0),[r,s,a]},Cu=(t,e=!1)=>{const[n,r,s]=ni(t,e);let a,o;const i=Math.round(parseFloat(n.toFixed(_e))*q);return i===0||i===q?(a=0,o=0):(a=Math.max(Math.sqrt(Math.pow(r,xn)+Math.pow(s,xn)),0),parseFloat(a.toFixed(_e))===0?o=0:(o=Math.atan2(s,r)*Qn/Math.PI,o<0&&(o+=me))),[n,a,o]},ri=(t,e=!1)=>{e||(t=ve(t,{maxLength:gt,validateRange:!1}));const n=et(Lr,t,!0);return ar(n,!0)},si=(t,e=!1)=>{e||(t=ve(t,{maxLength:gt,validateRange:!1}));const n=t.map((l,u)=>l/Vo[u]),[r,s,a]=n.map(l=>l>ba?Math.cbrt(l):(l*xs+I)/Ir),o=Math.min(Math.max(Ir*s-I,0),q);let i,c;return o===0||o===q?(i=0,c=0):(i=(r-s)*jo,c=(s-a)*qo),[o,i,c]},ku=(t,e=!1)=>{const[n,r,s]=si(t,e);let a,o;return n===0||n===q?(a=0,o=0):(a=Math.max(Math.sqrt(Math.pow(r,xn)+Math.pow(s,xn)),0),o=Math.atan2(s,r)*Qn/Math.PI,o<0&&(o+=me)),[n,a,o]},ai=t=>{const[e,n,r,s]=ve(t,{alpha:!0,maxRange:_}),a=_r(e),o=_r(n),i=_r(r),c=_r(s*_);let l;return c==="ff"?l=`#${a}${o}${i}`:l=`#${a}${o}${i}${c}`,l},Sa=t=>{if(x(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);if(!(/^#[\da-f]{6}$/.test(t)||/^#[\da-f]{3}$/.test(t)||/^#[\da-f]{8}$/.test(t)||/^#[\da-f]{4}$/.test(t)))throw new SyntaxError(`Invalid property value: ${t}`);const e=[];if(/^#[\da-f]{3}$/.test(t)){const[,n,r,s]=t.match(/^#([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,I),parseInt(`${r}${r}`,I),parseInt(`${s}${s}`,I),1)}else if(/^#[\da-f]{4}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f])([\da-f])([\da-f])([\da-f])$/);e.push(parseInt(`${n}${n}`,I),parseInt(`${r}${r}`,I),parseInt(`${s}${s}`,I),Qo(`${a}${a}`))}else if(/^#[\da-f]{8}$/.test(t)){const[,n,r,s,a]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,I),parseInt(r,I),parseInt(s,I),Qo(a))}else{const[,n,r,s]=t.match(/^#([\da-f]{2})([\da-f]{2})([\da-f]{2})$/);e.push(parseInt(n,I),parseInt(r,I),parseInt(s,I),1)}return e},Fu=t=>{const[e,n,r,s]=Sa(t),[a,o,i]=Fa([e,n,r],!0);return[a,o,i,s]},xu=t=>{const[e,n,r,s]=Fu(t),[a,o,i]=et(wa,[e,n,r],!0);return[a,o,i,s]},oi=(t,e={})=>{if(x(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e,s=new RegExp(`^rgba?\\(\\s*(${Cs}|${Go})\\s*\\)$`);if(!s.test(t)){const p=Ne(n,r);return p instanceof A||x(p),p}const[,a]=t.match(s),[o,i,c,l=""]=a.replace(/[,/]/g," ").split(/\s+/);let u,f,d;o===g?u=0:(o.endsWith("%")?u=parseFloat(o)*_/q:u=parseFloat(o),u=Math.min(Math.max(R(u,He),0),_)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*_/q:f=parseFloat(i),f=Math.min(Math.max(R(f,He),0),_)),c===g?d=0:(c.endsWith("%")?d=parseFloat(c)*_/q:d=parseFloat(c),d=Math.min(Math.max(R(d,He),0),_));const b=Qe(l);return["rgb",u,f,d,n===Z&&l===g?g:b]},Ms=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!$a.test(t)){const E=Ne(n,r);return E instanceof A||x(E),E}const[,s]=t.match($a),[a,o,i,c=""]=s.replace(/[,/]/g," ").split(/\s+/);let l,u,f;a===g?l=0:l=As(a),o===g?u=0:u=Math.min(Math.max(parseFloat(o),0),q),i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q);const d=Qe(c);if(n==="hsl")return[n,a===g?a:l,o===g?o:u,i===g?i:f,c===g?c:d];l=l/me*Fs,f/=q;const b=u/q*Math.min(f,1-f),p=l%Fs,m=(8+l)%Fs,$=(4+l)%Fs,w=f-b*Math.max(-1,Math.min(p-gt,gt**xn-p,1)),N=f-b*Math.max(-1,Math.min(m-gt,gt**xn-m,1)),C=f-b*Math.max(-1,Math.min($-gt,gt**xn-$,1));return["rgb",Math.min(Math.max(R(w*_,He),0),_),Math.min(Math.max(R(N*_,He),0),_),Math.min(Math.max(R(C*_,He),0),_),d]},Aa=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ya.test(t)){const w=Ne(n,r);return w instanceof A||x(w),w}const[,s]=t.match(ya),[a,o,i,c=""]=s.replace("/"," ").split(/\s+/);let l,u,f;a===g?l=0:l=As(a),o===g?u=0:u=Math.min(Math.max(parseFloat(o),0),q)/q,i===g?f=0:f=Math.min(Math.max(parseFloat(i),0),q)/q;const d=Qe(c);if(n==="hwb")return[n,a===g?a:l,o===g?o:u*q,i===g?i:f*q,c===g?c:d];if(u+f>=1){const w=R(u/(u+f)*_,He);return["rgb",w,w,w,d]}const b=(1-u-f)/_;let[,p,m,$]=Ms(`hsl(${l} 100 50)`);return p=R((p*b+u)*_,He),m=R((m*b+u)*_,He),$=R(($*b+u)*_,He),["rgb",Math.min(Math.max(p,0),_),Math.min(Math.max(m,0),_),Math.min(Math.max($,0),_),d]},Hr=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!Na.test(t)){const O=Ne(n,r);return O instanceof A||x(O),O}const s=1.25,a=8,[,o]=t.match(Na),[i,c,l,u=""]=o.replace("/"," ").split(/\s+/);let f,d,b;i===g?f=0:(i.endsWith("%")?(f=parseFloat(i),f>q&&(f=q)):f=parseFloat(i),f<0&&(f=0)),c===g?d=0:d=c.endsWith("%")?parseFloat(c)*s:parseFloat(c),l===g?b=0:b=l.endsWith("%")?parseFloat(l)*s:parseFloat(l);const p=Qe(u);if(Zt.test(n))return["lab",i===g?i:R(f,I),c===g?c:R(d,I),l===g?l:R(b,I),u===g?u:p];const m=(f+I)/Ir,$=d/jo+m,w=m-b/qo,N=Math.pow(m,Wr),C=Math.pow($,Wr),E=Math.pow(w,Wr),J=[C>ba?C:($*Ir-I)/xs,f>a?N:f/xs,E>ba?E:(w*Ir-I)/xs],[P,D,M]=J.map((O,j)=>O*Vo[j]);return["xyz-d50",R(P,I),R(D,I),R(M,I),p]},Ps=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!Ea.test(t)){const C=Ne(n,r);return C instanceof A||x(C),C}const s=1.5,[,a]=t.match(Ea),[o,i,c,l=""]=a.replace("/"," ").split(/\s+/);let u,f,d;o===g?u=0:(u=parseFloat(o),u<0&&(u=0)),i===g?f=0:f=i.endsWith("%")?parseFloat(i)*s:parseFloat(i),c===g?d=0:d=As(c);const b=Qe(l);if(Zt.test(n))return["lch",o===g?o:R(u,I),i===g?i:R(f,I),c===g?c:R(d,I),l===g?l:b];const p=f*Math.cos(d*Math.PI/Qn),m=f*Math.sin(d*Math.PI/Qn),[,$,w,N]=Hr(`lab(${u} ${p} ${m})`);return["xyz-d50",R($,I),R(w,I),R(N,I),b]},Ds=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!Ca.test(t)){const N=Ne(n,r);return N instanceof A||x(N),N}const s=.4,[,a]=t.match(Ca),[o,i,c,l=""]=a.replace("/"," ").split(/\s+/);let u,f,d;o===g?u=0:(u=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),u<0&&(u=0)),i===g?f=0:i.endsWith("%")?f=parseFloat(i)*s/q:f=parseFloat(i),c===g?d=0:c.endsWith("%")?d=parseFloat(c)*s/q:d=parseFloat(c);const b=Qe(l);if(Zt.test(n))return["oklab",o===g?o:R(u,I),i===g?i:R(f,I),c===g?c:R(d,I),l===g?l:b];const p=et(Ko,[u,f,d]).map(N=>Math.pow(N,Wr)),[m,$,w]=et(Xo,p,!0);return["xyz-d65",R(m,I),R($,I),R(w,I),b]},Bs=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e;if(!ka.test(t)){const E=Ne(n,r);return E instanceof A||x(E),E}const s=.4,[,a]=t.match(ka),[o,i,c,l=""]=a.replace("/"," ").split(/\s+/);let u,f,d;o===g?u=0:(u=o.endsWith("%")?parseFloat(o)/q:parseFloat(o),u<0&&(u=0)),i===g?f=0:(i.endsWith("%")?f=parseFloat(i)*s/q:f=parseFloat(i),f<0&&(f=0)),c===g?d=0:d=As(c);const b=Qe(l);if(Zt.test(n))return["oklch",o===g?o:R(u,I),i===g?i:R(f,I),c===g?c:R(d,I),l===g?l:b];const p=f*Math.cos(d*Math.PI/Qn),m=f*Math.sin(d*Math.PI/Qn),$=et(Ko,[u,p,m]).map(E=>Math.pow(E,Wr)),[w,N,C]=et(Xo,$,!0);return["xyz-d65",R(w,I),R(N,I),R(C,I),b]},wt=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",d50:r=!1,format:s="",nullable:a=!1}=e;if(!rr.test(t)){const C=Ne(s,a);return C instanceof A||x(C),C}const[,o]=t.match(rr);let[i,c,l,u,f=""]=o.replace("/"," ").split(/\s+/),d,b,p;i==="xyz"&&(i="xyz-d65"),c===g?d=0:d=c.endsWith("%")?parseFloat(c)/q:parseFloat(c),l===g?b=0:b=l.endsWith("%")?parseFloat(l)/q:parseFloat(l),u===g?p=0:p=u.endsWith("%")?parseFloat(u)/q:parseFloat(u);const m=Qe(f);if(Zt.test(s)||s===Z&&i===n)return[i,c===g?c:R(d,Or),l===g?l:R(b,Or),u===g?u:R(p,Or),f===g?f:m];let $=0,w=0,N=0;if(i==="srgb-linear")[$,w,N]=et(wa,[d,b,p]),r&&([$,w,N]=et($e,[$,w,N],!0));else if(i==="display-p3"){const C=Fa([d*_,b*_,p*_]);[$,w,N]=et(vu,C),r&&([$,w,N]=et($e,[$,w,N],!0))}else if(i==="rec2020"){const C=1.09929682680944,E=.018053968510807,J=.45,P=[d,b,p].map(D=>{let M;return D<E*J*Or?M=D/(J*Or):M=Math.pow((D+C-1)/C,1/J),M});[$,w,N]=et(bu,P),r&&([$,w,N]=et($e,[$,w,N],!0))}else if(i==="a98-rgb"){const C=2.19921875,E=[d,b,p].map(J=>Math.pow(J,C));[$,w,N]=et(wu,E),r&&([$,w,N]=et($e,[$,w,N],!0))}else if(i==="prophoto-rgb"){const C=[d,b,p].map(E=>{let J;return E>1/(I*va)?J=Math.pow(E,1.8):J=E/I,J});[$,w,N]=et($u,C),r||([$,w,N]=et(Lr,[$,w,N],!0))}else/^xyz(?:-d(?:50|65))?$/.test(i)?([$,w,N]=[d,b,p],i==="xyz-d50"?r||([$,w,N]=et(Lr,[$,w,N])):r&&([$,w,N]=et($e,[$,w,N],!0))):([$,w,N]=xa([d*_,b*_,p*_]),r&&([$,w,N]=et($e,[$,w,N],!0)));return[r?"xyz-d50":"xyz-d65",R($,I),R(w,I),R(N,I),s===Z&&f===g?f:m]},Ct=(t,e={})=>{if(x(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{d50:n=!1,format:r="",nullable:s=!1}=e;if(!Yo.test(t)){const l=Ne(r,s);return l instanceof A||x(l),l}let a=0,o=0,i=0,c=0;if(yt.test(t)){if(r===at)return["rgb",0,0,0,0];if(r===Q)return t}else if(/^[a-z]+$/.test(t))if(Object.hasOwn(sr,t)){if(r===Q)return t;const[l,u,f]=sr[t];if(c=1,r===at)return["rgb",l,u,f,c];[a,o,i]=xa([l,u,f],!0),n&&([a,o,i]=et($e,[a,o,i],!0))}else switch(r){case at:return s&&t!=="transparent"?new A:["rgb",0,0,0,0];case Q:return t==="transparent"?t:"";case Z:return t==="transparent"?["rgb",0,0,0,0]:new A}else if(t[0]==="#"){if(Zt.test(r))return["rgb",...Sa(t)];[a,o,i,c]=xu(t),n&&([a,o,i]=et($e,[a,o,i],!0))}else if(t.startsWith("lab")){if(Zt.test(r))return Hr(t,e);[,a,o,i,c]=Hr(t),n||([a,o,i]=et(Lr,[a,o,i],!0))}else if(t.startsWith("lch")){if(Zt.test(r))return Ps(t,e);[,a,o,i,c]=Ps(t),n||([a,o,i]=et(Lr,[a,o,i],!0))}else if(t.startsWith("oklab")){if(Zt.test(r))return Ds(t,e);[,a,o,i,c]=Ds(t),n&&([a,o,i]=et($e,[a,o,i],!0))}else if(t.startsWith("oklch")){if(Zt.test(r))return Bs(t,e);[,a,o,i,c]=Bs(t),n&&([a,o,i]=et($e,[a,o,i],!0))}else{let l,u,f;if(t.startsWith("hsl")?[,l,u,f,c]=Ms(t):t.startsWith("hwb")?[,l,u,f,c]=Aa(t):[,l,u,f,c]=oi(t,e),Zt.test(r))return["rgb",Math.round(l),Math.round(u),Math.round(f),c];[a,o,i]=xa([l,u,f]),n&&([a,o,i]=et($e,[a,o,i],!0))}return[n?"xyz-d50":"xyz-d65",R(a,I),R(o,I),R(i,I),c]},Sn=(t,e={})=>{if(x(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r="",nullable:s=!1}=e,a=bt({namespace:ma,name:"resolveColorValue",value:t},e),o=vt(a);if(o instanceof it){if(o.isNull)return o;const b=o.item;return x(b),b}if(!Yo.test(t)){const b=Ne(r,s);return b instanceof A?(k(a,null),b):(k(a,b),x(b),b)}let i="",c=0,l=0,u=0,f=0;if(yt.test(t)){if(r===Q)return k(a,t),t}else if(/^[a-z]+$/.test(t))if(Object.hasOwn(sr,t)){if(r===Q)return k(a,t),t;[c,l,u]=sr[t],f=1}else switch(r){case Q:{if(t==="transparent")return k(a,t),t;const b="";return k(a,b),b}case Z:{if(t==="transparent"){const b=["rgb",0,0,0,0];return k(a,b),b}return k(a,null),new A}case at:default:{if(s&&t!=="transparent")return k(a,null),new A;const b=["rgb",0,0,0,0];return k(a,b),b}}else if(t[0]==="#")[c,l,u,f]=Sa(t);else if(t.startsWith("hsl"))[,c,l,u,f]=Ms(t,e);else if(t.startsWith("hwb"))[,c,l,u,f]=Aa(t,e);else if(/^l(?:ab|ch)/.test(t)){let b,p,m;if(t.startsWith("lab")?[i,b,p,m,f]=Hr(t,e):[i,b,p,m,f]=Ps(t,e),Zt.test(r)){const $=[i,b,p,m,f];return k(a,$),$}[c,l,u]=ri([b,p,m])}else if(/^okl(?:ab|ch)/.test(t)){let b,p,m;if(t.startsWith("oklab")?[i,b,p,m,f]=Ds(t,e):[i,b,p,m,f]=Bs(t,e),Zt.test(r)){const $=[i,b,p,m,f];return k(a,$),$}[c,l,u]=ar([b,p,m])}else[,c,l,u,f]=oi(t,e);if(r===Z&&n==="srgb"){const b=["srgb",c/_,l/_,u/_,f];return k(a,b),b}const d=["rgb",Math.round(c),Math.round(l),Math.round(u),f];return k(a,d),d},tn=(t,e={})=>{if(x(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r="",nullable:s=!1}=e,a=bt({namespace:ma,name:"resolveColorFunc",value:t},e),o=vt(a);if(o instanceof it){if(o.isNull)return o;const E=o.item;return x(E),E}if(!rr.test(t)){const E=Ne(r,s);return E instanceof A?(k(a,null),E):(k(a,E),x(E),E)}const[i,c,l,u,f]=wt(t,e);if(Zt.test(r)||r===Z&&i===n){const E=[i,c,l,u,f];return k(a,E),E}const d=parseFloat(`${c}`),b=parseFloat(`${l}`),p=parseFloat(`${u}`),m=Qe(`${f}`),[$,w,N]=ar([d,b,p],!0),C=["rgb",$,w,N,m];return k(a,C),C},Ma=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorSpace:n="",format:r=""}=e;let s="",a,o,i,c,l,u,f;if(r===Z){let d;if(t.startsWith(ht)?d=wt(t,e):d=Ct(t,e),d instanceof A)return d;if([s,l,u,f,c]=d,s===n)return[l,u,f,c];[a,o,i]=et(Ss,[l,u,f],!0)}else if(t.startsWith(ht)){const[,d]=t.match(rr),[b]=d.replace("/"," ").split(/\s+/);b==="srgb-linear"?[,a,o,i,c]=tn(t,{format:at}):([,l,u,f,c]=wt(t),[a,o,i]=et(Ss,[l,u,f],!0))}else[,l,u,f,c]=Ct(t),[a,o,i]=et(Ss,[l,u,f],!0);return[Math.min(Math.max(a,0),1),Math.min(Math.max(o,0),1),Math.min(Math.max(i,0),1),c]},Ur=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(n===Z){let i;if(t.startsWith(ht)?i=tn(t,e):i=Sn(t,e),i instanceof A)return i;[,r,s,a,o]=i}else if(t.startsWith(ht)){const[,i]=t.match(rr),[c]=i.replace("/"," ").split(/\s+/);c==="srgb"?([,r,s,a,o]=tn(t,{format:at}),r*=_,s*=_,a*=_):[,r,s,a,o]=tn(t)}else/^(?:ok)?l(?:ab|ch)/.test(t)?([r,s,a,o]=Ma(t),[r,s,a]=ti([r,s,a])):[,r,s,a,o]=Sn(t,{format:at});return[r,s,a,o]},ii=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{d50:n=!1,format:r=""}=e;let s,a,o,i;if(r===Z){let c;if(t.startsWith(ht)?c=wt(t,e):c=Ct(t,e),c instanceof A)return c;[,s,a,o,i]=c}else if(t.startsWith(ht)){const[,c]=t.match(rr),[l]=c.replace("/"," ").split(/\s+/);n?l==="xyz-d50"?[,s,a,o,i]=tn(t,{format:at}):[,s,a,o,i]=wt(t,e):/^xyz(?:-d65)?$/.test(l)?[,s,a,o,i]=tn(t,{format:at}):[,s,a,o,i]=wt(t)}else[,s,a,o,i]=Ct(t,e);return[s,a,o,i]},Pa=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if($a.test(t))return[,r,s,a,o]=Ms(t,{format:"hsl"}),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];let i,c,l;if(n===Z){let u;if(t.startsWith(ht)?u=wt(t,e):u=Ct(t,e),u instanceof A)return u;[,i,c,l,o]=u}else t.startsWith(ht)?[,i,c,l,o]=wt(t):[,i,c,l,o]=Ct(t);return[r,s,a]=ei([i,c,l],!0),n==="hsl"?[Math.round(r),Math.round(s),Math.round(a),o]:[n===Z&&s===0?g:r,s,a,o]},Da=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ya.test(t))return[,r,s,a,o]=Aa(t,{format:"hwb"}),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[r,s,a,o];let i,c,l;if(n===Z){let u;if(t.startsWith(ht)?u=wt(t,e):u=Ct(t,e),u instanceof A)return u;[,i,c,l,o]=u}else t.startsWith(ht)?[,i,c,l,o]=wt(t):[,i,c,l,o]=Ct(t);return[r,s,a]=Eu([i,c,l],!0),n==="hwb"?[Math.round(r),Math.round(s),Math.round(a),o]:[n===Z&&s+a>=100?g:r,s,a,o]},Ba=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(Na.test(t))return[,r,s,a,o]=Hr(t,{format:at}),[r,s,a,o];let i,c,l;if(n===Z){let u;if(e.d50=!0,t.startsWith(ht)?u=wt(t,e):u=Ct(t,e),u instanceof A)return u;[,i,c,l,o]=u}else t.startsWith(ht)?[,i,c,l,o]=wt(t,{d50:!0}):[,i,c,l,o]=Ct(t,{d50:!0});return[r,s,a]=si([i,c,l],!0),[r,s,a,o]},Ra=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(Ea.test(t))return[,r,s,a,o]=Ps(t,{format:at}),[r,s,a,o];let i,c,l;if(n===Z){let u;if(e.d50=!0,t.startsWith(ht)?u=wt(t,e):u=Ct(t,e),u instanceof A)return u;[,i,c,l,o]=u}else t.startsWith(ht)?[,i,c,l,o]=wt(t,{d50:!0}):[,i,c,l,o]=Ct(t,{d50:!0});return[r,s,a]=ku([i,c,l],!0),[r,s,n===Z&&s===0?g:a,o]},Oa=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(Ca.test(t))return[,r,s,a,o]=Ds(t,{format:at}),[r,s,a,o];let i,c,l;if(n===Z){let u;if(t.startsWith(ht)?u=wt(t,e):u=Ct(t,e),u instanceof A)return u;[,i,c,l,o]=u}else t.startsWith(ht)?[,i,c,l,o]=wt(t):[,i,c,l,o]=Ct(t);return[r,s,a]=ni([i,c,l],!0),[r,s,a,o]},Wa=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{format:n=""}=e;let r,s,a,o;if(ka.test(t))return[,r,s,a,o]=Bs(t,{format:at}),[r,s,a,o];let i,c,l;if(n===Z){let u;if(t.startsWith(ht)?u=wt(t,e):u=Ct(t,e),u instanceof A)return u;[,i,c,l,o]=u}else t.startsWith(ht)?[,i,c,l,o]=wt(t):[,i,c,l,o]=Ct(t);return[r,s,a]=Cu([i,c,l],!0),[r,s,n===Z&&s===0?g:a,o]},Rs=(t,e={})=>{if(x(t))t=t.toLowerCase().trim();else throw new TypeError(`${t} is not a string.`);const{format:n="",nullable:r=!1}=e,s=bt({namespace:ma,name:"resolveColorMix",value:t},e),a=vt(s);if(a instanceof it){if(a.isNull)return a;const P=a.item;return x(P),P}const o=[];let i="",c="",l="",u="",f="",d="",b=!1;if(!Zo.test(t))if(t.startsWith(gn)&&Jo.test(t)){const P=new RegExp(`^(?:${ua}|${ha})$`),D=t.match(Jo);for(const M of D)if(M){let O=Rs(M,{format:n===Q?n:at});if(Array.isArray(O)){const[j,z,Y,tt,ot]=O;if(z===0&&Y===0&&tt===0&&ot===0){t="";break}P.test(j)?ot===1?O=`color(${j} ${z} ${Y} ${tt})`:O=`color(${j} ${z} ${Y} ${tt} / ${ot})`:ot===1?O=`${j}(${z} ${Y} ${tt})`:O=`${j}(${z} ${Y} ${tt} / ${ot})`}else if(!Zo.test(O)){t="";break}o.push(O),t=t.replace(M,O)}if(!t)return ye(s,n,r)}else if(t.startsWith(gn)&&t.endsWith(")")&&t.includes(Es)){const P=new RegExp(`in\\s+(${Ns})`),D=t.replace(gn,"").replace(/\)$/,""),[M="",O="",j=""]=Dn(D,{delimiter:","}),[z="",Y=""]=Dn(O),[tt="",ot=""]=Dn(j),ft=kt(z,{format:Q}),St=kt(tt,{format:Q});if(P.test(M)&&ft&&St)if(n===Q){const[,Ft]=M.match(P);nr.test(Ft)?[,i,c]=Ft.match(nr):i=Ft,l=ft,Y&&(u=Y),f=St,ot&&(d=ot),t=t.replace(z,ft).replace(tt,St),b=!0}else{const Ft=kt(z,e),Qt=kt(tt,e);x(Ft)&&x(Qt)&&(t=t.replace(z,Ft).replace(tt,Qt))}else return ye(s,n,r)}else return ye(s,n,r);if(o.length&&n===Q){const P=new RegExp(`^color-mix\\(\\s*in\\s+(${Ns})\\s*,`),[,D]=t.match(P);if(nr.test(D)?[,i,c]=D.match(nr):i=D,o.length===2){let[M,O]=o;M=M.replace(/(?=[()])/g,"\\"),O=O.replace(/(?=[()])/g,"\\");const j=new RegExp(`(${M})(?:\\s+(${Gt}))?`),z=new RegExp(`(${O})(?:\\s+(${Gt}))?`);[,l,u]=t.match(j),[,f,d]=t.match(z)}else{let[M]=o;M=M.replace(/(?=[()])/g,"\\");const O=`${M}(?:\\s+${Gt})?`,j=`(${M})(?:\\s+(${Gt}))?`,z=new RegExp(`^${j}$`),Y=new RegExp(`${j}\\s*\\)$`),tt=new RegExp(`^(${Zn})(?:\\s+(${Gt}))?$`);if(Y.test(t)){const ot=new RegExp(`(${Jn})\\s*,\\s*(${O})\\s*\\)$`),[,ft,St]=t.match(ot);[,l,u]=ft.match(tt),[,f,d]=St.match(z)}else{const ot=new RegExp(`(${O})\\s*,\\s*(${Jn})\\s*\\)$`),[,ft,St]=t.match(ot);[,l,u]=ft.match(z),[,f,d]=St.match(tt)}}}else if(!b){const[,P,D,M]=t.match(Nu),O=new RegExp(`^(${Zn})(?:\\s+(${Gt}))?$`);[,l,u]=D.match(O),[,f,d]=M.match(O),nr.test(P)?[,i,c]=P.match(nr):i=P}let p,m,$;if(u&&d){const P=parseFloat(u)/q,D=parseFloat(d)/q;if(P<0||P>1||D<0||D>1||P===0&&D===0)return ye(s,n,r);const M=P+D;p=P/M,m=D/M,$=M<1?M:1}else{if(u){if(p=parseFloat(u)/q,p<0||p>1)return ye(s,n,r);m=1-p}else if(d){if(m=parseFloat(d)/q,m<0||m>1)return ye(s,n,r);p=1-m}else p=Rr,m=Rr;$=1}if(i==="xyz"&&(i="xyz-d65"),n===Q){let P="",D="";if(l.startsWith(gn)||l.startsWith(Es))P=l;else if(l.startsWith(ht)){const[M,O,j,z,Y]=wt(l,e);Y===1?P=`color(${M} ${O} ${j} ${z})`:P=`color(${M} ${O} ${j} ${z} / ${Y})`}else{const M=Ct(l,e);if(Array.isArray(M)){const[O,j,z,Y,tt]=M;tt===1?O==="rgb"?P=`${O}(${j}, ${z}, ${Y})`:P=`${O}(${j} ${z} ${Y})`:O==="rgb"?P=`${O}a(${j}, ${z}, ${Y}, ${tt})`:P=`${O}(${j} ${z} ${Y} / ${tt})`}else{if(!x(M)||!M)return k(s,""),"";P=M}}if(f.startsWith(gn)||f.startsWith(Es))D=f;else if(f.startsWith(ht)){const[M,O,j,z,Y]=wt(f,e);Y===1?D=`color(${M} ${O} ${j} ${z})`:D=`color(${M} ${O} ${j} ${z} / ${Y})`}else{const M=Ct(f,e);if(Array.isArray(M)){const[O,j,z,Y,tt]=M;tt===1?O==="rgb"?D=`${O}(${j}, ${z}, ${Y})`:D=`${O}(${j} ${z} ${Y})`:O==="rgb"?D=`${O}a(${j}, ${z}, ${Y}, ${tt})`:D=`${O}(${j} ${z} ${Y} / ${tt})`}else{if(!x(M)||!M)return k(s,""),"";D=M}}if(u&&d)P+=` ${parseFloat(u)}%`,D+=` ${parseFloat(d)}%`;else if(u){const M=parseFloat(u);M!==q*Rr&&(P+=` ${M}%`)}else if(d){const M=q-parseFloat(d);M!==q*Rr&&(P+=` ${M}%`)}if(c){const M=`color-mix(in ${i} ${c} hue, ${P}, ${D})`;return k(s,M),M}else{const M=`color-mix(in ${i}, ${P}, ${D})`;return k(s,M),M}}let w=0,N=0,C=0,E=0;if(/^srgb(?:-linear)?$/.test(i)){let P,D;if(i==="srgb"?(yt.test(l)?P=[g,g,g,g]:P=Ur(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Ur(f,{colorSpace:i,format:Z})):(yt.test(l)?P=[g,g,g,g]:P=Ma(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Ma(f,{colorSpace:i,format:Z})),P instanceof A||D instanceof A)return ye(s,n,r);const[M,O,j,z]=P,[Y,tt,ot,ft]=D,St=M===g&&Y===g,Ft=O===g&&tt===g,Qt=j===g&&ot===g,Fe=z===g&&ft===g,[[te,Ot,Wt,xe],[ee,It,Dt,Ve]]=Tr([M,O,j,z],[Y,tt,ot,ft],!0),pt=xe*p,dt=Ve*m;if(E=pt+dt,E===0?(w=te*p+ee*m,N=Ot*p+It*m,C=Wt*p+Dt*m):(w=(te*pt+ee*dt)/E,N=(Ot*pt+It*dt)/E,C=(Wt*pt+Dt*dt)/E,E=parseFloat(E.toFixed(3))),n===at){const Nt=[i,St?g:R(w,I),Ft?g:R(N,I),Qt?g:R(C,I),Fe?g:E*$];return k(s,Nt),Nt}w*=_,N*=_,C*=_}else if(yu.test(i)){let P,D;if(yt.test(l)?P=[g,g,g,g]:P=ii(l,{colorSpace:i,d50:i==="xyz-d50",format:Z}),yt.test(f)?D=[g,g,g,g]:D=ii(f,{colorSpace:i,d50:i==="xyz-d50",format:Z}),P instanceof A||D instanceof A)return ye(s,n,r);const[M,O,j,z]=P,[Y,tt,ot,ft]=D,St=M===g&&Y===g,Ft=O===g&&tt===g,Qt=j===g&&ot===g,Fe=z===g&&ft===g,[[te,Ot,Wt,xe],[ee,It,Dt,Ve]]=Tr([M,O,j,z],[Y,tt,ot,ft],!0),pt=xe*p,dt=Ve*m;E=pt+dt;let Nt,Lt,Tt;if(E===0?(Nt=te*p+ee*m,Lt=Ot*p+It*m,Tt=Wt*p+Dt*m):(Nt=(te*pt+ee*dt)/E,Lt=(Ot*pt+It*dt)/E,Tt=(Wt*pt+Dt*dt)/E,E=parseFloat(E.toFixed(3))),n===at){const ln=[i,St?g:R(Nt,I),Ft?g:R(Lt,I),Qt?g:R(Tt,I),Fe?g:E*$];return k(s,ln),ln}i==="xyz-d50"?[w,N,C]=ri([Nt,Lt,Tt],!0):[w,N,C]=ar([Nt,Lt,Tt],!0)}else if(/^h(?:sl|wb)$/.test(i)){let P,D;if(i==="hsl"?(yt.test(l)?P=[g,g,g,g]:P=Pa(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Pa(f,{colorSpace:i,format:Z})):(yt.test(l)?P=[g,g,g,g]:P=Da(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Da(f,{colorSpace:i,format:Z})),P instanceof A||D instanceof A)return ye(s,n,r);const[M,O,j,z]=P,[Y,tt,ot,ft]=D,St=z===g&&ft===g;let[[Ft,Qt,Fe,te],[Ot,Wt,xe,ee]]=Tr([M,O,j,z],[Y,tt,ot,ft],!0);c&&([Ft,Ot]=Gi(Ft,Ot,c));const It=te*p,Dt=ee*m;E=It+Dt;const Ve=(Ft*p+Ot*m)%me;let pt,dt;if(E===0?(pt=Qt*p+Wt*m,dt=Fe*p+xe*m):(pt=(Qt*It+Wt*Dt)/E,dt=(Fe*It+xe*Dt)/E,E=parseFloat(E.toFixed(3))),[w,N,C]=Ur(`${i}(${Ve} ${pt} ${dt})`),n===at){const Nt=["srgb",R(w/_,I),R(N/_,I),R(C/_,I),St?g:E*$];return k(s,Nt),Nt}}else if(/^(?:ok)?lch$/.test(i)){let P,D;if(i==="lch"?(yt.test(l)?P=[g,g,g,g]:P=Ra(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Ra(f,{colorSpace:i,format:Z})):(yt.test(l)?P=[g,g,g,g]:P=Wa(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Wa(f,{colorSpace:i,format:Z})),P instanceof A||D instanceof A)return ye(s,n,r);const[M,O,j,z]=P,[Y,tt,ot,ft]=D,St=M===g&&Y===g,Ft=O===g&&tt===g,Qt=j===g&&ot===g,Fe=z===g&&ft===g;let[[te,Ot,Wt,xe],[ee,It,Dt,Ve]]=Tr([M,O,j,z],[Y,tt,ot,ft],!0);c&&([Wt,Dt]=Gi(Wt,Dt,c));const pt=xe*p,dt=Ve*m;E=pt+dt;const Nt=(Wt*p+Dt*m)%me;let Lt,Tt;if(E===0?(Lt=te*p+ee*m,Tt=Ot*p+It*m):(Lt=(te*pt+ee*dt)/E,Tt=(Ot*pt+It*dt)/E,E=parseFloat(E.toFixed(3))),n===at){const ln=[i,St?g:R(Lt,I),Ft?g:R(Tt,I),Qt?g:R(Nt,I),Fe?g:E*$];return k(s,ln),ln}[,w,N,C]=Sn(`${i}(${Lt} ${Tt} ${Nt})`)}else{let P,D;if(i==="lab"?(yt.test(l)?P=[g,g,g,g]:P=Ba(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Ba(f,{colorSpace:i,format:Z})):(yt.test(l)?P=[g,g,g,g]:P=Oa(l,{colorSpace:i,format:Z}),yt.test(f)?D=[g,g,g,g]:D=Oa(f,{colorSpace:i,format:Z})),P instanceof A||D instanceof A)return ye(s,n,r);const[M,O,j,z]=P,[Y,tt,ot,ft]=D,St=M===g&&Y===g,Ft=O===g&&tt===g,Qt=j===g&&ot===g,Fe=z===g&&ft===g,[[te,Ot,Wt,xe],[ee,It,Dt,Ve]]=Tr([M,O,j,z],[Y,tt,ot,ft],!0),pt=xe*p,dt=Ve*m;E=pt+dt;let Nt,Lt,Tt;if(E===0?(Nt=te*p+ee*m,Lt=Ot*p+It*m,Tt=Wt*p+Dt*m):(Nt=(te*pt+ee*dt)/E,Lt=(Ot*pt+It*dt)/E,Tt=(Wt*pt+Dt*dt)/E,E=parseFloat(E.toFixed(3))),n===at){const ln=[i,St?g:R(Nt,I),Ft?g:R(Lt,I),Qt?g:R(Tt,I),Fe?g:E*$];return k(s,ln),ln}[,w,N,C]=Sn(`${i}(${Nt} ${Lt} ${Tt})`)}const J=["rgb",Math.round(w),Math.round(N),Math.round(C),parseFloat((E*$).toFixed(3))];return k(s,J),J},{CloseParen:li,Comment:Su,EOF:Au,Ident:Mu,Whitespace:Pu}=v,Du="css-var",ci=new RegExp($s),ui=new RegExp(Pr);function hi(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{customProperty:n={}}=e,r=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i,c]=o;if(i===li)break;if(c===fa){const[l,u]=hi(t,e);t=l,u&&r.push(u)}else if(i===Mu)if(c.startsWith("--")){let l;Object.hasOwn(n,c)?l=n[c]:typeof n.callback=="function"&&(l=n.callback(c)),l&&r.push(l)}else c&&r.push(c)}let s=!1;if(r.length>1){const o=r[r.length-1];s=bn(o)}let a="";for(let o of r){if(o=o.trim(),ui.test(o)){const i=zr(o,e);x(i)&&(s?bn(i)&&(a=i):a=i)}else ci.test(o)?(o=Yr(o,e),s?bn(o)&&(a=o):a=o):o&&!/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(o)&&(s?bn(o)&&(a=o):a=o);if(a)break}return[t,a]}function Bu(t,e={}){const n=[];for(;t.length;){const r=t.shift(),[s="",a=""]=r;if(a===fa){const[o,i]=hi(t,e);if(!i)return new A;t=o,n.push(i)}else switch(s){case li:{n.length&&n[n.length-1]===" "?n.splice(-1,1,a):n.push(a);break}case Pu:{if(n.length){const o=n[n.length-1];x(o)&&!o.endsWith("(")&&o!==" "&&n.push(a)}break}default:s!==Su&&s!==Au&&n.push(a)}}return n}function zr(t,e={}){const{format:n=""}=e;if(x(t)){if(!ui.test(t)||n===Q)return t;t=t.trim()}else throw new TypeError(`${t} is not a string.`);const r=bt({namespace:Du,name:"resolveVar",value:t},e),s=vt(r);if(s instanceof it)return s.isNull?s:s.item;const a=Ye({css:t}),o=Bu(a,e);if(Array.isArray(o)){let i=o.join("");return ci.test(i)&&(i=Yr(i,e)),k(r,i),i}else return k(r,null),new A}const Ru=(t,e={})=>{const n=zr(t,e);return x(n)?n:""};function Bt(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2],t[3]*e[0]+t[4]*e[1]+t[5]*e[2],t[6]*e[0]+t[7]*e[1]+t[8]*e[2]]}const Ou=[.955473421488075,-.02309845494876471,.06325924320057072,-.0283697093338637,1.0099953980813041,.021041441191917323,.012314014864481998,-.020507649298898964,1.330365926242124];/**
 * Bradford chromatic adaptation from D50 to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Ue(t){return Bt(Ou,t)}const Wu=[1.0479297925449969,.022946870601609652,-.05019226628920524,.02962780877005599,.9904344267538799,-.017073799063418826,-.009243040646204504,.015055191490298152,.7518742814281371];/**
 * Bradford chromatic adaptation from D65 to D50
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_ChromAdapt.html
 */function ze(t){return Bt(Wu,t)}/**
 * @param {number} hue - Hue as degrees 0..360
 * @param {number} sat - Saturation as percentage 0..100
 * @param {number} light - Lightness as percentage 0..100
 * @return {number[]} Array of sRGB components; in-gamut colors in range [0..1]
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hslToRgb.js
 */function fi(t){let e=t[0]%360;const n=t[1]/100,r=t[2]/100;return e<0&&(e+=360),[Ia(0,e,n,r),Ia(8,e,n,r),Ia(4,e,n,r)]}function Ia(t,e,n,r){const s=(t+e/30)%12;return r-n*Math.min(r,1-r)*Math.max(-1,Math.min(s-3,9-s,1))}/**
 * @param {number} hue -  Hue as degrees 0..360
 * @param {number} white -  Whiteness as percentage 0..100
 * @param {number} black -  Blackness as percentage 0..100
 * @return {number[]} Array of RGB components 0..1
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/hwbToRgb.js
 */function Iu(t){const e=t[0],n=t[1]/100,r=t[2]/100;if(n+r>=1){const o=n/(n+r);return[o,o,o]}const s=fi([e,100,50]),a=1-n-r;return[s[0]*a+n,s[1]*a+n,s[2]*a+n]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Lu(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Tu(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(Math.pow(t[1],2)+Math.pow(t[2],2)),e>=0?e:e+360]}const or=[.3457/.3585,1,.2958/.3585];/**
 * Convert Lab to D50-adapted XYZ
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function pi(t){const e=903.2962962962963,n=216/24389,r=(t[0]+16)/116,s=t[1]/500+r,a=r-t[2]/200;return[(Math.pow(s,3)>n?Math.pow(s,3):(116*s-16)/e)*or[0],(t[0]>8?Math.pow((t[0]+16)/116,3):t[0]/e)*or[1],(Math.pow(a,3)>n?Math.pow(a,3):(116*a-16)/e)*or[2]]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function di(t){const e=t[2]*Math.PI/180;return[t[0],t[1]*Math.cos(e),t[1]*Math.sin(e)]}/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function mi(t){const e=180*Math.atan2(t[2],t[1])/Math.PI;return[t[0],Math.sqrt(t[1]**2+t[2]**2),e>=0?e:e+360]}const _u=[1.2268798758459243,-.5578149944602171,.2813910456659647,-.0405757452148008,1.112286803280317,-.0717110580655164,-.0763729366746601,-.4214933324022432,1.5869240198367816],Hu=[1,.3963377773761749,.2158037573099136,1,-.1055613458156586,-.0638541728258133,1,-.0894841775298119,-1.2914855480194092];/**
 * Given OKLab, convert to XYZ relative to D65
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js
 */function La(t){const e=Bt(Hu,t);return Bt(_u,[e[0]**3,e[1]**3,e[2]**3])}/**
 * Assuming XYZ is relative to D50, convert to CIE Lab
 * from CIE standard, which now defines these as a rational fraction
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function gi(t){const e=Ta(t[0]/or[0]),n=Ta(t[1]/or[1]);return[116*n-16,500*(e-n),200*(n-Ta(t[2]/or[2]))]}const Uu=216/24389,zu=24389/27;function Ta(t){return t>Uu?Math.cbrt(t):(zu*t+16)/116}const Gu=[.819022437996703,.3619062600528904,-.1288737815209879,.0329836539323885,.9292868615863434,.0361446663506424,.0481771893596242,.2642395317527308,.6335478284694309],ju=[.210454268309314,.7936177747023054,-.0040720430116193,1.9779985324311684,-2.42859224204858,.450593709617411,.0259040424655478,.7827717124575296,-.8086757549230774];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * XYZ <-> LMS matrices recalculated for consistent reference white
 * @see https://github.com/w3c/csswg-drafts/issues/6642#issuecomment-943521484
 */function _a(t){const e=Bt(Gu,t);return Bt(ju,[Math.cbrt(e[0]),Math.cbrt(e[1]),Math.cbrt(e[2])])}const qu=[30757411/17917100,-6372589/17917100,-4539589/17917100,-.666684351832489,1.616481236634939,467509/29648200,792561/44930125,-1921689/44930125,.942103121235474];/**
 * Convert XYZ to linear-light rec2020
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Vu=[446124/178915,-333277/357830,-72051/178915,-14852/17905,63121/35810,423/17905,11844/330415,-50337/660830,316169/330415];/**
 * Convert XYZ to linear-light P3
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Xu(t){return Bt(Vu,t)}const Ku=[1.3457868816471583,-.25557208737979464,-.05110186497554526,-.5446307051249019,1.5082477428451468,.02052744743642139,0,0,1.2119675456389452];/**
 * Convert D50 XYZ to linear-light prophoto-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */const Yu=[1829569/896150,-506331/896150,-308931/896150,-851781/878810,1648619/878810,36519/878810,16779/1248040,-147721/1248040,1266979/1248040];/**
 * Convert XYZ to linear-light a98-rgb
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const Zu=[12831/3959,-329/214,-1974/3959,-851781/878810,1648619/878810,36519/878810,705/12673,-2585/12673,705/667];/**
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Gr(t){return Bt(Zu,t)}/**
 * Convert an array of linear-light rec2020 RGB  in the range 0.0-1.0
 * to gamma corrected form ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const vi=1.09929682680944,Ju=.018053968510807;function Ha(t){const e=t<0?-1:1,n=Math.abs(t);return n>Ju?e*(vi*Math.pow(n,.45)-(vi-1)):4.5*t}/**
 * Convert an array of linear-light sRGB values in the range 0.0-1.0 to gamma corrected form
 * Extended transfer function:
 *  For negative values, linear portion extends on reflection
 *  of axis, then uses reflected pow below that
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function jr(t){return[Ua(t[0]),Ua(t[1]),Ua(t[2])]}function Ua(t){const e=t<0?-1:1,n=Math.abs(t);return n>.0031308?e*(1.055*Math.pow(n,1/2.4)-.055):12.92*t}/**
 * Convert an array of linear-light display-p3 RGB in the range 0.0-1.0
 * to gamma corrected form
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Qu(t){return jr(t)}/**
 * Convert an array of linear-light prophoto-rgb in the range 0.0-1.0
 * to gamma corrected form.
 * Transfer curve is gamma 1.8 with a small linear portion.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const th=1/512;function za(t){const e=t<0?-1:1,n=Math.abs(t);return n>=th?e*Math.pow(n,1/1.8):16*t}/**
 * Convert an array of linear-light a98-rgb in the range 0.0-1.0
 * to gamma corrected form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function Ga(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,256/563)}/**
 * Convert an array of rec2020 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 * ITU-R BT.2020-2 p.4
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const bi=1.09929682680944,eh=.018053968510807;function ja(t){const e=t<0?-1:1,n=Math.abs(t);return n<4.5*eh?t/4.5:e*Math.pow((n+bi-1)/bi,1/.45)}const nh=[63426534/99577255,20160776/139408157,47086771/278816314,26158966/99577255,.677998071518871,8267143/139408157,0,19567812/697040785,1.0609850577107909];/**
 * Convert an array of linear-light rec2020 values to CIE XYZ
 * using  D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 *//**
 * Convert an array of of sRGB values where in-gamut values are in the range
 * [0 - 1] to linear light (un-companded) form.
 * Extended transfer function:
 *  For negative values, linear portion is extended on reflection of axis,
 *  then reflected power function is used.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see https://en.wikipedia.org/wiki/SRGB
 */function Os(t){return[qa(t[0]),qa(t[1]),qa(t[2])]}function qa(t){const e=t<0?-1:1,n=Math.abs(t);return n<=.04045?t/12.92:e*Math.pow((n+.055)/1.055,2.4)}/**
 * Convert an array of display-p3 RGB values in the range 0.0 - 1.0
 * to linear light (un-companded) form.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function rh(t){return Os(t)}const sh=[608311/1250200,189793/714400,198249/1000160,35783/156275,247089/357200,198249/2500400,0,32229/714400,5220557/5000800];/**
 * Convert an array of linear-light display-p3 values to CIE XYZ
 * using D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 */function ah(t){return Bt(sh,t)}/**
 * Convert an array of prophoto-rgb values where in-gamut Colors are in the
 * range [0.0 - 1.0] to linear light (un-companded) form. Transfer curve is
 * gamma 1.8 with a small linear portion. Extended transfer function
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */const oh=16/512;function Va(t){const e=t<0?-1:1,n=Math.abs(t);return n<=oh?t/16:e*Math.pow(n,1.8)}const ih=[.7977666449006423,.13518129740053308,.0313477341283922,.2880748288194013,.711835234241873,8993693872564e-17,0,0,.8251046025104602];/**
 * Convert an array of linear-light prophoto-rgb values to CIE D50 XYZ.
 * Matrix cannot be expressed in rational form, but is calculated to 64 bit accuracy.
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see see https://github.com/w3c/csswg-drafts/issues/7675
 */function Xa(t){const e=t<0?-1:1,n=Math.abs(t);return e*Math.pow(n,563/256)}const lh=[573536/994567,263643/1420810,187206/994567,591459/1989134,6239551/9945670,374412/4972835,53769/1989134,351524/4972835,4929758/4972835];/**
 * Convert an array of linear-light a98-rgb values to CIE XYZ
 * http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * has greater numerical precision than section ******* of
 * https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * but the values below were calculated from first principles
 * from the chromaticity coordinates of R G B W
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 * @see http://www.brucelindbloom.com/index.html?Eqn_RGB_XYZ_Matrix.html
 * @see https://www.adobe.com/digitalimag/pdfs/AdobeRGB1998.pdf
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/matrixmaker.html
 */const ch=[506752/1228815,87881/245763,12673/70218,87098/409605,175762/245763,12673/175545,7918/409605,87881/737289,1001167/1053270];/**
 * Convert an array of linear-light sRGB values to CIE XYZ
 * using sRGB's own white, D65 (no chromatic adaptation)
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */function qr(t){return Bt(ch,t)}/**
 * Convert an array of gamma-corrected sRGB values in the 0.0 to 1.0 range to HSL.
 *
 * @param {Color} RGB [r, g, b]
 * - Red component 0..1
 * - Green component 0..1
 * - Blue component 0..1
 * @return {number[]} Array of HSL values: Hue as degrees 0..360, Saturation and Lightness as percentages 0..100
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/utilities.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 *
 * @see https://github.com/w3c/csswg-drafts/blob/main/css-color-4/better-rgbToHsl.js
 */function uh(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r),o=(a+s)/2,i=s-a;let c=Number.NaN,l=0;if(Math.round(1e5*i)!==0){const u=Math.round(1e5*o);switch(l=u===0||u===1e5?0:(s-o)/Math.min(o,1-o),s){case e:c=(n-r)/i+(n<r?6:0);break;case n:c=(r-e)/i+2;break;case r:c=(e-n)/i+4}c*=60}return l<0&&(c+=180,l=Math.abs(l)),c>=360&&(c-=360),[c,100*l,100*o]}function hh(t){const e=t[0],n=t[1],r=t[2],s=Math.max(e,n,r),a=Math.min(e,n,r);let o=Number.NaN;const i=s-a;if(i!==0){switch(s){case e:o=(n-r)/i+(n<r?6:0);break;case n:o=(r-e)/i+2;break;case r:o=(e-n)/i+4}o*=60}return o>=360&&(o-=360),o}function fh(t){let e=t;return e=Os(e),e=qr(e),e=ze(e),e}function Ka(t){let e=t;return e=Ue(e),e=Gr(e),e=jr(e),e}function ph(t){let e=t;return e=fi(e),e=Os(e),e=qr(e),e=ze(e),e}function dh(t){let e=t;return e=Ue(e),e=Gr(e),e=jr(e),e=uh(e),e}function mh(t){let e=t;return e=Iu(e),e=Os(e),e=qr(e),e=ze(e),e}function gh(t){let e=t;e=Ue(e),e=Gr(e);const n=jr(e),r=Math.min(n[0],n[1],n[2]),s=1-Math.max(n[0],n[1],n[2]);return[hh(n),100*r,100*s]}function vh(t){let e=t;return e=pi(e),e}function bh(t){let e=t;return e=gi(e),e}function wh(t){let e=t;return e=Lu(e),e=pi(e),e}function $h(t){let e=t;return e=gi(e),e=Tu(e),e}function yh(t){let e=t;return e=La(e),e=ze(e),e}function Nh(t){let e=t;return e=Ue(e),e=_a(e),e}function Eh(t){let e=t;return e=di(e),e=La(e),e=ze(e),e}function wi(t){let e=t;return e=Ue(e),e=_a(e),e=mi(e),e}function Ch(t){let e=t;return e=qr(e),e=ze(e),e}function kh(t){let e=t;return e=Ue(e),e=Gr(e),e}function Fh(t){let e=t;/**
 * Convert an array of a98-rgb values in the range 0.0 - 1.0
 * to linear light (un-companded) form. Negative values are also now accepted
 *
 * @license W3C https://www.w3.org/Consortium/Legal/2015/copyright-software-and-document
 * @copyright This software or document includes material copied from or derived from https://github.com/w3c/csswg-drafts/blob/main/css-color-4/conversions.js. Copyright © 2022 W3C® (MIT, ERCIM, Keio, Beihang).
 */var n;return e=[Xa((n=e)[0]),Xa(n[1]),Xa(n[2])],e=Bt(lh,e),e=ze(e),e}function xh(t){let e=t;var n;return e=Ue(e),e=Bt(Yu,e),e=[Ga((n=e)[0]),Ga(n[1]),Ga(n[2])],e}function Sh(t){let e=t;return e=rh(e),e=ah(e),e=ze(e),e}function Ah(t){let e=t;return e=Ue(e),e=Xu(e),e=Qu(e),e}function Mh(t){let e=t;var n;return e=[ja((n=e)[0]),ja(n[1]),ja(n[2])],e=Bt(nh,e),e=ze(e),e}function Ph(t){let e=t;var n;return e=Ue(e),e=Bt(qu,e),e=[Ha((n=e)[0]),Ha(n[1]),Ha(n[2])],e}function Dh(t){let e=t;var n;return e=[Va((n=e)[0]),Va(n[1]),Va(n[2])],e=Bt(ih,e),e}function Bh(t){let e=t;var n;return e=Bt(Ku,e),e=[za((n=e)[0]),za(n[1]),za(n[2])],e}function Rh(t){let e=t;return e=ze(e),e}function Oh(t){let e=t;return e=Ue(e),e}function Wh(t){return t[0]>=-1e-4&&t[0]<=1.0001&&t[1]>=-1e-4&&t[1]<=1.0001&&t[2]>=-1e-4&&t[2]<=1.0001}function $i(t){return[t[0]<0?0:t[0]>1?1:t[0],t[1]<0?0:t[1]>1?1:t[1],t[2]<0?0:t[2]>1?1:t[2]]}/**
 * @license MIT https://github.com/facelessuser/coloraide/blob/main/LICENSE.md
 */function Ih(t,e,n){const r=t[0],s=t[2];let a=e(t);const o=e([r,0,s]);for(let i=0;i<4;i++){if(i>0){const l=n(a);l[0]=r,l[2]=s,a=e(l)}const c=Lh(o,a);if(!c)break;a=c}return $i(a)}function Lh(t,e){let n=1/0,r=-1/0;const s=[0,0,0];for(let a=0;a<3;a++){const o=t[a],i=e[a]-o;s[a]=i;const c=0,l=1;if(i){const u=1/i,f=(c-o)*u,d=(l-o)*u;r=Math.max(Math.min(f,d),r),n=Math.min(Math.max(f,d),n)}else if(o<c||o>l)return!1}return!(r>n||n<0)&&(r<0&&(r=n),!!isFinite(r)&&[t[0]+s[0]*r,t[1]+s[1]*r,t[2]+s[2]*r])}const Th={aliceblue:[240,248,255],antiquewhite:[250,235,215],aqua:[0,255,255],aquamarine:[127,255,212],azure:[240,255,255],beige:[245,245,220],bisque:[255,228,196],black:[0,0,0],blanchedalmond:[255,235,205],blue:[0,0,255],blueviolet:[138,43,226],brown:[165,42,42],burlywood:[222,184,135],cadetblue:[95,158,160],chartreuse:[127,255,0],chocolate:[210,105,30],coral:[255,127,80],cornflowerblue:[100,149,237],cornsilk:[255,248,220],crimson:[220,20,60],cyan:[0,255,255],darkblue:[0,0,139],darkcyan:[0,139,139],darkgoldenrod:[184,134,11],darkgray:[169,169,169],darkgreen:[0,100,0],darkgrey:[169,169,169],darkkhaki:[189,183,107],darkmagenta:[139,0,139],darkolivegreen:[85,107,47],darkorange:[255,140,0],darkorchid:[153,50,204],darkred:[139,0,0],darksalmon:[233,150,122],darkseagreen:[143,188,143],darkslateblue:[72,61,139],darkslategray:[47,79,79],darkslategrey:[47,79,79],darkturquoise:[0,206,209],darkviolet:[148,0,211],deeppink:[255,20,147],deepskyblue:[0,191,255],dimgray:[105,105,105],dimgrey:[105,105,105],dodgerblue:[30,144,255],firebrick:[178,34,34],floralwhite:[255,250,240],forestgreen:[34,139,34],fuchsia:[255,0,255],gainsboro:[220,220,220],ghostwhite:[248,248,255],gold:[255,215,0],goldenrod:[218,165,32],gray:[128,128,128],green:[0,128,0],greenyellow:[173,255,47],grey:[128,128,128],honeydew:[240,255,240],hotpink:[255,105,180],indianred:[205,92,92],indigo:[75,0,130],ivory:[255,255,240],khaki:[240,230,140],lavender:[230,230,250],lavenderblush:[255,240,245],lawngreen:[124,252,0],lemonchiffon:[255,250,205],lightblue:[173,216,230],lightcoral:[240,128,128],lightcyan:[224,255,255],lightgoldenrodyellow:[250,250,210],lightgray:[211,211,211],lightgreen:[144,238,144],lightgrey:[211,211,211],lightpink:[255,182,193],lightsalmon:[255,160,122],lightseagreen:[32,178,170],lightskyblue:[135,206,250],lightslategray:[119,136,153],lightslategrey:[119,136,153],lightsteelblue:[176,196,222],lightyellow:[255,255,224],lime:[0,255,0],limegreen:[50,205,50],linen:[250,240,230],magenta:[255,0,255],maroon:[128,0,0],mediumaquamarine:[102,205,170],mediumblue:[0,0,205],mediumorchid:[186,85,211],mediumpurple:[147,112,219],mediumseagreen:[60,179,113],mediumslateblue:[123,104,238],mediumspringgreen:[0,250,154],mediumturquoise:[72,209,204],mediumvioletred:[199,21,133],midnightblue:[25,25,112],mintcream:[245,255,250],mistyrose:[255,228,225],moccasin:[255,228,181],navajowhite:[255,222,173],navy:[0,0,128],oldlace:[253,245,230],olive:[128,128,0],olivedrab:[107,142,35],orange:[255,165,0],orangered:[255,69,0],orchid:[218,112,214],palegoldenrod:[238,232,170],palegreen:[152,251,152],paleturquoise:[175,238,238],palevioletred:[219,112,147],papayawhip:[255,239,213],peachpuff:[255,218,185],peru:[205,133,63],pink:[255,192,203],plum:[221,160,221],powderblue:[176,224,230],purple:[128,0,128],rebeccapurple:[102,51,153],red:[255,0,0],rosybrown:[188,143,143],royalblue:[65,105,225],saddlebrown:[139,69,19],salmon:[250,128,114],sandybrown:[244,164,96],seagreen:[46,139,87],seashell:[255,245,238],sienna:[160,82,45],silver:[192,192,192],skyblue:[135,206,235],slateblue:[106,90,205],slategray:[112,128,144],slategrey:[112,128,144],snow:[255,250,250],springgreen:[0,255,127],steelblue:[70,130,180],tan:[210,180,140],teal:[0,128,128],thistle:[216,191,216],tomato:[255,99,71],turquoise:[64,224,208],violet:[238,130,238],wheat:[245,222,179],white:[255,255,255],whitesmoke:[245,245,245],yellow:[255,255,0],yellowgreen:[154,205,50]};function yi(t){const[e,n,r]=t.map(s=>s<=.03928?s/12.92:Math.pow((s+.055)/1.055,2.4));return .2126*e+.7152*n+.0722*r}function Ni(t,e){const n=yi(t),r=yi(e);return(Math.max(n,r)+.05)/(Math.min(n,r)+.05)}var y,B;function Rt(t){return[Number.isNaN(t[0])?0:t[0],Number.isNaN(t[1])?0:t[1],Number.isNaN(t[2])?0:t[2]]}function Ei(t){switch(t.colorNotation){case y.HEX:case y.RGB:case y.sRGB:return{...t,colorNotation:y.XYZ_D50,channels:fh(Rt(t.channels))};case y.Linear_sRGB:return{...t,colorNotation:y.XYZ_D50,channels:Ch(Rt(t.channels))};case y.Display_P3:return{...t,colorNotation:y.XYZ_D50,channels:Sh(Rt(t.channels))};case y.Rec2020:return{...t,colorNotation:y.XYZ_D50,channels:Mh(Rt(t.channels))};case y.A98_RGB:return{...t,colorNotation:y.XYZ_D50,channels:Fh(Rt(t.channels))};case y.ProPhoto_RGB:return{...t,colorNotation:y.XYZ_D50,channels:Dh(Rt(t.channels))};case y.HSL:return{...t,colorNotation:y.XYZ_D50,channels:ph(Rt(t.channels))};case y.HWB:return{...t,colorNotation:y.XYZ_D50,channels:mh(Rt(t.channels))};case y.Lab:return{...t,colorNotation:y.XYZ_D50,channels:vh(Rt(t.channels))};case y.OKLab:return{...t,colorNotation:y.XYZ_D50,channels:yh(Rt(t.channels))};case y.LCH:return{...t,colorNotation:y.XYZ_D50,channels:wh(Rt(t.channels))};case y.OKLCH:return{...t,colorNotation:y.XYZ_D50,channels:Eh(Rt(t.channels))};case y.XYZ_D50:return{...t,colorNotation:y.XYZ_D50,channels:Rt(t.channels)};case y.XYZ_D65:return{...t,colorNotation:y.XYZ_D50,channels:Rh(Rt(t.channels))};default:throw new Error("Unsupported color notation")}}(function(t){t.A98_RGB="a98-rgb",t.Display_P3="display-p3",t.HEX="hex",t.HSL="hsl",t.HWB="hwb",t.LCH="lch",t.Lab="lab",t.Linear_sRGB="srgb-linear",t.OKLCH="oklch",t.OKLab="oklab",t.ProPhoto_RGB="prophoto-rgb",t.RGB="rgb",t.sRGB="srgb",t.Rec2020="rec2020",t.XYZ_D50="xyz-d50",t.XYZ_D65="xyz-d65"})(y||(y={})),function(t){t.ColorKeyword="color-keyword",t.HasAlpha="has-alpha",t.HasDimensionValues="has-dimension-values",t.HasNoneKeywords="has-none-keywords",t.HasNumberValues="has-number-values",t.HasPercentageAlpha="has-percentage-alpha",t.HasPercentageValues="has-percentage-values",t.HasVariableAlpha="has-variable-alpha",t.Hex="hex",t.LegacyHSL="legacy-hsl",t.LegacyRGB="legacy-rgb",t.NamedColor="named-color",t.RelativeColorSyntax="relative-color-syntax",t.ColorMix="color-mix",t.ColorMixVariadic="color-mix-variadic",t.ContrastColor="contrast-color",t.Experimental="experimental"}(B||(B={}));const Ci=new Set([y.A98_RGB,y.Display_P3,y.HEX,y.Linear_sRGB,y.ProPhoto_RGB,y.RGB,y.sRGB,y.Rec2020,y.XYZ_D50,y.XYZ_D65]);function vn(t,e){const n={...t};if(t.colorNotation!==e){const r=Ei(n);switch(e){case y.HEX:case y.RGB:n.colorNotation=y.RGB,n.channels=Ka(r.channels);break;case y.sRGB:n.colorNotation=y.sRGB,n.channels=Ka(r.channels);break;case y.Linear_sRGB:n.colorNotation=y.Linear_sRGB,n.channels=kh(r.channels);break;case y.Display_P3:n.colorNotation=y.Display_P3,n.channels=Ah(r.channels);break;case y.Rec2020:n.colorNotation=y.Rec2020,n.channels=Ph(r.channels);break;case y.ProPhoto_RGB:n.colorNotation=y.ProPhoto_RGB,n.channels=Bh(r.channels);break;case y.A98_RGB:n.colorNotation=y.A98_RGB,n.channels=xh(r.channels);break;case y.HSL:n.colorNotation=y.HSL,n.channels=dh(r.channels);break;case y.HWB:n.colorNotation=y.HWB,n.channels=gh(r.channels);break;case y.Lab:n.colorNotation=y.Lab,n.channels=bh(r.channels);break;case y.LCH:n.colorNotation=y.LCH,n.channels=$h(r.channels);break;case y.OKLCH:n.colorNotation=y.OKLCH,n.channels=wi(r.channels);break;case y.OKLab:n.colorNotation=y.OKLab,n.channels=Nh(r.channels);break;case y.XYZ_D50:n.colorNotation=y.XYZ_D50,n.channels=r.channels;break;case y.XYZ_D65:n.colorNotation=y.XYZ_D65,n.channels=Oh(r.channels);break;default:throw new Error("Unsupported color notation")}}else n.channels=Rt(t.channels);if(e===t.colorNotation)n.channels=Jt(t.channels,[0,1,2],n.channels,[0,1,2]);else if(Ci.has(e)&&Ci.has(t.colorNotation))n.channels=Jt(t.channels,[0,1,2],n.channels,[0,1,2]);else switch(e){case y.HSL:switch(t.colorNotation){case y.HWB:n.channels=Jt(t.channels,[0],n.channels,[0]);break;case y.Lab:case y.OKLab:n.channels=Jt(t.channels,[2],n.channels,[0]);break;case y.LCH:case y.OKLCH:n.channels=Jt(t.channels,[0,1,2],n.channels,[2,1,0])}break;case y.HWB:switch(t.colorNotation){case y.HSL:n.channels=Jt(t.channels,[0],n.channels,[0]);break;case y.LCH:case y.OKLCH:n.channels=Jt(t.channels,[0],n.channels,[2])}break;case y.Lab:case y.OKLab:switch(t.colorNotation){case y.HSL:n.channels=Jt(t.channels,[0],n.channels,[2]);break;case y.Lab:case y.OKLab:n.channels=Jt(t.channels,[0,1,2],n.channels,[0,1,2]);break;case y.LCH:case y.OKLCH:n.channels=Jt(t.channels,[0],n.channels,[0])}break;case y.LCH:case y.OKLCH:switch(t.colorNotation){case y.HSL:n.channels=Jt(t.channels,[0,1,2],n.channels,[2,1,0]);break;case y.HWB:n.channels=Jt(t.channels,[0],n.channels,[2]);break;case y.Lab:case y.OKLab:n.channels=Jt(t.channels,[0],n.channels,[0]);break;case y.LCH:case y.OKLCH:n.channels=Jt(t.channels,[0,1,2],n.channels,[0,1,2])}}return n.channels=_h(n.channels,e),n}function _h(t,e){const n=[...t];switch(e){case y.HSL:!Number.isNaN(n[1])&&Vr(n[1],4)<=0&&(n[0]=Number.NaN);break;case y.HWB:Math.max(0,Vr(n[1],4))+Math.max(0,Vr(n[2],4))>=100&&(n[0]=Number.NaN);break;case y.LCH:!Number.isNaN(n[1])&&Vr(n[1],4)<=0&&(n[2]=Number.NaN);break;case y.OKLCH:!Number.isNaN(n[1])&&Vr(n[1],6)<=0&&(n[2]=Number.NaN)}return n}function Jt(t,e,n,r){const s=[...n];for(const a of e)Number.isNaN(t[e[a]])&&(s[r[a]]=Number.NaN);return s}function ki(t){const e=new Map;switch(t.colorNotation){case y.RGB:case y.HEX:e.set("r",rt(255*t.channels[0])),e.set("g",rt(255*t.channels[1])),e.set("b",rt(255*t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha));break;case y.HSL:e.set("h",rt(t.channels[0])),e.set("s",rt(t.channels[1])),e.set("l",rt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha));break;case y.HWB:e.set("h",rt(t.channels[0])),e.set("w",rt(t.channels[1])),e.set("b",rt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha));break;case y.Lab:case y.OKLab:e.set("l",rt(t.channels[0])),e.set("a",rt(t.channels[1])),e.set("b",rt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha));break;case y.LCH:case y.OKLCH:e.set("l",rt(t.channels[0])),e.set("c",rt(t.channels[1])),e.set("h",rt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha));break;case y.sRGB:case y.A98_RGB:case y.Display_P3:case y.Rec2020:case y.Linear_sRGB:case y.ProPhoto_RGB:e.set("r",rt(t.channels[0])),e.set("g",rt(t.channels[1])),e.set("b",rt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha));break;case y.XYZ_D50:case y.XYZ_D65:e.set("x",rt(t.channels[0])),e.set("y",rt(t.channels[1])),e.set("z",rt(t.channels[2])),typeof t.alpha=="number"&&e.set("alpha",rt(t.alpha))}return e}function Fi(t){const e=new Map(t);for(const[n,r]of t)Number.isNaN(r[4].value)&&e.set(n,rt(0));return e}function rt(t){return Number.isNaN(t)?[v.Number,"none",-1,-1,{value:Number.NaN,type:S.Number}]:[v.Number,t.toString(),-1,-1,{value:t,type:S.Number}]}function Vr(t,e=7){if(Number.isNaN(t))return 0;const n=Math.pow(10,e);return Math.round(t*n)/n}function V(t,e,n,r){return Math.min(Math.max(t/e,n),r)}const Hh=/[A-Z]/g;function ut(t){return t.replace(Hh,e=>String.fromCharCode(e.charCodeAt(0)+32))}function Xr(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(nt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,-2147483647,2147483647);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,-2147483647,2147483647);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}const Uh=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","xyz","xyz-d50","xyz-d65"]);function zh(t,e){const n=[],r=[],s=[],a=[];let o,i,c=!1,l=!1;const u={colorNotation:y.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([])};let f=n;for(let $=0;$<t.value.length;$++){let w=t.value[$];if(Ae(w)||Me(w))for(;Ae(t.value[$+1])||Me(t.value[$+1]);)$++;else if(f===n&&n.length&&(f=r),f===r&&r.length&&(f=s),T(w)&&as(w.value)&&w.value[4].value==="/"){if(f===a)return!1;f=a}else{if(le(w)){if(f===a&&ut(w.getName())==="var"){u.syntaxFlags.add(B.HasVariableAlpha),f.push(w);continue}if(!fs.has(ut(w.getName())))return!1;const[[N]]=Er([[w]],{censorIntoStandardRepresentableValues:!0,globals:i,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!N||!T(N)||!mt(N.value))return!1;Number.isNaN(N.value[4].value)&&(N.value[4].value=0),w=N}if(f===n&&n.length===0&&T(w)&&$t(w.value)&&Uh.has(ut(w.value[4].value))){if(c)return!1;c=ut(w.value[4].value),u.colorNotation=Gh(c),l&&(l.colorNotation!==u.colorNotation&&(l=vn(l,u.colorNotation)),o=ki(l),i=Fi(o))}else if(f===n&&n.length===0&&T(w)&&$t(w.value)&&ut(w.value[4].value)==="from"){if(l||c)return!1;for(;Ae(t.value[$+1])||Me(t.value[$+1]);)$++;if($++,w=t.value[$],l=e(w),l===!1)return!1;l.syntaxFlags.has(B.Experimental)&&u.syntaxFlags.add(B.Experimental),u.syntaxFlags.add(B.RelativeColorSyntax)}else{if(!T(w))return!1;if($t(w.value)&&o&&o.has(ut(w.value[4].value))){f.push(new G(o.get(ut(w.value[4].value))));continue}f.push(w)}}}if(!c||f.length!==1||n.length!==1||r.length!==1||s.length!==1||!T(n[0])||!T(r[0])||!T(s[0])||o&&!o.has("alpha"))return!1;const d=Xr(n[0].value,0,u);if(!d||!H(d))return!1;const b=Xr(r[0].value,1,u);if(!b||!H(b))return!1;const p=Xr(s[0].value,2,u);if(!p||!H(p))return!1;const m=[d,b,p];if(a.length===1)if(u.syntaxFlags.add(B.HasAlpha),T(a[0])){const $=Xr(a[0].value,3,u);if(!$||!H($))return!1;m.push($)}else u.alpha=a[0];else if(o&&o.has("alpha")){const $=Xr(o.get("alpha"),3,u);if(!$||!H($))return!1;m.push($)}return u.channels=[m[0][4].value,m[1][4].value,m[2][4].value],m.length===4&&(u.alpha=m[3][4].value),u}function Gh(t){switch(t){case"srgb":return y.sRGB;case"srgb-linear":return y.Linear_sRGB;case"display-p3":return y.Display_P3;case"a98-rgb":return y.A98_RGB;case"prophoto-rgb":return y.ProPhoto_RGB;case"rec2020":return y.Rec2020;case"xyz":case"xyz-d65":return y.XYZ_D65;case"xyz-d50":return y.XYZ_D50;default:throw new Error("Unknown color space name: "+t)}}const jh=new Set(["srgb","srgb-linear","display-p3","a98-rgb","prophoto-rgb","rec2020","lab","oklab","xyz","xyz-d50","xyz-d65"]),Ya=new Set(["hsl","hwb","lch","oklch"]),qh=new Set(["shorter","longer","increasing","decreasing"]);function Vh(t,e){let n=null,r=null,s=null,a=!1;for(let o=0;o<t.value.length;o++){const i=t.value[o];if(!Ze(i)){if(T(i)&&$t(i.value)){if(!n&&ut(i.value[4].value)==="in"){n=i;continue}if(n&&!r){r=ut(i.value[4].value);continue}if(n&&r&&!s&&Ya.has(r)){s=ut(i.value[4].value);continue}if(n&&r&&s&&!a&&ut(i.value[4].value)==="hue"){a=!0;continue}return!1}return!(!T(i)||!we(i.value))&&!!r&&(s||a?!!(r&&s&&a&&Ya.has(r)&&qh.has(s))&&xi(r,s,Za(t.value.slice(o+1),e)):jh.has(r)?Xh(r,Za(t.value.slice(o+1),e)):!!Ya.has(r)&&xi(r,"shorter",Za(t.value.slice(o+1),e)))}}return!1}function Za(t,e){const n=[];let r=1,s=!1,a=!1;for(let l=0;l<t.length;l++){let u=t[l];if(!Ze(u)){if(!T(u)||!we(u.value)){if(!s){const f=e(u);if(f){s=f;continue}}if(!a){if(le(u)&&fs.has(ut(u.getName()))){if([[u]]=Er([[u]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0}),!u||!T(u)||!mt(u.value))return!1;Number.isNaN(u.value[4].value)&&(u.value[4].value=0)}if(T(u)&&nt(u.value)&&u.value[4].value>=0){a=u.value[4].value;continue}}return!1}if(!s)return!1;n.push({color:s,percentage:a}),s=!1,a=!1}}s&&n.push({color:s,percentage:a});let o=0,i=0;for(let l=0;l<n.length;l++){const u=n[l].percentage;if(u!==!1){if(u<0||u>100)return!1;o+=u}else i++}const c=Math.max(0,100-o);o=0;for(let l=0;l<n.length;l++)n[l].percentage===!1&&(n[l].percentage=c/i),o+=n[l].percentage;if(o===0)return{colors:[{color:{channels:[0,0,0],colorNotation:y.sRGB,alpha:0,syntaxFlags:new Set},percentage:0}],alphaMultiplier:0};if(o>100)for(let l=0;l<n.length;l++){let u=n[l].percentage;u=u/o*100,n[l].percentage=u}if(o<100){r=o/100;for(let l=0;l<n.length;l++){let u=n[l].percentage;u=u/o*100,n[l].percentage=u}}return{colors:n,alphaMultiplier:r}}function Xh(t,e){var n;if(!e||!e.colors.length)return!1;const r=e.colors.slice();r.reverse();let s=y.RGB;switch(t){case"srgb":s=y.RGB;break;case"srgb-linear":s=y.Linear_sRGB;break;case"display-p3":s=y.Display_P3;break;case"a98-rgb":s=y.A98_RGB;break;case"prophoto-rgb":s=y.ProPhoto_RGB;break;case"rec2020":s=y.Rec2020;break;case"lab":s=y.Lab;break;case"oklab":s=y.OKLab;break;case"xyz-d50":s=y.XYZ_D50;break;case"xyz":case"xyz-d65":s=y.XYZ_D65;break;default:return!1}if(r.length===1){const o=vn(r[0].color,s);return o.colorNotation=s,o.syntaxFlags.add(B.ColorMixVariadic),typeof o.alpha!="number"?!1:(o.alpha=o.alpha*e.alphaMultiplier,o)}for(;r.length>=2;){const o=r.pop(),i=r.pop();if(!o||!i)return!1;const c=Kh(s,o.color,o.percentage,i.color,i.percentage);if(!c)return!1;r.push({color:c,percentage:o.percentage+i.percentage})}const a=(n=r[0])==null?void 0:n.color;return!!a&&(e.colors.some(o=>o.color.syntaxFlags.has(B.Experimental))&&a.syntaxFlags.add(B.Experimental),typeof a.alpha=="number"&&(a.alpha=a.alpha*e.alphaMultiplier,e.colors.length!==2&&a.syntaxFlags.add(B.ColorMixVariadic),a))}function Kh(t,e,n,r,s){const a=n/(n+s);let o=e.alpha;if(typeof o!="number")return!1;let i=r.alpha;if(typeof i!="number")return!1;o=Number.isNaN(o)?i:o,i=Number.isNaN(i)?o:i;const c=vn(e,t).channels,l=vn(r,t).channels;c[0]=be(c[0],l[0]),l[0]=be(l[0],c[0]),c[1]=be(c[1],l[1]),l[1]=be(l[1],c[1]),c[2]=be(c[2],l[2]),l[2]=be(l[2],c[2]),c[0]=Ge(c[0],o),c[1]=Ge(c[1],o),c[2]=Ge(c[2],o),l[0]=Ge(l[0],i),l[1]=Ge(l[1],i),l[2]=Ge(l[2],i);const u=Ee(o,i,a);return{colorNotation:t,channels:[An(Ee(c[0],l[0],a),u),An(Ee(c[1],l[1],a),u),An(Ee(c[2],l[2],a),u)],alpha:u,syntaxFlags:new Set([B.ColorMix])}}function xi(t,e,n){var r;if(!n||!n.colors.length)return!1;const s=n.colors.slice();s.reverse();let a=y.HSL;switch(t){case"hsl":a=y.HSL;break;case"hwb":a=y.HWB;break;case"lch":a=y.LCH;break;case"oklch":a=y.OKLCH;break;default:return!1}if(s.length===1){const i=vn(s[0].color,a);return i.colorNotation=a,i.syntaxFlags.add(B.ColorMixVariadic),typeof i.alpha!="number"?!1:(i.alpha=i.alpha*n.alphaMultiplier,i)}for(;s.length>=2;){const i=s.pop(),c=s.pop();if(!i||!c)return!1;const l=Yh(a,e,i.color,i.percentage,c.color,c.percentage);if(!l)return!1;s.push({color:l,percentage:i.percentage+c.percentage})}const o=(r=s[0])==null?void 0:r.color;return!!o&&(n.colors.some(i=>i.color.syntaxFlags.has(B.Experimental))&&o.syntaxFlags.add(B.Experimental),typeof o.alpha=="number"&&(o.alpha=o.alpha*n.alphaMultiplier,n.colors.length!==2&&o.syntaxFlags.add(B.ColorMixVariadic),o))}function Yh(t,e,n,r,s,a){const o=r/(r+a);let i=0,c=0,l=0,u=0,f=0,d=0,b=n.alpha;if(typeof b!="number")return!1;let p=s.alpha;if(typeof p!="number")return!1;b=Number.isNaN(b)?p:b,p=Number.isNaN(p)?b:p;const m=vn(n,t).channels,$=vn(s,t).channels;switch(t){case y.HSL:case y.HWB:i=m[0],c=$[0],l=m[1],u=$[1],f=m[2],d=$[2];break;case y.LCH:case y.OKLCH:l=m[0],u=$[0],f=m[1],d=$[1],i=m[2],c=$[2]}i=be(i,c),Number.isNaN(i)&&(i=0),c=be(c,i),Number.isNaN(c)&&(c=0),l=be(l,u),u=be(u,l),f=be(f,d),d=be(d,f);const w=c-i;switch(e){case"shorter":w>180?i+=360:w<-180&&(c+=360);break;case"longer":-180<w&&w<180&&(w>0?i+=360:c+=360);break;case"increasing":w<0&&(c+=360);break;case"decreasing":w>0&&(i+=360);break;default:throw new Error("Unknown hue interpolation method")}l=Ge(l,b),f=Ge(f,b),u=Ge(u,p),d=Ge(d,p);let N=[0,0,0];const C=Ee(b,p,o);switch(t){case y.HSL:case y.HWB:N=[Ee(i,c,o),An(Ee(l,u,o),C),An(Ee(f,d,o),C)];break;case y.LCH:case y.OKLCH:N=[An(Ee(l,u,o),C),An(Ee(f,d,o),C),Ee(i,c,o)]}return{colorNotation:t,channels:N,alpha:C,syntaxFlags:new Set([B.ColorMix])}}function be(t,e){return Number.isNaN(t)?e:t}function Ee(t,e,n){return t*n+e*(1-n)}function Ge(t,e){return Number.isNaN(e)?t:Number.isNaN(t)?Number.NaN:t*e}function An(t,e){return e===0||Number.isNaN(e)?t:Number.isNaN(t)?Number.NaN:t/e}function Zh(t){const e=ut(t[4].value);if(e.match(/[^a-f0-9]/))return!1;const n={colorNotation:y.HEX,channels:[0,0,0],alpha:1,syntaxFlags:new Set([B.Hex])},r=e.length;if(r===3){const s=e[0],a=e[1],o=e[2];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n}if(r===6){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n}if(r===4){const s=e[0],a=e[1],o=e[2],i=e[3];return n.channels=[parseInt(s+s,16)/255,parseInt(a+a,16)/255,parseInt(o+o,16)/255],n.alpha=parseInt(i+i,16)/255,n.syntaxFlags.add(B.HasAlpha),n}if(r===8){const s=e[0]+e[1],a=e[2]+e[3],o=e[4]+e[5],i=e[6]+e[7];return n.channels=[parseInt(s,16)/255,parseInt(a,16)/255,parseInt(o,16)/255],n.alpha=parseInt(i,16)/255,n.syntaxFlags.add(B.HasAlpha),n}return!1}function Kr(t){if(H(t))return t[4].value=t[4].value%360,t[1]=t[4].value.toString(),t;if(st(t)){let e=t[4].value;switch(ut(t[4].unit)){case"deg":break;case"rad":e=180*t[4].value/Math.PI;break;case"grad":e=.9*t[4].value;break;case"turn":e=360*t[4].value;break;default:return!1}return e%=360,[v.Number,e.toString(),t[2],t[3],{value:e,type:S.Number}]}return!1}function Jh(t,e,n){if(e===0){const r=Kr(t);return r!==!1&&(st(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(nt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){if(e!==3)return!1;let r=V(t[4].value,1,0,100);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function Qh(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(e===0){const r=Kr(t);return r!==!1&&(st(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(nt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);let r=t[4].value;return e===3?r=V(t[4].value,100,0,1):e===1&&(r=V(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=t[4].value;return e===3?r=V(t[4].value,1,0,1):e===1&&(r=V(t[4].value,1,0,2147483647)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function Si(t,e,n,r){const s=[],a=[],o=[],i=[],c={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let l=s;for(let p=0;p<t.value.length;p++){let m=t.value[p];if(!Ae(m)&&!Me(m)){if(T(m)&&we(m.value)){if(l===s){l=a;continue}if(l===a){l=o;continue}if(l===o){l=i;continue}if(l===i)return!1}if(le(m)){if(l===i&&m.getName().toLowerCase()==="var"){c.syntaxFlags.add(B.HasVariableAlpha),l.push(m);continue}if(!fs.has(m.getName().toLowerCase()))return!1;const[[$]]=Er([[m]],{censorIntoStandardRepresentableValues:!0,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!$||!T($)||!mt($.value))return!1;Number.isNaN($.value[4].value)&&($.value[4].value=0),m=$}if(!T(m))return!1;l.push(m)}}if(l.length!==1||s.length!==1||a.length!==1||o.length!==1||!T(s[0])||!T(a[0])||!T(o[0]))return!1;const u=e(s[0].value,0,c);if(!u||!H(u))return!1;const f=e(a[0].value,1,c);if(!f||!H(f))return!1;const d=e(o[0].value,2,c);if(!d||!H(d))return!1;const b=[u,f,d];if(i.length===1)if(c.syntaxFlags.add(B.HasAlpha),T(i[0])){const p=e(i[0].value,3,c);if(!p||!H(p))return!1;b.push(p)}else c.alpha=i[0];return c.channels=[b[0][4].value,b[1][4].value,b[2][4].value],b.length===4&&(c.alpha=b[3][4].value),c}function Mn(t,e,n,r,s){const a=[],o=[],i=[],c=[];let l,u,f=!1;const d={colorNotation:n,channels:[0,0,0],alpha:1,syntaxFlags:new Set(r)};let b=a;for(let N=0;N<t.value.length;N++){let C=t.value[N];if(Ae(C)||Me(C))for(;Ae(t.value[N+1])||Me(t.value[N+1]);)N++;else if(b===a&&a.length&&(b=o),b===o&&o.length&&(b=i),T(C)&&as(C.value)&&C.value[4].value==="/"){if(b===c)return!1;b=c}else{if(le(C)){if(b===c&&C.getName().toLowerCase()==="var"){d.syntaxFlags.add(B.HasVariableAlpha),b.push(C);continue}if(!fs.has(C.getName().toLowerCase()))return!1;const[[E]]=Er([[C]],{censorIntoStandardRepresentableValues:!0,globals:u,precision:-1,toCanonicalUnits:!0,rawPercentages:!0});if(!E||!T(E)||!mt(E.value))return!1;Number.isNaN(E.value[4].value)&&(E.value[4].value=0),C=E}if(b===a&&a.length===0&&T(C)&&$t(C.value)&&C.value[4].value.toLowerCase()==="from"){if(f)return!1;for(;Ae(t.value[N+1])||Me(t.value[N+1]);)N++;if(N++,C=t.value[N],f=s(C),f===!1)return!1;f.syntaxFlags.has(B.Experimental)&&d.syntaxFlags.add(B.Experimental),d.syntaxFlags.add(B.RelativeColorSyntax),f.colorNotation!==n&&(f=vn(f,n)),l=ki(f),u=Fi(l)}else{if(!T(C))return!1;if($t(C.value)&&l){const E=C.value[4].value.toLowerCase();if(l.has(E)){b.push(new G(l.get(E)));continue}}b.push(C)}}}if(b.length!==1||a.length!==1||o.length!==1||i.length!==1||!T(a[0])||!T(o[0])||!T(i[0])||l&&!l.has("alpha"))return!1;const p=e(a[0].value,0,d);if(!p||!H(p))return!1;const m=e(o[0].value,1,d);if(!m||!H(m))return!1;const $=e(i[0].value,2,d);if(!$||!H($))return!1;const w=[p,m,$];if(c.length===1)if(d.syntaxFlags.add(B.HasAlpha),T(c[0])){const N=e(c[0].value,3,d);if(!N||!H(N))return!1;w.push(N)}else d.alpha=c[0];else if(l&&l.has("alpha")){const N=e(l.get("alpha"),3,d);if(!N||!H(N))return!1;w.push(N)}return d.channels=[w[0][4].value,w[1][4].value,w[2][4].value],w.length===4&&(d.alpha=w[3][4].value),d}function t0(t,e){if(t.value.some(n=>T(n)&&we(n.value))){const n=e0(t);if(n!==!1)return n}{const n=n0(t,e);if(n!==!1)return n}return!1}function e0(t){return Si(t,Jh,y.HSL,[B.LegacyHSL])}function n0(t,e){return Mn(t,Qh,y.HSL,[],e)}function r0(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(e===0){const r=Kr(t);return r!==!1&&(st(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(nt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);let r=t[4].value;return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=t[4].value;return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function s0(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(nt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===1||e===2?r=V(t[4].value,.8,-2147483647,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,100);return e===1||e===2?r=V(t[4].value,1,-2147483647,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function a0(t,e){return Mn(t,s0,y.Lab,[],e)}function o0(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(e===2){const r=Kr(t);return r!==!1&&(st(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(nt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,1,0,100);return e===1?r=V(t[4].value,100/150,0,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,100);return e===1?r=V(t[4].value,1,0,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function i0(t,e){return Mn(t,o0,y.LCH,[],e)}const Ai=new Map;for(const[t,e]of Object.entries(Th))Ai.set(t,e);function l0(t){const e=Ai.get(ut(t));return!!e&&{colorNotation:y.RGB,channels:[e[0]/255,e[1]/255,e[2]/255],alpha:1,syntaxFlags:new Set([B.ColorKeyword,B.NamedColor])}}function c0(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(nt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,0,1);return e===1||e===2?r=V(t[4].value,250,-2147483647,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,1);return e===1||e===2?r=V(t[4].value,1,-2147483647,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function u0(t,e){return Mn(t,c0,y.OKLab,[],e)}function h0(t,e,n){if($t(t)&&ut(t[4].value)==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(e===2){const r=Kr(t);return r!==!1&&(st(t)&&n.syntaxFlags.add(B.HasDimensionValues),r)}if(nt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,0,1);return e===1?r=V(t[4].value,250,0,2147483647):e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,1,0,1);return e===1?r=V(t[4].value,1,0,2147483647):e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function f0(t,e){return Mn(t,h0,y.OKLCH,[],e)}function p0(t,e,n){if(nt(t)){e===3?n.syntaxFlags.add(B.HasPercentageAlpha):n.syntaxFlags.add(B.HasPercentageValues);const r=V(t[4].value,100,0,1);return[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,255,0,1);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function d0(t,e,n){if($t(t)&&t[4].value.toLowerCase()==="none")return n.syntaxFlags.add(B.HasNoneKeywords),[v.Number,"none",t[2],t[3],{value:Number.NaN,type:S.Number}];if(nt(t)){e!==3&&n.syntaxFlags.add(B.HasPercentageValues);let r=V(t[4].value,100,-2147483647,2147483647);return e===3&&(r=V(t[4].value,100,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}if(H(t)){e!==3&&n.syntaxFlags.add(B.HasNumberValues);let r=V(t[4].value,255,-2147483647,2147483647);return e===3&&(r=V(t[4].value,1,0,1)),[v.Number,r.toString(),t[2],t[3],{value:r,type:S.Number}]}return!1}function m0(t,e){if(t.value.some(n=>T(n)&&we(n.value))){const n=g0(t);if(n!==!1)return(!n.syntaxFlags.has(B.HasNumberValues)||!n.syntaxFlags.has(B.HasPercentageValues))&&n}else{const n=v0(t,e);if(n!==!1)return n}return!1}function g0(t){return Si(t,p0,y.RGB,[B.LegacyRGB])}function v0(t,e){return Mn(t,d0,y.RGB,[],e)}function b0(t){const e=Ka(t);if(Wh(e))return $i(e);let n=t;return n=wi(n),n[0]<1e-6&&(n=[0,0,0]),n[0]>.999999&&(n=[1,0,0]),jr(Ih(n,w0,$0))}function w0(t){return t=di(t),t=La(t),Gr(t)}function $0(t){return t=qr(t),t=_a(t),mi(t)}function y0(t,e){let n=!1;for(let o=0;o<t.value.length;o++){const i=t.value[o];if(!Ae(i)&&!Me(i)&&(n||(n=e(i),!n)))return!1}if(!n)return!1;n.channels=Rt(n.channels),n.channels=b0(Ei(n).channels),n.colorNotation=y.sRGB;const r={colorNotation:y.sRGB,channels:[0,0,0],alpha:1,syntaxFlags:new Set([B.ContrastColor,B.Experimental])},s=Ni(n.channels,[1,1,1]),a=Ni(n.channels,[0,0,0]);return r.channels=s>a?[1,1,1]:[0,0,0],r}function Ce(t){if(le(t))switch(ut(t.getName())){case"rgb":case"rgba":return m0(t,Ce);case"hsl":case"hsla":return t0(t,Ce);case"hwb":return e=Ce,Mn(t,r0,y.HWB,[],e);case"lab":return a0(t,Ce);case"lch":return i0(t,Ce);case"oklab":return u0(t,Ce);case"oklch":return f0(t,Ce);case"color":return zh(t,Ce);case"color-mix":return Vh(t,Ce);case"contrast-color":return y0(t,Ce)}var e;if(T(t)){if(Wl(t.value))return Zh(t.value);if($t(t.value)){const n=l0(t.value[4].value);return n!==!1?n:ut(t.value[4].value)==="transparent"&&{colorNotation:y.RGB,channels:[0,0,0],alpha:0,syntaxFlags:new Set([B.ColorKeyword])}}}return!1}const{CloseParen:Mi,Comment:Pi,Dimension:N0,EOF:Di,Function:Bi,Ident:E0,Number:C0,OpenParen:Ri,Percentage:k0,Whitespace:Oi}=v,{HasNoneKeywords:Ja}=B,Wi="relative-color",F0=8,ir=10,Qa=16,x0=100,to=255,Ii=new RegExp(`^${Br}(${Zn}|${ks})\\s+`),S0=/(?:hsla?|hwb)$/,A0=new RegExp(`^(?:${To}|${cu})$`),M0=new RegExp(Do),P0=new RegExp(Br),Li=new RegExp(`^${hu}`),Ti=new RegExp(`^${Br}`),D0=new RegExp(Pr);function _i(t,e={}){if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{colorSpace:n="",format:r=""}=e,s=new Map([["color",["r","g","b","alpha"]],["hsl",["h","s","l","alpha"]],["hsla",["h","s","l","alpha"]],["hwb",["h","w","b","alpha"]],["lab",["l","a","b","alpha"]],["lch",["l","c","h","alpha"]],["oklab",["l","a","b","alpha"]],["oklch",["l","c","h","alpha"]],["rgb",["r","g","b","alpha"]],["rgba",["r","g","b","alpha"]]]).get(n);if(!s)return new A;const a=new Set,o=[[],[],[],[]];let i=0,c=0,l=!1;for(;t.length;){const f=t.shift();if(!Array.isArray(f))throw new TypeError(`${f} is not an array.`);const[d,b,,,p]=f,m=o[i];if(Array.isArray(m))switch(d){case N0:{const $=Qi(f,e);x($)?m.push($):m.push(b);break}case Bi:{m.push(b),l=!0,c++,M0.test(b)&&a.add(c);break}case E0:{if(!s.includes(b))return new A;m.push(b),l||i++;break}case C0:{m.push(Number(p?.value)),l||i++;break}case Ri:{m.push(b),c++;break}case Mi:{l&&(m[m.length-1]===" "?m.splice(-1,1,b):m.push(b),a.has(c)&&a.delete(c),c--,c===0&&(l=!1,i++));break}case k0:{m.push(Number(p?.value)/x0),l||i++;break}case Oi:{if(m.length&&l){const $=m[m.length-1];(typeof $=="number"||x($)&&!$.endsWith("(")&&$!==" ")&&m.push(b)}break}default:d!==Pi&&d!==Di&&l&&m.push(b)}}const u=[];for(const f of o)if(f.length===1){const[d]=f;Kn(d)&&u.push(d)}else if(f.length){const d=Ji(f.join(""),{format:r});u.push(d)}return u}function B0(t,e={}){const{colorScheme:n="normal",currentColor:r="",format:s=""}=e;if(x(t)){if(t=t.toLowerCase().trim(),!t)return new A;if(!Ti.test(t))return t}else return new A;const a=bt({namespace:Wi,name:"extractOriginColor",value:t},e),o=vt(a);if(o instanceof it)return o.isNull?o:o.item;if(/currentcolor/.test(t))if(r)t=t.replace(/currentcolor/g,r);else return k(a,null),new A;let i="";if(Li.test(t)&&([,i]=t.match(Li)),e.colorSpace=i,t.includes(Es)){const c=t.replace(new RegExp(`^${i}\\(`),"").replace(/\)$/,""),[,l=""]=Dn(c),u=kt(l,{colorScheme:n,format:Q});if(u==="")return k(a,null),new A;if(s===Q)t=t.replace(l,u);else{const f=kt(u,e);x(f)&&(t=t.replace(l,f))}}if(Ii.test(t)){const[,c]=t.match(Ii),[,l]=t.split(c);if(/^[a-z]+$/.test(c)){if(!/^transparent$/.test(c)&&!Object.hasOwn(sr,c))return k(a,null),new A}else if(s===Q){const u=kt(c,e);x(u)&&(t=t.replace(c,u))}if(s===Q){const u=Ye({css:l}),f=_i(u,e);if(f instanceof A)return k(a,null),f;const[d,b,p,m]=f;let $="";Kn(m)?$=` ${d} ${b} ${p} / ${m})`:$=` ${f.join(" ")})`,l!==$&&(t=t.replace(l,$))}}else{const[,c]=t.split(Ti),l=Ye({css:c}),u=[];let f=0;for(;l.length;){const[C,E]=l.shift();switch(C){case Bi:case Ri:{u.push(E),f++;break}case Mi:{const J=u[u.length-1];J===" "?u.splice(-1,1,E):x(J)&&u.push(E),f--;break}case Oi:{const J=u[u.length-1];x(J)&&!J.endsWith("(")&&J!==" "&&u.push(E);break}default:C!==Pi&&C!==Di&&u.push(E)}if(f===0)break}const d=eo(u.join("").trim(),e);if(d instanceof A)return k(a,null),d;const b=_i(l,e);if(b instanceof A)return k(a,null),b;const[p,m,$,w]=b;let N="";Kn(w)?N=` ${p} ${m} ${$} / ${w})`:N=` ${b.join(" ")})`,t=t.replace(c,`${d}${N}`)}return k(a,t),t}function eo(t,e={}){const{format:n=""}=e;if(x(t)){if(D0.test(t)){if(n===Q)return t;throw new SyntaxError(`Unexpected token ${fa} found.`)}else if(!P0.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=bt({namespace:Wi,name:"resolveRelativeColor",value:t},e),s=vt(r);if(s instanceof it)return s.isNull?s:s.item;const a=B0(t,e);if(a instanceof A)return k(r,null),a;if(t=a,n===Q)return t.startsWith("rgba(")?t=t.replace(/^rgba\(/,"rgb("):t.startsWith("hsla(")&&(t=t.replace(/^hsla\(/,"hsl(")),t;const o=Ye({css:t}),i=zl(o),c=Ce(i);if(!c)return k(r,null),new A;const{alpha:l,channels:u,colorNotation:f,syntaxFlags:d}=c;let b;Number.isNaN(Number(l))?d instanceof Set&&d.has(Ja)?b=g:b=0:b=R(Number(l),F0);let p,m,$;[p,m,$]=u;let w;if(A0.test(f)){const N=d instanceof Set&&d.has(Ja);Number.isNaN(p)?N?p=g:p=0:p=R(p,Qa),Number.isNaN(m)?N?m=g:m=0:m=R(m,Qa),Number.isNaN($)?N?$=g:$=0:$=R($,Qa),b===1?w=`${f}(${p} ${m} ${$})`:w=`${f}(${p} ${m} ${$} / ${b})`}else if(S0.test(f)){Number.isNaN(p)&&(p=0),Number.isNaN(m)&&(m=0),Number.isNaN($)&&($=0);let[N,C,E]=Ur(`${f}(${p} ${m} ${$} / ${b})`);N=R(N/to,ir),C=R(C/to,ir),E=R(E/to,ir),b===1?w=`color(srgb ${N} ${C} ${E})`:w=`color(srgb ${N} ${C} ${E} / ${b})`}else{const N=f==="rgb"?"srgb":f,C=d instanceof Set&&d.has(Ja);Number.isNaN(p)?C?p=g:p=0:p=R(p,ir),Number.isNaN(m)?C?m=g:m=0:m=R(m,ir),Number.isNaN($)?C?$=g:$=0:$=R($,ir),b===1?w=`color(${N} ${p} ${m} ${$})`:w=`color(${N} ${p} ${m} ${$} / ${b})`}return k(r,w),w}const R0="resolve",Pn="rgba(0, 0, 0, 0)",O0=new RegExp($s),Hi=new RegExp(fu),W0=new RegExp(Uo),I0=new RegExp(Pr),kt=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{colorScheme:n="normal",currentColor:r="",format:s=at,nullable:a=!1}=e,o=bt({namespace:R0,name:"resolve",value:t},e),i=vt(o);if(i instanceof it)return i.isNull?i:i.item;if(I0.test(t)){if(s===Q)return k(o,t),t;const p=zr(t,e);if(p instanceof A)switch(s){case"hex":case"hexAlpha":return k(o,p),p;default:{if(a)return k(o,p),p;const m=Pn;return k(o,m),m}}else t=p}if(e.format!==s&&(e.format=s),t=t.toLowerCase(),Hi.test(t)&&t.endsWith(")")){const p=t.replace(Hi,"").replace(/\)$/,""),[m="",$=""]=Dn(p,{delimiter:","});if(m&&$){if(s===Q){const C=kt(m,e),E=kt($,e);let J;return C&&E?J=`light-dark(${C}, ${E})`:J="",k(o,J),J}let w;n==="dark"?w=kt($,e):w=kt(m,e);let N;return w instanceof A?a?N=w:N=Pn:N=w,k(o,N),N}switch(s){case Q:return k(o,""),"";case"hex":case"hexAlpha":return k(o,null),new A;case at:default:{const w=Pn;return k(o,w),w}}}if(W0.test(t)){const p=eo(t,e);if(s===at){let m;return p instanceof A?a?m=p:m=Pn:m=p,k(o,m),m}if(s===Q){let m="";return p instanceof A?m="":m=p,k(o,m),m}p instanceof A?t="":t=p}O0.test(t)&&(t=Yr(t,e));let c="",l=NaN,u=NaN,f=NaN,d=NaN;if(t==="transparent")switch(s){case Q:return k(o,t),t;case"hex":return k(o,null),new A;case"hexAlpha":{const p="#00000000";return k(o,p),p}case at:default:{const p=Pn;return k(o,p),p}}else if(t==="currentcolor"){if(s===Q)return k(o,t),t;if(r){let p;if(r.startsWith(gn)?p=Rs(r,e):r.startsWith(ht)?p=tn(r,e):p=Sn(r,e),p instanceof A)return k(o,p),p;[c,l,u,f,d]=p}else if(s===at){const p=Pn;return k(o,p),p}}else if(s===Q)if(t.startsWith(gn)){const p=Rs(t,e);return k(o,p),p}else if(t.startsWith(ht)){const[p,m,$,w,N]=tn(t,e);let C="";return N===1?C=`color(${p} ${m} ${$} ${w})`:C=`color(${p} ${m} ${$} ${w} / ${N})`,k(o,C),C}else{const p=Sn(t,e);if(x(p))return k(o,p),p;const[m,$,w,N,C]=p;let E="";return m==="rgb"?C===1?E=`${m}(${$}, ${w}, ${N})`:E=`${m}a(${$}, ${w}, ${N}, ${C})`:C===1?E=`${m}(${$} ${w} ${N})`:E=`${m}(${$} ${w} ${N} / ${C})`,k(o,E),E}else if(t.startsWith(gn)){/currentcolor/.test(t)&&r&&(t=t.replace(/currentcolor/g,r)),/transparent/.test(t)&&(t=t.replace(/transparent/g,Pn));const p=Rs(t,e);if(p instanceof A)return k(o,p),p;[c,l,u,f,d]=p}else if(t.startsWith(ht)){const p=tn(t,e);if(p instanceof A)return k(o,p),p;[c,l,u,f,d]=p}else if(t){const p=Sn(t,e);if(p instanceof A)return k(o,p),p;[c,l,u,f,d]=p}let b="";switch(s){case"hex":{if(Number.isNaN(l)||Number.isNaN(u)||Number.isNaN(f)||Number.isNaN(d)||d===0)return k(o,null),new A;b=ai([l,u,f,1]);break}case"hexAlpha":{if(Number.isNaN(l)||Number.isNaN(u)||Number.isNaN(f)||Number.isNaN(d))return k(o,null),new A;b=ai([l,u,f,d]);break}case at:default:switch(c){case"rgb":{d===1?b=`${c}(${l}, ${u}, ${f})`:b=`${c}a(${l}, ${u}, ${f}, ${d})`;break}case"lab":case"lch":case"oklab":case"oklch":{d===1?b=`${c}(${l} ${u} ${f})`:b=`${c}(${l} ${u} ${f} / ${d})`;break}default:d===1?b=`color(${c} ${l} ${u} ${f})`:b=`color(${c} ${l} ${u} ${f} / ${d})`}}return k(o,b),b},L0=(t,e={})=>{e.nullable=!1;const n=kt(t,e);return n instanceof A?null:n},{CloseParen:T0,Comma:_0,Comment:H0,Delim:U0,EOF:z0,Function:G0,Ident:j0,OpenParen:q0,Whitespace:V0}=v,Ui="util",X0=10,Ws=16,lr=360,Is=180,K0=new RegExp(`^(?:${Zn})$`),Y0=/^(?:(?:ok)?l(?:ab|ch)|color(?:-mix)?|hsla?|hwb|rgba?|var)\(/,Z0=new RegExp(ks),Dn=(t,e={})=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const{delimiter:n=" ",preserveComment:r=!1}=e,s=bt({namespace:Ui,name:"splitValue",value:t},{delimiter:n,preserveComment:r}),a=vt(s);if(a instanceof it)return a.item;let o;n===","?o=/^,$/:n==="/"?o=/^\/$/:o=/^\s+$/;const i=Ye({css:t});let c=0,l="";const u=[];for(;i.length;){const[f,d]=i.shift();switch(f){case _0:{o.test(d)&&c===0?(u.push(l.trim()),l=""):l+=d;break}case U0:{o.test(d)&&c===0?(u.push(l.trim()),l=""):l+=d;break}case H0:{r&&(n===","||n==="/")&&(l+=d);break}case G0:case q0:{l+=d,c++;break}case T0:{l+=d,c--;break}case V0:{o.test(d)?c===0?l&&(u.push(l.trim()),l=""):l+=" ":l.endsWith(" ")||(l+=" ");break}default:f===z0?(u.push(l.trim()),l=""):l+=d}}return k(s,u),u},J0=t=>{if(x(t))t=t.trim();else throw new TypeError(`${t} is not a string.`);const e=bt({namespace:Ui,name:"extractDashedIdent",value:t}),n=vt(e);if(n instanceof it)return n.item;const r=Ye({css:t}),s=new Set;for(;r.length;){const[o,i]=r.shift();o===j0&&i.startsWith("--")&&s.add(i)}const a=[...s];return k(e,a),a},bn=(t,e={})=>{if(x(t)&&(t=t.toLowerCase().trim(),t&&x(t))){if(/^[a-z]+$/.test(t)){if(/^(?:currentcolor|transparent)$/.test(t)||Object.hasOwn(sr,t))return!0}else if(K0.test(t)||Z0.test(t)||Y0.test(t)&&(e.nullable=!0,e.format||(e.format=Q),kt(t,e)))return!0}return!1},zi=(t,e=!1)=>typeof t>"u"?"":JSON.stringify(t,(n,r)=>{let s;return typeof r>"u"?s=null:typeof r=="function"?e?s=r.toString().replace(/\s/g,"").substring(0,Ws):s=r.name:r instanceof Map||r instanceof Set?s=[...r]:typeof r=="bigint"?s=r.toString():s=r,s}),R=(t,e=0)=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);if(Number.isFinite(e)){if(e<0||e>Ws)throw new RangeError(`${e} is not between 0 and ${Ws}.`)}else throw new TypeError(`${e} is not a finite number.`);if(e===0)return Math.round(t);let n;return e===Ws?n=t.toPrecision(6):e<X0?n=t.toPrecision(4):n=t.toPrecision(5),parseFloat(n)},Gi=(t,e,n="shorter")=>{if(!Number.isFinite(t))throw new TypeError(`${t} is not a finite number.`);if(!Number.isFinite(e))throw new TypeError(`${e} is not a finite number.`);switch(n){case"decreasing":{e>t&&(t+=lr);break}case"increasing":{e<t&&(e+=lr);break}case"longer":{e>t&&e<t+Is?t+=lr:e>t+Is*-1&&e<=t&&(e+=lr);break}case"shorter":default:e>t+Is?t+=lr:e<t+Is*-1&&(e+=lr)}return[t,e]},Q0=4096;var Ls,Ts;class it{constructor(e,n=!1){U(this,Ls),U(this,Ts),F(this,Ts,e),F(this,Ls,!!n)}get item(){return h(this,Ts)}get isNull(){return h(this,Ls)}}Ls=new WeakMap,Ts=new WeakMap;class A extends it{constructor(){super(Symbol("null"),!0)}}const cr=new Jc({max:Q0}),k=(t,e)=>{t&&(e===null?cr.set(t,new A):e instanceof it?cr.set(t,e):cr.set(t,new it(e)))},vt=t=>{if(t&&cr.has(t)){const e=cr.get(t);return e instanceof it?e:(cr.delete(t),!1)}return!1},bt=(t,e={})=>{const{customProperty:n={},dimension:r={}}=e;let s="";return t&&Object.keys(t).length&&typeof n.callback!="function"&&typeof r.callback!="function"&&(t.opt=zi(e),s=zi(t)),s},{CloseParen:t1,Comment:ji,Dimension:e1,EOF:n1,Function:r1,OpenParen:s1,Whitespace:qi}=v,Vi="css-calc",a1=3,_s=16,Xi=100,o1=new RegExp($s),Ki=new RegExp(`^calc\\((${zt})\\)$`),i1=new RegExp(Do),l1=new RegExp(Pr),Hs=new RegExp(au),Yi=/\s[*+/-]\s/,Us=new RegExp(`^(${zt})(${Yn}|${Mr})$`),ur=new RegExp(`^(${zt})(${Yn}|${Mr}|%)$`),wn=new RegExp(`^(${zt})%$`);var $n,hr,fr,en,pr,dr,je,Bn,Rn,nn,rn,yn,Nn,sn,On,Wn;class c1{constructor(){U(this,$n),U(this,hr),U(this,fr),U(this,en),U(this,pr),U(this,dr),U(this,je),U(this,Bn),U(this,Rn),U(this,nn),U(this,rn),U(this,yn),U(this,Nn),U(this,sn),U(this,On),U(this,Wn),F(this,$n,!1),F(this,hr,[]),F(this,fr,[]),F(this,en,!1),F(this,pr,[]),F(this,dr,[]),F(this,je,!1),F(this,Bn,[]),F(this,Rn,[]),F(this,nn,[]),F(this,rn,[]),F(this,yn,!1),F(this,Nn,[]),F(this,sn,[]),F(this,On,[]),F(this,Wn,[])}get hasNum(){return h(this,$n)}set hasNum(e){F(this,$n,!!e)}get numSum(){return h(this,hr)}get numMul(){return h(this,fr)}get hasPct(){return h(this,en)}set hasPct(e){F(this,en,!!e)}get pctSum(){return h(this,pr)}get pctMul(){return h(this,dr)}get hasDim(){return h(this,je)}set hasDim(e){F(this,je,!!e)}get dimSum(){return h(this,Bn)}get dimSub(){return h(this,Rn)}get dimMul(){return h(this,nn)}get dimDiv(){return h(this,rn)}get hasEtc(){return h(this,yn)}set hasEtc(e){F(this,yn,!!e)}get etcSum(){return h(this,Nn)}get etcSub(){return h(this,sn)}get etcMul(){return h(this,On)}get etcDiv(){return h(this,Wn)}clear(){F(this,$n,!1),F(this,hr,[]),F(this,fr,[]),F(this,en,!1),F(this,pr,[]),F(this,dr,[]),F(this,je,!1),F(this,Bn,[]),F(this,Rn,[]),F(this,nn,[]),F(this,rn,[]),F(this,yn,!1),F(this,Nn,[]),F(this,sn,[]),F(this,On,[]),F(this,Wn,[])}sort(e=[]){const n=[...e];return n.length>1&&n.sort((r,s)=>{let a;if(ur.test(r)&&ur.test(s)){const[,o,i]=r.match(ur),[,c,l]=s.match(ur);i===l?Number(o)===Number(c)?a=0:Number(o)>Number(c)?a=1:a=-1:i>l?a=1:a=-1}else r===s?a=0:r>s?a=1:a=-1;return a}),n}multiply(){const e=[];let n;if(h(this,$n)){n=1;for(const r of h(this,fr))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;!h(this,en)&&!h(this,je)&&!this.hasEtc&&(Number.isFinite(n)&&(n=R(n,_s)),e.push(n))}if(h(this,en)){typeof n!="number"&&(n=1);for(const r of h(this,dr))if(n*=r,n===0||!Number.isFinite(n)||Number.isNaN(n))break;Number.isFinite(n)&&(n=`${R(n,_s)}%`),!h(this,je)&&!this.hasEtc&&e.push(n)}if(h(this,je)){let r="",s="",a="";h(this,nn).length&&(h(this,nn).length===1?[s]=h(this,nn):s=`${this.sort(h(this,nn)).join(" * ")}`),h(this,rn).length&&(h(this,rn).length===1?[a]=h(this,rn):a=`${this.sort(h(this,rn)).join(" * ")}`),Number.isFinite(n)?(s?a?a.includes("*")?r=Ht(`calc(${n} * ${s} / (${a}))`,{toCanonicalUnits:!0}):r=Ht(`calc(${n} * ${s} / ${a})`,{toCanonicalUnits:!0}):r=Ht(`calc(${n} * ${s})`,{toCanonicalUnits:!0}):a.includes("*")?r=Ht(`calc(${n} / (${a}))`,{toCanonicalUnits:!0}):r=Ht(`calc(${n} / ${a})`,{toCanonicalUnits:!0}),e.push(r.replace(/^calc/,""))):(!e.length&&n!==void 0&&e.push(n),s?(a?a.includes("*")?r=Ht(`calc(${s} / (${a}))`,{toCanonicalUnits:!0}):r=Ht(`calc(${s} / ${a})`,{toCanonicalUnits:!0}):r=Ht(`calc(${s})`,{toCanonicalUnits:!0}),e.length?e.push("*",r.replace(/^calc/,"")):e.push(r.replace(/^calc/,""))):(r=Ht(`calc(${a})`,{toCanonicalUnits:!0}),e.length?e.push("/",r.replace(/^calc/,"")):e.push("1","/",r.replace(/^calc/,""))))}if(h(this,yn)){if(h(this,On).length){!e.length&&n!==void 0&&e.push(n);const r=this.sort(h(this,On)).join(" * ");e.length?e.push(`* ${r}`):e.push(`${r}`)}if(h(this,Wn).length){const r=this.sort(h(this,Wn)).join(" * ");r.includes("*")?e.length?e.push(`/ (${r})`):e.push(`1 / (${r})`):e.length?e.push(`/ ${r}`):e.push(`1 / ${r}`)}}return e.length?e.join(" "):""}sum(){const e=[];if(h(this,$n)){let n=0;for(const r of h(this,hr))if(n+=r,!Number.isFinite(n)||Number.isNaN(n))break;e.push(n)}if(h(this,en)){let n=0;for(const r of h(this,pr))if(n+=r,!Number.isFinite(n))break;Number.isFinite(n)&&(n=`${n}%`),e.length?e.push(`+ ${n}`):e.push(n)}if(h(this,je)){let n,r,s;h(this,Bn).length&&(r=this.sort(h(this,Bn)).join(" + ")),h(this,Rn).length&&(s=this.sort(h(this,Rn)).join(" + ")),r?s?s.includes("-")?n=Ht(`calc(${r} - (${s}))`,{toCanonicalUnits:!0}):n=Ht(`calc(${r} - ${s})`,{toCanonicalUnits:!0}):n=Ht(`calc(${r})`,{toCanonicalUnits:!0}):n=Ht(`calc(-1 * (${s}))`,{toCanonicalUnits:!0}),e.length?e.push("+",n.replace(/^calc/,"")):e.push(n.replace(/^calc/,""))}if(h(this,yn)){if(h(this,Nn).length){const n=this.sort(h(this,Nn)).map(r=>{let s;return Yi.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?h(this,Nn).length>1?e.push(`+ (${n})`):e.push(`+ ${n}`):e.push(`${n}`)}if(h(this,sn).length){const n=this.sort(h(this,sn)).map(r=>{let s;return Yi.test(r)&&!r.startsWith("(")&&!r.endsWith(")")?s=`(${r})`:s=r,s}).join(" + ");e.length?h(this,sn).length>1?e.push(`- (${n})`):e.push(`- ${n}`):h(this,sn).length>1?e.push(`-1 * (${n})`):e.push(`-1 * ${n}`)}}return e.length?e.join(" "):""}}$n=new WeakMap,hr=new WeakMap,fr=new WeakMap,en=new WeakMap,pr=new WeakMap,dr=new WeakMap,je=new WeakMap,Bn=new WeakMap,Rn=new WeakMap,nn=new WeakMap,rn=new WeakMap,yn=new WeakMap,Nn=new WeakMap,sn=new WeakMap,On=new WeakMap,Wn=new WeakMap;const Zi=(t=[],e=!1)=>{if(t.length<a1)throw new Error(`Unexpected array length ${t.length}.`);const n=t.shift();if(!x(n)||!n.endsWith("("))throw new Error(`Unexpected token ${n}.`);const r=t.pop();if(r!==")")throw new Error(`Unexpected token ${r}.`);if(t.length===1){const[l]=t;if(!Kn(l))throw new Error(`Unexpected token ${l}.`);return`${n}${l}${r}`}const s=[],a=new c1;let o="";const i=t.length;for(let l=0;l<i;l++){const u=t[l];if(!Kn(u))throw new Error(`Unexpected token ${u}.`);if(u==="*"||u==="/")o=u;else if(u==="+"||u==="-"){const f=a.multiply();f&&s.push(f,u),a.clear(),o=""}else{const f=Number(u),d=`${u}`;switch(o){case"/":{if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(1/f);else if(wn.test(d)){const[,b]=d.match(wn);a.hasPct=!0,a.pctMul.push(Xi*Xi/Number(b))}else Us.test(d)?(a.hasDim=!0,a.dimDiv.push(d)):(a.hasEtc=!0,a.etcDiv.push(d));break}case"*":default:if(Number.isFinite(f))a.hasNum=!0,a.numMul.push(f);else if(wn.test(d)){const[,b]=d.match(wn);a.hasPct=!0,a.pctMul.push(Number(b))}else Us.test(d)?(a.hasDim=!0,a.dimMul.push(d)):(a.hasEtc=!0,a.etcMul.push(d))}}if(l===i-1){const f=a.multiply();f&&s.push(f),a.clear(),o=""}}let c="";if(e&&(s.includes("+")||s.includes("-"))){const l=[];a.clear(),o="";const u=s.length;for(let f=0;f<u;f++){const d=s[f];if(Kn(d))if(d==="+"||d==="-")o=d;else{const b=Number(d),p=`${d}`;switch(o){case"-":{if(Number.isFinite(b))a.hasNum=!0,a.numSum.push(-1*b);else if(wn.test(p)){const[,m]=p.match(wn);a.hasPct=!0,a.pctSum.push(-1*Number(m))}else Us.test(p)?(a.hasDim=!0,a.dimSub.push(p)):(a.hasEtc=!0,a.etcSub.push(p));break}case"+":default:if(Number.isFinite(b))a.hasNum=!0,a.numSum.push(b);else if(wn.test(p)){const[,m]=p.match(wn);a.hasPct=!0,a.pctSum.push(Number(m))}else Us.test(p)?(a.hasDim=!0,a.dimSum.push(p)):(a.hasEtc=!0,a.etcSum.push(p))}}if(f===u-1){const b=a.sum();b&&l.push(b),a.clear(),o=""}}c=l.join(" ").replace(/\+\s-/g,"- ")}else c=s.join(" ").replace(/\+\s-/g,"- ");return c.startsWith("(")&&c.endsWith(")")&&c.lastIndexOf("(")===0&&c.indexOf(")")===c.length-1&&(c=c.replace(/^\(/,"").replace(/\)$/,"")),`${n}${c}${r}`},Ji=(t,e={})=>{const{format:n=""}=e;if(x(t)){if(!Hs.test(t)||n!==Q)return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=bt({namespace:Vi,name:"serializeCalc",value:t},e),s=vt(r);if(s instanceof it)return s.item;const a=Ye({css:t}).map(c=>{const[l,u]=c;let f="";return l!==qi&&l!==ji&&(f=u),f}).filter(c=>c);let o=a.findLastIndex(c=>/\($/.test(c));for(;o;){const c=a.findIndex((f,d)=>f===")"&&d>o),l=a.slice(o,c+1);let u=Zi(l);Hs.test(u)&&(u=Ht(u,{toCanonicalUnits:!0})),a.splice(o,c-o+1,u),o=a.findLastIndex(f=>/\($/.test(f))}const i=Zi(a,!0);return k(r,i),i},Qi=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const[,,,,n={}]=t,{unit:r,value:s}=n,{dimension:a={}}=e;if(r==="px")return`${s}${r}`;const o=Number(s);if(r&&Number.isFinite(o)){let i;if(Object.hasOwn(a,r)?i=a[r]:typeof a.callback=="function"&&(i=a.callback(r)),i=Number(i),Number.isFinite(i))return`${o*i}px`}return new A},u1=(t,e={})=>{if(!Array.isArray(t))throw new TypeError(`${t} is not an array.`);const{format:n=""}=e,r=new Set;let s=0;const a=[];for(;t.length;){const o=t.shift();if(!Array.isArray(o))throw new TypeError(`${o} is not an array.`);const[i="",c=""]=o;switch(i){case e1:{if(n===Q&&!r.has(s))a.push(c);else{const l=Qi(o,e);x(l)?a.push(l):a.push(c)}break}case r1:case s1:{a.push(c),s++,i1.test(c)&&r.add(s);break}case t1:{a.length&&a[a.length-1]===" "?a.splice(-1,1,c):a.push(c),r.has(s)&&r.delete(s),s--;break}case qi:{if(a.length){const l=a[a.length-1];x(l)&&!l.endsWith("(")&&l!==" "&&a.push(c)}break}default:i!==ji&&i!==n1&&a.push(c)}}return a},Yr=(t,e={})=>{const{format:n=""}=e;if(x(t)){if(l1.test(t)){if(n===Q)return t;{const c=zr(t,e);return x(c)?c:""}}else if(!o1.test(t))return t;t=t.toLowerCase().trim()}else throw new TypeError(`${t} is not a string.`);const r=bt({namespace:Vi,name:"cssCalc",value:t},e),s=vt(r);if(s instanceof it)return s.item;const a=Ye({css:t}),o=u1(a,e);let i=Ht(o.join(""),{toCanonicalUnits:!0});if(Hs.test(t)){if(ur.test(i)){const[,c,l]=i.match(ur);i=`${R(Number(c),_s)}${l}`}i&&!Hs.test(i)&&n===Q&&(i=`calc(${i})`)}if(n===Q){if(/\s[-+*/]\s/.test(i)&&!i.includes("NaN"))i=Ji(i,e);else if(Ki.test(i)){const[,c]=i.match(Ki);i=`calc(${R(Number(c),_s)})`}}return k(r,i),i},h1="css-gradient",zs=`${zt}(?:${Yn})`,tl=`${zs}|${Gt}`,f1=`${zt}(?:${Mr})|0`,jt=`${f1}|${Gt}`,el=`${Po}(?:${Mr}|%)|0`,p1=`${Po}(?:${Mr})|0`,se="center",no="left|right",ro="top|bottom",En="start|end",mr=`${no}|x-(?:${En})`,gr=`${ro}|y-(?:${En})`,Zr=`block-(?:${En})`,Jr=`inline-(?:${En})`,d1=`${se}|${mr}|${gr}|${Zr}|${Jr}|${jt}`,m1=[`(?:${se}|${mr})\\s+(?:${se}|${gr})`,`(?:${se}|${gr})\\s+(?:${se}|${mr})`,`(?:${se}|${mr}|${jt})\\s+(?:${se}|${gr}|${jt})`,`(?:${se}|${Zr})\\s+(?:${se}|${Jr})`,`(?:${se}|${Jr})\\s+(?:${se}|${Zr})`,`(?:${se}|${En})\\s+(?:${se}|${En})`].join("|"),g1=[`(?:${mr})\\s+(?:${jt})\\s+(?:${gr})\\s+(?:${jt})`,`(?:${gr})\\s+(?:${jt})\\s+(?:${mr})\\s+(?:${jt})`,`(?:${Zr})\\s+(?:${jt})\\s+(?:${Jr})\\s+(?:${jt})`,`(?:${Jr})\\s+(?:${jt})\\s+(?:${Zr})\\s+(?:${jt})`,`(?:${En})\\s+(?:${jt})\\s+(?:${En})\\s+(?:${jt})`].join("|"),nl="(?:clos|farth)est-(?:corner|side)",Gs=[`${nl}(?:\\s+${nl})?`,`${p1}`,`(?:${el})\\s+(?:${el})`].join("|"),js="circle|ellipse",rl=`from\\s+${zs}`,an=`at\\s+(?:${d1}|${m1}|${g1})`,sl=`to\\s+(?:(?:${no})(?:\\s(?:${ro}))?|(?:${ro})(?:\\s(?:${no}))?)`,ke=`in\\s+(?:${_o}|${Lo})`,al=/^(?:repeating-)?(?:conic|linear|radial)-gradient\(/,v1=/^((?:repeating-)?(?:conic|linear|radial)-gradient)\(/,b1=t=>{if(x(t)&&(t=t.trim(),al.test(t))){const[,e]=t.match(v1);return e}return""},w1=(t,e)=>{if(x(t)&&x(e)){t=t.trim(),e=e.trim();let n="";const r=[];if(/^(?:repeating-)?linear-gradient$/.test(e)?(n=[`(?:${zs}|${sl})(?:\\s+${ke})?`,`${ke}(?:\\s+(?:${zs}|${sl}))?`].join("|"),r.push(/to\s+bottom/)):/^(?:repeating-)?radial-gradient$/.test(e)?(n=[`(?:${js})(?:\\s+(?:${Gs}))?(?:\\s+${an})?(?:\\s+${ke})?`,`(?:${Gs})(?:\\s+(?:${js}))?(?:\\s+${an})?(?:\\s+${ke})?`,`${an}(?:\\s+${ke})?`,`${ke}(?:\\s+${js})(?:\\s+(?:${Gs}))?(?:\\s+${an})?`,`${ke}(?:\\s+${Gs})(?:\\s+(?:${js}))?(?:\\s+${an})?`,`${ke}(?:\\s+${an})?`].join("|"),r.push(/ellipse/,/farthest-corner/,/at\s+center/)):/^(?:repeating-)?conic-gradient$/.test(e)&&(n=[`${rl}(?:\\s+${an})?(?:\\s+${ke})?`,`${an}(?:\\s+${ke})?`,`${ke}(?:\\s+${rl})?(?:\\s+${an})?`].join("|"),r.push(/at\s+center/)),n){const s=new RegExp(`^(?:${n})$`).test(t);if(s){let a=t;for(const o of r)a=a.replace(o,"");return a=a.replace(/\s{2,}/g," ").trim(),{line:a,valid:s}}return{valid:s,line:t}}}return{line:t,valid:!1}},ol=(t,e,n={})=>{if(Array.isArray(t)&&t.length>1){const r=/^(?:repeating-)?conic-gradient$/.test(e)?tl:jt,s=new RegExp(`^(?:${r})$`),a=new RegExp(`(?:\\s+(?:${r})){1,2}$`),o=[],i=[];for(const c of t)if(x(c))if(s.test(c))o.push("hint"),i.push(c);else{const l=c.replace(a,"");if(bn(l,{format:Q})){const u=kt(l,n);o.push("color"),i.push(c.replace(l,u))}else return{colorStops:t,valid:!1}}return{valid:/^color(?:,(?:hint,)?color)+$/.test(o.join(",")),colorStops:i}}return{colorStops:t,valid:!1}},il=(t,e={})=>{if(x(t)){t=t.trim();const n=bt({namespace:h1,name:"parseGradient",value:t},e),r=vt(n);if(r instanceof it)return r.isNull?null:r.item;const s=b1(t),a=t.replace(al,"").replace(/\)$/,"");if(s&&a){const[o="",...i]=Dn(a,{delimiter:","}),c=/^(?:repeating-)?conic-gradient$/.test(s)?tl:jt,l=new RegExp(`(?:\\s+(?:${c})){1,2}$`);let u="";if(l.test(o)){const f=o.replace(l,"");if(bn(f,{format:Q})){const d=kt(f,e);u=o.replace(f,d)}}else bn(o,{format:Q})&&(u=kt(o,e));if(u){i.unshift(u);const{colorStops:f,valid:d}=ol(i,s,e);if(d){const b={value:t,type:s,colorStopList:f};return k(n,b),b}}else if(i.length>1){const{line:f,valid:d}=w1(o,s),{colorStops:b,valid:p}=ol(i,s,e);if(d&&p){const m={value:t,type:s,gradientLine:f,colorStopList:b};return k(n,m),m}}}return k(n,null),null}return null},$1=(t,e={})=>{const{format:n=at}=e,r=il(t,e);if(r){const{type:s="",gradientLine:a="",colorStopList:o=[]}=r;if(s&&Array.isArray(o)&&o.length>1)return a?`${s}(${a}, ${o.join(", ")})`:`${s}(${o.join(", ")})`}return n===Q?"":"none"},y1=(t,e={})=>il(t,e)!==null,qe="convert",N1=new RegExp($s),E1=new RegExp(Uo),C1=new RegExp(Pr),on=(t,e={})=>{if(x(t)){if(t=t.trim(),!t)return new A}else return new A;const n=bt({namespace:qe,name:"preProcess",value:t},e),r=vt(n);if(r instanceof it)return r.isNull?r:r.item;if(C1.test(t)){const s=zr(t,e);if(x(s))t=s;else return k(n,null),new A}if(E1.test(t)){const s=eo(t,e);if(x(s))t=s;else return k(n,null),new A}else N1.test(t)&&(t=Yr(t,e));if(t.startsWith("color-mix")){const s=structuredClone(e);s.format=at,s.nullable=!0;const a=kt(t,s);return k(n,a),a}return k(n,t),t},k1=t=>_r(t),F1=(t,e={})=>{if(x(t)){const o=on(t,e);if(o instanceof A)return null;t=o.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const{alpha:n=!1}=e,r=bt({namespace:qe,name:"colorToHex",value:t},e),s=vt(r);if(s instanceof it)return s.isNull?null:s.item;let a;return e.nullable=!0,n?(e.format="hexAlpha",a=kt(t,e)):(e.format="hex",a=kt(t,e)),x(a)?(k(r,a),a):(k(r,null),null)},x1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToHsl",value:t},e),r=vt(n);if(r instanceof it)return r.item;e.format="hsl";const s=Pa(t,e);return k(n,s),s},S1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToHwb",value:t},e),r=vt(n);if(r instanceof it)return r.item;e.format="hwb";const s=Da(t,e);return k(n,s),s},A1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToLab",value:t},e),r=vt(n);if(r instanceof it)return r.item;const s=Ba(t,e);return k(n,s),s},M1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToLch",value:t},e),r=vt(n);if(r instanceof it)return r.item;const s=Ra(t,e);return k(n,s),s},P1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToOklab",value:t},e),r=vt(n);if(r instanceof it)return r.item;const s=Oa(t,e);return k(n,s),s},D1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToOklch",value:t},e),r=vt(n);if(r instanceof it)return r.item;const s=Wa(t,e);return k(n,s),s},B1=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToRgb",value:t},e),r=vt(n);if(r instanceof it)return r.item;const s=Ur(t,e);return k(n,s),s},ll=(t,e={})=>{if(x(t)){const a=on(t,e);if(a instanceof A)return[0,0,0,0];t=a.toLowerCase()}else throw new TypeError(`${t} is not a string.`);const n=bt({namespace:qe,name:"colorToXyz",value:t},e),r=vt(n);if(r instanceof it)return r.item;let s;return t.startsWith("color(")?[,...s]=wt(t,e):[,...s]=Ct(t,e),k(n,s),s},R1=(t,e={})=>(e.d50=!0,ll(t,e)),O1={colorToHex:F1,colorToHsl:x1,colorToHwb:S1,colorToLab:A1,colorToLch:M1,colorToOklab:P1,colorToOklch:D1,colorToRgb:B1,colorToXyz:ll,colorToXyzD50:R1,numberToHex:k1};/*!
 * CSS color - Resolve, parse, convert CSS color.
 * @license MIT
 * @copyright asamuzaK (Kazz)
 * @see {@link https://github.com/asamuzaK/cssColor/blob/main/LICENSE}
 */const W1={cssCalc:Yr,cssVar:Ru,extractDashedIdent:J0,isColor:bn,isGradient:y1,resolveGradient:$1,splitValue:Dn};export{O1 as convert,L0 as resolve,W1 as utils};

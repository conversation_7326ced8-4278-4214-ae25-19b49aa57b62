{"version": 3, "file": "css-calc.js", "sources": ["../../../src/js/css-calc.ts"], "sourcesContent": ["/**\n * css-calc\n */\n\nimport { calc } from '@csstools/css-calc';\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString, isStringOrNumber } from './common';\nimport { resolveVar } from './css-var';\nimport { roundToPrecision } from './util';\nimport { MatchedRegExp, Options } from './typedef';\n\n/* constants */\nimport {\n  ANGLE,\n  LENGTH,\n  NUM,\n  SYN_FN_CALC,\n  SYN_FN_MATH_START,\n  SYN_FN_VAR,\n  SYN_FN_VAR_START,\n  VAL_SPEC\n} from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  Dimension: DIM,\n  EOF,\n  Function: FUNC,\n  OpenParen: PAREN_OPEN,\n  Whitespace: W_SPACE\n} = TokenType;\nconst NAMESPACE = 'css-calc';\n\n/* numeric constants */\nconst TRIA = 3;\nconst HEX = 16;\nconst MAX_PCT = 100;\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_CALC_NUM = new RegExp(`^calc\\\\((${NUM})\\\\)$`);\nconst REG_FN_MATH_START = new RegExp(SYN_FN_MATH_START);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\nconst REG_FN_VAR_START = new RegExp(SYN_FN_VAR_START);\nconst REG_OPERATOR = /\\s[*+/-]\\s/;\nconst REG_TYPE_DIM = new RegExp(`^(${NUM})(${ANGLE}|${LENGTH})$`);\nconst REG_TYPE_DIM_PCT = new RegExp(`^(${NUM})(${ANGLE}|${LENGTH}|%)$`);\nconst REG_TYPE_PCT = new RegExp(`^(${NUM})%$`);\n\n/**\n * Calclator\n */\nexport class Calculator {\n  /* private */\n  // number\n  #hasNum: boolean;\n  #numSum: number[];\n  #numMul: number[];\n  // percentage\n  #hasPct: boolean;\n  #pctSum: number[];\n  #pctMul: number[];\n  // dimension\n  #hasDim: boolean;\n  #dimSum: string[];\n  #dimSub: string[];\n  #dimMul: string[];\n  #dimDiv: string[];\n  // et cetra\n  #hasEtc: boolean;\n  #etcSum: string[];\n  #etcSub: string[];\n  #etcMul: string[];\n  #etcDiv: string[];\n\n  /**\n   * constructor\n   */\n  constructor() {\n    // number\n    this.#hasNum = false;\n    this.#numSum = [];\n    this.#numMul = [];\n    // percentage\n    this.#hasPct = false;\n    this.#pctSum = [];\n    this.#pctMul = [];\n    // dimension\n    this.#hasDim = false;\n    this.#dimSum = [];\n    this.#dimSub = [];\n    this.#dimMul = [];\n    this.#dimDiv = [];\n    // et cetra\n    this.#hasEtc = false;\n    this.#etcSum = [];\n    this.#etcSub = [];\n    this.#etcMul = [];\n    this.#etcDiv = [];\n  }\n\n  get hasNum() {\n    return this.#hasNum;\n  }\n\n  set hasNum(value: boolean) {\n    this.#hasNum = !!value;\n  }\n\n  get numSum() {\n    return this.#numSum;\n  }\n\n  get numMul() {\n    return this.#numMul;\n  }\n\n  get hasPct() {\n    return this.#hasPct;\n  }\n\n  set hasPct(value: boolean) {\n    this.#hasPct = !!value;\n  }\n\n  get pctSum() {\n    return this.#pctSum;\n  }\n\n  get pctMul() {\n    return this.#pctMul;\n  }\n\n  get hasDim() {\n    return this.#hasDim;\n  }\n\n  set hasDim(value: boolean) {\n    this.#hasDim = !!value;\n  }\n\n  get dimSum() {\n    return this.#dimSum;\n  }\n\n  get dimSub() {\n    return this.#dimSub;\n  }\n\n  get dimMul() {\n    return this.#dimMul;\n  }\n\n  get dimDiv() {\n    return this.#dimDiv;\n  }\n\n  get hasEtc() {\n    return this.#hasEtc;\n  }\n\n  set hasEtc(value: boolean) {\n    this.#hasEtc = !!value;\n  }\n\n  get etcSum() {\n    return this.#etcSum;\n  }\n\n  get etcSub() {\n    return this.#etcSub;\n  }\n\n  get etcMul() {\n    return this.#etcMul;\n  }\n\n  get etcDiv() {\n    return this.#etcDiv;\n  }\n\n  /**\n   * clear values\n   * @returns void\n   */\n  clear() {\n    // number\n    this.#hasNum = false;\n    this.#numSum = [];\n    this.#numMul = [];\n    // percentage\n    this.#hasPct = false;\n    this.#pctSum = [];\n    this.#pctMul = [];\n    // dimension\n    this.#hasDim = false;\n    this.#dimSum = [];\n    this.#dimSub = [];\n    this.#dimMul = [];\n    this.#dimDiv = [];\n    // et cetra\n    this.#hasEtc = false;\n    this.#etcSum = [];\n    this.#etcSub = [];\n    this.#etcMul = [];\n    this.#etcDiv = [];\n  }\n\n  /**\n   * sort values\n   * @param values - values\n   * @returns sorted values\n   */\n  sort(values: string[] = []): string[] {\n    const arr = [...values];\n    if (arr.length > 1) {\n      arr.sort((a, b) => {\n        let res;\n        if (REG_TYPE_DIM_PCT.test(a) && REG_TYPE_DIM_PCT.test(b)) {\n          const [, valA, unitA] = a.match(REG_TYPE_DIM_PCT) as MatchedRegExp;\n          const [, valB, unitB] = b.match(REG_TYPE_DIM_PCT) as MatchedRegExp;\n          if (unitA === unitB) {\n            if (Number(valA) === Number(valB)) {\n              res = 0;\n            } else if (Number(valA) > Number(valB)) {\n              res = 1;\n            } else {\n              res = -1;\n            }\n          } else if (unitA > unitB) {\n            res = 1;\n          } else {\n            res = -1;\n          }\n        } else {\n          if (a === b) {\n            res = 0;\n          } else if (a > b) {\n            res = 1;\n          } else {\n            res = -1;\n          }\n        }\n        return res;\n      });\n    }\n    return arr;\n  }\n\n  /**\n   * multiply values\n   * @returns resolved value\n   */\n  multiply(): string {\n    const value = [];\n    let num;\n    if (this.#hasNum) {\n      num = 1;\n      for (const i of this.#numMul) {\n        num *= i;\n        if (num === 0 || !Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      if (!this.#hasPct && !this.#hasDim && !this.hasEtc) {\n        if (Number.isFinite(num)) {\n          num = roundToPrecision(num, HEX);\n        }\n        value.push(num);\n      }\n    }\n    if (this.#hasPct) {\n      if (typeof num !== 'number') {\n        num = 1;\n      }\n      for (const i of this.#pctMul) {\n        num *= i;\n        if (num === 0 || !Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      if (Number.isFinite(num)) {\n        num = `${roundToPrecision(num, HEX)}%`;\n      }\n      if (!this.#hasDim && !this.hasEtc) {\n        value.push(num);\n      }\n    }\n    if (this.#hasDim) {\n      let dim = '';\n      let mul = '';\n      let div = '';\n      if (this.#dimMul.length) {\n        if (this.#dimMul.length === 1) {\n          [mul] = this.#dimMul as [string];\n        } else {\n          mul = `${this.sort(this.#dimMul).join(' * ')}`;\n        }\n      }\n      if (this.#dimDiv.length) {\n        if (this.#dimDiv.length === 1) {\n          [div] = this.#dimDiv as [string];\n        } else {\n          div = `${this.sort(this.#dimDiv).join(' * ')}`;\n        }\n      }\n      if (Number.isFinite(num)) {\n        if (mul) {\n          if (div) {\n            if (div.includes('*')) {\n              dim = calc(`calc(${num} * ${mul} / (${div}))`, {\n                toCanonicalUnits: true\n              });\n            } else {\n              dim = calc(`calc(${num} * ${mul} / ${div})`, {\n                toCanonicalUnits: true\n              });\n            }\n          } else {\n            dim = calc(`calc(${num} * ${mul})`, {\n              toCanonicalUnits: true\n            });\n          }\n        } else if (div.includes('*')) {\n          dim = calc(`calc(${num} / (${div}))`, {\n            toCanonicalUnits: true\n          });\n        } else {\n          dim = calc(`calc(${num} / ${div})`, {\n            toCanonicalUnits: true\n          });\n        }\n        value.push(dim.replace(/^calc/, ''));\n      } else {\n        if (!value.length && num !== undefined) {\n          value.push(num);\n        }\n        if (mul) {\n          if (div) {\n            if (div.includes('*')) {\n              dim = calc(`calc(${mul} / (${div}))`, {\n                toCanonicalUnits: true\n              });\n            } else {\n              dim = calc(`calc(${mul} / ${div})`, {\n                toCanonicalUnits: true\n              });\n            }\n          } else {\n            dim = calc(`calc(${mul})`, {\n              toCanonicalUnits: true\n            });\n          }\n          if (value.length) {\n            value.push('*', dim.replace(/^calc/, ''));\n          } else {\n            value.push(dim.replace(/^calc/, ''));\n          }\n        } else {\n          dim = calc(`calc(${div})`, {\n            toCanonicalUnits: true\n          });\n          if (value.length) {\n            value.push('/', dim.replace(/^calc/, ''));\n          } else {\n            value.push('1', '/', dim.replace(/^calc/, ''));\n          }\n        }\n      }\n    }\n    if (this.#hasEtc) {\n      if (this.#etcMul.length) {\n        if (!value.length && num !== undefined) {\n          value.push(num);\n        }\n        const mul = this.sort(this.#etcMul).join(' * ');\n        if (value.length) {\n          value.push(`* ${mul}`);\n        } else {\n          value.push(`${mul}`);\n        }\n      }\n      if (this.#etcDiv.length) {\n        const div = this.sort(this.#etcDiv).join(' * ');\n        if (div.includes('*')) {\n          if (value.length) {\n            value.push(`/ (${div})`);\n          } else {\n            value.push(`1 / (${div})`);\n          }\n        } else if (value.length) {\n          value.push(`/ ${div}`);\n        } else {\n          value.push(`1 / ${div}`);\n        }\n      }\n    }\n    if (value.length) {\n      return value.join(' ');\n    }\n    return '';\n  }\n\n  /**\n   * sum values\n   * @returns resolved value\n   */\n  sum(): string {\n    const value = [];\n    if (this.#hasNum) {\n      let num = 0;\n      for (const i of this.#numSum) {\n        num += i;\n        if (!Number.isFinite(num) || Number.isNaN(num)) {\n          break;\n        }\n      }\n      value.push(num);\n    }\n    if (this.#hasPct) {\n      let num: number | string = 0;\n      for (const i of this.#pctSum) {\n        num += i;\n        if (!Number.isFinite(num)) {\n          break;\n        }\n      }\n      if (Number.isFinite(num)) {\n        num = `${num}%`;\n      }\n      if (value.length) {\n        value.push(`+ ${num}`);\n      } else {\n        value.push(num);\n      }\n    }\n    if (this.#hasDim) {\n      let dim, sum, sub;\n      if (this.#dimSum.length) {\n        sum = this.sort(this.#dimSum).join(' + ');\n      }\n      if (this.#dimSub.length) {\n        sub = this.sort(this.#dimSub).join(' + ');\n      }\n      if (sum) {\n        if (sub) {\n          if (sub.includes('-')) {\n            dim = calc(`calc(${sum} - (${sub}))`, {\n              toCanonicalUnits: true\n            });\n          } else {\n            dim = calc(`calc(${sum} - ${sub})`, {\n              toCanonicalUnits: true\n            });\n          }\n        } else {\n          dim = calc(`calc(${sum})`, {\n            toCanonicalUnits: true\n          });\n        }\n      } else {\n        dim = calc(`calc(-1 * (${sub}))`, {\n          toCanonicalUnits: true\n        });\n      }\n      if (value.length) {\n        value.push('+', dim.replace(/^calc/, ''));\n      } else {\n        value.push(dim.replace(/^calc/, ''));\n      }\n    }\n    if (this.#hasEtc) {\n      if (this.#etcSum.length) {\n        const sum = this.sort(this.#etcSum)\n          .map(item => {\n            let res;\n            if (\n              REG_OPERATOR.test(item) &&\n              !item.startsWith('(') &&\n              !item.endsWith(')')\n            ) {\n              res = `(${item})`;\n            } else {\n              res = item;\n            }\n            return res;\n          })\n          .join(' + ');\n        if (value.length) {\n          if (this.#etcSum.length > 1) {\n            value.push(`+ (${sum})`);\n          } else {\n            value.push(`+ ${sum}`);\n          }\n        } else {\n          value.push(`${sum}`);\n        }\n      }\n      if (this.#etcSub.length) {\n        const sub = this.sort(this.#etcSub)\n          .map(item => {\n            let res;\n            if (\n              REG_OPERATOR.test(item) &&\n              !item.startsWith('(') &&\n              !item.endsWith(')')\n            ) {\n              res = `(${item})`;\n            } else {\n              res = item;\n            }\n            return res;\n          })\n          .join(' + ');\n        if (value.length) {\n          if (this.#etcSub.length > 1) {\n            value.push(`- (${sub})`);\n          } else {\n            value.push(`- ${sub}`);\n          }\n        } else if (this.#etcSub.length > 1) {\n          value.push(`-1 * (${sub})`);\n        } else {\n          value.push(`-1 * ${sub}`);\n        }\n      }\n    }\n    if (value.length) {\n      return value.join(' ');\n    }\n    return '';\n  }\n}\n\n/**\n * sort calc values\n * @param values - values to sort\n * @param [finalize] - finalize values\n * @returns sorted values\n */\nexport const sortCalcValues = (\n  values: (number | string)[] = [],\n  finalize: boolean = false\n): string => {\n  if (values.length < TRIA) {\n    throw new Error(`Unexpected array length ${values.length}.`);\n  }\n  const start = values.shift();\n  if (!isString(start) || !start.endsWith('(')) {\n    throw new Error(`Unexpected token ${start}.`);\n  }\n  const end = values.pop();\n  if (end !== ')') {\n    throw new Error(`Unexpected token ${end}.`);\n  }\n  if (values.length === 1) {\n    const [value] = values;\n    if (!isStringOrNumber(value)) {\n      throw new Error(`Unexpected token ${value}.`);\n    }\n    return `${start}${value}${end}`;\n  }\n  const sortedValues = [];\n  const cal = new Calculator();\n  let operator: string = '';\n  const l = values.length;\n  for (let i = 0; i < l; i++) {\n    const value = values[i];\n    if (!isStringOrNumber(value)) {\n      throw new Error(`Unexpected token ${value}.`);\n    }\n    if (value === '*' || value === '/') {\n      operator = value;\n    } else if (value === '+' || value === '-') {\n      const sortedValue = cal.multiply();\n      if (sortedValue) {\n        sortedValues.push(sortedValue, value);\n      }\n      cal.clear();\n      operator = '';\n    } else {\n      const numValue = Number(value);\n      const strValue = `${value}`;\n      switch (operator) {\n        case '/': {\n          if (Number.isFinite(numValue)) {\n            cal.hasNum = true;\n            cal.numMul.push(1 / numValue);\n          } else if (REG_TYPE_PCT.test(strValue)) {\n            const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n            cal.hasPct = true;\n            cal.pctMul.push((MAX_PCT * MAX_PCT) / Number(val));\n          } else if (REG_TYPE_DIM.test(strValue)) {\n            cal.hasDim = true;\n            cal.dimDiv.push(strValue);\n          } else {\n            cal.hasEtc = true;\n            cal.etcDiv.push(strValue);\n          }\n          break;\n        }\n        case '*':\n        default: {\n          if (Number.isFinite(numValue)) {\n            cal.hasNum = true;\n            cal.numMul.push(numValue);\n          } else if (REG_TYPE_PCT.test(strValue)) {\n            const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n            cal.hasPct = true;\n            cal.pctMul.push(Number(val));\n          } else if (REG_TYPE_DIM.test(strValue)) {\n            cal.hasDim = true;\n            cal.dimMul.push(strValue);\n          } else {\n            cal.hasEtc = true;\n            cal.etcMul.push(strValue);\n          }\n        }\n      }\n    }\n    if (i === l - 1) {\n      const sortedValue = cal.multiply();\n      if (sortedValue) {\n        sortedValues.push(sortedValue);\n      }\n      cal.clear();\n      operator = '';\n    }\n  }\n  let resolvedValue = '';\n  if (finalize && (sortedValues.includes('+') || sortedValues.includes('-'))) {\n    const finalizedValues = [];\n    cal.clear();\n    operator = '';\n    const l = sortedValues.length;\n    for (let i = 0; i < l; i++) {\n      const value = sortedValues[i];\n      if (isStringOrNumber(value)) {\n        if (value === '+' || value === '-') {\n          operator = value;\n        } else {\n          const numValue = Number(value);\n          const strValue = `${value}`;\n          switch (operator) {\n            case '-': {\n              if (Number.isFinite(numValue)) {\n                cal.hasNum = true;\n                cal.numSum.push(-1 * numValue);\n              } else if (REG_TYPE_PCT.test(strValue)) {\n                const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n                cal.hasPct = true;\n                cal.pctSum.push(-1 * Number(val));\n              } else if (REG_TYPE_DIM.test(strValue)) {\n                cal.hasDim = true;\n                cal.dimSub.push(strValue);\n              } else {\n                cal.hasEtc = true;\n                cal.etcSub.push(strValue);\n              }\n              break;\n            }\n            case '+':\n            default: {\n              if (Number.isFinite(numValue)) {\n                cal.hasNum = true;\n                cal.numSum.push(numValue);\n              } else if (REG_TYPE_PCT.test(strValue)) {\n                const [, val] = strValue.match(REG_TYPE_PCT) as MatchedRegExp;\n                cal.hasPct = true;\n                cal.pctSum.push(Number(val));\n              } else if (REG_TYPE_DIM.test(strValue)) {\n                cal.hasDim = true;\n                cal.dimSum.push(strValue);\n              } else {\n                cal.hasEtc = true;\n                cal.etcSum.push(strValue);\n              }\n            }\n          }\n        }\n      }\n      if (i === l - 1) {\n        const sortedValue = cal.sum();\n        if (sortedValue) {\n          finalizedValues.push(sortedValue);\n        }\n        cal.clear();\n        operator = '';\n      }\n    }\n    resolvedValue = finalizedValues.join(' ').replace(/\\+\\s-/g, '- ');\n  } else {\n    resolvedValue = sortedValues.join(' ').replace(/\\+\\s-/g, '- ');\n  }\n  if (\n    resolvedValue.startsWith('(') &&\n    resolvedValue.endsWith(')') &&\n    resolvedValue.lastIndexOf('(') === 0 &&\n    resolvedValue.indexOf(')') === resolvedValue.length - 1\n  ) {\n    resolvedValue = resolvedValue.replace(/^\\(/, '').replace(/\\)$/, '');\n  }\n  return `${start}${resolvedValue}${end}`;\n};\n\n/**\n * serialize calc\n * @param value - CSS value\n * @param [opt] - options\n * @returns serialized value\n */\nexport const serializeCalc = (value: string, opt: Options = {}): string => {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (!REG_FN_VAR_START.test(value) || format !== VAL_SPEC) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'serializeCalc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string;\n  }\n  const items: string[] = tokenize({ css: value })\n    .map((token: CSSToken): string => {\n      const [type, value] = token as [TokenType, string];\n      let res = '';\n      if (type !== W_SPACE && type !== COMMENT) {\n        res = value;\n      }\n      return res;\n    })\n    .filter(v => v);\n  let startIndex = items.findLastIndex((item: string) => /\\($/.test(item));\n  while (startIndex) {\n    const endIndex = items.findIndex((item: unknown, index: number) => {\n      return item === ')' && index > startIndex;\n    });\n    const slicedValues: string[] = items.slice(startIndex, endIndex + 1);\n    let serializedValue: string = sortCalcValues(slicedValues);\n    if (REG_FN_VAR_START.test(serializedValue)) {\n      serializedValue = calc(serializedValue, {\n        toCanonicalUnits: true\n      });\n    }\n    items.splice(startIndex, endIndex - startIndex + 1, serializedValue);\n    startIndex = items.findLastIndex((item: string) => /\\($/.test(item));\n  }\n  const serializedCalc = sortCalcValues(items, true);\n  setCache(cacheKey, serializedCalc);\n  return serializedCalc;\n};\n\n/**\n * resolve dimension\n * @param token - CSS token\n * @param [opt] - options\n * @returns resolved value\n */\nexport const resolveDimension = (\n  token: CSSToken,\n  opt: Options = {}\n): string | NullObject => {\n  if (!Array.isArray(token)) {\n    throw new TypeError(`${token} is not an array.`);\n  }\n  const [, , , , detail = {}] = token;\n  const { unit, value } = detail as {\n    unit: string;\n    value: number;\n  };\n  const { dimension = {} } = opt;\n  if (unit === 'px') {\n    return `${value}${unit}`;\n  }\n  const relativeValue = Number(value);\n  if (unit && Number.isFinite(relativeValue)) {\n    let pixelValue;\n    if (Object.hasOwn(dimension, unit)) {\n      pixelValue = dimension[unit];\n    } else if (typeof dimension.callback === 'function') {\n      pixelValue = dimension.callback(unit);\n    }\n    pixelValue = Number(pixelValue);\n    if (Number.isFinite(pixelValue)) {\n      return `${relativeValue * pixelValue}px`;\n    }\n  }\n  return new NullObject();\n};\n\n/**\n * parse tokens\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns parsed tokens\n */\nexport const parseTokens = (\n  tokens: CSSToken[],\n  opt: Options = {}\n): string[] => {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { format = '' } = opt;\n  const mathFunc = new Set();\n  let nest = 0;\n  const res: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type = '', value = ''] = token as [TokenType, string];\n    switch (type) {\n      case DIM: {\n        if (format === VAL_SPEC && !mathFunc.has(nest)) {\n          res.push(value);\n        } else {\n          const resolvedValue = resolveDimension(token, opt);\n          if (isString(resolvedValue)) {\n            res.push(resolvedValue);\n          } else {\n            res.push(value);\n          }\n        }\n        break;\n      }\n      case FUNC:\n      case PAREN_OPEN: {\n        res.push(value);\n        nest++;\n        if (REG_FN_MATH_START.test(value)) {\n          mathFunc.add(nest);\n        }\n        break;\n      }\n      case PAREN_CLOSE: {\n        if (res.length) {\n          const lastValue = res[res.length - 1];\n          if (lastValue === ' ') {\n            res.splice(-1, 1, value);\n          } else {\n            res.push(value);\n          }\n        } else {\n          res.push(value);\n        }\n        if (mathFunc.has(nest)) {\n          mathFunc.delete(nest);\n        }\n        nest--;\n        break;\n      }\n      case W_SPACE: {\n        if (res.length) {\n          const lastValue = res[res.length - 1];\n          if (\n            isString(lastValue) &&\n            !lastValue.endsWith('(') &&\n            lastValue !== ' '\n          ) {\n            res.push(value);\n          }\n        }\n        break;\n      }\n      default: {\n        if (type !== COMMENT && type !== EOF) {\n          res.push(value);\n        }\n      }\n    }\n  }\n  return res;\n};\n\n/**\n * CSS calc()\n * @param value - CSS value including calc()\n * @param [opt] - options\n * @returns resolved value\n */\nexport const cssCalc = (value: string, opt: Options = {}): string => {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (REG_FN_VAR.test(value)) {\n      if (format === VAL_SPEC) {\n        return value;\n      } else {\n        const resolvedValue = resolveVar(value, opt);\n        if (isString(resolvedValue)) {\n          return resolvedValue;\n        } else {\n          return '';\n        }\n      }\n    } else if (!REG_FN_CALC.test(value)) {\n      return value;\n    }\n    value = value.toLowerCase().trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'cssCalc',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    return cachedResult.item as string;\n  }\n  const tokens = tokenize({ css: value });\n  const values = parseTokens(tokens, opt);\n  let resolvedValue: string = calc(values.join(''), {\n    toCanonicalUnits: true\n  });\n  if (REG_FN_VAR_START.test(value)) {\n    if (REG_TYPE_DIM_PCT.test(resolvedValue)) {\n      const [, val, unit] = resolvedValue.match(\n        REG_TYPE_DIM_PCT\n      ) as MatchedRegExp;\n      resolvedValue = `${roundToPrecision(Number(val), HEX)}${unit}`;\n    }\n    // wrap with `calc()`\n    if (\n      resolvedValue &&\n      !REG_FN_VAR_START.test(resolvedValue) &&\n      format === VAL_SPEC\n    ) {\n      resolvedValue = `calc(${resolvedValue})`;\n    }\n  }\n  if (format === VAL_SPEC) {\n    if (/\\s[-+*/]\\s/.test(resolvedValue) && !resolvedValue.includes('NaN')) {\n      resolvedValue = serializeCalc(resolvedValue, opt);\n    } else if (REG_FN_CALC_NUM.test(resolvedValue)) {\n      const [, val] = resolvedValue.match(REG_FN_CALC_NUM) as MatchedRegExp;\n      resolvedValue = `calc(${roundToPrecision(Number(val), HEX)})`;\n    }\n  }\n  setCache(cacheKey, resolvedValue);\n  return resolvedValue;\n};\n"], "names": ["l", "value", "resolvedValue"], "mappings": ";;;;;;;;;;;;;;;AA6BA,MAAM;AAAA,EACJ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT,WAAW;AAAA,EACX;AAAA,EACA,UAAU;AAAA,EACV,WAAW;AAAA,EACX,YAAY;AACd,IAAI;AACJ,MAAM,YAAY;AAGlB,MAAM,OAAO;AACb,MAAM,MAAM;AACZ,MAAM,UAAU;AAGhB,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,kBAAkB,IAAI,OAAO,YAAY,GAAG,OAAO;AACzD,MAAM,oBAAoB,IAAI,OAAO,iBAAiB;AACtD,MAAM,aAAa,IAAI,OAAO,UAAU;AACxC,MAAM,mBAAmB,IAAI,OAAO,gBAAgB;AACpD,MAAM,eAAe;AACrB,MAAM,eAAe,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,IAAI;AAChE,MAAM,mBAAmB,IAAI,OAAO,KAAK,GAAG,KAAK,KAAK,IAAI,MAAM,MAAM;AACtE,MAAM,eAAe,IAAI,OAAO,KAAK,GAAG,KAAK;AAKtC,MAAM,WAAW;AAAA;AAAA;AAAA;AAAA,EA0BtB,cAAc;AAvBd;AAAA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAOE,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAEf,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAEf,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAEf,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAAA,EACjB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,uBAAK,SAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,uBAAK,SAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,uBAAK,SAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,OAAO,OAAgB;AACzB,uBAAK,SAAU,CAAC,CAAC;AAAA,EACnB;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA,EAEA,IAAI,SAAS;AACX,WAAO,mBAAK;AAAA,EACd;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,QAAQ;AAEN,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAEf,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAEf,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAEf,uBAAK,SAAU;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AACf,uBAAK,SAAU,CAAA;AAAA,EACjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,EAOA,KAAK,SAAmB,IAAc;AACpC,UAAM,MAAM,CAAC,GAAG,MAAM;AACtB,QAAI,IAAI,SAAS,GAAG;AAClB,UAAI,KAAK,CAAC,GAAG,MAAM;AACjB,YAAI;AACJ,YAAI,iBAAiB,KAAK,CAAC,KAAK,iBAAiB,KAAK,CAAC,GAAG;AACxD,gBAAM,CAAA,EAAG,MAAM,KAAK,IAAI,EAAE,MAAM,gBAAgB;AAChD,gBAAM,CAAA,EAAG,MAAM,KAAK,IAAI,EAAE,MAAM,gBAAgB;AAChD,cAAI,UAAU,OAAO;AACnB,gBAAI,OAAO,IAAI,MAAM,OAAO,IAAI,GAAG;AACjC,oBAAM;AAAA,YACR,WAAW,OAAO,IAAI,IAAI,OAAO,IAAI,GAAG;AACtC,oBAAM;AAAA,YACR,OAAO;AACL,oBAAM;AAAA,YACR;AAAA,UACF,WAAW,QAAQ,OAAO;AACxB,kBAAM;AAAA,UACR,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF,OAAO;AACL,cAAI,MAAM,GAAG;AACX,kBAAM;AAAA,UACR,WAAW,IAAI,GAAG;AAChB,kBAAM;AAAA,UACR,OAAO;AACL,kBAAM;AAAA,UACR;AAAA,QACF;AACA,eAAO;AAAA,MACT,CAAC;AAAA,IACH;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,WAAmB;AACjB,UAAM,QAAQ,CAAA;AACd,QAAI;AACJ,QAAI,mBAAK,UAAS;AAChB,YAAM;AACN,iBAAW,KAAK,mBAAK,UAAS;AAC5B,eAAO;AACP,YAAI,QAAQ,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC3D;AAAA,QACF;AAAA,MACF;AACA,UAAI,CAAC,mBAAK,YAAW,CAAC,mBAAK,YAAW,CAAC,KAAK,QAAQ;AAClD,YAAI,OAAO,SAAS,GAAG,GAAG;AACxB,gBAAM,iBAAiB,KAAK,GAAG;AAAA,QACjC;AACA,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AACA,QAAI,mBAAK,UAAS;AAChB,UAAI,OAAO,QAAQ,UAAU;AAC3B,cAAM;AAAA,MACR;AACA,iBAAW,KAAK,mBAAK,UAAS;AAC5B,eAAO;AACP,YAAI,QAAQ,KAAK,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC3D;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,cAAM,GAAG,iBAAiB,KAAK,GAAG,CAAC;AAAA,MACrC;AACA,UAAI,CAAC,mBAAK,YAAW,CAAC,KAAK,QAAQ;AACjC,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AACA,QAAI,mBAAK,UAAS;AAChB,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,MAAM;AACV,UAAI,mBAAK,SAAQ,QAAQ;AACvB,YAAI,mBAAK,SAAQ,WAAW,GAAG;AAC7B,WAAC,GAAG,IAAI,mBAAK;AAAA,QACf,OAAO;AACL,gBAAM,GAAG,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK,CAAC;AAAA,QAC9C;AAAA,MACF;AACA,UAAI,mBAAK,SAAQ,QAAQ;AACvB,YAAI,mBAAK,SAAQ,WAAW,GAAG;AAC7B,WAAC,GAAG,IAAI,mBAAK;AAAA,QACf,OAAO;AACL,gBAAM,GAAG,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK,CAAC;AAAA,QAC9C;AAAA,MACF;AACA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,YAAI,KAAK;AACP,cAAI,KAAK;AACP,gBAAI,IAAI,SAAS,GAAG,GAAG;AACrB,oBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,OAAO,GAAG,MAAM;AAAA,gBAC7C,kBAAkB;AAAA,cAAA,CACnB;AAAA,YACH,OAAO;AACL,oBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,MAAM,GAAG,KAAK;AAAA,gBAC3C,kBAAkB;AAAA,cAAA,CACnB;AAAA,YACH;AAAA,UACF,OAAO;AACL,kBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,cAClC,kBAAkB;AAAA,YAAA,CACnB;AAAA,UACH;AAAA,QACF,WAAW,IAAI,SAAS,GAAG,GAAG;AAC5B,gBAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,YACpC,kBAAkB;AAAA,UAAA,CACnB;AAAA,QACH,OAAO;AACL,gBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,YAClC,kBAAkB;AAAA,UAAA,CACnB;AAAA,QACH;AACA,cAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MACrC,OAAO;AACL,YAAI,CAAC,MAAM,UAAU,QAAQ,QAAW;AACtC,gBAAM,KAAK,GAAG;AAAA,QAChB;AACA,YAAI,KAAK;AACP,cAAI,KAAK;AACP,gBAAI,IAAI,SAAS,GAAG,GAAG;AACrB,oBAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,gBACpC,kBAAkB;AAAA,cAAA,CACnB;AAAA,YACH,OAAO;AACL,oBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,gBAClC,kBAAkB;AAAA,cAAA,CACnB;AAAA,YACH;AAAA,UACF,OAAO;AACL,kBAAM,KAAK,QAAQ,GAAG,KAAK;AAAA,cACzB,kBAAkB;AAAA,YAAA,CACnB;AAAA,UACH;AACA,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAC1C,OAAO;AACL,kBAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UACrC;AAAA,QACF,OAAO;AACL,gBAAM,KAAK,QAAQ,GAAG,KAAK;AAAA,YACzB,kBAAkB;AAAA,UAAA,CACnB;AACD,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAC1C,OAAO;AACL,kBAAM,KAAK,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,UAC/C;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,QAAI,mBAAK,UAAS;AAChB,UAAI,mBAAK,SAAQ,QAAQ;AACvB,YAAI,CAAC,MAAM,UAAU,QAAQ,QAAW;AACtC,gBAAM,KAAK,GAAG;AAAA,QAChB;AACA,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAC9C,YAAI,MAAM,QAAQ;AAChB,gBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,QACvB,OAAO;AACL,gBAAM,KAAK,GAAG,GAAG,EAAE;AAAA,QACrB;AAAA,MACF;AACA,UAAI,mBAAK,SAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAC9C,YAAI,IAAI,SAAS,GAAG,GAAG;AACrB,cAAI,MAAM,QAAQ;AAChB,kBAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UACzB,OAAO;AACL,kBAAM,KAAK,QAAQ,GAAG,GAAG;AAAA,UAC3B;AAAA,QACF,WAAW,MAAM,QAAQ;AACvB,gBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,QACvB,OAAO;AACL,gBAAM,KAAK,OAAO,GAAG,EAAE;AAAA,QACzB;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,QAAQ;AAChB,aAAO,MAAM,KAAK,GAAG;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AAAA;AAAA;AAAA;AAAA;AAAA,EAMA,MAAc;AACZ,UAAM,QAAQ,CAAA;AACd,QAAI,mBAAK,UAAS;AAChB,UAAI,MAAM;AACV,iBAAW,KAAK,mBAAK,UAAS;AAC5B,eAAO;AACP,YAAI,CAAC,OAAO,SAAS,GAAG,KAAK,OAAO,MAAM,GAAG,GAAG;AAC9C;AAAA,QACF;AAAA,MACF;AACA,YAAM,KAAK,GAAG;AAAA,IAChB;AACA,QAAI,mBAAK,UAAS;AAChB,UAAI,MAAuB;AAC3B,iBAAW,KAAK,mBAAK,UAAS;AAC5B,eAAO;AACP,YAAI,CAAC,OAAO,SAAS,GAAG,GAAG;AACzB;AAAA,QACF;AAAA,MACF;AACA,UAAI,OAAO,SAAS,GAAG,GAAG;AACxB,cAAM,GAAG,GAAG;AAAA,MACd;AACA,UAAI,MAAM,QAAQ;AAChB,cAAM,KAAK,KAAK,GAAG,EAAE;AAAA,MACvB,OAAO;AACL,cAAM,KAAK,GAAG;AAAA,MAChB;AAAA,IACF;AACA,QAAI,mBAAK,UAAS;AAChB,UAAI,KAAK,KAAK;AACd,UAAI,mBAAK,SAAQ,QAAQ;AACvB,cAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAAA,MAC1C;AACA,UAAI,mBAAK,SAAQ,QAAQ;AACvB,cAAM,KAAK,KAAK,mBAAK,QAAO,EAAE,KAAK,KAAK;AAAA,MAC1C;AACA,UAAI,KAAK;AACP,YAAI,KAAK;AACP,cAAI,IAAI,SAAS,GAAG,GAAG;AACrB,kBAAM,KAAK,QAAQ,GAAG,OAAO,GAAG,MAAM;AAAA,cACpC,kBAAkB;AAAA,YAAA,CACnB;AAAA,UACH,OAAO;AACL,kBAAM,KAAK,QAAQ,GAAG,MAAM,GAAG,KAAK;AAAA,cAClC,kBAAkB;AAAA,YAAA,CACnB;AAAA,UACH;AAAA,QACF,OAAO;AACL,gBAAM,KAAK,QAAQ,GAAG,KAAK;AAAA,YACzB,kBAAkB;AAAA,UAAA,CACnB;AAAA,QACH;AAAA,MACF,OAAO;AACL,cAAM,KAAK,cAAc,GAAG,MAAM;AAAA,UAChC,kBAAkB;AAAA,QAAA,CACnB;AAAA,MACH;AACA,UAAI,MAAM,QAAQ;AAChB,cAAM,KAAK,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MAC1C,OAAO;AACL,cAAM,KAAK,IAAI,QAAQ,SAAS,EAAE,CAAC;AAAA,MACrC;AAAA,IACF;AACA,QAAI,mBAAK,UAAS;AAChB,UAAI,mBAAK,SAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAC/B,IAAI,CAAA,SAAQ;AACX,cAAI;AACJ,cACE,aAAa,KAAK,IAAI,KACtB,CAAC,KAAK,WAAW,GAAG,KACpB,CAAC,KAAK,SAAS,GAAG,GAClB;AACA,kBAAM,IAAI,IAAI;AAAA,UAChB,OAAO;AACL,kBAAM;AAAA,UACR;AACA,iBAAO;AAAA,QACT,CAAC,EACA,KAAK,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI,mBAAK,SAAQ,SAAS,GAAG;AAC3B,kBAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UACzB,OAAO;AACL,kBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,UACvB;AAAA,QACF,OAAO;AACL,gBAAM,KAAK,GAAG,GAAG,EAAE;AAAA,QACrB;AAAA,MACF;AACA,UAAI,mBAAK,SAAQ,QAAQ;AACvB,cAAM,MAAM,KAAK,KAAK,mBAAK,QAAO,EAC/B,IAAI,CAAA,SAAQ;AACX,cAAI;AACJ,cACE,aAAa,KAAK,IAAI,KACtB,CAAC,KAAK,WAAW,GAAG,KACpB,CAAC,KAAK,SAAS,GAAG,GAClB;AACA,kBAAM,IAAI,IAAI;AAAA,UAChB,OAAO;AACL,kBAAM;AAAA,UACR;AACA,iBAAO;AAAA,QACT,CAAC,EACA,KAAK,KAAK;AACb,YAAI,MAAM,QAAQ;AAChB,cAAI,mBAAK,SAAQ,SAAS,GAAG;AAC3B,kBAAM,KAAK,MAAM,GAAG,GAAG;AAAA,UACzB,OAAO;AACL,kBAAM,KAAK,KAAK,GAAG,EAAE;AAAA,UACvB;AAAA,QACF,WAAW,mBAAK,SAAQ,SAAS,GAAG;AAClC,gBAAM,KAAK,SAAS,GAAG,GAAG;AAAA,QAC5B,OAAO;AACL,gBAAM,KAAK,QAAQ,GAAG,EAAE;AAAA,QAC1B;AAAA,MACF;AAAA,IACF;AACA,QAAI,MAAM,QAAQ;AAChB,aAAO,MAAM,KAAK,GAAG;AAAA,IACvB;AACA,WAAO;AAAA,EACT;AACF;AA7dE;AACA;AACA;AAEA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;AACA;AAmdK,MAAM,iBAAiB,CAC5B,SAA8B,IAC9B,WAAoB,UACT;AACX,MAAI,OAAO,SAAS,MAAM;AACxB,UAAM,IAAI,MAAM,2BAA2B,OAAO,MAAM,GAAG;AAAA,EAC7D;AACA,QAAM,QAAQ,OAAO,MAAA;AACrB,MAAI,CAAC,SAAS,KAAK,KAAK,CAAC,MAAM,SAAS,GAAG,GAAG;AAC5C,UAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,EAC9C;AACA,QAAM,MAAM,OAAO,IAAA;AACnB,MAAI,QAAQ,KAAK;AACf,UAAM,IAAI,MAAM,oBAAoB,GAAG,GAAG;AAAA,EAC5C;AACA,MAAI,OAAO,WAAW,GAAG;AACvB,UAAM,CAAC,KAAK,IAAI;AAChB,QAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,IAC9C;AACA,WAAO,GAAG,KAAK,GAAG,KAAK,GAAG,GAAG;AAAA,EAC/B;AACA,QAAM,eAAe,CAAA;AACrB,QAAM,MAAM,IAAI,WAAA;AAChB,MAAI,WAAmB;AACvB,QAAM,IAAI,OAAO;AACjB,WAAS,IAAI,GAAG,IAAI,GAAG,KAAK;AAC1B,UAAM,QAAQ,OAAO,CAAC;AACtB,QAAI,CAAC,iBAAiB,KAAK,GAAG;AAC5B,YAAM,IAAI,MAAM,oBAAoB,KAAK,GAAG;AAAA,IAC9C;AACA,QAAI,UAAU,OAAO,UAAU,KAAK;AAClC,iBAAW;AAAA,IACb,WAAW,UAAU,OAAO,UAAU,KAAK;AACzC,YAAM,cAAc,IAAI,SAAA;AACxB,UAAI,aAAa;AACf,qBAAa,KAAK,aAAa,KAAK;AAAA,MACtC;AACA,UAAI,MAAA;AACJ,iBAAW;AAAA,IACb,OAAO;AACL,YAAM,WAAW,OAAO,KAAK;AAC7B,YAAM,WAAW,GAAG,KAAK;AACzB,cAAQ,UAAA;AAAA,QACN,KAAK,KAAK;AACR,cAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,IAAI,QAAQ;AAAA,UAC9B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,kBAAM,CAAA,EAAG,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAM,UAAU,UAAW,OAAO,GAAG,CAAC;AAAA,UACnD,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B,OAAO;AACL,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B;AACA;AAAA,QACF;AAAA,QACA,KAAK;AAAA,QACL,SAAS;AACP,cAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,kBAAM,CAAA,EAAG,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,UAC7B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B,OAAO;AACL,gBAAI,SAAS;AACb,gBAAI,OAAO,KAAK,QAAQ;AAAA,UAC1B;AAAA,QACF;AAAA,MAAA;AAAA,IAEJ;AACA,QAAI,MAAM,IAAI,GAAG;AACf,YAAM,cAAc,IAAI,SAAA;AACxB,UAAI,aAAa;AACf,qBAAa,KAAK,WAAW;AAAA,MAC/B;AACA,UAAI,MAAA;AACJ,iBAAW;AAAA,IACb;AAAA,EACF;AACA,MAAI,gBAAgB;AACpB,MAAI,aAAa,aAAa,SAAS,GAAG,KAAK,aAAa,SAAS,GAAG,IAAI;AAC1E,UAAM,kBAAkB,CAAA;AACxB,QAAI,MAAA;AACJ,eAAW;AACX,UAAMA,KAAI,aAAa;AACvB,aAAS,IAAI,GAAG,IAAIA,IAAG,KAAK;AAC1B,YAAM,QAAQ,aAAa,CAAC;AAC5B,UAAI,iBAAiB,KAAK,GAAG;AAC3B,YAAI,UAAU,OAAO,UAAU,KAAK;AAClC,qBAAW;AAAA,QACb,OAAO;AACL,gBAAM,WAAW,OAAO,KAAK;AAC7B,gBAAM,WAAW,GAAG,KAAK;AACzB,kBAAQ,UAAA;AAAA,YACN,KAAK,KAAK;AACR,kBAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,KAAK,QAAQ;AAAA,cAC/B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,sBAAM,CAAA,EAAG,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,KAAK,OAAO,GAAG,CAAC;AAAA,cAClC,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B,OAAO;AACL,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B;AACA;AAAA,YACF;AAAA,YACA,KAAK;AAAA,YACL,SAAS;AACP,kBAAI,OAAO,SAAS,QAAQ,GAAG;AAC7B,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,sBAAM,CAAA,EAAG,GAAG,IAAI,SAAS,MAAM,YAAY;AAC3C,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,OAAO,GAAG,CAAC;AAAA,cAC7B,WAAW,aAAa,KAAK,QAAQ,GAAG;AACtC,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B,OAAO;AACL,oBAAI,SAAS;AACb,oBAAI,OAAO,KAAK,QAAQ;AAAA,cAC1B;AAAA,YACF;AAAA,UAAA;AAAA,QAEJ;AAAA,MACF;AACA,UAAI,MAAMA,KAAI,GAAG;AACf,cAAM,cAAc,IAAI,IAAA;AACxB,YAAI,aAAa;AACf,0BAAgB,KAAK,WAAW;AAAA,QAClC;AACA,YAAI,MAAA;AACJ,mBAAW;AAAA,MACb;AAAA,IACF;AACA,oBAAgB,gBAAgB,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,EAClE,OAAO;AACL,oBAAgB,aAAa,KAAK,GAAG,EAAE,QAAQ,UAAU,IAAI;AAAA,EAC/D;AACA,MACE,cAAc,WAAW,GAAG,KAC5B,cAAc,SAAS,GAAG,KAC1B,cAAc,YAAY,GAAG,MAAM,KACnC,cAAc,QAAQ,GAAG,MAAM,cAAc,SAAS,GACtD;AACA,oBAAgB,cAAc,QAAQ,OAAO,EAAE,EAAE,QAAQ,OAAO,EAAE;AAAA,EACpE;AACA,SAAO,GAAG,KAAK,GAAG,aAAa,GAAG,GAAG;AACvC;AAQO,MAAM,gBAAgB,CAAC,OAAe,MAAe,OAAe;AACzE,QAAM,EAAE,SAAS,GAAA,IAAO;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,CAAC,iBAAiB,KAAK,KAAK,KAAK,WAAW,UAAU;AACxD,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,YAAA,EAAc,KAAA;AAAA,EAC9B,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IAAA;AAAA,IAEF;AAAA,EAAA;AAEF,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,QAAkB,SAAS,EAAE,KAAK,OAAO,EAC5C,IAAI,CAAC,UAA4B;AAChC,UAAM,CAAC,MAAMC,MAAK,IAAI;AACtB,QAAI,MAAM;AACV,QAAI,SAAS,WAAW,SAAS,SAAS;AACxC,YAAMA;AAAAA,IACR;AACA,WAAO;AAAA,EACT,CAAC,EACA,OAAO,CAAA,MAAK,CAAC;AAChB,MAAI,aAAa,MAAM,cAAc,CAAC,SAAiB,MAAM,KAAK,IAAI,CAAC;AACvE,SAAO,YAAY;AACjB,UAAM,WAAW,MAAM,UAAU,CAAC,MAAe,UAAkB;AACjE,aAAO,SAAS,OAAO,QAAQ;AAAA,IACjC,CAAC;AACD,UAAM,eAAyB,MAAM,MAAM,YAAY,WAAW,CAAC;AACnE,QAAI,kBAA0B,eAAe,YAAY;AACzD,QAAI,iBAAiB,KAAK,eAAe,GAAG;AAC1C,wBAAkB,KAAK,iBAAiB;AAAA,QACtC,kBAAkB;AAAA,MAAA,CACnB;AAAA,IACH;AACA,UAAM,OAAO,YAAY,WAAW,aAAa,GAAG,eAAe;AACnE,iBAAa,MAAM,cAAc,CAAC,SAAiB,MAAM,KAAK,IAAI,CAAC;AAAA,EACrE;AACA,QAAM,iBAAiB,eAAe,OAAO,IAAI;AACjD,WAAS,UAAU,cAAc;AACjC,SAAO;AACT;AAQO,MAAM,mBAAmB,CAC9B,OACA,MAAe,OACS;AACxB,MAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,CAAA,EAAA,EAAA,EAAA,EAAS,SAAS,CAAA,CAAE,IAAI;AAC9B,QAAM,EAAE,MAAM,MAAA,IAAU;AAIxB,QAAM,EAAE,YAAY,CAAA,EAAC,IAAM;AAC3B,MAAI,SAAS,MAAM;AACjB,WAAO,GAAG,KAAK,GAAG,IAAI;AAAA,EACxB;AACA,QAAM,gBAAgB,OAAO,KAAK;AAClC,MAAI,QAAQ,OAAO,SAAS,aAAa,GAAG;AAC1C,QAAI;AACJ,QAAI,OAAO,OAAO,WAAW,IAAI,GAAG;AAClC,mBAAa,UAAU,IAAI;AAAA,IAC7B,WAAW,OAAO,UAAU,aAAa,YAAY;AACnD,mBAAa,UAAU,SAAS,IAAI;AAAA,IACtC;AACA,iBAAa,OAAO,UAAU;AAC9B,QAAI,OAAO,SAAS,UAAU,GAAG;AAC/B,aAAO,GAAG,gBAAgB,UAAU;AAAA,IACtC;AAAA,EACF;AACA,SAAO,IAAI,WAAA;AACb;AAQO,MAAM,cAAc,CACzB,QACA,MAAe,OACF;AACb,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD;AACA,QAAM,EAAE,SAAS,GAAA,IAAO;AACxB,QAAM,+BAAe,IAAA;AACrB,MAAI,OAAO;AACX,QAAM,MAAgB,CAAA;AACtB,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAA;AACrB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IACjD;AACA,UAAM,CAAC,OAAO,IAAI,QAAQ,EAAE,IAAI;AAChC,YAAQ,MAAA;AAAA,MACN,KAAK,KAAK;AACR,YAAI,WAAW,YAAY,CAAC,SAAS,IAAI,IAAI,GAAG;AAC9C,cAAI,KAAK,KAAK;AAAA,QAChB,OAAO;AACL,gBAAM,gBAAgB,iBAAiB,OAAO,GAAG;AACjD,cAAI,SAAS,aAAa,GAAG;AAC3B,gBAAI,KAAK,aAAa;AAAA,UACxB,OAAO;AACL,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,KAAK;AAAA,MACL,KAAK,YAAY;AACf,YAAI,KAAK,KAAK;AACd;AACA,YAAI,kBAAkB,KAAK,KAAK,GAAG;AACjC,mBAAS,IAAI,IAAI;AAAA,QACnB;AACA;AAAA,MACF;AAAA,MACA,KAAK,aAAa;AAChB,YAAI,IAAI,QAAQ;AACd,gBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,cAAI,cAAc,KAAK;AACrB,gBAAI,OAAO,IAAI,GAAG,KAAK;AAAA,UACzB,OAAO;AACL,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF,OAAO;AACL,cAAI,KAAK,KAAK;AAAA,QAChB;AACA,YAAI,SAAS,IAAI,IAAI,GAAG;AACtB,mBAAS,OAAO,IAAI;AAAA,QACtB;AACA;AACA;AAAA,MACF;AAAA,MACA,KAAK,SAAS;AACZ,YAAI,IAAI,QAAQ;AACd,gBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,cACE,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AACA;AAAA,MACF;AAAA,MACA,SAAS;AACP,YAAI,SAAS,WAAW,SAAS,KAAK;AACpC,cAAI,KAAK,KAAK;AAAA,QAChB;AAAA,MACF;AAAA,IAAA;AAAA,EAEJ;AACA,SAAO;AACT;AAQO,MAAM,UAAU,CAAC,OAAe,MAAe,OAAe;AACnE,QAAM,EAAE,SAAS,GAAA,IAAO;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,WAAW,KAAK,KAAK,GAAG;AAC1B,UAAI,WAAW,UAAU;AACvB,eAAO;AAAA,MACT,OAAO;AACL,cAAMC,iBAAgB,WAAW,OAAO,GAAG;AAC3C,YAAI,SAASA,cAAa,GAAG;AAC3B,iBAAOA;AAAAA,QACT,OAAO;AACL,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF,WAAW,CAAC,YAAY,KAAK,KAAK,GAAG;AACnC,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,YAAA,EAAc,KAAA;AAAA,EAC9B,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IAAA;AAAA,IAEF;AAAA,EAAA;AAEF,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,SAAS,SAAS,EAAE,KAAK,OAAO;AACtC,QAAM,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,gBAAwB,KAAK,OAAO,KAAK,EAAE,GAAG;AAAA,IAChD,kBAAkB;AAAA,EAAA,CACnB;AACD,MAAI,iBAAiB,KAAK,KAAK,GAAG;AAChC,QAAI,iBAAiB,KAAK,aAAa,GAAG;AACxC,YAAM,GAAG,KAAK,IAAI,IAAI,cAAc;AAAA,QAClC;AAAA,MAAA;AAEF,sBAAgB,GAAG,iBAAiB,OAAO,GAAG,GAAG,GAAG,CAAC,GAAG,IAAI;AAAA,IAC9D;AAEA,QACE,iBACA,CAAC,iBAAiB,KAAK,aAAa,KACpC,WAAW,UACX;AACA,sBAAgB,QAAQ,aAAa;AAAA,IACvC;AAAA,EACF;AACA,MAAI,WAAW,UAAU;AACvB,QAAI,aAAa,KAAK,aAAa,KAAK,CAAC,cAAc,SAAS,KAAK,GAAG;AACtE,sBAAgB,cAAc,eAAe,GAAG;AAAA,IAClD,WAAW,gBAAgB,KAAK,aAAa,GAAG;AAC9C,YAAM,CAAA,EAAG,GAAG,IAAI,cAAc,MAAM,eAAe;AACnD,sBAAgB,QAAQ,iBAAiB,OAAO,GAAG,GAAG,GAAG,CAAC;AAAA,IAC5D;AAAA,EACF;AACA,WAAS,UAAU,aAAa;AAChC,SAAO;AACT;"}
{"version": 3, "file": "css-var.js", "sources": ["../../../src/js/css-var.ts"], "sourcesContent": ["/**\n * css-var\n */\n\nimport { CSSToken, TokenType, tokenize } from '@csstools/css-tokenizer';\nimport {\n  CacheItem,\n  NullObject,\n  createCacheKey,\n  getCache,\n  setCache\n} from './cache';\nimport { isString } from './common';\nimport { cssCalc } from './css-calc';\nimport { isColor } from './util';\nimport { Options } from './typedef';\n\n/* constants */\nimport { FN_VAR, SYN_FN_CALC, SYN_FN_VAR, VAL_SPEC } from './constant';\nconst {\n  CloseParen: PAREN_CLOSE,\n  Comment: COMMENT,\n  EOF,\n  Ident: IDENT,\n  Whitespace: W_SPACE\n} = TokenType;\nconst NAMESPACE = 'css-var';\n\n/* regexp */\nconst REG_FN_CALC = new RegExp(SYN_FN_CALC);\nconst REG_FN_VAR = new RegExp(SYN_FN_VAR);\n\n/**\n * resolve custom property\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns result - [tokens, resolvedValue]\n */\nexport function resolveCustomProperty(\n  tokens: CSSToken[],\n  opt: Options = {}\n): [CSSToken[], string] {\n  if (!Array.isArray(tokens)) {\n    throw new TypeError(`${tokens} is not an array.`);\n  }\n  const { customProperty = {} } = opt;\n  const items: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    if (!Array.isArray(token)) {\n      throw new TypeError(`${token} is not an array.`);\n    }\n    const [type, value] = token as [TokenType, string];\n    // end of var()\n    if (type === PAREN_CLOSE) {\n      break;\n    }\n    // nested var()\n    if (value === FN_VAR) {\n      const [restTokens, item] = resolveCustomProperty(tokens, opt);\n      tokens = restTokens;\n      if (item) {\n        items.push(item);\n      }\n    } else if (type === IDENT) {\n      if (value.startsWith('--')) {\n        let item;\n        if (Object.hasOwn(customProperty, value)) {\n          item = customProperty[value] as string;\n        } else if (typeof customProperty.callback === 'function') {\n          item = customProperty.callback(value);\n        }\n        if (item) {\n          items.push(item);\n        }\n      } else if (value) {\n        items.push(value);\n      }\n    }\n  }\n  let resolveAsColor = false;\n  if (items.length > 1) {\n    const lastValue = items[items.length - 1];\n    resolveAsColor = isColor(lastValue);\n  }\n  let resolvedValue = '';\n  for (let item of items) {\n    item = item.trim();\n    if (REG_FN_VAR.test(item)) {\n      // recurse resolveVar()\n      const resolvedItem = resolveVar(item, opt);\n      if (isString(resolvedItem)) {\n        if (resolveAsColor) {\n          if (isColor(resolvedItem)) {\n            resolvedValue = resolvedItem;\n          }\n        } else {\n          resolvedValue = resolvedItem;\n        }\n      }\n    } else if (REG_FN_CALC.test(item)) {\n      item = cssCalc(item, opt);\n      if (resolveAsColor) {\n        if (isColor(item)) {\n          resolvedValue = item;\n        }\n      } else {\n        resolvedValue = item;\n      }\n    } else if (\n      item &&\n      !/^(?:inherit|initial|revert(?:-layer)?|unset)$/.test(item)\n    ) {\n      if (resolveAsColor) {\n        if (isColor(item)) {\n          resolvedValue = item;\n        }\n      } else {\n        resolvedValue = item;\n      }\n    }\n    if (resolvedValue) {\n      break;\n    }\n  }\n  return [tokens, resolvedValue];\n}\n\n/**\n * parse tokens\n * @param tokens - CSS tokens\n * @param [opt] - options\n * @returns parsed tokens\n */\nexport function parseTokens(\n  tokens: CSSToken[],\n  opt: Options = {}\n): string[] | NullObject {\n  const res: string[] = [];\n  while (tokens.length) {\n    const token = tokens.shift();\n    const [type = '', value = ''] = token as [TokenType, string];\n    if (value === FN_VAR) {\n      const [restTokens, resolvedValue] = resolveCustomProperty(tokens, opt);\n      if (!resolvedValue) {\n        return new NullObject();\n      }\n      tokens = restTokens;\n      res.push(resolvedValue);\n    } else {\n      switch (type) {\n        case PAREN_CLOSE: {\n          if (res.length) {\n            const lastValue = res[res.length - 1];\n            if (lastValue === ' ') {\n              res.splice(-1, 1, value);\n            } else {\n              res.push(value);\n            }\n          } else {\n            res.push(value);\n          }\n          break;\n        }\n        case W_SPACE: {\n          if (res.length) {\n            const lastValue = res[res.length - 1];\n            if (\n              isString(lastValue) &&\n              !lastValue.endsWith('(') &&\n              lastValue !== ' '\n            ) {\n              res.push(value);\n            }\n          }\n          break;\n        }\n        default: {\n          if (type !== COMMENT && type !== EOF) {\n            res.push(value);\n          }\n        }\n      }\n    }\n  }\n  return res;\n}\n\n/**\n * resolve CSS var()\n * @param value - CSS value including var()\n * @param [opt] - options\n * @returns resolved value\n */\nexport function resolveVar(\n  value: string,\n  opt: Options = {}\n): string | NullObject {\n  const { format = '' } = opt;\n  if (isString(value)) {\n    if (!REG_FN_VAR.test(value) || format === VAL_SPEC) {\n      return value;\n    }\n    value = value.trim();\n  } else {\n    throw new TypeError(`${value} is not a string.`);\n  }\n  const cacheKey: string = createCacheKey(\n    {\n      namespace: NAMESPACE,\n      name: 'resolveVar',\n      value\n    },\n    opt\n  );\n  const cachedResult = getCache(cacheKey);\n  if (cachedResult instanceof CacheItem) {\n    if (cachedResult.isNull) {\n      return cachedResult as NullObject;\n    }\n    return cachedResult.item as string;\n  }\n  const tokens = tokenize({ css: value });\n  const values = parseTokens(tokens, opt);\n  if (Array.isArray(values)) {\n    let color = values.join('');\n    if (REG_FN_CALC.test(color)) {\n      color = cssCalc(color, opt);\n    }\n    setCache(cacheKey, color);\n    return color;\n  } else {\n    setCache(cacheKey, null);\n    return new NullObject();\n  }\n}\n\n/**\n * CSS var()\n * @param value - CSS value including var()\n * @param [opt] - options\n * @returns resolved value\n */\nexport const cssVar = (value: string, opt: Options = {}): string => {\n  const resolvedValue = resolveVar(value, opt);\n  if (isString(resolvedValue)) {\n    return resolvedValue;\n  }\n  return '';\n};\n"], "names": [], "mappings": ";;;;;;AAmBA,MAAM;AAAA,EACJ,YAAY;AAAA,EACZ,SAAS;AAAA,EACT;AAAA,EACA,OAAO;AAAA,EACP,YAAY;AACd,IAAI;AACJ,MAAM,YAAY;AAGlB,MAAM,cAAc,IAAI,OAAO,WAAW;AAC1C,MAAM,aAAa,IAAI,OAAO,UAAU;AAQjC,SAAS,sBACd,QACA,MAAe,IACO;AACtB,MAAI,CAAC,MAAM,QAAQ,MAAM,GAAG;AAC1B,UAAM,IAAI,UAAU,GAAG,MAAM,mBAAmB;AAAA,EAClD;AACA,QAAM,EAAE,iBAAiB,CAAA,EAAC,IAAM;AAChC,QAAM,QAAkB,CAAA;AACxB,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAA;AACrB,QAAI,CAAC,MAAM,QAAQ,KAAK,GAAG;AACzB,YAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,IACjD;AACA,UAAM,CAAC,MAAM,KAAK,IAAI;AAEtB,QAAI,SAAS,aAAa;AACxB;AAAA,IACF;AAEA,QAAI,UAAU,QAAQ;AACpB,YAAM,CAAC,YAAY,IAAI,IAAI,sBAAsB,QAAQ,GAAG;AAC5D,eAAS;AACT,UAAI,MAAM;AACR,cAAM,KAAK,IAAI;AAAA,MACjB;AAAA,IACF,WAAW,SAAS,OAAO;AACzB,UAAI,MAAM,WAAW,IAAI,GAAG;AAC1B,YAAI;AACJ,YAAI,OAAO,OAAO,gBAAgB,KAAK,GAAG;AACxC,iBAAO,eAAe,KAAK;AAAA,QAC7B,WAAW,OAAO,eAAe,aAAa,YAAY;AACxD,iBAAO,eAAe,SAAS,KAAK;AAAA,QACtC;AACA,YAAI,MAAM;AACR,gBAAM,KAAK,IAAI;AAAA,QACjB;AAAA,MACF,WAAW,OAAO;AAChB,cAAM,KAAK,KAAK;AAAA,MAClB;AAAA,IACF;AAAA,EACF;AACA,MAAI,iBAAiB;AACrB,MAAI,MAAM,SAAS,GAAG;AACpB,UAAM,YAAY,MAAM,MAAM,SAAS,CAAC;AACxC,qBAAiB,QAAQ,SAAS;AAAA,EACpC;AACA,MAAI,gBAAgB;AACpB,WAAS,QAAQ,OAAO;AACtB,WAAO,KAAK,KAAA;AACZ,QAAI,WAAW,KAAK,IAAI,GAAG;AAEzB,YAAM,eAAe,WAAW,MAAM,GAAG;AACzC,UAAI,SAAS,YAAY,GAAG;AAC1B,YAAI,gBAAgB;AAClB,cAAI,QAAQ,YAAY,GAAG;AACzB,4BAAgB;AAAA,UAClB;AAAA,QACF,OAAO;AACL,0BAAgB;AAAA,QAClB;AAAA,MACF;AAAA,IACF,WAAW,YAAY,KAAK,IAAI,GAAG;AACjC,aAAO,QAAQ,MAAM,GAAG;AACxB,UAAI,gBAAgB;AAClB,YAAI,QAAQ,IAAI,GAAG;AACjB,0BAAgB;AAAA,QAClB;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF,WACE,QACA,CAAC,gDAAgD,KAAK,IAAI,GAC1D;AACA,UAAI,gBAAgB;AAClB,YAAI,QAAQ,IAAI,GAAG;AACjB,0BAAgB;AAAA,QAClB;AAAA,MACF,OAAO;AACL,wBAAgB;AAAA,MAClB;AAAA,IACF;AACA,QAAI,eAAe;AACjB;AAAA,IACF;AAAA,EACF;AACA,SAAO,CAAC,QAAQ,aAAa;AAC/B;AAQO,SAAS,YACd,QACA,MAAe,IACQ;AACvB,QAAM,MAAgB,CAAA;AACtB,SAAO,OAAO,QAAQ;AACpB,UAAM,QAAQ,OAAO,MAAA;AACrB,UAAM,CAAC,OAAO,IAAI,QAAQ,EAAE,IAAI;AAChC,QAAI,UAAU,QAAQ;AACpB,YAAM,CAAC,YAAY,aAAa,IAAI,sBAAsB,QAAQ,GAAG;AACrE,UAAI,CAAC,eAAe;AAClB,eAAO,IAAI,WAAA;AAAA,MACb;AACA,eAAS;AACT,UAAI,KAAK,aAAa;AAAA,IACxB,OAAO;AACL,cAAQ,MAAA;AAAA,QACN,KAAK,aAAa;AAChB,cAAI,IAAI,QAAQ;AACd,kBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,gBAAI,cAAc,KAAK;AACrB,kBAAI,OAAO,IAAI,GAAG,KAAK;AAAA,YACzB,OAAO;AACL,kBAAI,KAAK,KAAK;AAAA,YAChB;AAAA,UACF,OAAO;AACL,gBAAI,KAAK,KAAK;AAAA,UAChB;AACA;AAAA,QACF;AAAA,QACA,KAAK,SAAS;AACZ,cAAI,IAAI,QAAQ;AACd,kBAAM,YAAY,IAAI,IAAI,SAAS,CAAC;AACpC,gBACE,SAAS,SAAS,KAClB,CAAC,UAAU,SAAS,GAAG,KACvB,cAAc,KACd;AACA,kBAAI,KAAK,KAAK;AAAA,YAChB;AAAA,UACF;AACA;AAAA,QACF;AAAA,QACA,SAAS;AACP,cAAI,SAAS,WAAW,SAAS,KAAK;AACpC,gBAAI,KAAK,KAAK;AAAA,UAChB;AAAA,QACF;AAAA,MAAA;AAAA,IAEJ;AAAA,EACF;AACA,SAAO;AACT;AAQO,SAAS,WACd,OACA,MAAe,IACM;AACrB,QAAM,EAAE,SAAS,GAAA,IAAO;AACxB,MAAI,SAAS,KAAK,GAAG;AACnB,QAAI,CAAC,WAAW,KAAK,KAAK,KAAK,WAAW,UAAU;AAClD,aAAO;AAAA,IACT;AACA,YAAQ,MAAM,KAAA;AAAA,EAChB,OAAO;AACL,UAAM,IAAI,UAAU,GAAG,KAAK,mBAAmB;AAAA,EACjD;AACA,QAAM,WAAmB;AAAA,IACvB;AAAA,MACE,WAAW;AAAA,MACX,MAAM;AAAA,MACN;AAAA,IAAA;AAAA,IAEF;AAAA,EAAA;AAEF,QAAM,eAAe,SAAS,QAAQ;AACtC,MAAI,wBAAwB,WAAW;AACrC,QAAI,aAAa,QAAQ;AACvB,aAAO;AAAA,IACT;AACA,WAAO,aAAa;AAAA,EACtB;AACA,QAAM,SAAS,SAAS,EAAE,KAAK,OAAO;AACtC,QAAM,SAAS,YAAY,QAAQ,GAAG;AACtC,MAAI,MAAM,QAAQ,MAAM,GAAG;AACzB,QAAI,QAAQ,OAAO,KAAK,EAAE;AAC1B,QAAI,YAAY,KAAK,KAAK,GAAG;AAC3B,cAAQ,QAAQ,OAAO,GAAG;AAAA,IAC5B;AACA,aAAS,UAAU,KAAK;AACxB,WAAO;AAAA,EACT,OAAO;AACL,aAAS,UAAU,IAAI;AACvB,WAAO,IAAI,WAAA;AAAA,EACb;AACF;AAQO,MAAM,SAAS,CAAC,OAAe,MAAe,OAAe;AAClE,QAAM,gBAAgB,WAAW,OAAO,GAAG;AAC3C,MAAI,SAAS,aAAa,GAAG;AAC3B,WAAO;AAAA,EACT;AACA,SAAO;AACT;"}
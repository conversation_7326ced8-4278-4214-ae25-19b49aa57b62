{"version": 3, "file": "css-gradient.js", "sources": ["../../../src/js/css-gradient.ts"], "sourcesContent": ["/**\n * css-gradient\n */\n\nimport { CacheItem, createCache<PERSON><PERSON>, getCache, setCache } from './cache';\nimport { resolveColor } from './resolve';\nimport { isString } from './common';\nimport { MatchedRegExp, Options } from './typedef';\nimport { isColor, splitValue } from './util';\n\n/* constants */\nimport {\n  ANGLE,\n  CS_HUE,\n  CS_RECT,\n  LENGTH,\n  NUM,\n  NUM_POSITIVE,\n  PCT,\n  VAL_COMP,\n  VAL_SPEC\n} from './constant';\nconst NAMESPACE = 'css-gradient';\nconst DIM_ANGLE = `${NUM}(?:${ANGLE})`;\nconst DIM_ANGLE_PCT = `${DIM_ANGLE}|${PCT}`;\nconst DIM_LEN = `${NUM}(?:${LENGTH})|0`;\nconst DIM_LEN_PCT = `${DIM_LEN}|${PCT}`;\nconst DIM_LEN_PCT_POSI = `${NUM_POSITIVE}(?:${LENGTH}|%)|0`;\nconst DIM_LEN_POSI = `${NUM_POSITIVE}(?:${LENGTH})|0`;\nconst CTR = 'center';\nconst L_R = 'left|right';\nconst T_B = 'top|bottom';\nconst S_E = 'start|end';\nconst AXIS_X = `${L_R}|x-(?:${S_E})`;\nconst AXIS_Y = `${T_B}|y-(?:${S_E})`;\nconst BLOCK = `block-(?:${S_E})`;\nconst INLINE = `inline-(?:${S_E})`;\nconst POS_1 = `${CTR}|${AXIS_X}|${AXIS_Y}|${BLOCK}|${INLINE}|${DIM_LEN_PCT}`;\nconst POS_2 = [\n  `(?:${CTR}|${AXIS_X})\\\\s+(?:${CTR}|${AXIS_Y})`,\n  `(?:${CTR}|${AXIS_Y})\\\\s+(?:${CTR}|${AXIS_X})`,\n  `(?:${CTR}|${AXIS_X}|${DIM_LEN_PCT})\\\\s+(?:${CTR}|${AXIS_Y}|${DIM_LEN_PCT})`,\n  `(?:${CTR}|${BLOCK})\\\\s+(?:${CTR}|${INLINE})`,\n  `(?:${CTR}|${INLINE})\\\\s+(?:${CTR}|${BLOCK})`,\n  `(?:${CTR}|${S_E})\\\\s+(?:${CTR}|${S_E})`\n].join('|');\nconst POS_4 = [\n  `(?:${AXIS_X})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${AXIS_Y})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${AXIS_Y})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${AXIS_X})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${BLOCK})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${INLINE})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${INLINE})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${BLOCK})\\\\s+(?:${DIM_LEN_PCT})`,\n  `(?:${S_E})\\\\s+(?:${DIM_LEN_PCT})\\\\s+(?:${S_E})\\\\s+(?:${DIM_LEN_PCT})`\n].join('|');\nconst RAD_EXTENT = '(?:clos|farth)est-(?:corner|side)';\nconst RAD_SIZE = [\n  `${RAD_EXTENT}(?:\\\\s+${RAD_EXTENT})?`,\n  `${DIM_LEN_POSI}`,\n  `(?:${DIM_LEN_PCT_POSI})\\\\s+(?:${DIM_LEN_PCT_POSI})`\n].join('|');\nconst RAD_SHAPE = 'circle|ellipse';\nconst FROM_ANGLE = `from\\\\s+${DIM_ANGLE}`;\nconst AT_POSITION = `at\\\\s+(?:${POS_1}|${POS_2}|${POS_4})`;\nconst TO_SIDE_CORNER = `to\\\\s+(?:(?:${L_R})(?:\\\\s(?:${T_B}))?|(?:${T_B})(?:\\\\s(?:${L_R}))?)`;\nconst IN_COLOR_SPACE = `in\\\\s+(?:${CS_RECT}|${CS_HUE})`;\n\n/* type definitions */\n/**\n * @type ColorStopList - list of color stops\n */\ntype ColorStopList = [string, string, ...string[]];\n\n/**\n * @typedef ValidateGradientLine - validate gradient line\n * @property line - gradient line\n * @property valid - result\n */\ninterface ValidateGradientLine {\n  line: string;\n  valid: boolean;\n}\n\n/**\n * @typedef ValidateColorStops - validate color stops\n * @property colorStops - list of color stops\n * @property valid - result\n */\ninterface ValidateColorStops {\n  colorStops: string[];\n  valid: boolean;\n}\n\n/**\n * @typedef Gradient - parsed CSS gradient\n * @property value - input value\n * @property type - gradient type\n * @property [gradientLine] - gradient line\n * @property colorStopList - list of color stops\n */\ninterface Gradient {\n  value: string;\n  type: string;\n  gradientLine?: string;\n  colorStopList: ColorStopList;\n}\n\n/* regexp */\nconst REG_GRAD = /^(?:repeating-)?(?:conic|linear|radial)-gradient\\(/;\nconst REG_GRAD_CAPT = /^((?:repeating-)?(?:conic|linear|radial)-gradient)\\(/;\n\n/**\n * get gradient type\n * @param value - gradient value\n * @returns gradient type\n */\nexport const getGradientType = (value: string): string => {\n  if (isString(value)) {\n    value = value.trim();\n    if (REG_GRAD.test(value)) {\n      const [, type] = value.match(REG_GRAD_CAPT) as MatchedRegExp;\n      return type;\n    }\n  }\n  return '';\n};\n\n/**\n * validate gradient line\n * @param value - gradient line value\n * @param type - gradient type\n * @returns result\n */\nexport const validateGradientLine = (\n  value: string,\n  type: string\n): ValidateGradientLine => {\n  if (isString(value) && isString(type)) {\n    value = value.trim();\n    type = type.trim();\n    let lineSyntax = '';\n    const defaultValues = [];\n    if (/^(?:repeating-)?linear-gradient$/.test(type)) {\n      /*\n       * <linear-gradient-line> = [\n       *   [ <angle> | to <side-or-corner> ] ||\n       *   <color-interpolation-method>\n       * ]\n       */\n      lineSyntax = [\n        `(?:${DIM_ANGLE}|${TO_SIDE_CORNER})(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+(?:${DIM_ANGLE}|${TO_SIDE_CORNER}))?`\n      ].join('|');\n      defaultValues.push(/to\\s+bottom/);\n    } else if (/^(?:repeating-)?radial-gradient$/.test(type)) {\n      /*\n       * <radial-gradient-line> = [\n       *   [ [ <radial-shape> || <radial-size> ]? [ at <position> ]? ] ||\n       *   <color-interpolation-method>]?\n       */\n      lineSyntax = [\n        `(?:${RAD_SHAPE})(?:\\\\s+(?:${RAD_SIZE}))?(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `(?:${RAD_SIZE})(?:\\\\s+(?:${RAD_SHAPE}))?(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${AT_POSITION}(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${RAD_SHAPE})(?:\\\\s+(?:${RAD_SIZE}))?(?:\\\\s+${AT_POSITION})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${RAD_SIZE})(?:\\\\s+(?:${RAD_SHAPE}))?(?:\\\\s+${AT_POSITION})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${AT_POSITION})?`\n      ].join('|');\n      defaultValues.push(/ellipse/, /farthest-corner/, /at\\s+center/);\n    } else if (/^(?:repeating-)?conic-gradient$/.test(type)) {\n      /*\n       * <conic-gradient-line> = [\n       *   [ [ from <angle> ]? [ at <position> ]? ] ||\n       *   <color-interpolation-method>\n       * ]\n       */\n      lineSyntax = [\n        `${FROM_ANGLE}(?:\\\\s+${AT_POSITION})?(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${AT_POSITION}(?:\\\\s+${IN_COLOR_SPACE})?`,\n        `${IN_COLOR_SPACE}(?:\\\\s+${FROM_ANGLE})?(?:\\\\s+${AT_POSITION})?`\n      ].join('|');\n      defaultValues.push(/at\\s+center/);\n    }\n    if (lineSyntax) {\n      const reg = new RegExp(`^(?:${lineSyntax})$`);\n      const valid = reg.test(value);\n      if (valid) {\n        let line = value;\n        for (const defaultValue of defaultValues) {\n          line = line.replace(defaultValue, '');\n        }\n        line = line.replace(/\\s{2,}/g, ' ').trim();\n        return {\n          line,\n          valid\n        };\n      }\n      return {\n        valid,\n        line: value\n      };\n    }\n  }\n  return {\n    line: value,\n    valid: false\n  };\n};\n\n/**\n * validate color stop list\n * @param list\n * @param type\n * @param [opt]\n * @returns result\n */\nexport const validateColorStopList = (\n  list: string[],\n  type: string,\n  opt: Options = {}\n): ValidateColorStops => {\n  if (Array.isArray(list) && list.length > 1) {\n    const dimension = /^(?:repeating-)?conic-gradient$/.test(type)\n      ? DIM_ANGLE_PCT\n      : DIM_LEN_PCT;\n    const regColorHint = new RegExp(`^(?:${dimension})$`);\n    const regDimension = new RegExp(`(?:\\\\s+(?:${dimension})){1,2}$`);\n    const valueTypes = [];\n    const valueList = [];\n    for (const item of list) {\n      if (isString(item)) {\n        if (regColorHint.test(item)) {\n          valueTypes.push('hint');\n          valueList.push(item);\n        } else {\n          const itemColor = item.replace(regDimension, '');\n          if (isColor(itemColor, { format: VAL_SPEC })) {\n            const resolvedColor = resolveColor(itemColor, opt) as string;\n            valueTypes.push('color');\n            valueList.push(item.replace(itemColor, resolvedColor));\n          } else {\n            return {\n              colorStops: list,\n              valid: false\n            };\n          }\n        }\n      }\n    }\n    const valid = /^color(?:,(?:hint,)?color)+$/.test(valueTypes.join(','));\n    return {\n      valid,\n      colorStops: valueList\n    };\n  }\n  return {\n    colorStops: list,\n    valid: false\n  };\n};\n\n/**\n * parse CSS gradient\n * @param value - gradient value\n * @param [opt] - options\n * @returns parsed result\n */\nexport const parseGradient = (\n  value: string,\n  opt: Options = {}\n): Gradient | null => {\n  if (isString(value)) {\n    value = value.trim();\n    const cacheKey: string = createCacheKey(\n      {\n        namespace: NAMESPACE,\n        name: 'parseGradient',\n        value\n      },\n      opt\n    );\n    const cachedResult = getCache(cacheKey);\n    if (cachedResult instanceof CacheItem) {\n      if (cachedResult.isNull) {\n        return null;\n      }\n      return cachedResult.item as Gradient;\n    }\n    const type = getGradientType(value);\n    const gradValue = value.replace(REG_GRAD, '').replace(/\\)$/, '');\n    if (type && gradValue) {\n      const [lineOrColorStop = '', ...itemList] = splitValue(gradValue, {\n        delimiter: ','\n      });\n      const dimension = /^(?:repeating-)?conic-gradient$/.test(type)\n        ? DIM_ANGLE_PCT\n        : DIM_LEN_PCT;\n      const regDimension = new RegExp(`(?:\\\\s+(?:${dimension})){1,2}$`);\n      let colorStop = '';\n      if (regDimension.test(lineOrColorStop)) {\n        const itemColor = lineOrColorStop.replace(regDimension, '');\n        if (isColor(itemColor, { format: VAL_SPEC })) {\n          const resolvedColor = resolveColor(itemColor, opt) as string;\n          colorStop = lineOrColorStop.replace(itemColor, resolvedColor);\n        }\n      } else if (isColor(lineOrColorStop, { format: VAL_SPEC })) {\n        colorStop = resolveColor(lineOrColorStop, opt) as string;\n      }\n      if (colorStop) {\n        itemList.unshift(colorStop);\n        const { colorStops, valid } = validateColorStopList(\n          itemList,\n          type,\n          opt\n        );\n        if (valid) {\n          const res: Gradient = {\n            value,\n            type,\n            colorStopList: colorStops as ColorStopList\n          };\n          setCache(cacheKey, res);\n          return res;\n        }\n      } else if (itemList.length > 1) {\n        const { line: gradientLine, valid: validLine } = validateGradientLine(\n          lineOrColorStop,\n          type\n        );\n        const { colorStops, valid: validColorStops } = validateColorStopList(\n          itemList,\n          type,\n          opt\n        );\n        if (validLine && validColorStops) {\n          const res: Gradient = {\n            value,\n            type,\n            gradientLine,\n            colorStopList: colorStops as ColorStopList\n          };\n          setCache(cacheKey, res);\n          return res;\n        }\n      }\n    }\n    setCache(cacheKey, null);\n    return null;\n  }\n  return null;\n};\n\n/**\n * resolve CSS gradient\n * @param value - CSS value\n * @param [opt] - options\n * @returns result\n */\nexport const resolveGradient = (value: string, opt: Options = {}): string => {\n  const { format = VAL_COMP } = opt;\n  const gradient = parseGradient(value, opt);\n  if (gradient) {\n    const { type = '', gradientLine = '', colorStopList = [] } = gradient;\n    if (type && Array.isArray(colorStopList) && colorStopList.length > 1) {\n      if (gradientLine) {\n        return `${type}(${gradientLine}, ${colorStopList.join(', ')})`;\n      }\n      return `${type}(${colorStopList.join(', ')})`;\n    }\n  }\n  if (format === VAL_SPEC) {\n    return '';\n  }\n  return 'none';\n};\n\n/**\n * is CSS gradient\n * @param value - CSS value\n * @param [opt] - options\n * @returns result\n */\nexport const isGradient = (value: string, opt: Options = {}): boolean => {\n  const gradient = parseGradient(value, opt);\n  return gradient !== null;\n};\n"], "names": [], "mappings": ";;;;;AAsBA,MAAM,YAAY;AAClB,MAAM,YAAY,GAAG,GAAG,MAAM,KAAK;AACnC,MAAM,gBAAgB,GAAG,SAAS,IAAI,GAAG;AACzC,MAAM,UAAU,GAAG,GAAG,MAAM,MAAM;AAClC,MAAM,cAAc,GAAG,OAAO,IAAI,GAAG;AACrC,MAAM,mBAAmB,GAAG,YAAY,MAAM,MAAM;AACpD,MAAM,eAAe,GAAG,YAAY,MAAM,MAAM;AAChD,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,MAAM;AACZ,MAAM,SAAS,GAAG,GAAG,SAAS,GAAG;AACjC,MAAM,SAAS,GAAG,GAAG,SAAS,GAAG;AACjC,MAAM,QAAQ,YAAY,GAAG;AAC7B,MAAM,SAAS,aAAa,GAAG;AAC/B,MAAM,QAAQ,GAAG,GAAG,IAAI,MAAM,IAAI,MAAM,IAAI,KAAK,IAAI,MAAM,IAAI,WAAW;AAC1E,MAAM,QAAQ;AAAA,EACZ,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM;AAAA,EAC3C,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,MAAM;AAAA,EAC3C,MAAM,GAAG,IAAI,MAAM,IAAI,WAAW,WAAW,GAAG,IAAI,MAAM,IAAI,WAAW;AAAA,EACzE,MAAM,GAAG,IAAI,KAAK,WAAW,GAAG,IAAI,MAAM;AAAA,EAC1C,MAAM,GAAG,IAAI,MAAM,WAAW,GAAG,IAAI,KAAK;AAAA,EAC1C,MAAM,GAAG,IAAI,GAAG,WAAW,GAAG,IAAI,GAAG;AACvC,EAAE,KAAK,GAAG;AACV,MAAM,QAAQ;AAAA,EACZ,MAAM,MAAM,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACzE,MAAM,MAAM,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACzE,MAAM,KAAK,WAAW,WAAW,WAAW,MAAM,WAAW,WAAW;AAAA,EACxE,MAAM,MAAM,WAAW,WAAW,WAAW,KAAK,WAAW,WAAW;AAAA,EACxE,MAAM,GAAG,WAAW,WAAW,WAAW,GAAG,WAAW,WAAW;AACrE,EAAE,KAAK,GAAG;AACV,MAAM,aAAa;AACnB,MAAM,WAAW;AAAA,EACf,GAAG,UAAU,UAAU,UAAU;AAAA,EACjC,GAAG,YAAY;AAAA,EACf,MAAM,gBAAgB,WAAW,gBAAgB;AACnD,EAAE,KAAK,GAAG;AACV,MAAM,YAAY;AAClB,MAAM,aAAa,WAAW,SAAS;AACvC,MAAM,cAAc,YAAY,KAAK,IAAI,KAAK,IAAI,KAAK;AACvD,MAAM,iBAAiB,eAAe,GAAG,aAAa,GAAG,UAAU,GAAG,aAAa,GAAG;AACtF,MAAM,iBAAiB,YAAY,OAAO,IAAI,MAAM;AA2CpD,MAAM,WAAW;AACjB,MAAM,gBAAgB;AAOf,MAAM,kBAAkB,CAAC,UAA0B;AACxD,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAA;AACd,QAAI,SAAS,KAAK,KAAK,GAAG;AACxB,YAAM,CAAA,EAAG,IAAI,IAAI,MAAM,MAAM,aAAa;AAC1C,aAAO;AAAA,IACT;AAAA,EACF;AACA,SAAO;AACT;AAQO,MAAM,uBAAuB,CAClC,OACA,SACyB;AACzB,MAAI,SAAS,KAAK,KAAK,SAAS,IAAI,GAAG;AACrC,YAAQ,MAAM,KAAA;AACd,WAAO,KAAK,KAAA;AACZ,QAAI,aAAa;AACjB,UAAM,gBAAgB,CAAA;AACtB,QAAI,mCAAmC,KAAK,IAAI,GAAG;AAOjD,mBAAa;AAAA,QACX,MAAM,SAAS,IAAI,cAAc,WAAW,cAAc;AAAA,QAC1D,GAAG,cAAc,aAAa,SAAS,IAAI,cAAc;AAAA,MAAA,EACzD,KAAK,GAAG;AACV,oBAAc,KAAK,aAAa;AAAA,IAClC,WAAW,mCAAmC,KAAK,IAAI,GAAG;AAMxD,mBAAa;AAAA,QACX,MAAM,SAAS,cAAc,QAAQ,aAAa,WAAW,YAAY,cAAc;AAAA,QACvF,MAAM,QAAQ,cAAc,SAAS,aAAa,WAAW,YAAY,cAAc;AAAA,QACvF,GAAG,WAAW,UAAU,cAAc;AAAA,QACtC,GAAG,cAAc,UAAU,SAAS,cAAc,QAAQ,aAAa,WAAW;AAAA,QAClF,GAAG,cAAc,UAAU,QAAQ,cAAc,SAAS,aAAa,WAAW;AAAA,QAClF,GAAG,cAAc,UAAU,WAAW;AAAA,MAAA,EACtC,KAAK,GAAG;AACV,oBAAc,KAAK,WAAW,mBAAmB,aAAa;AAAA,IAChE,WAAW,kCAAkC,KAAK,IAAI,GAAG;AAOvD,mBAAa;AAAA,QACX,GAAG,UAAU,UAAU,WAAW,YAAY,cAAc;AAAA,QAC5D,GAAG,WAAW,UAAU,cAAc;AAAA,QACtC,GAAG,cAAc,UAAU,UAAU,YAAY,WAAW;AAAA,MAAA,EAC5D,KAAK,GAAG;AACV,oBAAc,KAAK,aAAa;AAAA,IAClC;AACA,QAAI,YAAY;AACd,YAAM,MAAM,IAAI,OAAO,OAAO,UAAU,IAAI;AAC5C,YAAM,QAAQ,IAAI,KAAK,KAAK;AAC5B,UAAI,OAAO;AACT,YAAI,OAAO;AACX,mBAAW,gBAAgB,eAAe;AACxC,iBAAO,KAAK,QAAQ,cAAc,EAAE;AAAA,QACtC;AACA,eAAO,KAAK,QAAQ,WAAW,GAAG,EAAE,KAAA;AACpC,eAAO;AAAA,UACL;AAAA,UACA;AAAA,QAAA;AAAA,MAEJ;AACA,aAAO;AAAA,QACL;AAAA,QACA,MAAM;AAAA,MAAA;AAAA,IAEV;AAAA,EACF;AACA,SAAO;AAAA,IACL,MAAM;AAAA,IACN,OAAO;AAAA,EAAA;AAEX;AASO,MAAM,wBAAwB,CACnC,MACA,MACA,MAAe,CAAA,MACQ;AACvB,MAAI,MAAM,QAAQ,IAAI,KAAK,KAAK,SAAS,GAAG;AAC1C,UAAM,YAAY,kCAAkC,KAAK,IAAI,IACzD,gBACA;AACJ,UAAM,eAAe,IAAI,OAAO,OAAO,SAAS,IAAI;AACpD,UAAM,eAAe,IAAI,OAAO,aAAa,SAAS,UAAU;AAChE,UAAM,aAAa,CAAA;AACnB,UAAM,YAAY,CAAA;AAClB,eAAW,QAAQ,MAAM;AACvB,UAAI,SAAS,IAAI,GAAG;AAClB,YAAI,aAAa,KAAK,IAAI,GAAG;AAC3B,qBAAW,KAAK,MAAM;AACtB,oBAAU,KAAK,IAAI;AAAA,QACrB,OAAO;AACL,gBAAM,YAAY,KAAK,QAAQ,cAAc,EAAE;AAC/C,cAAI,QAAQ,WAAW,EAAE,QAAQ,SAAA,CAAU,GAAG;AAC5C,kBAAM,gBAAgB,aAAa,WAAW,GAAG;AACjD,uBAAW,KAAK,OAAO;AACvB,sBAAU,KAAK,KAAK,QAAQ,WAAW,aAAa,CAAC;AAAA,UACvD,OAAO;AACL,mBAAO;AAAA,cACL,YAAY;AAAA,cACZ,OAAO;AAAA,YAAA;AAAA,UAEX;AAAA,QACF;AAAA,MACF;AAAA,IACF;AACA,UAAM,QAAQ,+BAA+B,KAAK,WAAW,KAAK,GAAG,CAAC;AACtE,WAAO;AAAA,MACL;AAAA,MACA,YAAY;AAAA,IAAA;AAAA,EAEhB;AACA,SAAO;AAAA,IACL,YAAY;AAAA,IACZ,OAAO;AAAA,EAAA;AAEX;AAQO,MAAM,gBAAgB,CAC3B,OACA,MAAe,OACK;AACpB,MAAI,SAAS,KAAK,GAAG;AACnB,YAAQ,MAAM,KAAA;AACd,UAAM,WAAmB;AAAA,MACvB;AAAA,QACE,WAAW;AAAA,QACX,MAAM;AAAA,QACN;AAAA,MAAA;AAAA,MAEF;AAAA,IAAA;AAEF,UAAM,eAAe,SAAS,QAAQ;AACtC,QAAI,wBAAwB,WAAW;AACrC,UAAI,aAAa,QAAQ;AACvB,eAAO;AAAA,MACT;AACA,aAAO,aAAa;AAAA,IACtB;AACA,UAAM,OAAO,gBAAgB,KAAK;AAClC,UAAM,YAAY,MAAM,QAAQ,UAAU,EAAE,EAAE,QAAQ,OAAO,EAAE;AAC/D,QAAI,QAAQ,WAAW;AACrB,YAAM,CAAC,kBAAkB,IAAI,GAAG,QAAQ,IAAI,WAAW,WAAW;AAAA,QAChE,WAAW;AAAA,MAAA,CACZ;AACD,YAAM,YAAY,kCAAkC,KAAK,IAAI,IACzD,gBACA;AACJ,YAAM,eAAe,IAAI,OAAO,aAAa,SAAS,UAAU;AAChE,UAAI,YAAY;AAChB,UAAI,aAAa,KAAK,eAAe,GAAG;AACtC,cAAM,YAAY,gBAAgB,QAAQ,cAAc,EAAE;AAC1D,YAAI,QAAQ,WAAW,EAAE,QAAQ,SAAA,CAAU,GAAG;AAC5C,gBAAM,gBAAgB,aAAa,WAAW,GAAG;AACjD,sBAAY,gBAAgB,QAAQ,WAAW,aAAa;AAAA,QAC9D;AAAA,MACF,WAAW,QAAQ,iBAAiB,EAAE,QAAQ,SAAA,CAAU,GAAG;AACzD,oBAAY,aAAa,iBAAiB,GAAG;AAAA,MAC/C;AACA,UAAI,WAAW;AACb,iBAAS,QAAQ,SAAS;AAC1B,cAAM,EAAE,YAAY,MAAA,IAAU;AAAA,UAC5B;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAEF,YAAI,OAAO;AACT,gBAAM,MAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UAAA;AAEjB,mBAAS,UAAU,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF,WAAW,SAAS,SAAS,GAAG;AAC9B,cAAM,EAAE,MAAM,cAAc,OAAO,cAAc;AAAA,UAC/C;AAAA,UACA;AAAA,QAAA;AAEF,cAAM,EAAE,YAAY,OAAO,gBAAA,IAAoB;AAAA,UAC7C;AAAA,UACA;AAAA,UACA;AAAA,QAAA;AAEF,YAAI,aAAa,iBAAiB;AAChC,gBAAM,MAAgB;AAAA,YACpB;AAAA,YACA;AAAA,YACA;AAAA,YACA,eAAe;AAAA,UAAA;AAEjB,mBAAS,UAAU,GAAG;AACtB,iBAAO;AAAA,QACT;AAAA,MACF;AAAA,IACF;AACA,aAAS,UAAU,IAAI;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,MAAM,kBAAkB,CAAC,OAAe,MAAe,OAAe;AAC3E,QAAM,EAAE,SAAS,SAAA,IAAa;AAC9B,QAAM,WAAW,cAAc,OAAO,GAAG;AACzC,MAAI,UAAU;AACZ,UAAM,EAAE,OAAO,IAAI,eAAe,IAAI,gBAAgB,CAAA,MAAO;AAC7D,QAAI,QAAQ,MAAM,QAAQ,aAAa,KAAK,cAAc,SAAS,GAAG;AACpE,UAAI,cAAc;AAChB,eAAO,GAAG,IAAI,IAAI,YAAY,KAAK,cAAc,KAAK,IAAI,CAAC;AAAA,MAC7D;AACA,aAAO,GAAG,IAAI,IAAI,cAAc,KAAK,IAAI,CAAC;AAAA,IAC5C;AAAA,EACF;AACA,MAAI,WAAW,UAAU;AACvB,WAAO;AAAA,EACT;AACA,SAAO;AACT;AAQO,MAAM,aAAa,CAAC,OAAe,MAAe,OAAgB;AACvE,QAAM,WAAW,cAAc,OAAO,GAAG;AACzC,SAAO,aAAa;AACtB;"}
/** Symbol key applied to transform types */
export const TransformKind = Symbol.for('TypeBox.Transform');
/** Symbol key applied to readonly types */
export const ReadonlyKind = Symbol.for('TypeBox.Readonly');
/** Symbol key applied to optional types */
export const OptionalKind = Symbol.for('TypeBox.Optional');
/** Symbol key applied to types */
export const Hint = Symbol.for('TypeBox.Hint');
/** Symbol key applied to types */
export const Kind = Symbol.for('TypeBox.Kind');

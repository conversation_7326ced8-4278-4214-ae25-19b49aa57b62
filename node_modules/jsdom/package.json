{"name": "jsdom", "version": "27.0.0", "description": "A JavaScript implementation of many web standards", "keywords": ["dom", "html", "whatwg", "w3c"], "maintainers": ["<PERSON> <<EMAIL>> (http://tmpvar.com)", "Domenic Denicola <<EMAIL>> (https://domenic.me/)", "<PERSON> <<EMAIL>> (https://blog.smayr.name/)", "<PERSON><PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (https://timothygu.me/)", "<PERSON><PERSON> <<EMAIL>> (https://zirro.se/)", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/jsdom/jsdom.git"}, "dependencies": {"@asamuzakjp/dom-selector": "^6.5.4", "cssstyle": "^5.3.0", "data-urls": "^6.0.0", "decimal.js": "^10.5.0", "html-encoding-sniffer": "^4.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.6", "is-potential-custom-element-name": "^1.0.1", "parse5": "^7.3.0", "rrweb-cssom": "^0.8.0", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^6.0.0", "w3c-xmlserializer": "^5.0.0", "webidl-conversions": "^8.0.0", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "whatwg-url": "^15.0.0", "ws": "^8.18.2", "xml-name-validator": "^5.0.0"}, "peerDependencies": {"canvas": "^3.0.0"}, "peerDependenciesMeta": {"canvas": {"optional": true}}, "devDependencies": {"@domenic/eslint-config": "^4.0.1", "benchmark": "^2.1.4", "eslint": "^9.35.0", "eslint-plugin-html": "^8.1.3", "globals": "^16.4.0", "js-yaml": "^4.1.0", "minimatch": "^10.0.1", "mocha": "^11.7.2", "mocha-sugar-free": "^1.4.0", "npm-run-all2": "^8.0.4", "pngjs": "^7.0.0", "server-destroy": "^1.0.1", "webidl2js": "^19.0.0", "yargs": "^18.0.0"}, "scripts": {"prepare": "npm-run-all prepare:*", "prepare:convert-idl": "node ./scripts/webidl/convert.js", "prepare:generate-js-globals": "node ./scripts/generate-js-globals.js", "pretest": "npm run prepare && npm run wpt:init", "test": "npm-run-all --continue-on-error test:*", "test:api": "mocha test/api/", "test:to-port-to-wpts": "mocha test/to-port-to-wpts/ && mocha test/to-port-to-wpts/level1/ && mocha test/to-port-to-wpts/level2/ && mocha test/to-port-to-wpts/level3/", "test:tuwpt": "mocha test/web-platform-tests/run-tuwpts.js", "test:wpt": "mocha test/web-platform-tests/run-wpts.js", "lint": "eslint --cache", "wpt:init": "git submodule update --init --recursive", "wpt:reset": "rm -rf ./test/web-platform-tests/tests && npm run wpt:init", "wpt:update": "git submodule update --init --recursive --remote && cd test/web-platform-tests/tests && python wpt.py manifest --path ../wpt-manifest.json", "update-authors": "git log --format=\"%aN <%aE>\" | sort -f | uniq > AUTHORS.txt", "benchmark": "node ./benchmark/runner"}, "main": "./lib/api.js", "type": "commonjs", "engines": {"node": ">=20"}}
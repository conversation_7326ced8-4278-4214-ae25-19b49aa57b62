#!/usr/bin/env node

import { spawn } from 'child_process';
import { dirname, join } from 'path';
import { fileURLToPath } from 'url';
import fs from 'fs';

const __dirname = dirname(fileURLToPath(import.meta.url));

// Check if .env file exists, if not create a template
const envPath = join(__dirname, '.env');
if (!fs.existsSync(envPath)) {
  const envTemplate = `# Environment Variables
# Copy this file to .env and fill in your API keys

# Server Configuration
PORT=3001

# AI Service API Keys
FAL_KEY=your_fal_ai_key_here
GROQ_API_KEY=your_groq_api_key_here

# Game Configuration
NODE_ENV=development
`;
  fs.writeFileSync(envPath, envTemplate);
}

// Function to start a process
function startProcess(command, args, options = {}) {
  return new Promise((resolve, reject) => {
    const proc = spawn(command, args, {
      stdio: 'inherit',
      cwd: options.cwd || __dirname,
      shell: true,
      ...options
    });

    proc.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Process exited with code ${code}`));
      }
    });

    proc.on('error', (error) => {
      reject(error);
    });
  });
}

// Main function
async function main() {
  const args = process.argv.slice(2);
  const mode = args[0] || 'dev';

  try {
    if (mode === 'dev' || mode === 'development') {
      // Start both frontend and backend concurrently
      const frontend = startProcess('npm', ['run', 'dev']);
      const backend = startProcess('npm', ['run', 'server:dev']);

      await Promise.race([frontend, backend]);

    } else if (mode === 'network') {
      // Start both frontend and backend concurrently with network access
      const frontend = startProcess('npx', ['vite', '--host']);
      const backend = startProcess('npm', ['run', 'server:dev']);

      await Promise.race([frontend, backend]);

    } else if (mode === 'server') {
      await startProcess('npm', ['run', 'server:dev']);

    } else if (mode === 'frontend') {
      await startProcess('npm', ['run', 'dev']);

    } else if (mode === 'frontend:network') {
      await startProcess('npx', ['vite', '--host']);

    } else if (mode === 'prod' || mode === 'production') {
      await startProcess('npm', ['run', 'server']);

    } else {
      console.error('Usage: node start.js [mode]\n\nModes:\n  dev (default)       - Start both frontend and backend in development\n  network             - Start both frontend and backend with network access\n  server              - Start only the backend server\n  frontend            - Start only the frontend development server\n  frontend:network    - Start only the frontend development server with network access\n  prod/production     - Start production server (backend only)');
      process.exit(1);
    }
  } catch (error) {
    console.error('Error starting services:', error.message);
    process.exit(1);
  }
}

main();
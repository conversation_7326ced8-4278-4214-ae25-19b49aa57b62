# Enemy Progression System

## 1. Overview

To provide a compelling reason for players to invest in the "Hangar" upgrade system, a robust enemy progression system must be implemented. This system will ensure that as players grow stronger, so do their adversaries. The core goal is to create a dynamic and challenging gameplay loop where player upgrades are essential for survival and success, particularly in higher-level content and OrangeSDK-run tournaments.

This system will replace or significantly enhance any static enemy definitions, ensuring that each encounter feels progressively more difficult and requires strategic thinking and adaptation.

## 2. Core Principles

*   **Scaled Difficulty:** Enemy stats and behaviors will scale with the player's progress, specifically the `currentLevel` from the `LevelManager` and potentially the player's overall Hangar upgrade level.
*   **Dynamic Variety:** New enemy types and attack patterns will be introduced as the player progresses, preventing the gameplay from becoming stale.
*   **Predictable Challenge:** While the game will get harder, the scaling will be understandable and predictable, allowing players to prepare and strategize.
*   **Meaningful Upgrades:** Every upgrade in the Hangar should feel like a direct counter to the escalating enemy threat.

## 3. Scaling Mechanism

The primary driver for enemy scaling will be the `currentLevel` from [`LevelManager.js`](src/managers/LevelManager.js:1). We can define a "scaling coefficient" or "difficulty tier" based on this level.

## 4. Progression Tiers

Instead of a smooth, continuous curve, we can define distinct "tiers" of difficulty. Every 5 or 10 levels, enemies receive a significant power boost and new abilities are introduced.

| Tier | Level Range | Scaling Coefficient | New Enemy Types / Behaviors |
| :--- | :--- | :--- | :--- |
| 1 | 1-5 | 1.0 - 1.8 | Basic enemies, single projectile attacks. |
| 2 | 6-10 | 2.0 - 3.6 | Faster enemies, introduce enemies with spread-shot or burst-fire patterns. |
| 3 | 11-15 | 4.0 - 7.2 | "Tank" enemies with high health, introduce enemies with simple homing projectiles. |
| 4 | 16-20 | 8.0 - 14.4 | "Swarm" enemies (low health, high speed, large numbers), introduce enemies that fire in complex patterns (e.g., spirals, walls). |
| 5 | 21+ | 16.0+ | "Elite" variants of all base types with multiple enhanced stats and abilities. Introduction of mini-bosses outside of scheduled boss fights. |

## 5. Stat Scaling

Each enemy's base stats will be multiplied by the scaling coefficient. This ensures that every enemy type remains relevant throughout the game.


**Stat Scaling Details:**

| Attribute | Scaling Formula | Rationale |
| :--- | :--- | :--- |
| **Health** | `baseHealth * coeff` | Directly increases time-to-kill, requiring more player firepower. |
| **Speed** | `baseSpeed * coeff` | Makes enemies harder to hit and dodge, increasing the pace of the game. |
| **Damage** | `baseDamage * coeff` | Increases the penalty for getting hit, making player health and shields more valuable. |
| **Fire Rate** | `baseFireRate / sqrt(coeff)` | Enemies shoot more frequently, creating denser bullet patterns. `sqrt` prevents it from becoming a bullet hell too quickly. |
| **Point Value** | `basePointValue * coeff` | Ensures the token economy keeps pace with the difficulty, rewarding players for overcoming tougher challenges. |

## 6. Behavioral and Ability Progression

Beyond raw stats, enemies should become more intelligent and gain new abilities.

*   **Tier 1 (Basic):** Move in simple patterns (straight down, sine wave). Fire single shots directly at the player.
*   **Tier 2 (Adaptive):** Some enemies begin to lead their shots. Introduction of "Sniper" enemies that aim precisely. "Gunner" enemies fire in 3-shot bursts.
*   **Tier 3 (Tactical):** "Tank" enemies move slowly but can temporarily shield nearby allies. "Homing" enemies fire projectiles that track the player for a short duration.
*   **Tier 4 (Complex):** "Swarm" enemies move in coordinated formations. "Pattern" enemies create complex bullet hell patterns (spirals, walls of bullets). Some enemies gain a short-dash ability to evade player fire.
*   **Tier 5 (Elite):** "Elite" enemies combine multiple abilities (e.g., a tank that can also dash and shield allies). Mini-bosses appear, having multiple phases and a wide array of attacks.

## 7. Implementation Plan

1.  **Create `EnemyScaling.js` utility:** This file will contain the `getDifficultyCoefficient` and `getTierForLevel` functions. This keeps scaling logic separate and reusable.

2.  **Modify `EnemyManager.js`:** When [`EnemyManager.js`](src/managers/EnemyManager.js:1) spawns an enemy, it must pass the `currentLevel` to the enemy constructor.


3.  **Refactor `Enemy.js`:** The [`Enemy.js`](src/entities/Enemy.js:1) constructor will accept the `level` and apply the scaling to its internal stats, as shown in the "Stat Scaling" section above. It will also use the tier to determine its AI and attack patterns.

4.  **Balance Iteration:** This is the most critical step. The scaling percentages and tier thresholds will need extensive playtesting to ensure the game is challenging but fair. Player feedback will be key.

## 8. Synergy with Hangar Upgrades

This enemy progression system directly creates the need for the Hangar upgrades.

*   As enemy **health** and **speed** increase, the player's **damage** and **fire rate** upgrades become essential to kill them before being overwhelmed.
*   As enemy **damage** and **fire rate** increase, the player's **max health** and **shield** upgrades become critical for survival.
*   As enemy **patterns** become more complex, the player's **engine tuning** (speed/acceleration) upgrades are necessary to navigate through bullet hell phases.

This creates a clear and compelling loop: **Play -> Earn WISH -> Upgrade in Hangar -> Tackle Harder Enemies -> Earn More WISH**. This loop is the core of the game's long-term engagement and its success as a competitive title.